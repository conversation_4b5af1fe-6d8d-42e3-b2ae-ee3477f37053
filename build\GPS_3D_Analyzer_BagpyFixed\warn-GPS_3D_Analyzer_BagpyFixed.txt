
This file lists modules Py<PERSON>nstalle<PERSON> was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'collections.abc' - imported by traceback (top-level), inspect (top-level), logging (top-level), typing (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), matplotlib (top-level), matplotlib.cbook (top-level), numpy.lib._npyio_impl (top-level), http.client (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), yaml.constructor (top-level), numpy.random.bit_generator (top-level), typing_extensions (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), matplotlib._path (top-level), matplotlib.colors (top-level), PIL.Image (top-level), PIL._typing (top-level), xml.etree.ElementTree (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.ImagePalette (top-level), PIL.ImageFilter (top-level), PIL.PngImagePlugin (top-level), pyparsing.core (top-level), pyparsing.results (top-level), cycler (top-level), matplotlib.cm (top-level), matplotlib.markers (top-level), matplotlib._mathtext (conditional), matplotlib.axes._base (top-level), matplotlib.spines (top-level), matplotlib.pyplot (conditional), matplotlib.typing (top-level), configparser (top-level), markdown_it.main (top-level), markdown_it.rules_inline.state_inline (top-level), markdown_it._compat (top-level), markdown_it.ruler (top-level), markdown_it.token (top-level), markdown_it.common.normalize_url (top-level), markdown_it.rules_core.state_core (top-level), markdown_it.renderer (top-level), markdown_it.utils (top-level), rich.scope (top-level), attr._compat (top-level), attr._make (top-level), importlib_metadata (top-level), importlib_metadata._meta (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), importlib_resources.readers (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), pkg_resources (top-level), platformdirs.api (conditional), platformdirs.windows (conditional), platformdirs.unix (conditional), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), wheel.cli.convert (top-level), wheel.cli.tags (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level), scipy._lib._array_api (top-level), scipy._lib.array_api_compat.common._helpers (top-level), scipy._lib.array_api_compat.common._typing (top-level), scipy._lib.array_api_compat._internal (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib._docscrape (top-level), scipy._lib.array_api_extra._delegation (top-level), scipy._lib.array_api_extra._lib._funcs (top-level), scipy._lib.array_api_extra._lib._at (top-level), scipy._lib.array_api_extra._lib._utils._helpers (top-level), scipy._lib.array_api_extra._lib._lazy (top-level), scipy._lib.array_api_extra.testing (top-level), scipy.special._support_alternative_backends (top-level), scipy._lib.pyprima (top-level), scipy.stats._stats_py (top-level), scipy.spatial.distance (top-level), scipy._lib.doccer (top-level), scipy.integrate._quadrature (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._resampling (top-level), scipy.stats._multicomp (top-level), scipy.stats._qmc (top-level), scipy.stats._sensitivity_analysis (top-level), scipy.ndimage._filters (top-level), scipy.ndimage._ni_support (top-level), scipy.linalg._decomp_cossin (top-level), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.indexing (conditional), pandas.core.internals.blocks (conditional), pandas.core.arrays.numeric (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io._util (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlite3.dbapi2 (top-level), pandas.core.groupby.groupby (top-level), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.arrays.numpy_ (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.string_ (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), seaborn._core.typing (top-level), seaborn._base (top-level), seaborn._core.data (top-level), seaborn.external.docscrape (top-level), seaborn._core.scales (top-level), seaborn._core.plot (top-level), seaborn._marks.base (top-level), seaborn._stats.base (top-level), seaborn._core.subplots (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), PIL.ImageDraw (top-level), scipy._lib.array_api_compat.common._fft (top-level), scipy.constants._codata (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional), psutil (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), pty (delayed, optional), _pyrepl.unix_console (top-level), xmlrpc.server (optional), rosbag.rosbag_main (delayed, optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\miniconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\miniconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), setuptools._distutils.dist (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), scipy._lib.array_api_compat.common._typing (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), _pyrepl.pager (delayed, optional), getpass (optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level), rosbag.rosbag_main (delayed, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), site (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by six (conditional), Cryptodome.Util.py3compat (conditional), gnupg._util (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
excluded module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named pyparsing.operatorPrecedence - imported by pyparsing (optional), catkin_pkg.condition (optional)
missing module named markupsafe.Markup - imported by markupsafe (top-level), jinja2.environment (top-level), jinja2.nodes (top-level), jinja2.runtime (top-level), jinja2.compiler (top-level), jinja2.filters (top-level), jinja2.sandbox (top-level), jinja2.ext (top-level)
missing module named markupsafe.EscapeFormatter - imported by markupsafe (top-level), jinja2.sandbox (top-level)
missing module named markupsafe.soft_str - imported by markupsafe (top-level), jinja2.runtime (top-level), jinja2.filters (top-level)
missing module named markupsafe.escape - imported by markupsafe (top-level), jinja2.runtime (top-level), jinja2.compiler (top-level), jinja2.filters (top-level), pandas.io.formats.style_render (top-level)
missing module named tputil - imported by jinja2.debug (conditional, optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional)
missing module named rosdep2 - imported by rospkg.manifest (delayed, conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional), gnupg._util (conditional, optional), rosbag.bag (optional), genmsg.gentools (optional), genmsg.msg_loader (optional), genpy.message (delayed, optional), genpy.dynamic (optional), rosgraph.network (optional), rospy.impl.tcpros_base (optional), rospy.topics (optional), rosbag.migration (optional)
missing module named netifaces - imported by rosgraph.network (delayed, conditional)
missing module named urlparse - imported by rosgraph.network (optional), rospy.core (optional), rospy.impl.masterslave (optional)
missing module named xmlrpclib - imported by rosgraph.masterapi (optional), rospy.core (optional), rospy.impl.registration (optional), rospy.impl.tcpros_pubsub (optional)
missing module named SocketServer - imported by rosgraph.xmlrpc (optional)
missing module named SimpleXMLRPCServer - imported by rosgraph.xmlrpc (optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), rosgraph.xmlrpc (optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional), rospy.core (optional)
excluded module named IPython - imported by pandas.io.formats.printing (delayed), bagpy.bagreader (delayed)
missing module named ipywidgets - imported by rich.live (delayed, conditional, optional), seaborn.widgets (optional)
missing module named 'sphinx.ext' - imported by scipy._lib._docscrape (delayed, conditional), seaborn.external.docscrape (delayed, conditional)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_extra.testing (delayed)
missing module named cupy - imported by scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._typing (top-level)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib._array_api (delayed, conditional), scipy._lib.array_api_extra._lib._utils._helpers (delayed, conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional), scipy._lib.array_api_extra.testing (delayed)
missing module named scipy._lib.array_api_compat.common.array_namespace - imported by scipy._lib.array_api_compat.common (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level)
excluded module named pytest - imported by scipy._lib._testutils (delayed), scipy._lib._array_api (delayed), scipy._lib.array_api_extra.testing (conditional), pandas._testing._io (delayed), pandas._testing (delayed)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named scipy.special.roots_legendre - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.integrate._rules._gauss_legendre (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.linalg._sketches (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), pandas.core.dtypes.common (delayed, conditional, optional), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level)
missing module named 'scikits.umfpack' - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.sparse.linalg.splu - imported by scipy.sparse.linalg (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.linalg.aslinearoperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.linalg._svdp (top-level), scipy.sparse.linalg._expm_multiply (top-level), scipy.sparse.linalg._onenormest (top-level)
missing module named scipy.sparse.linalg.lsmr - imported by scipy.sparse.linalg (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level)
missing module named scipy.sparse.linalg.LinearOperator - imported by scipy.sparse.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._differentiable_functions (top-level), scipy.optimize._trustregion_constr.minimize_trustregion_constr (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.tr_interior_point (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.linalg.interpolative (delayed), scipy.sparse.csgraph._laplacian (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.block_array - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.isspmatrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.cho_solve_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.cholesky_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.solve_banded - imported by scipy.linalg (top-level), scipy.spatial.transform._rotation_spline (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.optimize._nonlin (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.optimize._lsq.common (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level), scipy.linalg._decomp_polar (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy._lib.cobyqa.models (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg.solve_triangular - imported by scipy.linalg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scikits - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.optimize.minimize - imported by scipy.optimize (delayed, conditional, optional), scipy._lib.pyprima.common._project (delayed, conditional, optional), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level)
missing module named scipy.special.betainc - imported by scipy.special (top-level), scipy.stats._quantile (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.stats._multivariate (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.psi - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.kv - imported by scipy.special (top-level), scipy.stats._hypotests (top-level)
missing module named scipy.special.gamma - imported by scipy.special (top-level), scipy.stats._hypotests (top-level)
missing module named scipy.special.zeta - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level), scipy.optimize._dual_annealing (top-level), scipy.special._spfun_stats (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.factorial - imported by scipy.special (top-level), scipy.interpolate._polyint (top-level), scipy.stats._resampling (top-level)
missing module named scipy.special.poch - imported by scipy.special (top-level), scipy.interpolate._bsplines (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.comb - imported by scipy.special (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._rbfinterp (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._resampling (top-level), scipy.linalg._special_matrices (delayed)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), Cryptodome.Util._raw_api (conditional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named array_api_compat - imported by scipy._lib.array_api_extra._lib._utils._compat (optional)
missing module named 'dask.typing' - imported by scipy._lib.array_api_extra.testing (conditional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named 'statsmodels.nonparametric' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.tools' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.robust' - imported by seaborn.regression (delayed, conditional)
missing module named 'statsmodels.genmod' - imported by seaborn.regression (delayed, conditional)
missing module named statsmodels - imported by seaborn.regression (optional)
missing module named 'win32com.shell' - imported by seaborn.external.appdirs (conditional, optional)
missing module named 'com.sun' - imported by seaborn.external.appdirs (delayed, conditional, optional)
missing module named com - imported by seaborn.external.appdirs (delayed)
missing module named win32api - imported by seaborn.external.appdirs (delayed, conditional, optional)
missing module named win32com - imported by seaborn.external.appdirs (delayed)
missing module named UserDict - imported by pytz.lazy (optional), rosbag.rosbag_main (optional)
missing module named roslz4._roslz4 - imported by roslz4 (top-level)
missing module named gnupg.test - imported by gnupg.gnupg (conditional)
missing module named ordereddict - imported by gnupg._parsers (optional)
missing module named _suggestions - imported by traceback (delayed, optional)
missing module named 'setuptools.extern' - imported by setuptools_scm._version_cls (optional)
missing module named 'IPython.display' - imported by rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named _winreg - imported by pygments.formatters.img (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named chardet - imported by pygments.lexer (delayed, conditional, optional)
missing module named 'IPython.core' - imported by rich.pretty (delayed, optional), pandas.io.formats.printing (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.arrow.accessors (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io._util (conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'lxml.etree' - imported by pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named lxml - imported by pandas.io.xml (conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named fsspec - imported by pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named google - imported by pandas.io.gbq (conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
