#!/bin/bash

# SLAM核心节点崩溃修复脚本

echo "=========================================="
echo "🔧 SLAM核心节点崩溃修复"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "步骤1: 分析崩溃原因"
echo "===================="

# 检查最新的崩溃日志
LATEST_LOG=$(find ~/.ros/log -name "state_estimation_node-*.log" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)

if [ -f "$LATEST_LOG" ]; then
    echo "找到最新崩溃日志: $LATEST_LOG"
    echo ""
    echo "崩溃日志内容:"
    echo "=============="
    tail -20 "$LATEST_LOG"
    echo ""
else
    echo "未找到崩溃日志"
fi

echo ""
echo "步骤2: 检查系统环境"
echo "=================="

# 检查点云topic
echo "检查可用的点云topic:"
timeout 5 rostopic list | grep -E "(points|cloud)" || echo "未找到点云topic"

# 检查IMU topic
echo ""
echo "检查可用的IMU topic:"
timeout 5 rostopic list | grep -E "(imu|IMU)" || echo "未找到IMU topic"

# 检查GPS topic
echo ""
echo "检查可用的GPS topic:"
timeout 5 rostopic list | grep -E "(gps|gnss|GPS)" || echo "未找到GPS topic"

echo ""
echo "步骤3: 修复配置参数"
echo "=================="

# 设置安全的SLAM参数
echo "设置安全的SLAM参数..."
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 禁用可能导致崩溃的功能
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_icp_loop_closure false

# 设置保守的处理参数
rosparam set /state_estimation_node/voxel_size 0.8
rosparam set /state_estimation_node/max_iterations 20
rosparam set /state_estimation_node/transformation_epsilon 1e-5
rosparam set /state_estimation_node/euclidean_fitness_epsilon 1e-5

# 设置内存安全参数
rosparam set /state_estimation_node/max_points_per_scan 50000
rosparam set /state_estimation_node/downsample_ratio 0.5
rosparam set /state_estimation_node/enable_intensity_processing false

echo "✅ 安全参数设置完成"

echo ""
echo "步骤4: 创建安全启动配置"
echo "======================"

# 创建安全启动的launch文件
cat > src/state_estimation/launch/safe_slam.launch << 'EOF'
<?xml version="1.0"?>
<launch>
    <!-- 安全SLAM启动配置 -->
    
    <!-- SLAM核心节点 - 安全模式 -->
    <node name="state_estimation_node" pkg="state_estimation" type="state_estimation_node" output="screen">
        <!-- 基本topic配置 -->
        <param name="lidar_topic" value="/velodyne_points" />
        <param name="imu_topic" value="/imu/data" />
        <param name="gps_topic" value="/rtk/gnss" />
        
        <!-- 安全处理参数 -->
        <param name="voxel_size" value="0.8" />
        <param name="max_iterations" value="20" />
        <param name="transformation_epsilon" value="1e-5" />
        <param name="euclidean_fitness_epsilon" value="1e-5" />
        
        <!-- 禁用可能导致崩溃的功能 -->
        <param name="enable_gps" value="false" />
        <param name="enable_loop_closure" value="false" />
        <param name="enable_icp_loop_closure" value="false" />
        <param name="enable_intensity_processing" value="false" />
        
        <!-- 内存安全参数 -->
        <param name="max_points_per_scan" value="50000" />
        <param name="downsample_ratio" value="0.5" />
        <param name="enable_point_cloud_validation" value="true" />
        <param name="min_points_threshold" value="100" />
        
        <!-- 调试参数 -->
        <param name="enable_debug_output" value="true" />
        <param name="log_level" value="INFO" />
    </node>
    
</launch>
EOF

echo "✅ 安全启动配置创建完成"

echo ""
echo "步骤5: 测试修复效果"
echo "=================="

echo "选择测试方式:"
echo "1) 使用安全launch文件测试"
echo "2) 手动启动测试"
echo "3) 使用GDB调试测试"
echo "4) 跳过测试"
echo ""

read -p "请选择 (1-4): " test_choice

case $test_choice in
    1)
        echo ""
        echo "🧪 使用安全launch文件测试"
        echo "========================="
        
        echo "启动安全SLAM配置..."
        timeout 30 roslaunch state_estimation safe_slam.launch &
        LAUNCH_PID=$!
        
        sleep 10
        
        # 检查节点是否还在运行
        if ps -p $LAUNCH_PID > /dev/null; then
            echo "✅ SLAM节点运行稳定"
            
            # 检查topic输出
            echo ""
            echo "检查输出topic:"
            timeout 5 rostopic list | grep -E "(mapped|path)" || echo "暂无输出topic"
            
            echo ""
            echo "按任意键停止测试..."
            read -n 1
            kill $LAUNCH_PID 2>/dev/null
        else
            echo "❌ SLAM节点仍然崩溃"
            echo "需要进一步调试"
        fi
        ;;
        
    2)
        echo ""
        echo "🧪 手动启动测试"
        echo "==============="
        
        echo "手动启动SLAM节点..."
        timeout 30 rosrun state_estimation state_estimation_node &
        NODE_PID=$!
        
        sleep 10
        
        if ps -p $NODE_PID > /dev/null; then
            echo "✅ SLAM节点手动启动成功"
            echo ""
            echo "按任意键停止测试..."
            read -n 1
            kill $NODE_PID 2>/dev/null
        else
            echo "❌ SLAM节点手动启动失败"
        fi
        ;;
        
    3)
        echo ""
        echo "🐛 使用GDB调试测试"
        echo "=================="
        
        # 检查GDB
        if ! command -v gdb &> /dev/null; then
            echo "安装GDB..."
            sudo apt install -y gdb
        fi
        
        echo "准备GDB调试..."
        echo "GDB将在崩溃时自动显示调用栈"
        echo ""
        
        # 创建GDB脚本
        cat > /tmp/gdb_slam.gdb << 'EOF'
set environment ROS_MASTER_URI=http://localhost:11311
set environment ROS_HOSTNAME=localhost
set logging file /tmp/slam_crash.log
set logging on
run
bt full
info registers
thread apply all bt
quit
EOF
        
        echo "启动GDB调试SLAM节点..."
        gdb -batch -x /tmp/gdb_slam.gdb --args devel/lib/state_estimation/state_estimation_node
        
        echo ""
        echo "GDB调试完成，查看崩溃信息:"
        if [ -f "/tmp/slam_crash.log" ]; then
            cat /tmp/slam_crash.log
        fi
        ;;
        
    4)
        echo "跳过测试"
        ;;
        
    *)
        echo "无效选择"
        ;;
esac

echo ""
echo "步骤6: 提供解决方案"
echo "=================="

echo ""
echo "🔧 根据崩溃原因，提供以下解决方案:"
echo ""
echo "方案1: 使用安全模式启动"
echo "======================"
echo "  roslaunch state_estimation safe_slam.launch"
echo ""
echo "方案2: 修改点云topic"
echo "=================="
echo "  # 检查实际的点云topic名称"
echo "  rostopic list | grep points"
echo "  # 修改参数"
echo "  rosparam set /state_estimation_node/lidar_topic /your_actual_topic"
echo ""
echo "方案3: 降级处理参数"
echo "=================="
echo "  rosparam set /state_estimation_node/voxel_size 1.0"
echo "  rosparam set /state_estimation_node/max_iterations 10"
echo "  rosparam set /state_estimation_node/downsample_ratio 0.3"
echo ""
echo "方案4: 使用仅IMU模式"
echo "=================="
echo "  rosparam set /state_estimation_node/use_lidar false"
echo "  rosparam set /state_estimation_node/use_imu_only true"
echo ""
echo "方案5: 检查数据源"
echo "================"
echo "  # 确保bag文件包含正确的点云数据"
echo "  rosbag info your_data.bag | grep -E '(points|cloud)'"
echo ""

echo "=========================================="
echo "修复脚本执行完成"
echo "=========================================="
