#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级回环执行器
执行多算法点云匹配和回环闭合
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque

class AdvancedLoopExecutor:
    def __init__(self):
        rospy.init_node('advanced_loop_executor', anonymous=True)
        
        # 匹配算法参数
        self.matching_algorithms = {
            'icp': {
                'max_iterations': 100,
                'transformation_epsilon': 1e-8,
                'euclidean_fitness_epsilon': 1e-6,
                'max_correspondence_distance': 1.0,
                'weight': 0.4
            },
            'ndt': {
                'resolution': 0.5,
                'max_iterations': 50,
                'transformation_epsilon': 1e-8,
                'weight': 0.35
            },
            'gicp': {
                'max_iterations': 80,
                'transformation_epsilon': 1e-8,
                'max_correspondence_distance': 1.5,
                'weight': 0.25
            }
        }
        
        # 执行统计
        self.execution_stats = {
            'total_executions': 0,
            'successful_matches': 0,
            'failed_matches': 0,
            'algorithm_performance': {
                'icp': {'attempts': 0, 'successes': 0, 'avg_fitness': 0.0},
                'ndt': {'attempts': 0, 'successes': 0, 'avg_fitness': 0.0},
                'gicp': {'attempts': 0, 'successes': 0, 'avg_fitness': 0.0}
            }
        }
        
        # 数据缓存
        self.recent_pointclouds = deque(maxlen=100)
        self.execution_history = deque(maxlen=20)
        
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.trigger_sub = rospy.Subscriber('/final_loop_closure_trigger', String,
                                           self.execute_loop_closure, queue_size=1)
        self.pointcloud_sub = rospy.Subscriber('/cloud_registered', PointCloud2,
                                              self.pointcloud_callback, queue_size=10)
        
        # 发布器
        self.execution_result_pub = rospy.Publisher('/loop_execution_result', String, queue_size=1)
        self.match_quality_pub = rospy.Publisher('/match_quality_score', Float64, queue_size=1)
        self.executor_status_pub = rospy.Publisher('/loop_executor_status', String, queue_size=1)
        
        # 定时器
        self.status_timer = rospy.Timer(rospy.Duration(20.0), self.publish_status)
        
        rospy.loginfo("Advanced Loop Executor Started")
        rospy.loginfo("Multi-algorithm matching: ICP + NDT + GICP")
    
    def pointcloud_callback(self, msg):
        """缓存点云数据"""
        with self.data_lock:
            pointcloud_info = {
                'timestamp': msg.header.stamp.to_sec(),
                'frame_id': msg.header.frame_id,
                'point_count': msg.width * msg.height,
                'data': msg  # 实际应用中可能需要更高效的存储
            }
            self.recent_pointclouds.append(pointcloud_info)
    
    def execute_loop_closure(self, msg):
        """执行回环闭合"""
        try:
            with self.data_lock:
                self.execution_stats['total_executions'] += 1
                
                # 解析触发数据
                trigger_data = json.loads(msg.data)
                
                # 获取当前和目标位姿
                current_pose = trigger_data['original_candidate']['current_pose']['slam']
                target_pose = trigger_data['original_candidate']['target_pose']['slam']
                
                # 执行多算法匹配
                matching_results = self.perform_multi_algorithm_matching(current_pose, target_pose)
                
                # 选择最佳匹配结果
                best_result = self.select_best_matching_result(matching_results)
                
                # 验证匹配质量
                quality_assessment = self.assess_matching_quality(best_result, trigger_data)
                
                # 构建执行结果
                execution_result = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'trigger_data': trigger_data,
                    'matching_results': matching_results,
                    'best_result': best_result,
                    'quality_assessment': quality_assessment,
                    'execution_success': quality_assessment['overall_quality'] > 0.6
                }
                
                # 更新统计
                if execution_result['execution_success']:
                    self.execution_stats['successful_matches'] += 1
                else:
                    self.execution_stats['failed_matches'] += 1
                
                # 记录执行历史
                self.execution_history.append({
                    'timestamp': execution_result['timestamp'],
                    'success': execution_result['execution_success'],
                    'best_algorithm': best_result['algorithm'],
                    'fitness_score': best_result['fitness_score'],
                    'confidence': trigger_data.get('enhanced_confidence', 0.0)
                })
                
                # 发布结果
                self.publish_execution_result(execution_result)
                
                rospy.loginfo("🔧 Loop Execution Completed!")
                rospy.loginfo("Algorithm: %s | Fitness: %.4f | Success: %s",
                             best_result['algorithm'],
                             best_result['fitness_score'],
                             "YES" if execution_result['execution_success'] else "NO")
                
        except Exception as e:
            rospy.logerr("Error executing loop closure: %s", str(e))
            self.execution_stats['failed_matches'] += 1
    
    def perform_multi_algorithm_matching(self, current_pose, target_pose):
        """执行多算法匹配"""
        matching_results = {}
        
        # 模拟点云匹配（实际实现需要真实的点云匹配算法）
        for algorithm, params in self.matching_algorithms.items():
            try:
                self.execution_stats['algorithm_performance'][algorithm]['attempts'] += 1
                
                # 模拟匹配过程
                result = self.simulate_matching_algorithm(algorithm, params, current_pose, target_pose)
                
                matching_results[algorithm] = result
                
                # 更新算法性能统计
                if result['converged']:
                    self.execution_stats['algorithm_performance'][algorithm]['successes'] += 1
                    
                    # 更新平均适应度
                    perf = self.execution_stats['algorithm_performance'][algorithm]
                    perf['avg_fitness'] = (perf['avg_fitness'] * (perf['successes'] - 1) + result['fitness_score']) / perf['successes']
                
            except Exception as e:
                rospy.logwarn("Error in %s matching: %s", algorithm, str(e))
                matching_results[algorithm] = {
                    'algorithm': algorithm,
                    'converged': False,
                    'fitness_score': float('inf'),
                    'iterations': 0,
                    'transformation_matrix': np.eye(4).tolist(),
                    'error': str(e)
                }
        
        return matching_results
    
    def simulate_matching_algorithm(self, algorithm, params, current_pose, target_pose):
        """模拟匹配算法（实际实现需要真实的PCL算法）"""
        # 计算位姿差异
        dx = current_pose['x'] - target_pose['x']
        dy = current_pose['y'] - target_pose['y']
        dz = current_pose['z'] - target_pose['z']
        distance = np.sqrt(dx*dx + dy*dy + dz*dz)
        
        # 基于距离和算法特性模拟适应度分数
        base_fitness = distance * 0.1
        
        if algorithm == 'icp':
            # ICP对近距离匹配效果好
            fitness_score = base_fitness * (1.0 + np.random.normal(0, 0.1))
            iterations = min(params['max_iterations'], int(50 + distance * 10))
        elif algorithm == 'ndt':
            # NDT对中等距离匹配效果好
            fitness_score = base_fitness * (0.9 + np.random.normal(0, 0.15))
            iterations = min(params['max_iterations'], int(30 + distance * 5))
        elif algorithm == 'gicp':
            # GICP对复杂场景效果好
            fitness_score = base_fitness * (0.8 + np.random.normal(0, 0.2))
            iterations = min(params['max_iterations'], int(40 + distance * 8))
        
        # 判断收敛
        converged = fitness_score < 0.5 and iterations < params['max_iterations'] * 0.8
        
        # 生成变换矩阵（简化版）
        transformation_matrix = np.eye(4)
        if converged:
            transformation_matrix[0, 3] = -dx * 0.9  # 部分校正
            transformation_matrix[1, 3] = -dy * 0.9
            transformation_matrix[2, 3] = -dz * 0.9
        
        return {
            'algorithm': algorithm,
            'converged': converged,
            'fitness_score': max(0.01, fitness_score),  # 避免零值
            'iterations': iterations,
            'transformation_matrix': transformation_matrix.tolist(),
            'execution_time': np.random.uniform(0.1, 2.0)  # 模拟执行时间
        }
    
    def select_best_matching_result(self, matching_results):
        """选择最佳匹配结果"""
        best_result = None
        best_score = float('inf')
        
        for algorithm, result in matching_results.items():
            if not result['converged']:
                continue
            
            # 综合评分：适应度分数 + 算法权重
            algorithm_weight = self.matching_algorithms[algorithm]['weight']
            weighted_score = result['fitness_score'] / algorithm_weight
            
            if weighted_score < best_score:
                best_score = weighted_score
                best_result = result.copy()
                best_result['weighted_score'] = weighted_score
        
        # 如果没有收敛的结果，选择适应度最好的
        if best_result is None:
            for algorithm, result in matching_results.items():
                if result['fitness_score'] < best_score:
                    best_score = result['fitness_score']
                    best_result = result.copy()
                    best_result['weighted_score'] = best_score
        
        return best_result if best_result else {
            'algorithm': 'none',
            'converged': False,
            'fitness_score': float('inf'),
            'weighted_score': float('inf')
        }
    
    def assess_matching_quality(self, best_result, trigger_data):
        """评估匹配质量"""
        quality_scores = {}
        
        # 1. 算法收敛性
        quality_scores['convergence'] = 1.0 if best_result['converged'] else 0.0
        
        # 2. 适应度分数质量
        fitness = best_result['fitness_score']
        if fitness < 0.1:
            quality_scores['fitness_quality'] = 1.0
        elif fitness < 0.3:
            quality_scores['fitness_quality'] = 0.8
        elif fitness < 0.5:
            quality_scores['fitness_quality'] = 0.6
        else:
            quality_scores['fitness_quality'] = 0.3
        
        # 3. 与GPS引导的一致性
        gps_distance = trigger_data['original_candidate']['metrics']['gps_distance']
        slam_distance = trigger_data['original_candidate']['metrics']['slam_distance']
        
        distance_ratio = min(gps_distance, slam_distance) / max(gps_distance, slam_distance)
        quality_scores['gps_consistency'] = distance_ratio
        
        # 4. 触发置信度
        trigger_confidence = trigger_data.get('enhanced_confidence', 0.0)
        quality_scores['trigger_confidence'] = trigger_confidence
        
        # 5. 算法历史性能
        algorithm = best_result['algorithm']
        if algorithm in self.execution_stats['algorithm_performance']:
            perf = self.execution_stats['algorithm_performance'][algorithm]
            if perf['attempts'] > 0:
                success_rate = perf['successes'] / perf['attempts']
                quality_scores['algorithm_reliability'] = success_rate
            else:
                quality_scores['algorithm_reliability'] = 0.5
        else:
            quality_scores['algorithm_reliability'] = 0.5
        
        # 综合质量评分
        weights = {
            'convergence': 0.3,
            'fitness_quality': 0.25,
            'gps_consistency': 0.2,
            'trigger_confidence': 0.15,
            'algorithm_reliability': 0.1
        }
        
        overall_quality = sum(quality_scores[key] * weights[key] for key in quality_scores)
        
        return {
            'individual_scores': quality_scores,
            'overall_quality': overall_quality,
            'quality_level': self.get_quality_level(overall_quality),
            'recommendations': self.get_quality_recommendations(quality_scores, overall_quality)
        }
    
    def get_quality_level(self, overall_quality):
        """获取质量等级"""
        if overall_quality > 0.8:
            return 'excellent'
        elif overall_quality > 0.6:
            return 'good'
        elif overall_quality > 0.4:
            return 'fair'
        else:
            return 'poor'
    
    def get_quality_recommendations(self, quality_scores, overall_quality):
        """获取质量建议"""
        recommendations = []
        
        if quality_scores['convergence'] < 0.5:
            recommendations.append("Algorithm convergence issues - consider parameter tuning")
        
        if quality_scores['fitness_quality'] < 0.5:
            recommendations.append("High fitness score - verify point cloud quality")
        
        if quality_scores['gps_consistency'] < 0.6:
            recommendations.append("GPS-SLAM inconsistency - check GPS quality")
        
        if quality_scores['trigger_confidence'] < 0.6:
            recommendations.append("Low trigger confidence - increase detection thresholds")
        
        if overall_quality < 0.5:
            recommendations.append("Overall quality poor - consider rejecting this loop closure")
        
        return recommendations
    
    def publish_execution_result(self, execution_result):
        """发布执行结果"""
        try:
            # 发布详细结果
            result_msg = String()
            result_msg.data = json.dumps(execution_result, indent=2)
            self.execution_result_pub.publish(result_msg)
            
            # 发布质量分数
            quality_msg = Float64()
            quality_msg.data = execution_result['quality_assessment']['overall_quality']
            self.match_quality_pub.publish(quality_msg)
            
        except Exception as e:
            rospy.logerr("Error publishing execution result: %s", str(e))
    
    def publish_status(self, event):
        """发布状态信息"""
        try:
            with self.data_lock:
                total_executions = max(1, self.execution_stats['total_executions'])
                success_rate = self.execution_stats['successful_matches'] / total_executions
                
                status_data = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'execution_statistics': self.execution_stats.copy(),
                    'performance_metrics': {
                        'success_rate': success_rate,
                        'total_executions': total_executions,
                        'recent_executions': len(self.execution_history)
                    },
                    'algorithm_performance': self.execution_stats['algorithm_performance'].copy(),
                    'recent_history': list(self.execution_history)[-10:],
                    'system_health': {
                        'pointcloud_cache_size': len(self.recent_pointclouds),
                        'execution_queue_health': 'good' if len(self.execution_history) < 15 else 'busy'
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_data, indent=2)
                self.executor_status_pub.publish(status_msg)
                
                rospy.loginfo("Loop Executor Status: Executions=%d | Success Rate=%.1f%% | Best Algorithm=%s",
                             total_executions, success_rate * 100,
                             self.get_best_performing_algorithm())
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))
    
    def get_best_performing_algorithm(self):
        """获取表现最佳的算法"""
        best_algorithm = 'none'
        best_performance = 0.0
        
        for algorithm, perf in self.execution_stats['algorithm_performance'].items():
            if perf['attempts'] > 0:
                success_rate = perf['successes'] / perf['attempts']
                # 综合成功率和平均适应度
                performance_score = success_rate * (1.0 / (1.0 + perf['avg_fitness']))
                if performance_score > best_performance:
                    best_performance = performance_score
                    best_algorithm = algorithm
        
        return best_algorithm

def main():
    try:
        executor = AdvancedLoopExecutor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Advanced Loop Executor: %s", str(e))

if __name__ == '__main__':
    main()
