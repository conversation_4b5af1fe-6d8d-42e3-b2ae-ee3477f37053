<?xml version="1.0"?>
<launch>
    <!-- 增强版GPS强制回环检测优化的SLAM启动文件 -->
    <!-- 支持起点-终点、中间区域、重复访问等多种回环类型 -->

    <!-- 配置文件选择 -->
    <arg name="config_file" default="$(find state_estimation)/config/gps_loop_closure_params.yaml" />
    <arg name="use_preset" default="poor_gps_preset" />  <!-- 可选: poor_gps_preset, good_gps_preset, high_performance_preset, low_performance_preset -->

    <!-- 加载参数配置文件 -->
    <rosparam file="$(arg config_file)" command="load" />

    <!-- 可选：加载预设配置覆盖默认参数 -->
    <group if="$(eval arg('use_preset') != 'none')">
        <rosparam file="$(arg config_file)" command="load" ns="$(arg use_preset)" />
    </group>

    <!-- 命令行参数覆盖（优先级最高） -->
    <arg name="gps_topic" default="/rtk/gnss" />
    <arg name="loop_closure_distance_threshold" default="" />
    <arg name="min_trajectory_length" default="" />
    <arg name="gps_quality_threshold" default="" />
    <arg name="check_interval" default="" />
    <arg name="intermediate_loop_threshold" default="" />
    <arg name="min_loop_separation" default="" />
    <arg name="trajectory_window_size" default="" />
    <arg name="revisit_threshold" default="" />
    <arg name="force_search_radius" default="" />
    <arg name="max_search_candidates" default="" />
    <arg name="voxel_leaf_size" default="" />
    <arg name="min_keyframes_for_loop" default="" />
    <arg name="intermediate_search_radius" default="" />
    <arg name="revisit_search_radius" default="" />
    <arg name="start_end_score_threshold" default="" />
    <arg name="intermediate_score_threshold" default="" />
    <arg name="revisit_score_threshold" default="" />
    <arg name="sliding_window_size" default="" />
    
    <!-- 原始SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch">
        <!-- 包含您原有的SLAM配置 -->
    </include>
    
    <!-- 增强版GPS强制回环检测优化器 -->
    <node name="enhanced_gps_loop_closure_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
        <!-- 从YAML文件加载参数 -->
        <rosparam file="$(arg config_file)" command="load" ns="enhanced_gps_loop_closure_optimizer" />

        <!-- 可选：加载预设配置 -->
        <group if="$(eval arg('use_preset') != 'none')">
            <rosparam file="$(arg config_file)" command="load" ns="$(arg use_preset)/enhanced_gps_loop_closure_optimizer" />
        </group>

        <!-- 命令行参数覆盖（如果提供） -->
        <param name="gps_topic" value="$(arg gps_topic)" />
        <param name="loop_closure_distance_threshold" value="$(arg loop_closure_distance_threshold)" if="$(eval arg('loop_closure_distance_threshold') != '')" />
        <param name="min_trajectory_length" value="$(arg min_trajectory_length)" if="$(eval arg('min_trajectory_length') != '')" />
        <param name="gps_quality_threshold" value="$(arg gps_quality_threshold)" if="$(eval arg('gps_quality_threshold') != '')" />
        <param name="check_interval" value="$(arg check_interval)" if="$(eval arg('check_interval') != '')" />
        <param name="intermediate_loop_threshold" value="$(arg intermediate_loop_threshold)" if="$(eval arg('intermediate_loop_threshold') != '')" />
        <param name="min_loop_separation" value="$(arg min_loop_separation)" if="$(eval arg('min_loop_separation') != '')" />
        <param name="trajectory_window_size" value="$(arg trajectory_window_size)" if="$(eval arg('trajectory_window_size') != '')" />
        <param name="revisit_threshold" value="$(arg revisit_threshold)" if="$(eval arg('revisit_threshold') != '')" />

        <remap from="/rtk/gnss" to="$(arg gps_topic)" />
    </node>
    
    <!-- 增强版SLAM回环检测集成模块 -->
    <node name="enhanced_slam_loop_closure_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
        <!-- 从YAML文件加载参数 -->
        <rosparam file="$(arg config_file)" command="load" ns="enhanced_slam_loop_closure_integration" />

        <!-- 可选：加载预设配置 -->
        <group if="$(eval arg('use_preset') != 'none')">
            <rosparam file="$(arg config_file)" command="load" ns="$(arg use_preset)/enhanced_slam_loop_closure_integration" />
        </group>

        <!-- 命令行参数覆盖（如果提供） -->
        <param name="force_search_radius" value="$(arg force_search_radius)" if="$(eval arg('force_search_radius') != '')" />
        <param name="max_search_candidates" value="$(arg max_search_candidates)" if="$(eval arg('max_search_candidates') != '')" />
        <param name="voxel_leaf_size" value="$(arg voxel_leaf_size)" if="$(eval arg('voxel_leaf_size') != '')" />
        <param name="min_keyframes_for_loop" value="$(arg min_keyframes_for_loop)" if="$(eval arg('min_keyframes_for_loop') != '')" />
        <param name="intermediate_search_radius" value="$(arg intermediate_search_radius)" if="$(eval arg('intermediate_search_radius') != '')" />
        <param name="revisit_search_radius" value="$(arg revisit_search_radius)" if="$(eval arg('revisit_search_radius') != '')" />
        <param name="start_end_score_threshold" value="$(arg start_end_score_threshold)" if="$(eval arg('start_end_score_threshold') != '')" />
        <param name="intermediate_score_threshold" value="$(arg intermediate_score_threshold)" if="$(eval arg('intermediate_score_threshold') != '')" />
        <param name="revisit_score_threshold" value="$(arg revisit_score_threshold)" if="$(eval arg('revisit_score_threshold') != '')" />
        <param name="sliding_window_size" value="$(arg sliding_window_size)" if="$(eval arg('sliding_window_size') != '')" />
    </node>

    <!-- 强度值保持的PCD保存模块 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="enable_intensity_preservation" default="true" />

    <group if="$(arg enable_intensity_preservation)">
        <node name="intensity_preserving_pcd_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <!-- 加载强度保持配置 -->
            <rosparam file="$(arg intensity_config_file)" command="load" />

            <!-- 加载预设配置 -->
            <group if="$(eval arg('intensity_preset') != 'none')">
                <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
            </group>

            <!-- 话题重映射 -->
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
    </group>
    
    <!-- 参数服务器配置 -->
    <rosparam>
        # 增强版GPS强制回环检测配置
        enhanced_gps_loop_closure:
            # 基础回环检测
            distance_threshold: 5.0          # 起点-终点距离阈值(米)
            min_trajectory_length: 50.0      # 最小轨迹长度(米)
            gps_quality_threshold: -1        # GPS质量阈值
            check_interval: 1.0              # 检查间隔(秒)
            
            # 中间区域回环检测
            intermediate_threshold: 8.0      # 中间回环距离阈值(米)
            min_loop_separation: 30.0        # 最小回环分离距离(米)
            trajectory_window_size: 100      # 轨迹分析窗口大小
            
            # 重复访问回环检测
            revisit_threshold: 10.0          # 重访距离阈值(米)
        
        # 增强版SLAM回环检测配置
        enhanced_slam_loop_closure:
            # 基础配置
            force_search_radius: 10.0        # 基础搜索半径(米)
            max_candidates: 10               # 最大搜索候选数
            voxel_leaf_size: 0.1            # 体素滤波叶子大小(米)
            min_keyframes: 50               # 最小关键帧数
            
            # 不同回环类型的搜索半径
            intermediate_search_radius: 15.0 # 中间回环搜索半径(米)
            revisit_search_radius: 12.0     # 重访回环搜索半径(米)
            
            # 不同回环类型的匹配阈值
            start_end_score_threshold: 0.25  # 起点-终点匹配阈值
            intermediate_score_threshold: 0.35 # 中间回环匹配阈值
            revisit_score_threshold: 0.30    # 重访回环匹配阈值
            
            # 滑动窗口配置
            sliding_window_size: 100         # 滑动窗口大小
    </rosparam>
    
    <!-- 日志配置 -->
    <env name="ROSCONSOLE_CONFIG_FILE" value="$(find state_estimation)/config/rosconsole.conf"/>
    
</launch>
