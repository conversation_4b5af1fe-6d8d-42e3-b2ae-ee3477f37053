#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能GPS约束控制器
根据SLAM匹配质量动态控制GPS平面约束，避免约束干扰点云匹配
"""

import rospy
import numpy as np
from std_msgs.msg import Bool, String, Float64
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque

class IntelligentGPSConstraintController:
    def __init__(self):
        rospy.init_node('intelligent_gps_constraint_controller', anonymous=True)
        
        # 参数配置
        self.icp_fitness_threshold = rospy.get_param('~icp_fitness_threshold', 0.3)
        self.constraint_disable_fitness = rospy.get_param('~constraint_disable_fitness', 0.5)
        self.constraint_enable_fitness = rospy.get_param('~constraint_enable_fitness', 0.2)
        self.velocity_threshold = rospy.get_param('~velocity_threshold', 2.0)  # m/s
        self.angular_velocity_threshold = rospy.get_param('~angular_velocity_threshold', 0.5)  # rad/s
        self.constraint_cooldown = rospy.get_param('~constraint_cooldown', 5.0)  # seconds
        
        # 状态变量
        self.current_constraint_enabled = True
        self.last_constraint_change_time = 0
        self.icp_fitness_history = deque(maxlen=20)
        self.velocity_history = deque(maxlen=10)
        self.angular_velocity_history = deque(maxlen=10)
        
        # 统计信息
        self.total_constraint_changes = 0
        self.constraint_disable_count = 0
        self.constraint_enable_count = 0
        self.poor_matching_episodes = 0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.icp_fitness_sub = rospy.Subscriber(
            '/icp_fitness_score', Float64, self.icp_fitness_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber(
            '/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        self.loop_status_sub = rospy.Subscriber(
            '/enhanced_gps_loop_closure_status', String, self.loop_status_callback, queue_size=1)
        
        # 发布器
        self.constraint_control_pub = rospy.Publisher(
            '/gps_constraint_control', Bool, queue_size=1)
        self.controller_status_pub = rospy.Publisher(
            '/gps_constraint_controller_status', String, queue_size=1)
        
        # 定时器
        self.control_timer = rospy.Timer(
            rospy.Duration(1.0), self.control_loop)
        self.status_timer = rospy.Timer(
            rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("Intelligent GPS Constraint Controller Started")
        rospy.loginfo("ICP fitness threshold: %.3f", self.icp_fitness_threshold)
        rospy.loginfo("Constraint disable fitness: %.3f", self.constraint_disable_fitness)
        rospy.loginfo("Constraint enable fitness: %.3f", self.constraint_enable_fitness)
    
    def icp_fitness_callback(self, msg):
        """处理ICP适应度分数"""
        with self.data_lock:
            self.icp_fitness_history.append(msg.data)
    
    def pose_callback(self, msg):
        """处理位姿数据，计算速度"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            if hasattr(self, 'last_pose_time') and hasattr(self, 'last_position'):
                dt = current_time - self.last_pose_time
                if dt > 0:
                    # 计算线速度
                    current_pos = np.array([
                        msg.pose.position.x,
                        msg.pose.position.y,
                        msg.pose.position.z
                    ])
                    velocity = np.linalg.norm(current_pos - self.last_position) / dt
                    self.velocity_history.append(velocity)
                    
                    # 计算角速度（简化版）
                    current_quat = np.array([
                        msg.pose.orientation.x,
                        msg.pose.orientation.y,
                        msg.pose.orientation.z,
                        msg.pose.orientation.w
                    ])
                    # 简化的角速度计算
                    angular_velocity = np.linalg.norm(current_quat - self.last_orientation) / dt
                    self.angular_velocity_history.append(angular_velocity)
            
            self.last_pose_time = current_time
            self.last_position = np.array([
                msg.pose.position.x,
                msg.pose.position.y,
                msg.pose.position.z
            ])
            self.last_orientation = np.array([
                msg.pose.orientation.x,
                msg.pose.orientation.y,
                msg.pose.orientation.z,
                msg.pose.orientation.w
            ])
    
    def loop_status_callback(self, msg):
        """处理回环状态信息"""
        try:
            # 在回环检测期间暂时禁用约束
            if "loop closure" in msg.data.lower() or "回环" in msg.data:
                self.temporarily_disable_constraint("loop_closure_active")
        except Exception as e:
            rospy.logwarn("Error processing loop status: %s", str(e))
    
    def control_loop(self, event):
        """主控制循环"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查冷却时间
                if current_time - self.last_constraint_change_time < self.constraint_cooldown:
                    return
                
                # 分析当前状态
                should_enable = self.analyze_constraint_need()
                
                if should_enable != self.current_constraint_enabled:
                    self.change_constraint_state(should_enable, "quality_analysis")
                    
        except Exception as e:
            rospy.logerr("Error in control loop: %s", str(e))
    
    def analyze_constraint_need(self):
        """分析是否需要启用GPS约束"""
        try:
            # 1. 检查ICP匹配质量
            if len(self.icp_fitness_history) >= 5:
                recent_fitness = list(self.icp_fitness_history)[-5:]
                avg_fitness = np.mean(recent_fitness)
                max_fitness = np.max(recent_fitness)
                
                # 如果匹配质量很差，禁用约束
                if avg_fitness > self.constraint_disable_fitness or max_fitness > 0.8:
                    rospy.logwarn("Poor ICP fitness detected: avg=%.3f, max=%.3f - disabling constraint",
                                 avg_fitness, max_fitness)
                    return False
                
                # 如果匹配质量很好，可以启用约束
                if avg_fitness < self.constraint_enable_fitness and max_fitness < 0.3:
                    return True
            
            # 2. 检查运动状态
            if len(self.velocity_history) >= 3:
                recent_velocity = list(self.velocity_history)[-3:]
                avg_velocity = np.mean(recent_velocity)
                max_velocity = np.max(recent_velocity)
                
                # 高速运动时禁用约束
                if avg_velocity > self.velocity_threshold or max_velocity > self.velocity_threshold * 1.5:
                    rospy.logwarn("High velocity detected: avg=%.2f, max=%.2f - disabling constraint",
                                 avg_velocity, max_velocity)
                    return False
            
            # 3. 检查角速度
            if len(self.angular_velocity_history) >= 3:
                recent_angular_vel = list(self.angular_velocity_history)[-3:]
                avg_angular_vel = np.mean(recent_angular_vel)
                
                # 快速转向时禁用约束
                if avg_angular_vel > self.angular_velocity_threshold:
                    rospy.logwarn("High angular velocity detected: %.3f - disabling constraint",
                                 avg_angular_vel)
                    return False
            
            # 默认情况下保持当前状态
            return self.current_constraint_enabled
            
        except Exception as e:
            rospy.logerr("Error analyzing constraint need: %s", str(e))
            return self.current_constraint_enabled
    
    def temporarily_disable_constraint(self, reason):
        """临时禁用约束"""
        if self.current_constraint_enabled:
            self.change_constraint_state(False, reason)
            
            # 设置定时器重新启用
            def re_enable():
                rospy.sleep(3.0)  # 3秒后重新启用
                if not self.current_constraint_enabled:
                    self.change_constraint_state(True, "auto_re_enable")
            
            threading.Thread(target=re_enable, daemon=True).start()
    
    def change_constraint_state(self, enable, reason):
        """改变约束状态"""
        try:
            if enable == self.current_constraint_enabled:
                return
            
            self.current_constraint_enabled = enable
            self.last_constraint_change_time = rospy.Time.now().to_sec()
            self.total_constraint_changes += 1
            
            if enable:
                self.constraint_enable_count += 1
            else:
                self.constraint_disable_count += 1
            
            # 发布控制信号
            control_msg = Bool()
            control_msg.data = enable
            self.constraint_control_pub.publish(control_msg)
            
            # 日志输出
            status = "ENABLED" if enable else "DISABLED"
            color = "32" if enable else "31"  # 绿色或红色
            rospy.loginfo(f"\033[1;{color}m[GPS Constraint] {status} - Reason: {reason}\033[0m")
            
        except Exception as e:
            rospy.logerr("Error changing constraint state: %s", str(e))
    
    def publish_status(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'constraint_enabled': self.current_constraint_enabled,
                    'total_changes': self.total_constraint_changes,
                    'enable_count': self.constraint_enable_count,
                    'disable_count': self.constraint_disable_count,
                    'current_metrics': {
                        'avg_icp_fitness': float(np.mean(self.icp_fitness_history)) if self.icp_fitness_history else 0.0,
                        'avg_velocity': float(np.mean(self.velocity_history)) if self.velocity_history else 0.0,
                        'avg_angular_velocity': float(np.mean(self.angular_velocity_history)) if self.angular_velocity_history else 0.0
                    },
                    'thresholds': {
                        'icp_fitness_threshold': self.icp_fitness_threshold,
                        'constraint_disable_fitness': self.constraint_disable_fitness,
                        'constraint_enable_fitness': self.constraint_enable_fitness,
                        'velocity_threshold': self.velocity_threshold
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.controller_status_pub.publish(status_msg)
                
                # 控制台输出
                rospy.loginfo("GPS Constraint: %s | ICP Fitness: %.3f | Velocity: %.2f m/s | Changes: %d",
                             "ON" if self.current_constraint_enabled else "OFF",
                             status_report['current_metrics']['avg_icp_fitness'],
                             status_report['current_metrics']['avg_velocity'],
                             self.total_constraint_changes)
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        controller = IntelligentGPSConstraintController()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS Constraint Controller: %s", str(e))

if __name__ == '__main__':
    main()
