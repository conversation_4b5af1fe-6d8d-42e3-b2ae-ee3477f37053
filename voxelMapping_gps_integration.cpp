/**
 * αLiDAR voxelMapping.cpp GPS集成版本
 * 基于原始voxelMapping.cpp添加GPS高度校正功能
 * 
 * 使用说明:
 * 1. 备份原始的voxelMapping.cpp
 * 2. 将此文件替换原始文件，或者手动合并代码
 * 3. 确保已安装GeographicLib依赖
 */

// 在原有头文件基础上添加GPS相关头文件
#include <sensor_msgs/NavSatFix.h>
#include <GeographicLib/LocalCartesian.hpp>
#include <deque>
#include <mutex>

// GPS处理相关的全局变量 (添加到原有全局变量区域)
std::deque<sensor_msgs::NavSatFix> gps_buffer;
std::mutex gps_mutex;
std::unique_ptr<GeographicLib::LocalCartesian> local_cartesian;
bool gps_origin_set = false;
bool enable_gps_correction = true;

// GPS配置参数
double gps_height_correction_threshold = 0.3;  // 30cm阈值
double gps_correction_rate = 0.1;              // 10%校正率
double gps_timeout = 0.5;                      // GPS超时时间

// GPS状态监控
struct GPSStatus {
    bool is_rtk_fixed = false;
    double last_update_time = 0.0;
    int consecutive_good_fixes = 0;
    double position_std = 1.0;
} gps_status;

// 起始位置记录
Eigen::Vector3d start_gps_position = Eigen::Vector3d::Zero();
Eigen::Vector3d start_slam_position = Eigen::Vector3d::Zero();
bool start_positions_set = false;

// GPS回调函数 (添加到原有回调函数区域)
void gps_callback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
    std::lock_guard<std::mutex> lock(gps_mutex);
    
    // 检查GPS有效性
    if (gps_msg->status.status < sensor_msgs::NavSatStatus::STATUS_FIX) {
        return;
    }
    
    // 更新GPS状态
    gps_status.last_update_time = gps_msg->header.stamp.toSec();
    gps_status.is_rtk_fixed = (gps_msg->status.status == sensor_msgs::NavSatStatus::STATUS_GBAS_FIX);
    
    if (gps_status.is_rtk_fixed || gps_msg->status.status == sensor_msgs::NavSatStatus::STATUS_FIX) {
        gps_status.consecutive_good_fixes++;
    } else {
        gps_status.consecutive_good_fixes = 0;
    }
    
    // 设置坐标原点 (第一次收到稳定GPS数据时)
    if (!gps_origin_set && gps_status.consecutive_good_fixes > 5) {
        local_cartesian = std::make_unique<GeographicLib::LocalCartesian>(
            gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
        gps_origin_set = true;
        ROS_INFO("GPS坐标原点已设置: lat=%.8f, lon=%.8f, alt=%.3f",
                gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
    }
    
    // 添加到缓冲区
    gps_buffer.push_back(*gps_msg);
    while (gps_buffer.size() > 500) {
        gps_buffer.pop_front();
    }
}

// GPS辅助函数
Eigen::Vector3d convertGPSToLocal(const sensor_msgs::NavSatFix& gps_msg) {
    if (!gps_origin_set) return Eigen::Vector3d::Zero();
    
    double x, y, z;
    local_cartesian->Forward(gps_msg.latitude, gps_msg.longitude, gps_msg.altitude, x, y, z);
    return Eigen::Vector3d(x, y, z);
}

bool getGPSPosition(double timestamp, Eigen::Vector3d& gps_position, double& gps_std) {
    std::lock_guard<std::mutex> lock(gps_mutex);
    
    if (gps_buffer.empty() || !gps_origin_set) return false;
    
    // 查找时间戳最近的GPS数据
    double min_time_diff = std::numeric_limits<double>::max();
    sensor_msgs::NavSatFix closest_gps;
    bool found = false;
    
    for (const auto& gps_msg : gps_buffer) {
        double time_diff = std::abs(gps_msg.header.stamp.toSec() - timestamp);
        if (time_diff < min_time_diff && time_diff < gps_timeout) {
            min_time_diff = time_diff;
            closest_gps = gps_msg;
            found = true;
        }
    }
    
    if (!found) return false;
    
    gps_position = convertGPSToLocal(closest_gps);
    gps_std = gps_status.is_rtk_fixed ? 0.02 : 1.0;  // RTK: 2cm, 普通GPS: 1m
    
    return true;
}

Eigen::Vector3d correctHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp) {
    if (!enable_gps_correction) return slam_position;
    
    Eigen::Vector3d gps_position;
    double gps_std;
    
    if (!getGPSPosition(timestamp, gps_position, gps_std)) {
        return slam_position;
    }
    
    // 计算高度差
    double height_diff = gps_position.z() - slam_position.z();
    
    // 如果高度差超过阈值，进行校正
    if (std::abs(height_diff) > gps_height_correction_threshold) {
        Eigen::Vector3d corrected_position = slam_position;
        
        // 渐进式校正，避免突跳
        corrected_position.z() += height_diff * gps_correction_rate;
        
        ROS_INFO("GPS高度校正: SLAM=%.3f, GPS=%.3f, 校正后=%.3f", 
                slam_position.z(), gps_position.z(), corrected_position.z());
        
        return corrected_position;
    }
    
    return slam_position;
}

bool detectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp,
                         Eigen::Vector3d& loop_constraint) {
    if (!start_positions_set) return false;
    
    Eigen::Vector3d current_gps_pos;
    double gps_std;
    
    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }
    
    // 检查是否接近起始位置
    double distance_to_start = (current_gps_pos - start_gps_position).norm();
    
    if (distance_to_start < 3.0) {  // 3米范围内认为是回环
        // 计算回环约束
        Eigen::Vector3d slam_drift = current_slam_pos - start_slam_position;
        Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position;
        
        loop_constraint = gps_drift - slam_drift;
        
        ROS_INFO("GPS回环检测: 距离起点%.2fm, 约束向量[%.3f,%.3f,%.3f]",
                distance_to_start, loop_constraint.x(), 
                loop_constraint.y(), loop_constraint.z());
        
        return loop_constraint.norm() > 0.1;  // 约束大于10cm才应用
    }
    
    return false;
}

/**
 * 修改后的execute函数
 * 在原有execute函数基础上添加GPS处理逻辑
 */
void execute(){
    // ========== 原有代码保持不变 ==========
    if (flg_first_scan)
    {
        first_lidar_time = Measures.lidar_beg_time;
        p_imu->first_lidar_time = first_lidar_time;
        flg_first_scan = false;
        return;
    }

    double t_optimize_start = omp_get_wtime();
    p_imu->Process(Measures, kf, feats_undistort);
    state_point = kf.get_x();
    pos_lid = state_point.pos + state_point.rot * state_point.offset_T_L_I;

    if (feats_undistort->empty() || (feats_undistort == NULL)) {
        ROS_WARN("No point, skip this scan!\n");
        return;
    }

    // ========== GPS集成点1: 记录起始位置 ==========
    if (!start_positions_set && gps_origin_set && gps_status.consecutive_good_fixes > 10) {
        Eigen::Vector3d gps_pos;
        double gps_std;
        if (getGPSPosition(lidar_end_time, gps_pos, gps_std)) {
            start_gps_position = gps_pos;
            start_slam_position = state_point.pos;
            start_positions_set = true;
            ROS_INFO("起始位置已记录 - SLAM: [%.3f,%.3f,%.3f], GPS: [%.3f,%.3f,%.3f]",
                    start_slam_position.x(), start_slam_position.y(), start_slam_position.z(),
                    start_gps_position.x(), start_gps_position.y(), start_gps_position.z());
        }
    }

    // ========== 原有的特征点处理和体素地图构建代码保持不变 ==========
    // [这里是原有的大量处理代码，保持不变]
    
    // 原有的状态估计更新
    kf.update_iterated_dyn_share_diagonal();
    state_point = kf.get_x();
    
    // ========== GPS集成点2: 状态估计后的高度校正 ==========
    // GPS高度校正
    Eigen::Vector3d corrected_position = correctHeightWithGPS(state_point.pos, lidar_end_time);
    if ((corrected_position - state_point.pos).norm() > 0.01) {
        state_point.pos = corrected_position;
        kf.change_x(state_point);  // 更新滤波器状态
        ROS_DEBUG("应用GPS高度校正");
    }
    
    // ========== GPS集成点3: 回环约束检测 ==========
    Eigen::Vector3d loop_constraint;
    if (detectGPSLoopClosure(state_point.pos, lidar_end_time, loop_constraint)) {
        // 简单的位置校正方法
        if (loop_constraint.norm() < 2.0) {  // 约束不能太大
            state_point.pos += loop_constraint * 0.1;  // 10%的校正
            kf.change_x(state_point);
            ROS_INFO("应用GPS回环约束校正");
        }
    }

    // ========== 继续原有的地图更新等处理代码 ==========
    // [原有的后续处理代码保持不变]
}

/**
 * 修改后的main函数
 * 在原有main函数基础上添加GPS订阅
 */
int main(int argc, char** argv) {
    ros::init(argc, argv, "state_estimation_node");
    ros::NodeHandle nh;

    // ========== 原有的参数读取代码保持不变 ==========
    
    // ========== 添加GPS相关参数读取 ==========
    nh.param<bool>("gps/enable_correction", enable_gps_correction, true);
    nh.param<double>("gps/height_correction_threshold", gps_height_correction_threshold, 0.3);
    nh.param<double>("gps/correction_rate", gps_correction_rate, 0.1);
    nh.param<double>("gps/timeout", gps_timeout, 0.5);

    // ========== 原有的订阅者代码保持不变 ==========
    
    // ========== 添加GPS订阅者 ==========
    ros::Subscriber sub_gps = nh.subscribe("/ublox_gps/fix", 200, gps_callback);
    // 注意: 根据实际bag文件中的GPS topic名称调整
    // 可能的topic名称: /gps/fix, /rtk/fix, /navsat/fix 等

    ROS_INFO("GPS集成已启用 - 校正阈值: %.2fm, 校正率: %.1f%%", 
             gps_height_correction_threshold, gps_correction_rate * 100);

    // ========== 原有的主循环代码保持不变 ==========
    signal(SIGINT, SigHandle);
    ros::Rate rate(5000);
    bool status = ros::ok();
    while (status)
    {
        if (flg_exit) break;
        ros::spinOnce();
        if(sync_packages(Measures))
        {
            execute();  // 调用修改后的execute函数
        }
        status = ros::ok();
        rate.sleep();
    }
    
    return 0;
}

/**
 * 使用说明:
 *
 * 1. 编译前准备:
 *    sudo apt install libgeographic-dev
 *
 * 2. 修改CMakeLists.txt:
 *    添加: find_package(PkgConfig REQUIRED)
 *    添加: pkg_check_modules(GEOGRAPHIC REQUIRED geographic)
 *    添加: target_link_libraries中加入 ${GEOGRAPHIC_LIBRARIES}
 *
 * 3. 运行时监控:
 *    rostopic echo /ublox_gps/fix  # 监控GPS数据
 *    rostopic hz /ublox_gps/fix    # 监控GPS频率
 *
 * 4. 参数调优:
 *    gps_height_correction_threshold: 高度校正阈值，建议0.2-0.5m
 *    gps_correction_rate: 校正速率，建议0.05-0.2 (5%-20%)
 *
 * 5. 预期日志输出:
 *    "GPS坐标原点已设置"
 *    "起始位置已记录"
 *    "GPS高度校正"
 *    "GPS回环检测"
 */
