#!/bin/bash

# αLiDAR项目完整编译和运行脚本
# 适用于Ubuntu 20.04 + ROS Noetic

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 项目路径配置 (请根据实际情况修改)
PROJECT_ROOT="$HOME/alpha_lidar_GPS"  # 或者您的实际路径
WORKSPACE_PATH="$PROJECT_ROOT/software/alpha_lidar_ws"
DATASET_PATH="$HOME/datasets"  # 数据集路径

echo "=========================================="
echo "αLiDAR项目编译和运行脚本"
echo "=========================================="
echo "项目路径: $PROJECT_ROOT"
echo "工作空间: $WORKSPACE_PATH"
echo "数据集路径: $DATASET_PATH"
echo ""

# 函数：检查路径是否存在
check_path() {
    if [ ! -d "$1" ]; then
        log_error "路径不存在: $1"
        echo "请检查路径设置或创建相应目录"
        exit 1
    fi
}

# 函数：安装依赖
install_dependencies() {
    log_step "1. 安装系统依赖..."
    
    # 更新软件包列表
    sudo apt update
    
    # 安装基础编译工具
    sudo apt install -y \
        build-essential \
        cmake \
        git \
        python3-catkin-tools \
        python3-rosdep \
        python3-rosinstall \
        python3-rosinstall-generator \
        python3-wstool
    
    # 安装αLiDAR特定依赖
    sudo apt install -y \
        libpcl-dev \
        pcl-tools \
        libeigen3-dev \
        libopencv-dev \
        libgoogle-glog-dev \
        libgflags-dev \
        libatlas-base-dev \
        libblas-dev \
        liblapack-dev
    
    # 安装GPS集成依赖 (可选)
    sudo apt install -y \
        libgeographic-dev \
        libgeographic19
    
    log_info "依赖安装完成"
}

# 函数：初始化ROS环境
setup_ros_environment() {
    log_step "2. 设置ROS环境..."
    
    # 检查ROS是否安装
    if ! command -v rosversion &> /dev/null; then
        log_error "ROS未安装，请先安装ROS Noetic"
        echo "安装命令:"
        echo "sudo apt install ros-noetic-desktop-full"
        exit 1
    fi
    
    # 设置ROS环境
    source /opt/ros/noetic/setup.bash
    
    # 初始化rosdep (如果需要)
    if [ ! -f /etc/ros/rosdep/sources.list.d/20-default.list ]; then
        sudo rosdep init
    fi
    rosdep update
    
    log_info "ROS环境设置完成"
}

# 函数：编译项目
build_project() {
    log_step "3. 编译αLiDAR项目..."
    
    # 检查工作空间是否存在
    check_path "$WORKSPACE_PATH"
    
    # 进入工作空间
    cd "$WORKSPACE_PATH"
    
    # 安装ROS依赖
    log_info "安装ROS包依赖..."
    rosdep install --from-paths src --ignore-src -r -y
    
    # 清理之前的编译结果
    if [ -d "build" ] || [ -d "devel" ]; then
        log_info "清理之前的编译结果..."
        rm -rf build/ devel/
    fi
    
    # 编译项目
    log_info "开始编译..."
    if catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation" -j$(nproc); then
        log_info "✅ 编译成功!"
    else
        log_error "❌ 编译失败"
        exit 1
    fi
    
    # 设置环境变量
    source "$WORKSPACE_PATH/devel/setup.bash"
    
    log_info "项目编译完成"
}

# 函数：检查数据集
check_dataset() {
    log_step "4. 检查数据集..."
    
    if [ ! -d "$DATASET_PATH" ]; then
        log_warn "数据集目录不存在: $DATASET_PATH"
        mkdir -p "$DATASET_PATH"
        log_info "已创建数据集目录"
    fi
    
    # 查找bag文件
    BAG_FILES=$(find "$DATASET_PATH" -name "*.bag" 2>/dev/null || true)
    
    if [ -z "$BAG_FILES" ]; then
        log_warn "未找到bag文件"
        echo "请将数据集文件放置在: $DATASET_PATH"
    else
        log_info "找到以下数据集文件:"
        echo "$BAG_FILES"
    fi
}

# 函数：创建启动脚本
create_launch_scripts() {
    log_step "5. 创建启动脚本..."
    
    # 创建主启动脚本
    cat > "$HOME/start_alpha_lidar.sh" << EOF
#!/bin/bash

# αLiDAR主启动脚本

# 设置环境
source /opt/ros/noetic/setup.bash
source $WORKSPACE_PATH/devel/setup.bash

echo "=========================================="
echo "启动αLiDAR SLAM系统"
echo "=========================================="
echo "工作空间: $WORKSPACE_PATH"
echo ""
echo "请确保在其他终端中运行:"
echo "1. roscore"
echo "2. rosbag play <your_dataset.bag> --pause"
echo ""

# 启动SLAM节点
roslaunch state_estimation mapping_robosense.launch
EOF
    
    # 创建数据播放脚本
    cat > "$HOME/play_dataset.sh" << EOF
#!/bin/bash

# 数据集播放脚本

cd $DATASET_PATH

echo "=========================================="
echo "αLiDAR数据集播放"
echo "=========================================="
echo "数据集目录: $DATASET_PATH"
echo ""

# 列出可用的bag文件
echo "可用的数据集文件:"
ls -la *.bag 2>/dev/null || echo "未找到bag文件"
echo ""

# 如果只有一个bag文件，自动播放
BAG_COUNT=\$(ls *.bag 2>/dev/null | wc -l)
if [ \$BAG_COUNT -eq 1 ]; then
    BAG_FILE=\$(ls *.bag)
    echo "自动播放: \$BAG_FILE"
    echo "按空格键开始播放，Ctrl+C停止"
    rosbag play "\$BAG_FILE" --pause
elif [ \$BAG_COUNT -gt 1 ]; then
    echo "发现多个bag文件，请手动选择:"
    ls *.bag
    echo ""
    echo "使用命令: rosbag play <filename.bag> --pause"
else
    echo "未找到bag文件，请将数据集放置在此目录"
fi
EOF
    
    # 创建监控脚本
    cat > "$HOME/monitor_alpha_lidar.sh" << EOF
#!/bin/bash

# αLiDAR系统监控脚本

echo "=========================================="
echo "αLiDAR系统监控"
echo "=========================================="

# 检查ROS节点
echo "1. ROS节点状态:"
rosnode list 2>/dev/null || echo "roscore未运行"
echo ""

# 检查topic
echo "2. 活跃的topic:"
rostopic list 2>/dev/null || echo "无活跃topic"
echo ""

# 检查关键topic频率
echo "3. 关键topic频率:"
echo "点云数据:"
timeout 5 rostopic hz /velodyne_points 2>/dev/null || echo "  无点云数据"

echo "IMU数据:"
timeout 5 rostopic hz /imu/data 2>/dev/null || echo "  无IMU数据"

echo "里程计数据:"
timeout 5 rostopic hz /odometry 2>/dev/null || echo "  无里程计数据"

echo ""
echo "监控完成"
EOF
    
    # 设置执行权限
    chmod +x "$HOME/start_alpha_lidar.sh"
    chmod +x "$HOME/play_dataset.sh"
    chmod +x "$HOME/monitor_alpha_lidar.sh"
    
    log_info "启动脚本已创建:"
    echo "  - $HOME/start_alpha_lidar.sh (启动SLAM系统)"
    echo "  - $HOME/play_dataset.sh (播放数据集)"
    echo "  - $HOME/monitor_alpha_lidar.sh (系统监控)"
}

# 函数：显示使用说明
show_usage() {
    echo ""
    echo "=========================================="
    echo "使用说明"
    echo "=========================================="
    echo ""
    echo "快速启动 (在不同终端窗口中执行):"
    echo ""
    echo "终端1: 启动roscore"
    echo "  roscore"
    echo ""
    echo "终端2: 启动αLiDAR系统"
    echo "  $HOME/start_alpha_lidar.sh"
    echo ""
    echo "终端3: 播放数据集"
    echo "  $HOME/play_dataset.sh"
    echo ""
    echo "终端4: 监控系统 (可选)"
    echo "  $HOME/monitor_alpha_lidar.sh"
    echo ""
    echo "手动命令:"
    echo "  # 播放特定数据集"
    echo "  rosbag play $DATASET_PATH/your_dataset.bag --pause"
    echo ""
    echo "  # 启动RViz可视化"
    echo "  rviz"
    echo ""
    echo "  # 查看系统状态"
    echo "  rostopic list"
    echo "  rosnode list"
    echo ""
    echo "配置文件位置:"
    echo "  $WORKSPACE_PATH/src/state_estimation/config/"
    echo ""
    echo "日志文件位置:"
    echo "  ~/.ros/log/"
}

# 主函数
main() {
    # 检查是否为root用户
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查基本路径
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目根目录不存在: $PROJECT_ROOT"
        echo "请修改脚本中的PROJECT_ROOT变量为正确路径"
        exit 1
    fi
    
    # 执行各个步骤
    install_dependencies
    setup_ros_environment
    build_project
    check_dataset
    create_launch_scripts
    
    log_info "✅ αLiDAR项目设置完成!"
    show_usage
}

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        echo "αLiDAR项目编译和运行脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --build-only   仅编译项目"
        echo "  --deps-only    仅安装依赖"
        echo ""
        echo "环境变量:"
        echo "  PROJECT_ROOT   项目根目录路径 (默认: $PROJECT_ROOT)"
        echo "  DATASET_PATH   数据集目录路径 (默认: $DATASET_PATH)"
        exit 0
        ;;
    --build-only)
        setup_ros_environment
        build_project
        ;;
    --deps-only)
        install_dependencies
        ;;
    *)
        main
        ;;
esac
