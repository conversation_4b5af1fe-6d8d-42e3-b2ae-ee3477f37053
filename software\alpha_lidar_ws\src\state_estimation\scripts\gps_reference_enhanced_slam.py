#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS参考位置增强SLAM匹配器
仅使用GPS作为参考位置，通过增强SLAM自身匹配能力来解决首尾偏差
不直接修改SLAM位置，避免点云匹配错位
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix, PointCloud2
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque
import math

class GPSReferenceEnhancedSLAM:
    def __init__(self):
        rospy.init_node('gps_reference_enhanced_slam', anonymous=True)
        
        # GPS参考参数
        self.gps_reference_radius = rospy.get_param('~gps_reference_radius', 50.0)  # GPS参考半径
        self.slam_enhancement_radius = rospy.get_param('~slam_enhancement_radius', 30.0)  # SLAM增强半径
        self.min_time_gap = rospy.get_param('~min_time_gap', 30.0)  # 最小时间间隔
        self.trajectory_length_threshold = rospy.get_param('~trajectory_length_threshold', 100.0)  # 轨迹长度阈值
        
        # SLAM增强参数
        self.enhanced_search_radius = rospy.get_param('~enhanced_search_radius', 80.0)  # 增强搜索半径
        self.enhanced_voxel_size = rospy.get_param('~enhanced_voxel_size', 0.02)  # 增强体素大小
        self.enhanced_max_iterations = rospy.get_param('~enhanced_max_iterations', 500)  # 增强最大迭代次数
        self.multi_resolution_levels = rospy.get_param('~multi_resolution_levels', 3)  # 多分辨率层级
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.slam_trajectory = deque(maxlen=2000)
        self.keyframe_poses = deque(maxlen=1000)
        
        # 状态变量
        self.origin_set = False
        self.last_enhancement_time = 0
        self.total_enhancements = 0
        self.successful_matches = 0
        self.current_trajectory_length = 0.0
        
        # GPS质量权重
        self.gps_quality_weights = {
            4: 1.0,    # RTK固定解
            3: 0.9,    # RTK浮点解  
            2: 0.7,    # DGPS
            1: 0.5,    # GPS单点定位
            0: 0.3,    # 无效
            -1: 0.2    # 质量未知
        }
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        self.pointcloud_sub = rospy.Subscriber('/cloud_registered', PointCloud2, self.pointcloud_callback, queue_size=1)
        
        # 发布器
        self.enhancement_trigger_pub = rospy.Publisher('/slam_enhancement_trigger', String, queue_size=1)
        self.enhanced_params_pub = rospy.Publisher('/enhanced_slam_parameters', String, queue_size=1)
        self.reference_guidance_pub = rospy.Publisher('/gps_reference_guidance', String, queue_size=1)
        self.status_pub = rospy.Publisher('/gps_reference_slam_status', String, queue_size=1)
        
        # 定时器
        self.enhancement_timer = rospy.Timer(rospy.Duration(3.0), self.check_enhancement_opportunities)
        self.status_timer = rospy.Timer(rospy.Duration(15.0), self.publish_status)
        
        rospy.loginfo("GPS Reference Enhanced SLAM Started")
        rospy.loginfo("GPS reference radius: %.1f m, SLAM enhancement radius: %.1f m", 
                     self.gps_reference_radius, self.slam_enhancement_radius)
    
    def gps_callback(self, msg):
        """处理GPS数据作为参考位置"""
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # 获取GPS质量权重
            gps_quality = msg.status.status
            quality_weight = self.gps_quality_weights.get(gps_quality, 0.1)
            
            # 设置GPS原点
            if not self.origin_set:
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                self.origin_set = True
                local_x, local_y = 0.0, 0.0
                rospy.loginfo("🏁 GPS Reference Origin Set: lat=%.6f, lon=%.6f", 
                             msg.latitude, msg.longitude)
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'timestamp': current_time,
                'quality': gps_quality,
                'quality_weight': quality_weight,
                'lat': msg.latitude,
                'lon': msg.longitude
            }
            
            self.gps_trajectory.append(gps_point)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            self.slam_trajectory.append(slam_point)
            
            # 更新轨迹长度
            if len(self.slam_trajectory) >= 2:
                prev_point = self.slam_trajectory[-2]
                distance = self.calculate_distance(slam_point, prev_point)
                self.current_trajectory_length += distance
    
    def pointcloud_callback(self, msg):
        """处理点云数据，用于关键帧选择"""
        # 简化的关键帧选择逻辑
        if len(self.slam_trajectory) > 0:
            current_pose = self.slam_trajectory[-1]
            
            # 每隔一定距离或时间保存关键帧
            should_save = False
            if len(self.keyframe_poses) == 0:
                should_save = True
            else:
                last_keyframe = self.keyframe_poses[-1]
                distance = self.calculate_distance(current_pose, last_keyframe)
                time_gap = current_pose['timestamp'] - last_keyframe['timestamp']
                
                if distance > 5.0 or time_gap > 10.0:  # 5米或10秒间隔
                    should_save = True
            
            if should_save:
                keyframe = current_pose.copy()
                keyframe['pointcloud_timestamp'] = msg.header.stamp.to_sec()
                self.keyframe_poses.append(keyframe)
    
    def gps_to_local(self, lat, lon):
        """GPS坐标转换为局部坐标"""
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 精确坐标转换
        x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
        y = lat_diff * 110540.0
        
        return x, y
    
    def calculate_distance(self, point1, point2):
        """计算两点间距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        return math.sqrt(dx*dx + dy*dy)
    
    def check_enhancement_opportunities(self, event):
        """检查SLAM增强机会"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查数据充足性
                if len(self.gps_trajectory) < 20 or len(self.slam_trajectory) < 20:
                    return
                
                # 检查时间间隔
                if current_time - self.last_enhancement_time < self.min_time_gap:
                    return
                
                # 检查轨迹长度
                if self.current_trajectory_length < self.trajectory_length_threshold:
                    return
                
                # 获取当前位置
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 寻找GPS参考区域内的历史位置
                reference_candidates = self.find_gps_reference_candidates(current_gps)
                
                if reference_candidates:
                    # 分析SLAM增强需求
                    enhancement_plan = self.analyze_slam_enhancement_need(current_slam, reference_candidates)
                    
                    if enhancement_plan:
                        self.trigger_slam_enhancement(current_slam, current_gps, enhancement_plan)
                        
        except Exception as e:
            rospy.logerr("Error checking enhancement opportunities: %s", str(e))
    
    def find_gps_reference_candidates(self, current_gps):
        """寻找GPS参考区域内的候选位置"""
        candidates = []
        current_time = current_gps['timestamp']
        
        # 动态调整参考半径基于GPS质量
        quality_weight = current_gps['quality_weight']
        effective_radius = self.gps_reference_radius * (2.0 - quality_weight)  # 质量差时扩大搜索范围
        
        for i, gps_point in enumerate(self.gps_trajectory[:-30]):  # 排除最近30个点
            # 检查时间间隔
            if current_time - gps_point['timestamp'] < self.min_time_gap:
                continue
            
            # 检查GPS距离
            distance = self.calculate_distance(current_gps, gps_point)
            if distance <= effective_radius:
                candidates.append({
                    'gps_point': gps_point,
                    'slam_index': i,
                    'gps_distance': distance,
                    'time_gap': current_time - gps_point['timestamp'],
                    'combined_quality': (current_gps['quality_weight'] + gps_point['quality_weight']) / 2.0
                })
        
        # 按综合质量和距离排序
        candidates.sort(key=lambda x: x['gps_distance'] / (x['combined_quality'] + 0.1))
        return candidates[:10]  # 返回最好的10个候选
    
    def analyze_slam_enhancement_need(self, current_slam, candidates):
        """分析SLAM增强需求"""
        best_plan = None
        best_score = float('inf')
        
        for candidate in candidates:
            slam_index = candidate['slam_index']
            if slam_index >= len(self.slam_trajectory):
                continue
                
            historical_slam = self.slam_trajectory[slam_index]
            slam_distance = self.calculate_distance(current_slam, historical_slam)
            
            # 检查是否需要增强
            if slam_distance <= self.slam_enhancement_radius:
                # 计算增强计划评分
                gps_consistency = 1.0 / (1.0 + candidate['gps_distance'])
                slam_proximity = 1.0 / (1.0 + slam_distance)
                quality_factor = candidate['combined_quality']
                time_factor = min(candidate['time_gap'] / 300.0, 1.0)
                
                enhancement_score = (gps_consistency + slam_proximity + quality_factor + time_factor) / 4.0
                
                if enhancement_score > 0.6 and slam_distance < best_score:
                    best_score = slam_distance
                    best_plan = {
                        'candidate': candidate,
                        'historical_slam': historical_slam,
                        'slam_distance': slam_distance,
                        'enhancement_score': enhancement_score,
                        'enhancement_level': self.determine_enhancement_level(slam_distance, candidate['combined_quality'])
                    }
        
        return best_plan
    
    def determine_enhancement_level(self, slam_distance, quality_weight):
        """确定增强级别"""
        if slam_distance <= 10.0 and quality_weight >= 0.8:
            return 'high_precision'  # 高精度增强
        elif slam_distance <= 20.0 and quality_weight >= 0.6:
            return 'medium_precision'  # 中等精度增强
        else:
            return 'basic_enhancement'  # 基础增强
    
    def trigger_slam_enhancement(self, current_slam, current_gps, enhancement_plan):
        """触发SLAM增强"""
        try:
            self.last_enhancement_time = rospy.Time.now().to_sec()
            self.total_enhancements += 1
            
            candidate = enhancement_plan['candidate']
            enhancement_level = enhancement_plan['enhancement_level']
            
            # 根据增强级别设置参数
            enhanced_params = self.get_enhanced_parameters(enhancement_level)
            
            # 构建增强触发消息
            enhancement_data = {
                'type': 'gps_reference_slam_enhancement',
                'timestamp': self.last_enhancement_time,
                'enhancement_level': enhancement_level,
                'gps_reference': {
                    'distance': candidate['gps_distance'],
                    'quality_weight': candidate['combined_quality'],
                    'current_quality': current_gps['quality'],
                    'historical_quality': candidate['gps_point']['quality']
                },
                'slam_analysis': {
                    'distance': enhancement_plan['slam_distance'],
                    'enhancement_score': enhancement_plan['enhancement_score']
                },
                'target_poses': {
                    'current': {
                        'x': current_slam['x'],
                        'y': current_slam['y'],
                        'z': current_slam['z']
                    },
                    'reference': {
                        'x': enhancement_plan['historical_slam']['x'],
                        'y': enhancement_plan['historical_slam']['y'],
                        'z': enhancement_plan['historical_slam']['z']
                    }
                },
                'enhanced_parameters': enhanced_params
            }
            
            # 发布增强触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(enhancement_data, indent=2)
            self.enhancement_trigger_pub.publish(trigger_msg)
            
            # 发布增强参数
            params_msg = String()
            params_msg.data = json.dumps(enhanced_params, indent=2)
            self.enhanced_params_pub.publish(params_msg)
            
            # 发布GPS参考引导
            guidance_data = {
                'gps_reference_position': {
                    'x': current_gps['x'],
                    'y': current_gps['y']
                },
                'slam_target_region': {
                    'center_x': enhancement_plan['historical_slam']['x'],
                    'center_y': enhancement_plan['historical_slam']['y'],
                    'search_radius': enhanced_params['search_radius']
                },
                'confidence': enhancement_plan['enhancement_score']
            }
            
            guidance_msg = String()
            guidance_msg.data = json.dumps(guidance_data)
            self.reference_guidance_pub.publish(guidance_msg)
            
            # 详细的增强触发日志
            rospy.loginfo("=" * 60)
            rospy.loginfo("🎯 GPS参考增强SLAM匹配触发!")
            rospy.loginfo("=" * 60)
            rospy.loginfo("📍 GPS参考信息:")
            rospy.loginfo("   当前GPS质量: %d (权重: %.2f)", candidate['current_quality'], candidate['combined_quality'])
            rospy.loginfo("   历史GPS质量: %d", candidate['historical_quality'])
            rospy.loginfo("   GPS距离: %.2f 米", candidate['gps_distance'])
            rospy.loginfo("   时间间隔: %.1f 秒", candidate['time_gap'])
            rospy.loginfo("")
            rospy.loginfo("🔄 SLAM匹配分析:")
            rospy.loginfo("   SLAM距离: %.2f 米", enhancement_plan['slam_distance'])
            rospy.loginfo("   增强评分: %.3f", enhancement_plan['enhancement_score'])
            rospy.loginfo("   增强级别: %s", enhancement_level)
            rospy.loginfo("")
            rospy.loginfo("⚙️  增强参数:")
            for key, value in enhanced_params.items():
                if isinstance(value, (int, float)):
                    rospy.loginfo("   %s: %.3f", key, value)
                else:
                    rospy.loginfo("   %s: %s", key, value)
            rospy.loginfo("=" * 60)
            
        except Exception as e:
            rospy.logerr("Error triggering SLAM enhancement: %s", str(e))
    
    def get_enhanced_parameters(self, enhancement_level):
        """获取增强参数"""
        base_params = {
            'search_radius': self.enhanced_search_radius,
            'voxel_size': self.enhanced_voxel_size,
            'max_iterations': self.enhanced_max_iterations,
            'multi_resolution': True,
            'resolution_levels': self.multi_resolution_levels
        }
        
        if enhancement_level == 'high_precision':
            return {
                **base_params,
                'search_radius': 100.0,
                'voxel_size': 0.01,
                'max_iterations': 800,
                'resolution_levels': 4,
                'use_multiple_algorithms': True,
                'algorithms': ['ICP', 'NDT', 'GICP'],
                'outlier_rejection_threshold': 0.1
            }
        elif enhancement_level == 'medium_precision':
            return {
                **base_params,
                'search_radius': 80.0,
                'voxel_size': 0.02,
                'max_iterations': 600,
                'resolution_levels': 3,
                'use_multiple_algorithms': True,
                'algorithms': ['ICP', 'NDT'],
                'outlier_rejection_threshold': 0.2
            }
        else:  # basic_enhancement
            return {
                **base_params,
                'search_radius': 60.0,
                'voxel_size': 0.03,
                'max_iterations': 400,
                'resolution_levels': 2,
                'use_multiple_algorithms': False,
                'outlier_rejection_threshold': 0.3
            }
    
    def publish_status(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                # 计算GPS质量统计
                recent_qualities = [gps['quality'] for gps in list(self.gps_trajectory)[-50:]]
                avg_quality = np.mean(recent_qualities) if recent_qualities else 0
                
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'system_type': 'gps_reference_enhanced_slam',
                    'statistics': {
                        'total_enhancements': self.total_enhancements,
                        'successful_matches': self.successful_matches,
                        'gps_points': len(self.gps_trajectory),
                        'slam_points': len(self.slam_trajectory),
                        'keyframes': len(self.keyframe_poses),
                        'trajectory_length': self.current_trajectory_length,
                        'average_gps_quality': avg_quality
                    },
                    'current_parameters': {
                        'gps_reference_radius': self.gps_reference_radius,
                        'slam_enhancement_radius': self.slam_enhancement_radius,
                        'enhanced_search_radius': self.enhanced_search_radius,
                        'enhanced_voxel_size': self.enhanced_voxel_size
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.status_pub.publish(status_msg)
                
                rospy.loginfo("GPS Reference Enhanced SLAM: %d enhancements, %.1fm trajectory, GPS quality: %.1f", 
                             self.total_enhancements, self.current_trajectory_length, avg_quality)
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        enhancer = GPSReferenceEnhancedSLAM()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS Reference Enhanced SLAM: %s", str(e))

if __name__ == '__main__':
    main()
