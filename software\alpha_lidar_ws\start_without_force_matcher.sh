#!/bin/bash

# 不使用强制匹配器的启动脚本（如果编译有问题）

echo "=========================================="
echo "🎯 智能SLAM系统 (无强制匹配器版本)"
echo "=========================================="
echo ""
echo "此版本不包含强制匹配器，但仍有智能检测功能"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/intelligent_no_force_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "启动智能检测SLAM系统..."

# 启动基础SLAM系统
roslaunch state_estimation mapping_robosense.launch &
sleep 5

# 启动GPS回环检测
rosrun state_estimation enhanced_gps_loop_closure_optimizer.py &
sleep 2

# 启动SLAM集成
rosrun state_estimation enhanced_slam_loop_closure_integration &
sleep 2

# 启动智能检测器
rosrun state_estimation intelligent_start_end_detector.py \
    _gps_topic:=/rtk/gnss \
    _departure_threshold:=30.0 \
    _return_threshold:=2.0 \
    _min_trajectory_points:=100 \
    _gps_quality_threshold:=-1 &
sleep 2

# 启动强度保持
rosrun state_estimation intensity_preserving_pcd_saver \
    _save_directory:="$OUTPUT_DIR" &
sleep 2

# 启动简化分析器
rosrun state_estimation simple_intensity_analyzer.py &
sleep 2

# 启动自适应优化器
rosrun state_estimation adaptive_parameter_optimizer &

echo ""
echo "=========================================="
echo "🚀 智能SLAM系统已启动 (无强制匹配器)"
echo "=========================================="
echo ""
echo "系统特性："
echo "📍 智能GPS距离监控"
echo "🎯 自动回环检测触发"
echo "💎 完整强度值保持"
echo "⚡ 自适应参数优化"
echo ""
echo "监控命令："
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo "  GPS回环状态:  rostopic echo /enhanced_gps_loop_closure_status"
echo "  强制回环信号: rostopic echo /intelligent_force_loop_closure"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
