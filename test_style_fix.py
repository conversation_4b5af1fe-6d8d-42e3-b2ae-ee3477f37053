#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for style fixes
Test dark theme with green text
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import sys

def test_dark_theme_with_green_text():
    """测试深色主题和绿色文字"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("样式测试 - 深色主题 + 绿色文字")
    root.geometry("800x600")
    
    # 颜色配置
    colors = {
        'bg': '#2E2E2E',           # 主背景 - 深灰
        'fg': '#00C851',           # 主前景 - 深绿色
        'select_bg': '#404040',    # 选中背景 - 深灰
        'select_fg': '#00C851',    # 选中前景 - 深绿色
        'button_bg': '#404040',    # 按钮背景
        'button_fg': '#00C851',    # 按钮前景 - 深绿色
        'entry_bg': '#3C3C3C',     # 输入框背景 - 深灰
        'entry_fg': '#00C851',     # 输入框前景 - 深绿色
        'frame_bg': '#353535',     # 框架背景
        'accent': '#0078D4',       # 强调色 - 蓝色
        'text_green': '#00C851',   # 文字绿色
    }
    
    # 设置根窗口背景
    root.configure(bg=colors['bg'])
    
    # 配置ttk样式
    style = ttk.Style()
    style.theme_use('clam')
    
    # 配置样式
    style.configure('Dark.TFrame', 
                   background=colors['frame_bg'],
                   relief='flat')
    
    style.configure('Dark.TLabel', 
                   background=colors['bg'], 
                   foreground=colors['text_green'],
                   font=('Microsoft YaHei', 10))
    
    style.configure('Dark.TButton', 
                   background=colors['button_bg'], 
                   foreground=colors['text_green'],
                   font=('Microsoft YaHei', 9),
                   relief='flat',
                   borderwidth=1,
                   focuscolor='none')
    
    style.map('Dark.TButton',
             background=[('active', '#505050'),
                       ('pressed', '#606060')],
             foreground=[('active', colors['text_green']),
                       ('pressed', colors['text_green'])])
    
    style.configure('Dark.TEntry',
                   background=colors['entry_bg'],
                   foreground=colors['text_green'],
                   font=('Microsoft YaHei', 10),
                   relief='flat',
                   borderwidth=1,
                   selectbackground=colors['select_bg'],
                   selectforeground=colors['text_green'],
                   insertcolor=colors['text_green'])
    
    style.map('Dark.TEntry',
             focuscolor=[('!focus', 'none')],
             selectbackground=[('focus', colors['select_bg'])],
             selectforeground=[('focus', colors['text_green'])],
             bordercolor=[('focus', colors['text_green'])])
    
    style.configure('Dark.TLabelframe', 
                   background=colors['bg'], 
                   foreground=colors['text_green'],
                   relief='flat',
                   borderwidth=1)
    
    style.configure('Dark.TLabelframe.Label', 
                   background=colors['bg'], 
                   foreground=colors['text_green'],
                   font=('Microsoft YaHei', 10))
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="20", style='Dark.TFrame')
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="样式测试 - 深色主题 + 绿色文字", 
                           font=('Microsoft YaHei', 16, 'bold'), style='Dark.TLabel')
    title_label.pack(pady=(0, 20))
    
    # 测试控件组
    test_frame = ttk.LabelFrame(main_frame, text="测试控件", padding="15", style='Dark.TLabelframe')
    test_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    # 标签测试
    ttk.Label(test_frame, text="标签测试:", style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=5)
    ttk.Label(test_frame, text="这是一个深绿色的标签", style='Dark.TLabel').grid(row=0, column=1, padx=10, pady=5)
    
    # 输入框测试
    ttk.Label(test_frame, text="输入框测试:", style='Dark.TLabel').grid(row=1, column=0, sticky=tk.W, pady=5)
    test_entry = ttk.Entry(test_frame, width=30, style='Dark.TEntry')
    test_entry.grid(row=1, column=1, padx=10, pady=5)
    test_entry.insert(0, "请选择文字测试深色背景")
    
    # 按钮测试
    ttk.Label(test_frame, text="按钮测试:", style='Dark.TLabel').grid(row=2, column=0, sticky=tk.W, pady=5)
    button_frame = ttk.Frame(test_frame, style='Dark.TFrame')
    button_frame.grid(row=2, column=1, padx=10, pady=5)
    
    ttk.Button(button_frame, text="普通按钮", style='Dark.TButton').pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试按钮", style='Dark.TButton').pack(side=tk.LEFT, padx=5)
    
    # 文本框测试
    text_frame = ttk.LabelFrame(main_frame, text="文本框测试", padding="15", style='Dark.TLabelframe')
    text_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    test_text = scrolledtext.ScrolledText(
        text_frame,
        width=60,
        height=10,
        font=('Microsoft YaHei', 10),
        bg=colors['entry_bg'],
        fg=colors['text_green'],
        insertbackground=colors['text_green'],
        selectbackground=colors['select_bg'],
        selectforeground=colors['text_green'],
        relief='flat',
        borderwidth=1
    )
    test_text.pack(fill=tk.BOTH, expand=True)
    
    # 插入测试文本
    test_text.insert(tk.END, """样式测试结果:

✅ 深色主题配置
✅ 深绿色文字显示
✅ 深色选择背景
✅ 绿色选择文字
✅ 绿色光标显示

请测试以下功能:
1. 选择文本 - 应该显示深色背景 + 绿色文字
2. 输入框焦点 - 应该显示绿色边框
3. 按钮悬停 - 应该有颜色变化
4. 所有文字都应该是深绿色

如果以上功能正常，说明样式修复成功！
""")
    
    # 关闭按钮
    close_button = ttk.Button(main_frame, text="关闭测试", 
                             command=root.destroy, style='Dark.TButton')
    close_button.pack(pady=10)
    
    # 运行测试
    print("=" * 50)
    print("样式测试窗口已打开")
    print("=" * 50)
    print("测试项目:")
    print("1. 所有文字应该是深绿色 (#00C851)")
    print("2. 背景应该是深灰色")
    print("3. 选择文字时背景应该是深色")
    print("4. 输入框光标应该是绿色")
    print("5. 按钮悬停应该有效果")
    print("=" * 50)
    
    root.mainloop()
    
    print("测试完成!")

if __name__ == "__main__":
    test_dark_theme_with_green_text()
