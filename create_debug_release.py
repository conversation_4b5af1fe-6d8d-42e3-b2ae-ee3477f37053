#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create debug release package for real data testing
"""

import os
import shutil
import time

def create_debug_release():
    """创建调试版发布包"""
    print("📦 创建调试版发布包...")
    
    release_dir = "GPS_3D_Analyzer_Debug_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制调试版exe文件
    exe_path = "dist/GPS_3D_Analyzer_Debug.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        
        # 获取文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ 调试版exe文件复制完成 ({size_mb:.1f} MB)")
    else:
        print("❌ 调试版exe文件不存在")
        return False
    
    # 创建详细说明文件
    readme_content = f"""# GPS 3D 轨迹分析器 - 调试版

## 🔍 专门用于诊断真实数据处理问题

本版本包含详细的调试信息，帮助诊断为什么只处理100个点的问题。

### 🎯 调试功能
- **详细日志输出**: 显示完整的数据处理过程
- **错误追踪**: 详细的错误信息和堆栈跟踪
- **处理统计**: 实时显示处理的数据点数量
- **格式检测**: 显示检测到的数据格式和字段
- **多方法尝试**: 显示每种处理方法的结果

### 🚀 使用方法

#### 测试真实bag文件
1. 双击 GPS_3D_Analyzer_Debug.exe 启动程序
2. **观察控制台窗口** - 会显示详细的处理日志
3. 选择您的真实bag文件
4. 点击"开始分析"
5. **查看控制台输出** - 了解具体处理过程

#### 关键调试信息
程序会显示以下调试信息：
- `USE_BAGPY状态`: 显示bagpy是否可用
- `📁 读取bag文件`: 显示正在处理的文件
- `📋 可用话题`: 显示bag文件中的所有话题
- `📊 CSV数据读取完成`: 显示读取的数据行数
- `📋 CSV列名`: 显示CSV文件的列结构
- `📈 已处理 X 个GPS点`: 显示实际处理的点数

### 🔍 诊断步骤

#### 步骤1: 检查bagpy状态
查看控制台是否显示：
- `✅ bagpy可用，完整功能模式` - bagpy正常
- `⚠️ bagpy不可用，使用兼容模式` - bagpy有问题

#### 步骤2: 检查文件处理
查看控制台显示的处理方法：
- `🔧 尝试使用bagpy处理...` - 使用bagpy方法
- `⚠️ 尝试直接读取bag文件...` - 使用rosbag方法
- `⚠️ 尝试作为CSV文件处理...` - 使用CSV方法

#### 步骤3: 检查数据提取
查看最终结果：
- `🎉 GPS数据提取完成: X 个有效点` - 成功提取真实数据
- `生成演示GPS轨迹数据...` - 回退到演示数据（问题所在）

### 🎯 常见问题诊断

#### 问题1: 只显示100个点
**症状**: 控制台显示"生成演示GPS轨迹数据..."
**原因**: 所有真实数据处理方法都失败了
**解决**: 查看前面的错误信息，了解失败原因

#### 问题2: bagpy处理失败
**症状**: 显示"❌ bagpy处理失败"或"❌ bagpy处理异常"
**原因**: bagpy库有问题或话题不存在
**解决**: 检查话题名称是否正确

#### 问题3: 话题不存在
**症状**: 显示"❌ 话题 /rtk/gnss 不存在于bag文件中"
**原因**: 指定的GPS话题名称不正确
**解决**: 查看"📋 可用话题"列表，选择正确的GPS话题

#### 问题4: CSV格式问题
**症状**: 显示"未找到GPS坐标列"
**原因**: CSV文件格式不符合预期
**解决**: 检查CSV文件是否包含latitude、longitude列

### 🔧 手动修复建议

#### 修复1: 更改GPS话题
如果控制台显示可用话题列表，尝试：
1. 找到包含'gps'、'gnss'、'fix'的话题名称
2. 在程序界面中修改"GPS话题"字段
3. 重新开始分析

#### 修复2: 检查文件格式
如果是CSV文件：
1. 确保文件包含latitude、longitude列
2. 确保数据格式正确（数字格式）
3. 检查文件编码（UTF-8、GBK等）

#### 修复3: 使用其他格式
如果bag文件有问题：
1. 尝试将bag文件转换为CSV格式
2. 确保CSV包含必要的GPS字段
3. 使用程序的CSV处理功能

### 💻 系统要求
- **操作系统**: Windows 10 64位
- **内存**: 最小4GB内存
- **显卡**: 支持OpenGL的显卡
- **磁盘空间**: 100MB可用磁盘空间
- **调试**: 控制台窗口用于查看详细日志

### 📝 版本信息
- **版本**: 1.3.0 (调试版)
- **构建日期**: {time.strftime("%Y-%m-%d")}
- **平台**: Windows 10 64位
- **文件大小**: {size_mb:.1f} MB
- **特色**: 详细调试日志 + 控制台输出

### 🎉 使用说明

#### 正常使用
1. 启动程序（会显示控制台和GUI两个窗口）
2. 在GUI中选择数据文件
3. **重点关注控制台窗口的输出**
4. 根据调试信息诊断问题

#### 报告问题
如果仍然只处理100个点，请提供：
1. 控制台的完整输出
2. 使用的bag文件信息（大小、话题等）
3. 期望的GPS数据点数量

### 🚀 立即开始调试
双击 GPS_3D_Analyzer_Debug.exe 开始调试真实数据处理问题！

---
© 2024 GPS Analyzer Team. All rights reserved.
专门用于诊断和修复真实数据处理问题的调试版本。
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 调试版发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("🔍 GPS 3D轨迹分析器 - 调试版发布包创建")
    print("=" * 60)
    
    if create_debug_release():
        print("\n" + "=" * 60)
        print("🎉 调试版发布包创建成功!")
        print("=" * 60)
        print("📁 发布包: GPS_3D_Analyzer_Debug_Release/")
        print("🚀 exe文件: GPS_3D_Analyzer_Debug.exe")
        print("\n🔍 这个版本会显示详细的调试信息")
        print("🔍 帮助诊断为什么只处理100个点的问题")
        print("🔍 请运行后查看控制台输出")
        print("🔍 根据调试信息找出真实数据处理失败的原因")
    else:
        print("❌ 发布包创建失败")

if __name__ == "__main__":
    main()
