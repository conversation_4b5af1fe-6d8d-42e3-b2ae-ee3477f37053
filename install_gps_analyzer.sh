#!/bin/bash

# GPS轨迹分析工具安装脚本

echo "=========================================="
echo "GPS轨迹分析工具安装脚本"
echo "=========================================="

# 检查Python版本
echo "1. 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查pip
echo "2. 检查pip..."
python3 -m pip --version
if [ $? -ne 0 ]; then
    echo "❌ pip未安装，请先安装pip"
    exit 1
fi

# 安装依赖包
echo "3. 安装Python依赖包..."
echo "正在安装 rosbag..."
python3 -m pip install --user rospkg rosbag

echo "正在安装 numpy..."
python3 -m pip install --user numpy

echo "正在安装 matplotlib..."
python3 -m pip install --user matplotlib

echo "正在安装 open3d..."
python3 -m pip install --user open3d

# 检查ROS环境
echo "4. 检查ROS环境..."
if [ -z "$ROS_DISTRO" ]; then
    echo "⚠️  ROS环境未设置，请确保已source ROS setup.bash"
    echo "   例如: source /opt/ros/noetic/setup.bash"
else
    echo "✅ ROS环境: $ROS_DISTRO"
fi

# 设置执行权限
echo "5. 设置执行权限..."
chmod +x gps_trajectory_analyzer.py

echo ""
echo "=========================================="
echo "✅ 安装完成!"
echo "=========================================="
echo ""
echo "使用方法:"
echo "  python3 gps_trajectory_analyzer.py your_bag_file.bag"
echo ""
echo "高级选项:"
echo "  --topic /your/gps/topic    # 指定GPS topic"
echo "  --save-plot               # 保存2D轨迹图"
echo "  --export-data             # 导出数据到CSV"
echo ""
echo "示例:"
echo "  python3 gps_trajectory_analyzer.py data.bag --topic /rtk/gnss --save-plot --export-data"
echo ""
