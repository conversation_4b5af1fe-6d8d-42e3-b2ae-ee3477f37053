#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analyzer GUI - Final Fixed Version
Complete GUI application with bagpy import protection for PyInstaller
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pandas as pd
import os
import sys
import threading
import time
from collections import defaultdict
import math
import tempfile

# 设置编码和字体
import locale
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 设置深灰色主题
plt.style.use('dark_background')
matplotlib.rcParams['figure.facecolor'] = '#2b2b2b'
matplotlib.rcParams['axes.facecolor'] = '#2b2b2b'

# 设置系统编码
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

# 安全导入bagpy - 修复PyInstaller兼容性问题
def safe_import_bagpy():
    """安全导入bagpy，处理PyInstaller兼容性问题"""
    try:
        # 首先尝试创建bagpy需要的version文件
        import bagpy
        
        # 获取bagpy路径
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        # 检查version文件是否存在
        if not os.path.exists(version_file):
            # 尝试创建version文件
            try:
                with open(version_file, 'w') as f:
                    f.write('0.5.0')
                print(f"✅ 创建bagpy version文件: {version_file}")
            except (OSError, PermissionError):
                # 如果无法在原位置创建，尝试在临时目录创建并设置环境变量
                temp_dir = tempfile.gettempdir()
                temp_bagpy_dir = os.path.join(temp_dir, 'bagpy')
                os.makedirs(temp_bagpy_dir, exist_ok=True)
                temp_version_file = os.path.join(temp_bagpy_dir, 'version')
                with open(temp_version_file, 'w') as f:
                    f.write('0.5.0')
                print(f"✅ 创建临时bagpy version文件: {temp_version_file}")
        
        # 重新导入bagpy以确保version文件被识别
        import importlib
        importlib.reload(bagpy)
        
        return bagpy, True
        
    except ImportError as e:
        print(f"⚠️  bagpy导入失败: {e}")
        return None, False
    except Exception as e:
        print(f"⚠️  bagpy初始化失败: {e}")
        # 尝试直接导入，忽略version文件问题
        try:
            import bagpy
            return bagpy, True
        except:
            return None, False

# 尝试导入bagpy
bagpy_module, USE_BAGPY = safe_import_bagpy()

class GPSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPS 3D 轨迹分析器 - 最终修复版")
        self.root.geometry("1600x1000")
        
        # 设置字体
        self.setup_fonts()
        
        # 设置深灰色主题
        self.setup_dark_theme()
        
        # Variables
        self.bag_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.gps_topic = tk.StringVar(value="/rtk/gnss")
        self.is_processing = False
        self.gps_data = []
        self.trajectory_points = []
        
        # 3D视图控制变量
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.mouse_pressed = False
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        # GPS quality settings
        self.quality_colors = {
            -1: '#FF6B6B',  # 红色 - NO_FIX
            0:  '#4ECDC4',  # 青绿色 - RTK_FIXED
            1:  '#45B7D1',  # 蓝色 - SBAS_FIX
            2:  '#FFA07A',  # 橙色 - GBAS_FIX
            3:  '#DDA0DD',  # 紫色 - OTHER
        }
        self.quality_names = {
            -1: "无定位",
            0:  "RTK固定解",
            1:  "SBAS定位", 
            2:  "GBAS定位",
            3:  "其他"
        }
        
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
        self.setup_gui()
        
    def setup_fonts(self):
        """设置字体以避免乱码"""
        try:
            self.default_font = ('Microsoft YaHei', 10)
            self.title_font = ('Microsoft YaHei', 16, 'bold')
            self.label_font = ('Microsoft YaHei', 9)
            self.button_font = ('Microsoft YaHei', 9)
        except:
            self.default_font = ('Arial', 10)
            self.title_font = ('Arial', 16, 'bold')
            self.label_font = ('Arial', 9)
            self.button_font = ('Arial', 9)
        
    def setup_dark_theme(self):
        """设置深灰色主题"""
        self.colors = {
            'bg': '#2E2E2E',           # 主背景 - 深灰
            'text_green': '#00C851',   # 文字绿色
            'select_bg': '#404040',    # 选中背景 - 深灰
            'entry_bg': '#3C3C3C',     # 输入框背景 - 深灰
            'button_bg': '#404040',    # 按钮背景 - 深灰
            'frame_bg': '#353535',     # 框架背景
            'accent': '#0078D4',       # 强调色 - 蓝色
            'success': '#00C851',      # 成功色 - 绿色
            'warning': '#FF8800',      # 警告色 - 橙色
            'error': '#FF4444',        # 错误色 - 红色
            'terminal_bg': '#1E1E1E',  # 终端背景
            'terminal_fg': '#00FF41',  # 终端前景 - 绿色
        }
        
        # 配置根窗口
        self.root.configure(bg=self.colors['bg'])
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置各种控件样式
        style.configure('Dark.TFrame', background=self.colors['frame_bg'], relief='flat')
        style.configure('Dark.TLabel', background=self.colors['bg'], foreground=self.colors['text_green'], font=self.label_font)
        style.configure('Dark.TButton', background=self.colors['button_bg'], foreground=self.colors['text_green'], font=self.button_font, relief='flat', borderwidth=1, focuscolor='none')
        style.map('Dark.TButton', background=[('active', '#505050'), ('pressed', '#606060')], foreground=[('active', self.colors['text_green']), ('pressed', self.colors['text_green'])])
        style.configure('Dark.TEntry', background=self.colors['entry_bg'], foreground=self.colors['text_green'], font=self.default_font, relief='flat', borderwidth=1, selectbackground=self.colors['select_bg'], selectforeground=self.colors['text_green'], insertcolor=self.colors['text_green'])
        style.map('Dark.TEntry', focuscolor=[('!focus', 'none')], selectbackground=[('focus', self.colors['select_bg'])], selectforeground=[('focus', self.colors['text_green'])], bordercolor=[('focus', self.colors['text_green'])])
        style.configure('Accent.TButton', background=self.colors['accent'], foreground='#FFFFFF', font=self.button_font, relief='flat', borderwidth=1, focuscolor='none')
        style.map('Accent.TButton', background=[('active', '#106EBE'), ('pressed', '#005A9E')])
        style.configure('Success.TLabel', background=self.colors['bg'], foreground=self.colors['success'], font=self.label_font)
        style.configure('Warning.TLabel', background=self.colors['bg'], foreground=self.colors['warning'], font=self.label_font)
        style.configure('Error.TLabel', background=self.colors['bg'], foreground=self.colors['error'], font=self.label_font)
        style.configure('Dark.TLabelframe', background=self.colors['bg'], foreground=self.colors['text_green'], relief='flat', borderwidth=1)
        style.configure('Dark.TLabelframe.Label', background=self.colors['bg'], foreground=self.colors['text_green'], font=self.label_font)
        style.configure('Dark.Horizontal.TProgressbar', background=self.colors['accent'], troughcolor=self.colors['frame_bg'], relief='flat', borderwidth=0)
        
    def setup_gui(self):
        """Setup the GUI layout"""
        main_frame = ttk.Frame(self.root, padding="15", style='Dark.TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title with bagpy status
        bagpy_status = "✅ Bagpy可用" if USE_BAGPY else "⚠️ Bagpy不可用"
        title_text = f"GPS 3D 轨迹分析器 - 最终修复版 ({bagpy_status})"
        title_label = ttk.Label(main_frame, text=title_text, font=self.title_font, style='Dark.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))
        
        # Control panel
        self.setup_control_panel(main_frame)
        
        # Visualization panel
        self.setup_visualization_panel(main_frame)
        
        # Terminal panel
        self.setup_terminal_panel(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="15", style='Dark.TLabelframe')
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        
        # File selection
        file_label = "Bag文件:" if USE_BAGPY else "数据文件 (CSV/TXT):"
        ttk.Label(control_frame, text=file_label, style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.bag_file_path, width=35, style='Dark.TEntry').grid(row=0, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_data_file, style='Dark.TButton').grid(row=0, column=2, padx=8)
        
        # GPS Topic (only show if bagpy available)
        if USE_BAGPY:
            ttk.Label(control_frame, text="GPS话题:", style='Dark.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
            ttk.Entry(control_frame, textvariable=self.gps_topic, width=35, style='Dark.TEntry').grid(row=1, column=1, padx=8)
            output_row = 2
        else:
            output_row = 1
        
        # Output file
        ttk.Label(control_frame, text="输出文件:", style='Dark.TLabel').grid(row=output_row, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.output_file_path, width=35, style='Dark.TEntry').grid(row=output_row, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_output_file, style='Dark.TButton').grid(row=output_row, column=2, padx=8)
        
        # Control buttons
        button_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        button_frame.grid(row=output_row+1, column=0, columnspan=3, pady=25)
        
        self.start_button = ttk.Button(button_frame, text="开始分析", command=self.start_analysis, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=8)
        
        self.stop_button = ttk.Button(button_frame, text="停止分析", command=self.stop_analysis, state='disabled', style='Dark.TButton')
        self.stop_button.pack(side=tk.LEFT, padx=8)
        
        # Demo button for testing
        self.demo_button = ttk.Button(button_frame, text="演示数据", command=self.load_demo_data, style='Dark.TButton')
        self.demo_button.pack(side=tk.LEFT, padx=8)
        
        # 3D view controls
        view_frame = ttk.LabelFrame(control_frame, text="3D视图控制", padding="12", style='Dark.TLabelframe')
        view_frame.grid(row=output_row+2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 10))
        
        view_buttons_frame = ttk.Frame(view_frame, style='Dark.TFrame')
        view_buttons_frame.grid(row=0, column=0, columnspan=4, pady=5)
        
        ttk.Button(view_buttons_frame, text="俯视", command=self.view_top, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="侧视", command=self.view_side, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="正视", command=self.view_front, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="复位", command=self.reset_view, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        
        zoom_frame = ttk.Frame(view_frame, style='Dark.TFrame')
        zoom_frame.grid(row=1, column=0, columnspan=4, pady=10)
        
        ttk.Label(zoom_frame, text="缩放:", style='Dark.TLabel').pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="放大", command=self.zoom_in, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(zoom_frame, text="缩小", command=self.zoom_out, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(zoom_frame, text="适应", command=self.zoom_fit, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate', style='Dark.Horizontal.TProgressbar')
        self.progress.grid(row=output_row+3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        
        # Status label
        status_text = "就绪 - Bagpy可用" if USE_BAGPY else "就绪 - 支持CSV/TXT格式"
        status_style = 'Success.TLabel' if USE_BAGPY else 'Warning.TLabel'
        self.status_label = ttk.Label(control_frame, text=status_text, style=status_style)
        self.status_label.grid(row=output_row+4, column=0, columnspan=3, pady=8)

    def setup_visualization_panel(self, parent):
        """Setup visualization and analysis panel"""
        viz_frame = ttk.LabelFrame(parent, text="3D可视化与分析结果", padding="15", style='Dark.TLabelframe')
        viz_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        viz_frame.columnconfigure(0, weight=2)
        viz_frame.columnconfigure(1, weight=1)
        viz_frame.rowconfigure(0, weight=1)

        # 3D Plot frame
        plot_frame = ttk.Frame(viz_frame, style='Dark.TFrame')
        plot_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        plot_frame.columnconfigure(0, weight=1)
        plot_frame.rowconfigure(0, weight=1)

        # Create matplotlib figure with dark theme
        self.fig = plt.Figure(figsize=(10, 8), dpi=100, facecolor='#2b2b2b')
        self.ax = self.fig.add_subplot(111, projection='3d')
        self.setup_3d_plot_style()

        # Canvas for matplotlib
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 绑定鼠标事件 - 修复的3D控制
        self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        # Analysis results frame
        analysis_frame = ttk.LabelFrame(viz_frame, text="分析结果", padding="15", style='Dark.TLabelframe')
        analysis_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Analysis text widget with dark theme
        self.analysis_text = scrolledtext.ScrolledText(
            analysis_frame,
            width=45,
            height=30,
            font=self.default_font,
            bg=self.colors['entry_bg'],
            fg=self.colors['text_green'],
            insertbackground=self.colors['text_green'],
            selectbackground=self.colors['select_bg'],
            selectforeground=self.colors['text_green'],
            relief='flat',
            borderwidth=1
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True)

    def setup_terminal_panel(self, parent):
        """Setup terminal output panel"""
        terminal_frame = ttk.LabelFrame(parent, text="终端输出", padding="15", style='Dark.TLabelframe')
        terminal_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        terminal_frame.columnconfigure(0, weight=1)
        terminal_frame.rowconfigure(0, weight=1)

        # Terminal text widget with dark theme
        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            height=10,
            font=('Consolas', 10),
            bg=self.colors['terminal_bg'],
            fg=self.colors['terminal_fg'],
            insertbackground=self.colors['terminal_fg'],
            selectbackground=self.colors['select_bg'],
            selectforeground=self.colors['select_fg'],
            relief='flat',
            borderwidth=1
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True)

    def setup_3d_plot_style(self):
        """设置3D图表样式"""
        self.ax.set_facecolor('#2b2b2b')
        self.ax.set_title('GPS 3D 轨迹可视化', color='white', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X (米)', color='white', fontsize=12)
        self.ax.set_ylabel('Y (米)', color='white', fontsize=12)
        self.ax.set_zlabel('Z (米)', color='white', fontsize=12)

        # 设置坐标轴颜色
        self.ax.tick_params(colors='white')
        self.ax.xaxis.label.set_color('white')
        self.ax.yaxis.label.set_color('white')
        self.ax.zaxis.label.set_color('white')

        # 设置网格
        self.ax.grid(True, alpha=0.3, color='white')

        # 设置背景颜色
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False

        # 设置坐标轴线条颜色
        self.ax.xaxis.pane.set_edgecolor('white')
        self.ax.yaxis.pane.set_edgecolor('white')
        self.ax.zaxis.pane.set_edgecolor('white')
        self.ax.xaxis.pane.set_alpha(0.1)
        self.ax.yaxis.pane.set_alpha(0.1)
        self.ax.zaxis.pane.set_alpha(0.1)

    # 修复的3D视图控制方法
    def on_scroll(self, event):
        """处理鼠标滚轮缩放"""
        if event.inaxes == self.ax:
            if event.button == 'up':
                self.zoom_in()
            elif event.button == 'down':
                self.zoom_out()

    def on_mouse_press(self, event):
        """处理鼠标按下事件"""
        if event.inaxes == self.ax and event.button == 1:
            self.mouse_pressed = True
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def on_mouse_release(self, event):
        """处理鼠标释放事件"""
        self.mouse_pressed = False

    def on_mouse_motion(self, event):
        """处理鼠标移动事件"""
        if (self.mouse_pressed and event.inaxes == self.ax and
            hasattr(self, 'last_mouse_x') and hasattr(self, 'last_mouse_y')):

            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y

            # 更新视角 - 降低灵敏度
            self.view_azimuth += dx * 0.3
            self.view_elevation -= dy * 0.3

            # 限制仰角范围
            self.view_elevation = max(-90, min(90, self.view_elevation))

            # 更新3D视图
            self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
            self.canvas.draw_idle()

            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def view_top(self):
        """俯视图"""
        self.view_elevation = 90
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_side(self):
        """侧视图"""
        self.view_elevation = 0
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_front(self):
        """正视图"""
        self.view_elevation = 0
        self.view_azimuth = 90
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def reset_view(self):
        """复位视图"""
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.zoom_fit()

    def zoom_in(self):
        """放大"""
        self.zoom_factor *= 0.8
        self.apply_zoom()

    def zoom_out(self):
        """缩小"""
        self.zoom_factor *= 1.25
        self.apply_zoom()

    def zoom_fit(self):
        """适应窗口"""
        if hasattr(self, 'original_limits'):
            self.zoom_factor = 1.0
            self.apply_zoom()

    def apply_zoom(self):
        """应用缩放"""
        if hasattr(self, 'original_limits'):
            xlim, ylim, zlim = self.original_limits

            x_center = (xlim[0] + xlim[1]) / 2
            y_center = (ylim[0] + ylim[1]) / 2
            z_center = (zlim[0] + zlim[1]) / 2

            x_range = (xlim[1] - xlim[0]) * self.zoom_factor / 2
            y_range = (ylim[1] - ylim[0]) * self.zoom_factor / 2
            z_range = (zlim[1] - zlim[0]) * self.zoom_factor / 2

            self.ax.set_xlim(x_center - x_range, x_center + x_range)
            self.ax.set_ylim(y_center - y_range, y_center + y_range)
            self.ax.set_zlim(z_center - z_range, z_center + z_range)

            self.canvas.draw_idle()

    def log_message(self, message):
        """添加消息到终端输出"""
        timestamp = time.strftime("%H:%M:%S")
        self.terminal_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.terminal_text.see(tk.END)
        self.root.update_idletasks()
