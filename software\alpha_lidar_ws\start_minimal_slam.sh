#!/bin/bash

# 最小化SLAM启动脚本 - 避免崩溃

echo "=========================================="
echo "🛡️  最小化SLAM系统启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/minimal_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "🔧 设置最小化安全参数"
echo "===================="

# 设置最保守的参数
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 禁用所有可能导致崩溃的功能
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_icp_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false
rosparam set /state_estimation_node/enable_feature_extraction false

# 设置最保守的处理参数
rosparam set /state_estimation_node/voxel_size 1.0
rosparam set /state_estimation_node/max_iterations 10
rosparam set /state_estimation_node/transformation_epsilon 1e-4
rosparam set /state_estimation_node/euclidean_fitness_epsilon 1e-4

# 设置内存安全参数
rosparam set /state_estimation_node/max_points_per_scan 30000
rosparam set /state_estimation_node/downsample_ratio 0.3
rosparam set /state_estimation_node/min_points_threshold 500

# 设置调试参数
rosparam set /state_estimation_node/enable_debug_output true
rosparam set /state_estimation_node/enable_point_cloud_validation true

echo "✅ 安全参数设置完成"

echo ""
echo "🚀 启动最小化SLAM系统"
echo "===================="

echo "启动SLAM核心节点（最小化模式）..."
rosrun state_estimation state_estimation_node &
SLAM_PID=$!

echo "等待SLAM初始化..."
sleep 8

# 检查SLAM是否成功启动
if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM核心节点启动成功 (PID: $SLAM_PID)"
    
    # 等待更长时间确保稳定
    echo "等待系统稳定..."
    sleep 5
    
    if ps -p $SLAM_PID > /dev/null; then
        echo "✅ SLAM系统运行稳定"
        
        echo ""
        echo "检查输出topic..."
        sleep 3
        
        # 检查基本输出
        if rostopic list | grep -q "/aft_mapped_to_init"; then
            echo "✅ 位姿输出正常"
        else
            echo "⚠️  位姿输出未找到"
        fi
        
        if rostopic list | grep -q "/cloud_registered"; then
            echo "✅ 点云输出正常"
        else
            echo "⚠️  点云输出未找到"
        fi
        
        echo ""
        echo "🎉 最小化SLAM系统启动成功!"
        echo ""
        echo "系统状态："
        echo "  SLAM核心: 运行中 (PID: $SLAM_PID)"
        echo "  输出目录: $OUTPUT_DIR"
        echo "  运行模式: 最小化安全模式"
        echo ""
        echo "功能状态："
        echo "  ✅ 基础SLAM定位建图"
        echo "  ❌ GPS集成 (已禁用)"
        echo "  ❌ 回环检测 (已禁用)"
        echo "  ❌ 强度处理 (已禁用)"
        echo ""
        echo "监控命令："
        echo "  rostopic echo /aft_mapped_to_init"
        echo "  rostopic echo /cloud_registered"
        echo "  rostopic hz /velodyne_points"
        echo ""
        echo "在另一个终端播放bag文件:"
        echo "  rosbag play your_data.bag"
        echo ""
        echo "如果系统稳定运行，可以逐步启用更多功能:"
        echo "  1. 启用GPS: rosparam set /state_estimation_node/enable_gps true"
        echo "  2. 启用回环: rosparam set /state_estimation_node/enable_loop_closure true"
        echo "  3. 启用强度: rosparam set /state_estimation_node/enable_intensity_processing true"
        echo ""
        echo "按 Ctrl+C 停止系统"
        
        # 创建停止函数
        cleanup() {
            echo ""
            echo "正在停止SLAM系统..."
            kill $SLAM_PID 2>/dev/null
            echo "✅ SLAM系统已停止"
            exit 0
        }
        
        # 捕获Ctrl+C信号
        trap cleanup SIGINT
        
        # 等待用户中断
        wait
        
    else
        echo "❌ SLAM系统启动后崩溃"
        echo ""
        echo "建议的调试步骤:"
        echo "1. 检查数据源: ./validate_data_source.sh"
        echo "2. 运行崩溃修复: ./fix_slam_crash.sh"
        echo "3. 使用GDB调试: gdb --args devel/lib/state_estimation/state_estimation_node"
    fi
    
else
    echo "❌ SLAM核心节点启动失败"
    echo ""
    echo "可能的原因:"
    echo "1. 点云topic不存在或无数据"
    echo "2. IMU topic不存在或无数据"
    echo "3. 内存不足"
    echo "4. 编译问题"
    echo ""
    echo "建议的解决步骤:"
    echo ""
    echo "步骤1: 验证数据源"
    echo "  ./validate_data_source.sh"
    echo ""
    echo "步骤2: 检查topic是否存在"
    echo "  rostopic list | grep -E '(points|imu)'"
    echo ""
    echo "步骤3: 检查是否有数据流"
    echo "  rostopic hz /velodyne_points"
    echo "  rostopic hz /imu/data"
    echo ""
    echo "步骤4: 运行崩溃修复脚本"
    echo "  ./fix_slam_crash.sh"
    echo ""
    echo "步骤5: 查看详细错误日志"
    echo "  tail -f ~/.ros/log/latest/state_estimation_node-*.log"
fi

echo ""
echo "=========================================="
echo "最小化SLAM启动完成"
echo "=========================================="
