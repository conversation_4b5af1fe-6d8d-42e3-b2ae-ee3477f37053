#!/bin/bash

# 智能首尾回环检测SLAM系统启动脚本
# 实现您提出的优化策略

echo "=========================================="
echo "🧠 智能首尾回环检测SLAM系统"
echo "=========================================="
echo ""
echo "优化策略："
echo "✓ 起点离开30米后开始监控"
echo "✓ 实时GPS位置与起点比较"
echo "✓ 质量非0的GPS也参与检测"
echo "✓ 直线距离<50米时强制匹配"
echo "✓ SLAM首尾帧范围全局精细匹配"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/intelligent_output_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "选择智能检测配置："
echo "1) 标准智能检测 (离开30m, 返回50m)"
echo "2) 敏感智能检测 (离开20m, 返回40m)"
echo "3) 宽松智能检测 (离开40m, 返回60m)"
echo "4) 超敏感检测 (离开15m, 返回30m)"
echo "5) 自定义配置"
echo ""

read -p "请选择配置 (1-5): " choice

case $choice in
    1)
        echo "启动标准智能检测配置..."
        DEPARTURE_THRESHOLD=30.0
        RETURN_THRESHOLD=50.0
        GPS_QUALITY=0
        ;;
    2)
        echo "启动敏感智能检测配置..."
        DEPARTURE_THRESHOLD=20.0
        RETURN_THRESHOLD=40.0
        GPS_QUALITY=0
        ;;
    3)
        echo "启动宽松智能检测配置..."
        DEPARTURE_THRESHOLD=40.0
        RETURN_THRESHOLD=60.0
        GPS_QUALITY=0
        ;;
    4)
        echo "启动超敏感检测配置..."
        DEPARTURE_THRESHOLD=15.0
        RETURN_THRESHOLD=30.0
        GPS_QUALITY=0
        ;;
    5)
        echo "自定义配置："
        read -p "起点离开阈值 (米, 推荐30): " DEPARTURE_THRESHOLD
        read -p "返回检测阈值 (米, 推荐50): " RETURN_THRESHOLD
        read -p "GPS质量阈值 (0=接受质量非0, -1=接受所有): " GPS_QUALITY
        
        DEPARTURE_THRESHOLD=${DEPARTURE_THRESHOLD:-30.0}
        RETURN_THRESHOLD=${RETURN_THRESHOLD:-50.0}
        GPS_QUALITY=${GPS_QUALITY:-0}
        ;;
    *)
        echo "使用默认配置..."
        DEPARTURE_THRESHOLD=30.0
        RETURN_THRESHOLD=50.0
        GPS_QUALITY=0
        ;;
esac

echo ""
echo "启动参数："
echo "  起点离开阈值: ${DEPARTURE_THRESHOLD}米"
echo "  返回检测阈值: ${RETURN_THRESHOLD}米"
echo "  GPS质量阈值: ${GPS_QUALITY}"
echo "  输出目录: $OUTPUT_DIR"
echo ""

# 启动智能SLAM系统
roslaunch state_estimation intelligent_slam_system.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=poor_gps_preset \
    save_directory:="$OUTPUT_DIR" \
    start_departure_threshold:="$DEPARTURE_THRESHOLD" \
    return_detection_threshold:="$RETURN_THRESHOLD" \
    gps_quality_threshold:="$GPS_QUALITY" \
    min_trajectory_length:=100.0 \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_intelligent_detection:=true \
    enable_performance_monitoring:=true

echo ""
echo "=========================================="
echo "智能SLAM系统已启动"
echo "=========================================="
echo ""
echo "监控命令："
echo "  智能检测状态: rostopic echo /intelligent_start_end_status"
echo "  GPS回环状态: rostopic echo /enhanced_gps_loop_closure_status"
echo "  强制回环信号: rostopic echo /force_loop_closure"
echo "  检测触发信号: rostopic echo /start_end_detection_trigger"
echo ""
echo "实时监控脚本："
echo "  ./monitor_intelligent_detection.sh"
