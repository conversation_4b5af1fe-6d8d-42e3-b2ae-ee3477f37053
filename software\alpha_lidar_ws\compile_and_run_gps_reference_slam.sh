#!/bin/bash

# GPS参考增强SLAM系统完整编译和执行脚本

echo "=========================================="
echo "🔧 GPS参考增强SLAM系统编译和执行"
echo "=========================================="
echo ""
echo "系统概述："
echo "🎯 GPS仅作为参考位置，不直接修改SLAM"
echo "✅ 避免GPS约束导致的点云匹配错位"
echo "🚀 通过增强SLAM自身匹配能力解决首尾偏差"
echo "💎 多分辨率、多算法自适应匹配"
echo ""

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    echo "当前目录: $(pwd)"
    echo "请执行: cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash
echo "✓ ROS环境已设置"

echo ""
echo "步骤2: 安装依赖"
echo "安装Python依赖..."
pip3 install numpy scipy

echo "安装ROS依赖..."
sudo apt update
sudo apt install -y \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    libpcl-dev \
    pcl-tools \
    libeigen3-dev

echo "✓ 依赖安装完成"

echo ""
echo "步骤3: 清理并编译"
rm -rf build devel
echo "✓ 清理完成"

echo "开始编译..."
catkin_make -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    # 设置权限
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤4: 验证系统组件"
    
    # 检查核心组件
    components=(
        "devel/lib/state_estimation/state_estimation_node:SLAM核心节点"
        "devel/lib/state_estimation/force_start_end_loop_matcher:强制匹配器"
        "devel/lib/state_estimation/intensity_preserving_pcd_saver:强度保持模块"
        "src/state_estimation/scripts/gps_reference_enhanced_slam.py:GPS参考增强器"
        "src/state_estimation/scripts/intelligent_start_end_detector.py:智能检测器"
        "src/state_estimation/scripts/gps_soft_constraint_loop_detector.py:软约束检测器"
    )
    
    all_good=true
    for component in "${components[@]}"; do
        IFS=':' read -r path name <<< "$component"
        if [ -f "$path" ]; then
            echo "✅ $name"
        else
            echo "⚠️  $name (缺失)"
            if [[ "$path" == *"state_estimation_node"* ]]; then
                all_good=false
            fi
        fi
    done
    
    echo ""
    if [ "$all_good" = true ]; then
        echo "🎉 所有核心组件准备就绪!"
        
        echo ""
        echo "步骤5: 系统功能验证"
        
        # 验证Python脚本语法
        echo "验证Python脚本..."
        python3 -m py_compile src/state_estimation/scripts/gps_reference_enhanced_slam.py && echo "✅ GPS参考增强器语法正确" || echo "❌ GPS参考增强器语法错误"
        python3 -m py_compile src/state_estimation/scripts/gps_soft_constraint_loop_detector.py && echo "✅ 软约束检测器语法正确" || echo "❌ 软约束检测器语法错误"
        
        # 检查launch文件
        if [ -f "src/state_estimation/launch/optimized_slam_simple.launch" ]; then
            echo "✅ Launch文件存在"
        else
            echo "❌ Launch文件缺失"
        fi
        
        echo ""
        echo "=========================================="
        echo "🚀 启动选项"
        echo "=========================================="
        echo ""
        echo "选择启动方式："
        echo "1) 启动GPS参考增强SLAM系统"
        echo "2) 启动RVIZ可视化"
        echo "3) 同时启动SLAM和RVIZ"
        echo "4) 运行系统测试"
        echo "5) 仅编译，稍后手动启动"
        echo ""
        
        read -p "请选择 (1-5): " choice
        
        case $choice in
            1)
                echo ""
                echo "🚀 启动GPS参考增强SLAM系统..."
                ./start_gps_reference_enhanced_slam.sh
                ;;
            2)
                echo ""
                echo "🖥️  启动RVIZ可视化..."
                if [ -f "src/state_estimation/rviz/gps_soft_loop_slam.rviz" ]; then
                    rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz
                else
                    echo "RVIZ配置文件不存在，使用默认配置"
                    rviz
                fi
                ;;
            3)
                echo ""
                echo "🚀 同时启动SLAM和RVIZ..."
                # 在后台启动SLAM
                ./start_gps_reference_enhanced_slam.sh &
                SLAM_PID=$!
                sleep 8
                
                # 启动RVIZ
                if [ -f "src/state_estimation/rviz/gps_soft_loop_slam.rviz" ]; then
                    rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz
                else
                    rviz
                fi
                
                # 清理后台进程
                kill $SLAM_PID 2>/dev/null
                ;;
            4)
                echo ""
                echo "🧪 运行系统测试..."
                
                # 启动roscore
                if ! pgrep -x "roscore" > /dev/null; then
                    echo "启动 roscore..."
                    roscore &
                    sleep 3
                fi
                
                # 测试GPS参考增强器
                echo "测试GPS参考增强器..."
                timeout 10 rosrun state_estimation gps_reference_enhanced_slam.py &
                sleep 5
                
                if pgrep -f "gps_reference_enhanced_slam.py" > /dev/null; then
                    echo "✅ GPS参考增强器启动成功"
                    pkill -f "gps_reference_enhanced_slam.py"
                else
                    echo "❌ GPS参考增强器启动失败"
                fi
                
                # 测试软约束检测器
                echo "测试软约束检测器..."
                timeout 10 rosrun state_estimation gps_soft_constraint_loop_detector.py &
                sleep 5
                
                if pgrep -f "gps_soft_constraint_loop_detector.py" > /dev/null; then
                    echo "✅ 软约束检测器启动成功"
                    pkill -f "gps_soft_constraint_loop_detector.py"
                else
                    echo "❌ 软约束检测器启动失败"
                fi
                
                echo ""
                echo "🎉 系统测试完成!"
                ;;
            5)
                echo ""
                echo "✅ 编译完成，可以手动启动系统"
                ;;
            *)
                echo ""
                echo "无效选择，编译完成"
                ;;
        esac
        
    else
        echo "⚠️  部分组件缺失，但核心功能可用"
        echo ""
        echo "可用的启动命令："
        echo "  ./start_gps_reference_enhanced_slam.sh"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方案："
    echo "1. 检查ROS完整安装:"
    echo "   sudo apt install ros-noetic-desktop-full"
    echo ""
    echo "2. 检查PCL库:"
    echo "   sudo apt install libpcl-dev pcl-tools"
    echo ""
    echo "3. 检查Python依赖:"
    echo "   pip3 install numpy scipy"
    echo ""
    echo "4. 单独编译state_estimation包:"
    echo "   catkin_make --only-pkg-with-deps state_estimation -j1"
    echo ""
    echo "5. 检查具体错误信息并修复"
    
    exit 1
fi

echo ""
echo "=========================================="
echo "📋 使用说明"
echo "=========================================="
echo ""
echo "手动启动命令："
echo "  启动SLAM系统: ./start_gps_reference_enhanced_slam.sh"
echo "  启动RVIZ:     rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz"
echo "  播放数据:     rosbag play your_data.bag"
echo ""
echo "监控命令："
echo "  GPS参考状态: rostopic echo /gps_reference_slam_status"
echo "  增强触发:    rostopic echo /slam_enhancement_trigger"
echo "  增强参数:    rostopic echo /enhanced_slam_parameters"
echo "  参考引导:    rostopic echo /gps_reference_guidance"
echo ""
echo "参数调节："
echo "  GPS参考半径: rosparam set /gps_reference_enhanced_slam/gps_reference_radius 40.0"
echo "  增强半径:    rosparam set /gps_reference_enhanced_slam/slam_enhancement_radius 25.0"
echo "  搜索半径:    rosparam set /gps_reference_enhanced_slam/enhanced_search_radius 100.0"
echo ""
echo "系统优势："
echo "✅ GPS仅作参考，不直接修改SLAM位置"
echo "✅ 完全避免点云匹配错位问题"
echo "✅ 根据GPS质量动态调整增强级别"
echo "✅ 多分辨率、多算法自适应匹配"
echo "✅ SLAM自身匹配能力显著提升"
echo ""
echo "=========================================="
echo "GPS参考增强SLAM系统准备完成!"
echo "=========================================="
