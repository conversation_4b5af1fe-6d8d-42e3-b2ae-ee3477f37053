#!/bin/bash

# 修复运行时错误后的GPS SLAM启动脚本

echo "=========================================="
echo "🚀 GPS SLAM系统启动 (已修复运行时错误)"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_slam_fixed_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "步骤1: 检查依赖"

# 检查Python依赖
echo "检查Python依赖..."
python3 -c "import psutil" 2>/dev/null && echo "✅ psutil可用" || {
    echo "⚠️  psutil不可用，安装中..."
    pip3 install psutil || sudo apt install -y python3-psutil
}

python3 -c "import numpy" 2>/dev/null && echo "✅ numpy可用" || {
    echo "⚠️  numpy不可用，安装中..."
    pip3 install numpy
}

# 检查ROS依赖
rospack find tf2_ros >/dev/null 2>&1 && echo "✅ tf2_ros可用" || {
    echo "⚠️  tf2_ros不可用，安装中..."
    sudo apt update
    sudo apt install -y ros-noetic-tf2-ros ros-noetic-tf2-geometry-msgs
}

echo ""
echo "步骤2: 检查系统组件"

# 检查可执行文件
components=(
    "devel/lib/state_estimation/state_estimation_node:SLAM核心节点"
    "devel/lib/state_estimation/intensity_preserving_pcd_saver:强度保持模块"
    "devel/lib/state_estimation/enhanced_slam_loop_closure_integration:SLAM集成"
    "devel/lib/state_estimation/force_start_end_loop_matcher:强制匹配器"
    "src/state_estimation/scripts/intelligent_start_end_detector.py:智能检测器"
    "src/state_estimation/scripts/intelligent_gps_constraint_controller.py:GPS约束控制器"
    "src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py:GPS回环优化器"
)

available_components=()
for component in "${components[@]}"; do
    IFS=':' read -r path name <<< "$component"
    if [ -f "$path" ]; then
        echo "✅ $name"
        available_components+=("$path")
    else
        echo "⚠️  $name (不可用)"
    fi
done

echo ""
echo "步骤3: 启动系统组件"

# 启动核心SLAM节点
if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "启动SLAM核心节点..."
    rosrun state_estimation state_estimation_node &
    sleep 3
    echo "✅ SLAM核心节点已启动"
else
    echo "❌ SLAM核心节点不可用，无法继续"
    exit 1
fi

# 启动SLAM集成模块
if [ -f "devel/lib/state_estimation/enhanced_slam_loop_closure_integration" ]; then
    echo "启动SLAM集成模块..."
    rosrun state_estimation enhanced_slam_loop_closure_integration &
    sleep 2
    echo "✅ SLAM集成模块已启动"
fi

# 启动GPS回环优化器（修复后的版本）
if [ -f "src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py" ]; then
    echo "启动GPS回环优化器..."
    rosrun state_estimation enhanced_gps_loop_closure_optimizer.py &
    sleep 2
    echo "✅ GPS回环优化器已启动"
fi

# 启动智能检测器
if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
    echo "启动智能检测器..."
    rosrun state_estimation intelligent_start_end_detector.py \
        _gps_topic:=/rtk/gnss \
        _departure_threshold:=30.0 \
        _return_threshold:=50.0 \
        _min_trajectory_points:=100 \
        _gps_quality_threshold:=-1 &
    sleep 2
    echo "✅ 智能检测器已启动"
fi

# 启动GPS约束控制器
if [ -f "src/state_estimation/scripts/intelligent_gps_constraint_controller.py" ]; then
    echo "启动GPS约束控制器..."
    rosrun state_estimation intelligent_gps_constraint_controller.py \
        _icp_fitness_threshold:=0.3 \
        _constraint_disable_fitness:=0.5 \
        _constraint_enable_fitness:=0.2 \
        _velocity_threshold:=2.0 \
        _constraint_cooldown:=5.0 &
    sleep 2
    echo "✅ GPS约束控制器已启动"
fi

# 启动强制匹配器
if [ -f "devel/lib/state_estimation/force_start_end_loop_matcher" ]; then
    echo "启动强制匹配器..."
    rosrun state_estimation force_start_end_loop_matcher \
        _voxel_size:=0.05 \
        _search_radius:=50.0 \
        _max_iterations:=200 \
        _outlier_rejection_threshold:=0.5 &
    sleep 2
    echo "✅ 强制匹配器已启动"
fi

# 启动强度保持模块
if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
    echo "启动强度保持模块..."
    rosrun state_estimation intensity_preserving_pcd_saver \
        _save_directory:="$OUTPUT_DIR" \
        _save_interval:=10.0 &
    sleep 2
    echo "✅ 强度保持模块已启动"
fi

# 启动简化分析器（跳过性能监控器避免psutil错误）
if [ -f "src/state_estimation/scripts/simple_intensity_analyzer.py" ]; then
    echo "启动简化分析器..."
    rosrun state_estimation simple_intensity_analyzer.py &
    sleep 2
    echo "✅ 简化分析器已启动"
fi

echo ""
echo "步骤4: 设置系统参数"

# 等待系统稳定
sleep 5

# 设置GPS约束参数
echo "设置GPS约束参数..."
rosparam set /state_estimation_node/gps/enable_plane_constraint true
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.05
rosparam set /state_estimation_node/gps/xy_correction_rate 0.02

# 设置智能约束控制参数
echo "设置智能约束控制参数..."
rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.4
rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.15
rosparam set /intelligent_gps_constraint_controller/velocity_threshold 2.0

# 设置智能检测参数
echo "设置智能检测参数..."
rosparam set /intelligent_start_end_detector/return_threshold 50.0
rosparam set /intelligent_start_end_detector/departure_threshold 30.0

echo "✅ 参数设置完成"

echo ""
echo "=========================================="
echo "🚀 GPS SLAM系统已启动 (运行时错误已修复)"
echo "=========================================="
echo ""
echo "系统状态:"
echo "  输出目录: $OUTPUT_DIR"
echo "  运行模式: 完整GPS功能"
echo ""
echo "系统功能:"
echo "✅ SLAM核心定位建图"
echo "✅ GPS集成和约束控制"
echo "✅ 智能首尾回环检测"
echo "✅ 强制精细匹配"
echo "✅ 强度值完整保持"
echo "✅ 实时参数优化"
echo ""
echo "监控命令:"
echo "  GPS约束状态: rostopic echo /gps_constraint_control"
echo "  ICP匹配质量: rostopic echo /icp_fitness_score"
echo "  智能检测:    rostopic echo /intelligent_detector_status"
echo "  强制匹配:    rostopic echo /force_match_score"
echo "  GPS回环状态: rostopic echo /enhanced_gps_loop_closure_status"
echo "  SLAM位姿:    rostopic echo /aft_mapped_to_init"
echo ""
echo "参数调节命令:"
echo "  调节GPS约束: rosparam set /state_estimation_node/gps/plane_constraint_weight 0.1"
echo "  调节检测阈值: rosparam set /intelligent_start_end_detector/return_threshold 40.0"
echo "  调节匹配阈值: rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.3"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果:"
echo "✅ GPS接近起点时自动触发精细匹配"
echo "✅ ICP匹配质量差时自动禁用GPS约束"
echo "✅ 首尾偏差从几十米降低到几米内"
echo "✅ 完整保持点云强度信息"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
