#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rebuild GPS Analyzer with bagpy fix
"""

import os
import sys
import subprocess
import shutil

def fix_bagpy_version():
    """修复bagpy版本文件问题"""
    print("🔧 修复bagpy版本文件...")
    
    try:
        import bagpy
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        if not os.path.exists(version_file):
            # 创建版本文件
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
            print(f"✅ 创建版本文件: {version_file}")
        else:
            print(f"✅ 版本文件已存在: {version_file}")
            
        return True
    except Exception as e:
        print(f"❌ 修复bagpy版本文件失败: {e}")
        return False

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  清理 {dir_name}/")
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        if spec_file != 'GPS_3D_Analyzer_Fixed.spec':
            os.remove(spec_file)
            print(f"  清理 {spec_file}")
    
    print("✅ 构建目录清理完成")

def rebuild_with_bagpy_fix():
    """使用bagpy修复重新构建"""
    print("🚀 重新构建exe文件...")
    
    try:
        # 方法1: 使用更详细的参数
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed", 
            "--name=GPS_3D_Analyzer_Fixed",
            "--hidden-import=bagpy",
            "--hidden-import=bagpy.bagreader",
            "--hidden-import=matplotlib.backends.backend_tkagg",
            "--hidden-import=mpl_toolkits.mplot3d",
            "--hidden-import=pandas",
            "--hidden-import=numpy",
            "--hidden-import=tkinter",
            "--hidden-import=tkinter.ttk",
            "--hidden-import=tkinter.filedialog",
            "--hidden-import=tkinter.messagebox",
            "--hidden-import=tkinter.scrolledtext",
            "--collect-all=bagpy",
            "--collect-all=matplotlib",
            "--exclude-module=PyQt5",
            "--exclude-module=PyQt6",
            "--exclude-module=PySide2",
            "--exclude-module=PySide6",
            "--add-data=temp_version;bagpy",  # 添加版本文件
            "gps_gui_analyzer_fixed.py"
        ]
        
        # 创建临时版本文件
        with open("temp_version", "w") as f:
            f.write("0.5.0\n")
        print("✅ 创建临时版本文件")
        
        print("执行PyInstaller命令...")
        print(f"命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        # 清理临时文件
        if os.path.exists("temp_version"):
            os.remove("temp_version")
        
        if result.returncode == 0:
            print("✅ PyInstaller构建成功")
            return True
        else:
            print(f"❌ PyInstaller构建失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_release_package():
    """创建发布包"""
    print("📦 创建发布包...")
    
    exe_path = "dist/GPS_3D_Analyzer_Fixed.exe"
    if not os.path.exists(exe_path):
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 创建发布目录
    release_dir = "GPS_3D_Analyzer_Fixed_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy2(exe_path, release_dir)
    print("✅ 复制可执行文件")
    
    # 创建README
    readme_content = """# GPS 3D 轨迹分析器 - 修复版

## 🔧 修复内容
- ✅ 修复bagpy导入错误
- ✅ 深灰色主题 + 深绿色文字
- ✅ 修复鼠标3D控制
- ✅ 完整中文支持

## 🚀 使用方法
1. 双击 GPS_3D_Analyzer_Fixed.exe 启动
2. 选择ROS bag文件
3. 设置GPS话题（默认: /rtk/gnss）
4. 点击"开始分析"
5. 查看3D轨迹和分析结果

## 💻 系统要求
- Windows 10 64位
- 最小4GB内存
- 支持OpenGL的显卡

## 🎨 界面特色
- 深灰色专业主题
- 深绿色文字显示
- 深色选择背景
- 稳定的3D交互控制

如有问题，请检查终端输出中的错误信息。
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # 获取文件大小
    exe_size = os.path.getsize(os.path.join(release_dir, "GPS_3D_Analyzer_Fixed.exe"))
    exe_size_mb = exe_size / (1024 * 1024)
    
    print(f"✅ 发布包创建完成")
    print(f"📁 发布目录: {release_dir}/")
    print(f"📦 可执行文件大小: {exe_size_mb:.1f} MB")
    
    return True

def main():
    """主函数"""
    print("🔧 GPS 3D轨迹分析器 - bagpy修复重构建")
    print("=" * 60)
    
    # 检查主文件
    if not os.path.exists("gps_gui_analyzer_fixed.py"):
        print("❌ 主程序文件不存在: gps_gui_analyzer_fixed.py")
        return False
    
    # 修复bagpy版本文件
    if not fix_bagpy_version():
        print("⚠️  bagpy版本文件修复失败，继续尝试构建...")
    
    # 清理构建目录
    clean_build()
    
    # 重新构建
    if not rebuild_with_bagpy_fix():
        print("❌ 重新构建失败")
        return False
    
    # 创建发布包
    if not create_release_package():
        print("❌ 发布包创建失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 修复构建完成！")
    print("=" * 60)
    print("📁 新的可执行文件: GPS_3D_Analyzer_Fixed_Release/GPS_3D_Analyzer_Fixed.exe")
    print("🔧 主要修复: bagpy导入错误")
    print("🎨 界面特色: 深色主题 + 绿色文字")
    print("✅ 现在可以正常运行了！")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
