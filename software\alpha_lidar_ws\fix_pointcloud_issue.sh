#!/bin/bash

# 点云数据问题修复脚本

echo "=========================================="
echo "🔍 点云数据问题诊断和修复"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "步骤1: 检查bag文件内容"
echo "===================="

# 查找bag文件
BAG_FILES=$(find . -name "*.bag" -type f 2>/dev/null | head -5)

if [ -z "$BAG_FILES" ]; then
    echo "在当前目录未找到bag文件"
    echo "请指定bag文件路径:"
    read -p "bag文件路径: " BAG_PATH
    
    if [ ! -f "$BAG_PATH" ]; then
        echo "❌ 文件不存在: $BAG_PATH"
        exit 1
    fi
    BAG_FILES="$BAG_PATH"
fi

echo "找到的bag文件:"
for bag in $BAG_FILES; do
    echo "  $bag"
done

# 选择要分析的bag文件
if [ $(echo "$BAG_FILES" | wc -l) -gt 1 ]; then
    echo ""
    echo "请选择要分析的bag文件:"
    select BAG_FILE in $BAG_FILES; do
        if [ -n "$BAG_FILE" ]; then
            break
        fi
    done
else
    BAG_FILE="$BAG_FILES"
fi

echo ""
echo "分析bag文件: $BAG_FILE"
echo "=========================="

# 获取bag文件信息
echo "bag文件基本信息:"
rosbag info "$BAG_FILE"

echo ""
echo "步骤2: 检查点云topic"
echo "=================="

# 提取点云相关topic
POINTCLOUD_TOPICS=$(rosbag info "$BAG_FILE" 2>/dev/null | grep -E "(points|cloud|velodyne|lidar)" | awk '{print $1}')

echo "找到的点云topic:"
if [ -n "$POINTCLOUD_TOPICS" ]; then
    for topic in $POINTCLOUD_TOPICS; do
        echo "  $topic"
        
        # 获取消息类型和数量
        TOPIC_INFO=$(rosbag info "$BAG_FILE" 2>/dev/null | grep "$topic")
        echo "    $TOPIC_INFO"
    done
else
    echo "❌ 未找到点云topic"
    echo ""
    echo "所有可用topic:"
    rosbag info "$BAG_FILE" 2>/dev/null | grep -E "^ /" | awk '{print $1}'
    exit 1
fi

echo ""
echo "步骤3: 选择正确的点云topic"
echo "========================"

# 让用户选择正确的点云topic
if [ $(echo "$POINTCLOUD_TOPICS" | wc -l) -gt 1 ]; then
    echo "请选择要使用的点云topic:"
    select SELECTED_TOPIC in $POINTCLOUD_TOPICS; do
        if [ -n "$SELECTED_TOPIC" ]; then
            break
        fi
    done
else
    SELECTED_TOPIC="$POINTCLOUD_TOPICS"
fi

echo "选择的点云topic: $SELECTED_TOPIC"

echo ""
echo "步骤4: 测试点云数据"
echo "=================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "播放bag文件进行测试..."
rosbag play "$BAG_FILE" --pause &
BAG_PID=$!
sleep 3

# 恢复播放
rostopic pub /clock rosgraph_msgs/Clock "clock: {secs: 0, nsecs: 0}" --once
rosbag play "$BAG_FILE" -r 0.1 &
BAG_TEST_PID=$!
sleep 5

echo ""
echo "检查点云数据流..."

# 检查topic是否存在
if rostopic list | grep -q "$SELECTED_TOPIC"; then
    echo "✅ 点云topic存在: $SELECTED_TOPIC"
    
    # 检查数据频率
    echo "检查数据频率..."
    FREQ_OUTPUT=$(timeout 10 rostopic hz "$SELECTED_TOPIC" 2>/dev/null)
    if [ -n "$FREQ_OUTPUT" ]; then
        echo "$FREQ_OUTPUT"
        FREQ=$(echo "$FREQ_OUTPUT" | grep "average rate" | awk '{print $3}')
        if [ -n "$FREQ" ]; then
            echo "✅ 数据频率: $FREQ Hz"
        fi
    else
        echo "❌ 无法获取数据频率"
    fi
    
    # 检查消息内容
    echo ""
    echo "检查消息内容..."
    MSG_SAMPLE=$(timeout 5 rostopic echo "$SELECTED_TOPIC" -n 1 2>/dev/null)
    if [ -n "$MSG_SAMPLE" ]; then
        echo "✅ 成功读取消息"
        
        # 分析点云信息
        WIDTH=$(echo "$MSG_SAMPLE" | grep "width:" | awk '{print $2}')
        HEIGHT=$(echo "$MSG_SAMPLE" | grep "height:" | awk '{print $2}')
        
        if [ -n "$WIDTH" ] && [ -n "$HEIGHT" ]; then
            TOTAL_POINTS=$((WIDTH * HEIGHT))
            echo "点云尺寸: ${WIDTH} x ${HEIGHT} = ${TOTAL_POINTS} 点"
            
            if [ $TOTAL_POINTS -eq 0 ]; then
                echo "❌ 点云为空！这是崩溃的原因"
            elif [ $TOTAL_POINTS -lt 100 ]; then
                echo "⚠️  点云点数过少: $TOTAL_POINTS"
            else
                echo "✅ 点云点数正常: $TOTAL_POINTS"
            fi
        fi
        
        # 检查数据字段
        if echo "$MSG_SAMPLE" | grep -q "x:"; then
            echo "✅ 包含坐标信息"
        else
            echo "❌ 缺少坐标信息"
        fi
        
        if echo "$MSG_SAMPLE" | grep -q "intensity"; then
            echo "✅ 包含强度信息"
        else
            echo "⚠️  无强度信息"
        fi
        
    else
        echo "❌ 无法读取消息内容"
    fi
    
else
    echo "❌ 点云topic不存在: $SELECTED_TOPIC"
fi

# 停止测试播放
kill $BAG_TEST_PID $BAG_PID 2>/dev/null

echo ""
echo "步骤5: 生成修复方案"
echo "=================="

# 创建修复配置
cat > pointcloud_fix_config.yaml << EOF
# 点云修复配置
pointcloud_topic: "$SELECTED_TOPIC"
enable_point_validation: true
min_points_threshold: 100
max_points_threshold: 200000
enable_empty_scan_skip: true
enable_point_filtering: true
distance_filter_min: 0.5
distance_filter_max: 100.0
EOF

echo "✅ 创建了点云修复配置: pointcloud_fix_config.yaml"

# 创建修复后的launch文件
cat > src/state_estimation/launch/fixed_pointcloud_slam.launch << EOF
<?xml version="1.0"?>
<launch>
    <!-- 修复点云问题的SLAM配置 -->
    
    <!-- 点云预处理节点 -->
    <node name="pointcloud_preprocessor" pkg="state_estimation" type="pointcloud_preprocessor.py" output="screen">
        <param name="input_topic" value="$SELECTED_TOPIC" />
        <param name="output_topic" value="/processed_points" />
        <param name="min_points" value="100" />
        <param name="max_points" value="100000" />
        <param name="enable_filtering" value="true" />
        <param name="min_distance" value="0.5" />
        <param name="max_distance" value="80.0" />
    </node>
    
    <!-- SLAM核心节点 -->
    <node name="state_estimation_node" pkg="state_estimation" type="state_estimation_node" output="screen">
        <param name="lidar_topic" value="/processed_points" />
        <param name="imu_topic" value="/imu/data" />
        <param name="gps_topic" value="/rtk/gnss" />
        
        <!-- 点云处理参数 -->
        <param name="enable_point_cloud_validation" value="true" />
        <param name="min_points_threshold" value="100" />
        <param name="max_points_threshold" value="100000" />
        <param name="enable_empty_scan_protection" value="true" />
        
        <!-- 安全处理参数 -->
        <param name="voxel_size" value="0.8" />
        <param name="max_iterations" value="15" />
        <param name="downsample_ratio" value="0.5" />
        
        <!-- 禁用可能导致崩溃的功能 -->
        <param name="enable_gps" value="false" />
        <param name="enable_loop_closure" value="false" />
        <param name="enable_intensity_processing" value="false" />
    </node>
    
</launch>
EOF

echo "✅ 创建了修复launch文件: src/state_estimation/launch/fixed_pointcloud_slam.launch"

echo ""
echo "步骤6: 创建点云预处理器"
echo "======================"

# 创建点云预处理脚本
cat > src/state_estimation/scripts/pointcloud_preprocessor.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点云预处理器 - 修复空点云和异常数据
"""

import rospy
from sensor_msgs.msg import PointCloud2
import sensor_msgs.point_cloud2 as pc2
from std_msgs.msg import Header

class PointCloudPreprocessor:
    def __init__(self):
        rospy.init_node('pointcloud_preprocessor', anonymous=True)
        
        # 参数
        self.input_topic = rospy.get_param('~input_topic', '/velodyne_points')
        self.output_topic = rospy.get_param('~output_topic', '/processed_points')
        self.min_points = rospy.get_param('~min_points', 100)
        self.max_points = rospy.get_param('~max_points', 100000)
        self.enable_filtering = rospy.get_param('~enable_filtering', True)
        self.min_distance = rospy.get_param('~min_distance', 0.5)
        self.max_distance = rospy.get_param('~max_distance', 80.0)
        
        # 统计
        self.total_scans = 0
        self.empty_scans = 0
        self.filtered_scans = 0
        
        # 订阅和发布
        self.sub = rospy.Subscriber(self.input_topic, PointCloud2, self.pointcloud_callback, queue_size=1)
        self.pub = rospy.Publisher(self.output_topic, PointCloud2, queue_size=1)
        
        # 定时器
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("PointCloud Preprocessor Started")
        rospy.loginfo("Input: %s -> Output: %s", self.input_topic, self.output_topic)
        rospy.loginfo("Point range: %d - %d", self.min_points, self.max_points)
    
    def pointcloud_callback(self, msg):
        try:
            self.total_scans += 1
            
            # 检查点云是否为空
            if msg.width == 0 or msg.height == 0:
                self.empty_scans += 1
                rospy.logwarn("Empty pointcloud detected, skipping...")
                return
            
            total_points = msg.width * msg.height
            
            # 检查点数范围
            if total_points < self.min_points:
                self.empty_scans += 1
                rospy.logwarn("Too few points: %d < %d, skipping...", total_points, self.min_points)
                return
            
            if total_points > self.max_points:
                rospy.logwarn("Too many points: %d > %d, will downsample", total_points, self.max_points)
            
            # 处理点云
            processed_msg = self.process_pointcloud(msg)
            
            if processed_msg:
                self.pub.publish(processed_msg)
                self.filtered_scans += 1
            else:
                self.empty_scans += 1
                
        except Exception as e:
            rospy.logerr("Error processing pointcloud: %s", str(e))
    
    def process_pointcloud(self, msg):
        try:
            # 读取点云数据
            points = list(pc2.read_points(msg, skip_nans=True))
            
            if len(points) < self.min_points:
                return None
            
            # 距离过滤
            if self.enable_filtering:
                filtered_points = []
                for point in points:
                    x, y, z = point[0], point[1], point[2]
                    distance = (x*x + y*y + z*z) ** 0.5
                    
                    if self.min_distance <= distance <= self.max_distance:
                        filtered_points.append(point)
                
                points = filtered_points
            
            if len(points) < self.min_points:
                return None
            
            # 降采样（如果点数过多）
            if len(points) > self.max_points:
                step = len(points) // self.max_points
                points = points[::step]
            
            # 创建新的点云消息
            header = Header()
            header.stamp = msg.header.stamp
            header.frame_id = msg.header.frame_id
            
            processed_msg = pc2.create_cloud_xyz32(header, points)
            
            return processed_msg
            
        except Exception as e:
            rospy.logerr("Error in process_pointcloud: %s", str(e))
            return None
    
    def publish_status(self, event):
        if self.total_scans > 0:
            success_rate = (self.filtered_scans / self.total_scans) * 100
            rospy.loginfo("PointCloud Stats: Total=%d, Processed=%d, Empty=%d, Success=%.1f%%", 
                         self.total_scans, self.filtered_scans, self.empty_scans, success_rate)

def main():
    try:
        preprocessor = PointCloudPreprocessor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()
EOF

chmod +x src/state_estimation/scripts/pointcloud_preprocessor.py

echo "✅ 创建了点云预处理器: src/state_estimation/scripts/pointcloud_preprocessor.py"

echo ""
echo "步骤7: 创建修复启动脚本"
echo "======================"

cat > start_fixed_slam.sh << EOF
#!/bin/bash

# 修复点云问题后的SLAM启动脚本

echo "=========================================="
echo "🔧 启动修复后的SLAM系统"
echo "=========================================="

source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "使用点云topic: $SELECTED_TOPIC"
echo "启动修复后的SLAM系统..."

# 使用修复后的launch文件
roslaunch state_estimation fixed_pointcloud_slam.launch &
LAUNCH_PID=\$!

sleep 5

echo ""
echo "系统启动完成，现在可以播放bag文件:"
echo "  rosbag play $BAG_FILE"
echo ""
echo "监控命令:"
echo "  rostopic echo /processed_points -n 1"
echo "  rostopic echo /aft_mapped_to_init"
echo "  rostopic hz /processed_points"
echo ""
echo "按 Ctrl+C 停止系统"

trap "kill \$LAUNCH_PID 2>/dev/null; exit 0" SIGINT
wait
EOF

chmod +x start_fixed_slam.sh

echo "✅ 创建了修复启动脚本: start_fixed_slam.sh"

echo ""
echo "=========================================="
echo "🎯 点云问题修复完成"
echo "=========================================="
echo ""
echo "发现的问题:"
echo "  - 点云topic: $SELECTED_TOPIC"
echo "  - 可能存在空点云或异常数据"
echo ""
echo "修复方案:"
echo "  ✅ 创建了点云预处理器"
echo "  ✅ 创建了修复launch文件"
echo "  ✅ 创建了修复启动脚本"
echo ""
echo "下一步操作:"
echo "1. 编译预处理器:"
echo "   catkin_make"
echo ""
echo "2. 启动修复后的系统:"
echo "   ./start_fixed_slam.sh"
echo ""
echo "3. 在另一个终端播放bag:"
echo "   rosbag play $BAG_FILE"
echo ""
echo "4. 监控系统状态:"
echo "   rostopic hz /processed_points"
echo "   rostopic echo /aft_mapped_to_init"
EOF
