#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GPS约束优化分析脚本
用于分析和优化αLiDAR系统中的GPS约束效果
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
from nav_msgs.msg import Odometry
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
import tf.transformations as tf_trans
from collections import deque
import time

class GPSConstraintOptimizer:
    def __init__(self):
        rospy.init_node('gps_constraint_optimizer', anonymous=True)
        
        # 数据缓冲区
        self.slam_trajectory = deque(maxlen=10000)
        self.gps_trajectory = deque(maxlen=10000)
        self.timestamps = deque(maxlen=10000)
        
        # GPS原点
        self.gps_origin = None
        self.gps_origin_set = False
        
        # 统计信息
        self.stats = {
            'total_corrections': 0,
            'height_corrections': 0,
            'xy_corrections': 0,
            'loop_closures': 0,
            'max_xy_error': 0.0,
            'max_height_error': 0.0,
            'avg_xy_error': 0.0,
            'avg_height_error': 0.0
        }
        
        # 订阅器
        self.slam_sub = rospy.Subscriber('/odometry', Odometry, self.slam_callback)
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback)
        
        # 发布器
        self.analysis_pub = rospy.Publisher('/gps_constraint_analysis', PoseStamped, queue_size=10)
        
        rospy.loginfo("GPS约束优化器已启动")
        
    def gps_to_local(self, lat, lon, alt):
        """将GPS坐标转换为局部坐标"""
        if not self.gps_origin_set:
            return None
            
        # 简化的坐标转换
        R = 6371000.0  # 地球半径
        deg_to_rad = np.pi / 180.0
        
        lat_rad = lat * deg_to_rad
        lon_rad = lon * deg_to_rad
        lat0_rad = self.gps_origin['lat'] * deg_to_rad
        lon0_rad = self.gps_origin['lon'] * deg_to_rad
        
        x = R * (lon_rad - lon0_rad) * np.cos(lat0_rad)
        y = R * (lat_rad - lat0_rad)
        z = alt - self.gps_origin['alt']
        
        return np.array([x, y, z])
    
    def slam_callback(self, msg):
        """SLAM轨迹回调"""
        timestamp = msg.header.stamp.to_sec()
        position = np.array([
            msg.pose.pose.position.x,
            msg.pose.pose.position.y,
            msg.pose.pose.position.z
        ])
        
        self.slam_trajectory.append(position)
        self.timestamps.append(timestamp)
        
        # 分析GPS约束效果
        self.analyze_constraints()
    
    def gps_callback(self, msg):
        """GPS数据回调"""
        if msg.status.status < 1:  # GPS信号质量检查
            return
            
        # 设置GPS原点
        if not self.gps_origin_set:
            self.gps_origin = {
                'lat': msg.latitude,
                'lon': msg.longitude,
                'alt': msg.altitude
            }
            self.gps_origin_set = True
            rospy.loginfo(f"GPS原点已设置: lat={msg.latitude:.8f}, lon={msg.longitude:.8f}, alt={msg.altitude:.3f}")
            return
        
        # 转换GPS坐标
        gps_local = self.gps_to_local(msg.latitude, msg.longitude, msg.altitude)
        if gps_local is not None:
            self.gps_trajectory.append(gps_local)
    
    def analyze_constraints(self):
        """分析GPS约束效果"""
        if len(self.slam_trajectory) < 10 or len(self.gps_trajectory) < 10:
            return
            
        # 获取最新的SLAM和GPS位置
        slam_pos = self.slam_trajectory[-1]
        
        # 找到时间最接近的GPS位置
        if len(self.gps_trajectory) > 0:
            gps_pos = self.gps_trajectory[-1]
            
            # 计算位置误差
            if len(self.slam_trajectory) > 1 and len(self.gps_trajectory) > 1:
                slam_start = self.slam_trajectory[0]
                gps_start = self.gps_trajectory[0]
                
                slam_drift = slam_pos - slam_start
                gps_drift = gps_pos - gps_start
                
                position_error = gps_drift - slam_drift
                xy_error = np.linalg.norm(position_error[:2])
                height_error = abs(position_error[2])
                
                # 更新统计信息
                self.stats['max_xy_error'] = max(self.stats['max_xy_error'], xy_error)
                self.stats['max_height_error'] = max(self.stats['max_height_error'], height_error)
                
                # 计算平均误差
                n = len(self.slam_trajectory)
                self.stats['avg_xy_error'] = (self.stats['avg_xy_error'] * (n-1) + xy_error) / n
                self.stats['avg_height_error'] = (self.stats['avg_height_error'] * (n-1) + height_error) / n
                
                # 检查是否需要校正
                if xy_error > 0.5:  # XY误差超过50cm
                    self.stats['xy_corrections'] += 1
                    rospy.logwarn(f"Large XY error detected: {xy_error:.3f} m")
                
                if height_error > 0.3:  # 高度误差超过30cm
                    self.stats['height_corrections'] += 1
                    rospy.logwarn(f"Large height error detected: {height_error:.3f} m")
                
                # 每100次分析打印一次统计信息
                if len(self.slam_trajectory) % 100 == 0:
                    self.print_statistics()
    
    def print_statistics(self):
        """打印统计信息"""
        rospy.loginfo("=== GPS约束效果统计 ===")
        rospy.loginfo(f"轨迹点数: SLAM={len(self.slam_trajectory)}, GPS={len(self.gps_trajectory)}")
        rospy.loginfo(f"最大XY误差: {self.stats['max_xy_error']:.3f} m")
        rospy.loginfo(f"最大高度误差: {self.stats['max_height_error']:.3f} m")
        rospy.loginfo(f"平均XY误差: {self.stats['avg_xy_error']:.3f} m")
        rospy.loginfo(f"平均高度误差: {self.stats['avg_height_error']:.3f} m")
        rospy.loginfo(f"XY校正次数: {self.stats['xy_corrections']}")
        rospy.loginfo(f"高度校正次数: {self.stats['height_corrections']}")
        
        # 评估约束效果
        if self.stats['avg_xy_error'] < 0.2:
            rospy.loginfo("✅ GPS平面约束效果良好")
        elif self.stats['avg_xy_error'] < 0.5:
            rospy.logwarn("⚠️  GPS平面约束效果一般，建议调整参数")
        else:
            rospy.logerr("❌ GPS平面约束效果较差，需要优化")
    
    def generate_optimization_suggestions(self):
        """生成优化建议"""
        suggestions = []
        
        if self.stats['avg_xy_error'] > 0.5:
            suggestions.append("建议增加gps/xy_correction_rate参数（当前建议值：0.1）")
            suggestions.append("建议降低gps/xy_correction_threshold参数（当前建议值：0.3）")
        
        if self.stats['avg_height_error'] > 0.3:
            suggestions.append("建议增加gps/correction_rate参数（当前建议值：0.15）")
        
        if self.stats['xy_corrections'] > len(self.slam_trajectory) * 0.5:
            suggestions.append("GPS信号质量可能不稳定，建议检查RTK状态")
        
        return suggestions
    
    def run(self):
        """运行优化器"""
        rate = rospy.Rate(1)  # 1Hz
        
        while not rospy.is_shutdown():
            # 定期生成优化建议
            if len(self.slam_trajectory) > 500:  # 有足够数据后
                suggestions = self.generate_optimization_suggestions()
                if suggestions:
                    rospy.loginfo("=== GPS约束优化建议 ===")
                    for suggestion in suggestions:
                        rospy.loginfo(f"  • {suggestion}")
            
            rate.sleep()

if __name__ == '__main__':
    try:
        optimizer = GPSConstraintOptimizer()
        optimizer.run()
    except rospy.ROSInterruptException:
        pass
