#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analysis Tool
Extract GPS data from ROS bag files and create 3D visualization
Support different GPS quality color coding and statistical analysis
"""

try:
    import rosbag
    USE_ROSBAG = True
except ImportError:
    try:
        import bagpy
        USE_ROSBAG = False
        print("⚠️  Using bagpy instead of rosbag (Windows compatibility mode)")
    except ImportError:
        print("❌ Error: Need to install rosbag or bagpy")
        print("Please run: pip install bagpy")
        exit(1)

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import sys
import os
from collections import defaultdict
import math
import pandas as pd

class GPS3DTrajectoryAnalyzer:
    def __init__(self):
        self.gps_data = []
        self.trajectory_points = []
        # GPS quality color mapping
        self.quality_colors = {
            -1: 'red',      # NO_FIX: Red
            0:  'green',    # RTK_FIXED: Green (Fixed solution)
            1:  'blue',     # SBAS_FIX: Blue
            2:  'yellow',   # GBAS_FIX: Yellow
            3:  'purple',   # OTHER: Purple
        }
        self.quality_names = {
            -1: "NO_FIX",
            0:  "RTK_FIXED",
            1:  "SBAS_FIX", 
            2:  "GBAS_FIX",
            3:  "OTHER"
        }
        self.line_styles = {
            -1: '--',  # NO_FIX: Dashed
            0:  '-',   # RTK_FIXED: Solid
            1:  '--',  # SBAS_FIX: Dashed
            2:  '--',  # GBAS_FIX: Dashed
            3:  '--',  # OTHER: Dashed
        }
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
    def extract_gps_from_bag(self, bag_file, topic_name="/rtk/gnss"):
        """Extract GPS data from bag file"""
        print(f"🔍 Extracting GPS data from {bag_file}...")
        
        if USE_ROSBAG:
            return self._extract_with_rosbag(bag_file, topic_name)
        else:
            return self._extract_with_bagpy(bag_file, topic_name)
    
    def _extract_with_rosbag(self, bag_file, topic_name):
        """Extract GPS data using rosbag"""
        try:
            bag = rosbag.Bag(bag_file, 'r')
            gps_count = 0
            
            for topic, msg, t in bag.read_messages(topics=[topic_name]):
                gps_info = {
                    'timestamp': t.to_sec(),
                    'latitude': msg.latitude,
                    'longitude': msg.longitude,
                    'altitude': msg.altitude,
                    'status': msg.status.status,
                    'service': msg.status.service,
                }
                self.gps_data.append(gps_info)
                gps_count += 1
                
                if gps_count % 500 == 0:
                    print(f"  Extracted {gps_count} GPS points...")
                    
            bag.close()
            print(f"✅ GPS data extraction completed, total {len(self.gps_data)} points")
            
        except Exception as e:
            print(f"❌ Failed to read bag file: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def _extract_with_bagpy(self, bag_file, topic_name):
        """Extract GPS data using bagpy"""
        try:
            # Read bag file using bagpy
            bag = bagpy.bagreader(bag_file)
            
            # Read GPS data
            print(f"📡 Reading topic: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            
            # Read CSV file
            df = pd.read_csv(gps_csv)
            
            gps_count = 0
            for index, row in df.iterrows():
                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1
                    
                    if gps_count % 500 == 0:
                        print(f"  Extracted {gps_count} GPS points...")
                        
                except (ValueError, KeyError) as e:
                    continue
                    
            print(f"✅ GPS data extraction completed, total {len(self.gps_data)} points")
            
        except Exception as e:
            print(f"❌ Failed to read bag file: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def convert_to_local_coordinates(self):
        """Convert GPS coordinates to local coordinate system"""
        if not self.gps_data:
            return
            
        # Use first valid GPS point as origin
        for gps in self.gps_data:
            if gps['status'] >= 0:  # Valid GPS
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude'] 
                self.origin_alt = gps['altitude']
                break
                
        if self.origin_lat is None:
            # Use first point as origin
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return
                
        print(f"📍 GPS origin set to: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}, alt={self.origin_alt:.3f}")
        
        # Convert to local coordinates
        for gps in self.gps_data:
            # Simplified coordinate conversion (suitable for small areas)
            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt
            
            # Convert to meters (simplified formula for small areas)
            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff
            
            point_info = {
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)
    
    def analyze_gps_quality(self):
        """Analyze GPS quality statistics"""
        if not self.gps_data:
            return {}
            
        status_count = defaultdict(int)
        total_points = len(self.gps_data)
        
        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3  # Other status
            status_count[status] += 1
            
        print("\n" + "="*50)
        print("📊 GPS Quality Analysis")
        print("="*50)
        print(f"Total GPS points: {total_points:,}")
        print("-" * 40)
        
        for status, count in sorted(status_count.items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "UNKNOWN")
            color = self.quality_colors.get(status, 'gray')
            line_style = self.line_styles.get(status, '--')
            style_desc = "Solid" if line_style == '-' else "Dashed"
            print(f"{quality_name:12}: {count:6,d} points ({percentage:5.1f}%) - {color} {style_desc}")
            
        # Calculate trajectory statistics
        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])
            trajectory_length = self.calculate_trajectory_length()
            
            print("\n" + "="*50)
            print("📏 Trajectory Statistics")
            print("="*50)
            print(f"Total trajectory length: {trajectory_length:.1f} meters")
            print(f"X range: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} meters")
            print(f"Y range: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} meters") 
            print(f"Z range: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} meters")
            
            # Calculate start-end distance
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                print(f"Start-end distance: {start_end_distance:.1f} meters")
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0
                print(f"Closure error: {closure_error:.2f}%")
            
        return status_count
    
    def calculate_trajectory_length(self):
        """Calculate total trajectory length"""
        if len(self.trajectory_points) < 2:
            return 0.0
            
        total_length = 0.0
        for i in range(1, len(self.trajectory_points)):
            p1 = np.array(self.trajectory_points[i-1]['position'])
            p2 = np.array(self.trajectory_points[i]['position'])
            total_length += np.linalg.norm(p2 - p1)
            
        return total_length

    def create_3d_visualization(self):
        """Create 3D trajectory visualization"""
        if not self.trajectory_points:
            print("❌ No trajectory points to visualize")
            return False

        print("🎨 Creating 3D visualization...")

        # Create 3D plot
        fig = plt.figure(figsize=(16, 12))

        # Main 3D plot
        ax1 = fig.add_subplot(221, projection='3d')

        # Group by GPS status
        status_groups = defaultdict(list)
        for i, point in enumerate(self.trajectory_points):
            status = point['status']
            if status not in self.quality_names:
                status = 3
            status_groups[status].append(i)

        # Plot 3D trajectory
        for status, indices in status_groups.items():
            if len(indices) < 2:
                continue

            points = [self.trajectory_points[i]['position'] for i in indices]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            z_coords = [p[2] for p in points]

            color = self.quality_colors[status]
            quality_name = self.quality_names[status]
            line_style = self.line_styles[status]

            ax1.plot(x_coords, y_coords, z_coords,
                    color=color, linewidth=2, linestyle=line_style,
                    label=f'{quality_name} ({len(indices)} pts)', alpha=0.8)

        # Mark start and end points
        if self.trajectory_points:
            start_pos = self.trajectory_points[0]['position']
            end_pos = self.trajectory_points[-1]['position']

            ax1.scatter(start_pos[0], start_pos[1], start_pos[2],
                       c='green', s=100, marker='o', label='Start Point')
            ax1.scatter(end_pos[0], end_pos[1], end_pos[2],
                       c='red', s=100, marker='s', label='End Point')

        ax1.set_xlabel('X (meters)')
        ax1.set_ylabel('Y (meters)')
        ax1.set_zlabel('Z (meters)')
        ax1.set_title('GPS 3D Trajectory')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # XY plane view
        ax2 = fig.add_subplot(222)
        for status, indices in status_groups.items():
            if len(indices) < 2:
                continue

            points = [self.trajectory_points[i]['position'] for i in indices]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]

            color = self.quality_colors[status]
            quality_name = self.quality_names[status]
            line_style = self.line_styles[status]

            ax2.plot(x_coords, y_coords, color=color, linewidth=2,
                    linestyle=line_style, label=quality_name, alpha=0.8)

        if self.trajectory_points:
            start_pos = self.trajectory_points[0]['position']
            end_pos = self.trajectory_points[-1]['position']
            ax2.plot(start_pos[0], start_pos[1], 'go', markersize=8, label='Start')
            ax2.plot(end_pos[0], end_pos[1], 'rs', markersize=8, label='End')

        ax2.set_xlabel('X (meters)')
        ax2.set_ylabel('Y (meters)')
        ax2.set_title('GPS Trajectory - Top View (XY Plane)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.axis('equal')

        # Height profile
        ax3 = fig.add_subplot(223)
        if self.trajectory_points:
            distances = [0]
            heights = [self.trajectory_points[0]['position'][2]]

            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                dist = np.linalg.norm(p2[:2] - p1[:2])  # Only XY distance
                distances.append(distances[-1] + dist)
                heights.append(p2[2])

            # Color by status
            for status, indices in status_groups.items():
                if len(indices) < 2:
                    continue

                status_distances = [distances[i] for i in indices]
                status_heights = [heights[i] for i in indices]

                color = self.quality_colors[status]
                quality_name = self.quality_names[status]
                line_style = self.line_styles[status]

                ax3.plot(status_distances, status_heights, color=color,
                        linewidth=2, linestyle=line_style, label=quality_name, alpha=0.8)

        ax3.set_xlabel('Distance (meters)')
        ax3.set_ylabel('Height (meters)')
        ax3.set_title('GPS Trajectory - Height Profile')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # Quality distribution pie chart
        ax4 = fig.add_subplot(224)
        if status_groups:
            labels = []
            sizes = []
            colors = []

            for status, indices in status_groups.items():
                quality_name = self.quality_names[status]
                labels.append(f'{quality_name}\n({len(indices)} pts)')
                sizes.append(len(indices))
                colors.append(self.quality_colors[status])

            ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax4.set_title('GPS Quality Distribution')

        plt.tight_layout()
        return True

    def run_analysis(self, bag_file, topic_name="/rtk/gnss"):
        """Run complete GPS analysis"""
        print("🚀 GPS 3D Trajectory Analysis Tool")
        print("="*60)

        # Check if file exists
        if not os.path.exists(bag_file):
            print(f"❌ Error: bag file does not exist: {bag_file}")
            return False

        # Extract GPS data
        if not self.extract_gps_from_bag(bag_file, topic_name):
            print("❌ GPS data extraction failed")
            return False

        # Convert coordinates
        self.convert_to_local_coordinates()

        # Analyze quality
        quality_stats = self.analyze_gps_quality()

        # Create visualization
        if self.create_3d_visualization():
            print("\n🖥️  3D visualization window opened")
            print("💡 Operation tips:")
            print("   - Close window to exit program")
            print("   - You can zoom and pan the images")
            print("   - 3D plot can be rotated by dragging")

            # Print legend
            print("\n" + "="*50)
            print("🎨 Visualization Legend")
            print("="*50)

            for status, count in sorted(quality_stats.items()):
                if count > 0:
                    quality_name = self.quality_names[status]
                    color = self.quality_colors[status]
                    line_style = self.line_styles[status]
                    style_desc = "Solid line" if line_style == '-' else "Dashed line"
                    print(f"{quality_name:12}: {color} {style_desc}")

            print("\n🔵 Marker descriptions:")
            print("   Green circle: Trajectory start point")
            print("   Red square: Trajectory end point")
            print("\n📝 Quality descriptions:")
            print("   RTK_FIXED: RTK fixed solution, highest accuracy (Green solid)")
            print("   SBAS_FIX:  SBAS differential positioning (Blue dashed)")
            print("   GBAS_FIX:  GBAS differential positioning (Yellow dashed)")
            print("   NO_FIX:    No positioning solution (Red dashed)")

            plt.show()
        else:
            print("❌ Visualization creation failed")
            return False

        print("\n✅ GPS trajectory analysis completed")
        return True

def main():
    parser = argparse.ArgumentParser(
        description='GPS 3D Trajectory Analysis Tool - Analyze GPS trajectory quality from ROS bag files',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Usage examples:
  python3 gps_3d_analyzer.py data.bag
  python3 gps_3d_analyzer.py data.bag --topic /gps/fix

Supported GPS status:
  - RTK_FIXED (0): RTK fixed solution, highest accuracy
  - SBAS_FIX (1):  SBAS differential positioning
  - GBAS_FIX (2):  GBAS differential positioning
  - NO_FIX (-1):   No positioning solution
        """
    )
    parser.add_argument('bag_file', help='ROS bag file path')
    parser.add_argument('--topic', default='/rtk/gnss',
                       help='GPS topic name (default: /rtk/gnss)')

    args = parser.parse_args()

    # Run analysis
    analyzer = GPS3DTrajectoryAnalyzer()
    success = analyzer.run_analysis(args.bag_file, args.topic)

    if not success:
        print("❌ GPS trajectory analysis failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
