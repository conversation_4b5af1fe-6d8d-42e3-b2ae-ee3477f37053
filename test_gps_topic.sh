#!/bin/bash

# GPS Topic测试脚本

echo "=========================================="
echo "GPS Topic 测试脚本"
echo "=========================================="

# 检查bag文件中的GPS topic
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag_file_path>"
    echo "例如: $0 ~/datasets/UM982loop_715std_maximum_synced.bag"
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ Bag文件不存在: $BAG_FILE"
    exit 1
fi

echo "1. 分析bag文件中的GPS相关topic..."
echo "Bag文件: $BAG_FILE"
echo ""

# 查找GPS相关的topic
echo "GPS相关的topic:"
rosbag info "$BAG_FILE" | grep -E "(rtk|gps|gnss|fix|navsat)" -i

echo ""
echo "2. 详细的topic信息:"
rosbag info "$BAG_FILE" | grep -A 20 "topics:"

echo ""
echo "3. 建议的配置:"
if rosbag info "$BAG_FILE" | grep -q "/rtk/gnss"; then
    echo "✅ 找到 /rtk/gnss topic"
    echo "   配置文件已正确设置为: /rtk/gnss"
elif rosbag info "$BAG_FILE" | grep -q "/gps/fix"; then
    echo "⚠️  找到 /gps/fix topic"
    echo "   需要修改配置文件中的gps_topic为: /gps/fix"
elif rosbag info "$BAG_FILE" | grep -q "/ublox_gps/fix"; then
    echo "⚠️  找到 /ublox_gps/fix topic"
    echo "   需要修改配置文件中的gps_topic为: /ublox_gps/fix"
else
    echo "❌ 未找到标准的GPS topic"
    echo "   请检查bag文件中的实际topic名称"
fi

echo ""
echo "4. 测试GPS数据 (如果roscore正在运行):"
echo "   rosbag play $BAG_FILE --pause &"
echo "   rostopic echo /rtk/gnss -n 1"
echo "   rostopic hz /rtk/gnss"

echo ""
echo "=========================================="
