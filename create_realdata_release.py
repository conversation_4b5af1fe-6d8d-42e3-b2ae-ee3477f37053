#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create real data processing release package
"""

import os
import shutil
import time

def create_realdata_release():
    """创建真实数据处理版发布包"""
    print("📦 创建真实数据处理版发布包...")
    
    release_dir = "GPS_3D_Analyzer_RealData_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制真实数据处理版exe文件
    exe_path = "dist/GPS_3D_Analyzer_RealData.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        
        # 获取文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ 真实数据处理版exe文件复制完成 ({size_mb:.1f} MB)")
    else:
        print("❌ 真实数据处理版exe文件不存在")
        return False
    
    # 创建详细说明文件
    readme_content = f"""# GPS 3D 轨迹分析器 - 真实数据处理版

## 🎉 完全支持真实数据处理！

本版本彻底解决了只能处理演示数据的问题，现在可以完整处理真实的ROS bag文件和其他格式的GPS数据。

### ✅ 新增功能
- **多格式支持**: 支持ROS bag、CSV、TXT等多种格式
- **智能数据检测**: 自动识别数据格式和GPS字段
- **rosbag直接读取**: 即使bagpy不可用也能读取bag文件
- **CSV智能解析**: 自动识别GPS坐标列
- **大数据处理**: 支持处理大量GPS数据点
- **进度显示**: 实时显示数据处理进度

### 🎯 支持的数据格式

#### ROS Bag文件 (.bag)
- **优先方式**: 使用bagpy库（如果可用）
- **备用方式**: 直接使用rosbag库读取
- **话题支持**: 支持自定义GPS话题名称
- **消息类型**: 支持标准GPS消息格式

#### CSV文件 (.csv)
- **智能列检测**: 自动识别纬度、经度、高度列
- **编码支持**: 支持UTF-8、GBK、Latin-1编码
- **灵活格式**: 支持各种CSV格式的GPS数据

#### 文本文件 (.txt)
- **分隔符支持**: 支持逗号、制表符等分隔符
- **自动解析**: 智能识别GPS数据格式

### 🚀 使用方法

#### 处理ROS Bag文件
1. 双击 GPS_3D_Analyzer_RealData.exe 启动程序
2. 点击"浏览"选择ROS bag文件
3. 设置GPS话题（默认: /rtk/gnss）
4. 选择输出文件位置
5. 点击"开始分析"
6. 程序会自动选择最佳读取方式

#### 处理CSV/TXT文件
1. 启动程序后点击"浏览"
2. 选择CSV或TXT格式的GPS数据文件
3. 程序会自动识别GPS坐标列
4. 设置输出文件位置
5. 点击"开始分析"

#### 演示模式
1. 如果没有数据文件，可以使用"演示数据"按钮
2. 或者在开始分析时选择使用演示数据

### 🔧 智能处理流程

#### 数据读取优先级
1. **Bagpy方式**: 如果bagpy可用且文件是.bag格式
2. **Rosbag方式**: 如果是.bag文件但bagpy不可用
3. **CSV方式**: 如果是.csv或.txt文件
4. **演示数据**: 如果所有方式都失败

#### 自动错误恢复
- 如果一种方式失败，自动尝试下一种方式
- 详细的错误日志和处理进度显示
- 智能的数据格式检测和转换

### 📊 处理能力

#### 大数据支持
- **数据量**: 支持处理数万个GPS点
- **内存优化**: 高效的内存使用和垃圾回收
- **进度显示**: 每100个点显示一次进度
- **中断支持**: 可以随时停止处理

#### 数据质量
- **GPS状态识别**: 自动识别RTK、SBAS等状态
- **坐标转换**: 精确的GPS到本地坐标转换
- **质量分析**: 详细的GPS质量统计
- **轨迹平滑**: 智能的轨迹处理算法

### 🎨 界面特色
- **深灰色专业主题** (#2E2E2E)
- **深绿色文字显示** (#00C851)
- **深色选择背景** (#404040)
- **实时处理日志** - 显示详细处理过程
- **智能状态提示** - 根据数据源显示不同状态
- **多格式文件选择** - 支持多种文件类型

### 🎯 GPS质量颜色编码
- 🟢 **青绿色实线**: RTK固定解（最高精度）
- 🔵 **蓝色虚线**: SBAS定位
- 🟠 **橙色虚线**: GBAS定位
- 🔴 **红色虚线**: 无定位解
- 🟣 **紫色虚线**: 其他状态

### 🎮 3D控制功能
- **鼠标拖拽**: 旋转3D视角
- **鼠标滚轮**: 缩放视图
- **视角按钮**: 俯视、侧视、正视、复位
- **缩放控制**: 放大、缩小、适应视图
- **流畅交互**: 优化的3D渲染性能

### 💻 系统要求
- **操作系统**: Windows 10 64位
- **内存**: 最小4GB内存（推荐8GB用于大数据）
- **显卡**: 支持OpenGL的显卡
- **磁盘空间**: 100MB可用磁盘空间
- **依赖**: 无需额外安装，开箱即用

### 🔍 故障排除

#### 数据文件问题
- **Bag文件**: 确保文件完整且包含GPS话题
- **CSV文件**: 确保包含纬度、经度列
- **编码问题**: 程序会自动尝试多种编码

#### 处理性能
- **大文件**: 处理大文件时请耐心等待
- **内存不足**: 关闭其他程序释放内存
- **进度停滞**: 检查文件是否损坏

#### 显示问题
- **3D不显示**: 更新显卡驱动
- **界面异常**: 确保系统支持OpenGL

### 📝 版本信息
- **版本**: 1.2.0 (真实数据处理版)
- **构建日期**: {time.strftime("%Y-%m-%d")}
- **平台**: Windows 10 64位
- **文件大小**: {size_mb:.1f} MB
- **数据支持**: ROS Bag + CSV + TXT + 演示数据

### 🎉 特别说明

#### 完全解决的问题
- ✅ **真实数据处理**: 不再只是演示数据
- ✅ **多格式支持**: 支持各种GPS数据格式
- ✅ **智能错误恢复**: 一种方式失败自动尝试其他方式
- ✅ **大数据处理**: 支持处理大量GPS数据点
- ✅ **进度显示**: 实时显示处理进度

#### 数据处理流程
1. **文件检测**: 自动识别文件格式
2. **方式选择**: 选择最佳读取方式
3. **数据提取**: 提取GPS坐标和状态信息
4. **坐标转换**: 转换为本地坐标系
5. **质量分析**: 分析GPS质量分布
6. **3D可视化**: 生成3D轨迹图
7. **结果导出**: 保存轨迹数据

### 🚀 立即开始
双击 GPS_3D_Analyzer_RealData.exe 即可开始处理真实的GPS数据！

### 📞 技术支持
如果遇到数据处理问题，请提供：
- 数据文件格式和大小
- 错误信息截图
- 系统配置信息

---
© 2024 GPS Analyzer Team. All rights reserved.
专业的GPS轨迹分析工具，真正支持真实数据处理！
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 真实数据处理版发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("🎉 GPS 3D轨迹分析器 - 真实数据处理版发布包创建")
    print("=" * 60)
    
    if create_realdata_release():
        print("\n" + "=" * 60)
        print("🎉 真实数据处理版发布包创建成功!")
        print("=" * 60)
        print("📁 发布包: GPS_3D_Analyzer_RealData_Release/")
        print("🚀 exe文件: GPS_3D_Analyzer_RealData.exe")
        print("\n✅ 现在可以处理真实的GPS数据了！")
        print("✅ 支持ROS Bag、CSV、TXT等多种格式")
        print("✅ 智能数据检测和错误恢复")
        print("✅ 大数据处理能力")
        print("✅ 深色主题 + 绿色文字完美保留")
    else:
        print("❌ 发布包创建失败")

if __name__ == "__main__":
    main()
