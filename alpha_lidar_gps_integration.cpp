/**
 * αLiDAR系统GPS集成方案
 * 专门解决首尾高度差问题的GPS融合实现
 */

#include <ros/ros.h>
#include <sensor_msgs/NavSatFix.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <nav_msgs/Odometry.h>
#include <tf/transform_datatypes.h>
#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/LocalCartesian.hpp>
#include <deque>
#include <mutex>

class AlphaLidarGPSIntegration {
private:
    // GPS数据缓冲区
    std::deque<sensor_msgs::NavSatFix> gps_buffer_;
    std::mutex gps_mutex_;
    
    // 坐标转换器
    std::unique_ptr<GeographicLib::LocalCartesian> local_cartesian_;
    bool is_origin_set_ = false;
    
    // GPS状态监控
    struct GPSStatus {
        bool is_rtk_fixed = false;
        double last_update_time = 0.0;
        int consecutive_good_fixes = 0;
        Eigen::Vector3d last_position;
        double position_std = 1.0;
    } gps_status_;
    
    // 配置参数
    struct Config {
        double gps_timeout = 1.0;           // GPS超时时间(秒)
        double max_gps_jump = 5.0;          // 最大GPS跳跃距离(米)
        double height_correction_threshold = 0.5;  // 高度校正阈值(米)
        double rtk_position_std = 0.02;     // RTK位置标准差(米)
        double gps_position_std = 1.0;      // 普通GPS位置标准差(米)
        int min_good_fixes = 5;             // 最少连续好的定位数量
        bool enable_height_correction = true;
        bool enable_loop_closure_gps = true;
    } config_;
    
    // 起始位置记录
    Eigen::Vector3d start_gps_position_;
    bool start_position_set_ = false;
    
public:
    AlphaLidarGPSIntegration() {
        local_cartesian_ = std::make_unique<GeographicLib::LocalCartesian>();
    }
    
    /**
     * GPS回调函数
     */
    void gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
        std::lock_guard<std::mutex> lock(gps_mutex_);
        
        // 检查GPS状态
        updateGPSStatus(*gps_msg);
        
        // 添加到缓冲区
        gps_buffer_.push_back(*gps_msg);
        
        // 保持缓冲区大小
        while (gps_buffer_.size() > 1000) {
            gps_buffer_.pop_front();
        }
        
        // 设置坐标原点
        if (!is_origin_set_ && isGPSValid(*gps_msg)) {
            setCoordinateOrigin(*gps_msg);
        }
        
        // 记录起始位置
        if (!start_position_set_ && gps_status_.consecutive_good_fixes > config_.min_good_fixes) {
            start_gps_position_ = convertToLocalCartesian(*gps_msg);
            start_position_set_ = true;
            ROS_INFO("GPS起始位置已设置: [%.3f, %.3f, %.3f]", 
                    start_gps_position_.x(), start_gps_position_.y(), start_gps_position_.z());
        }
    }
    
    /**
     * 获取GPS约束用于状态估计
     */
    bool getGPSConstraint(double timestamp, Eigen::Vector3d& gps_position, 
                         Eigen::Matrix3d& gps_covariance) {
        std::lock_guard<std::mutex> lock(gps_mutex_);
        
        // 查找时间戳最近的GPS数据
        auto closest_gps = findClosestGPS(timestamp);
        if (!closest_gps || !isGPSValid(*closest_gps)) {
            return false;
        }
        
        // 转换到局部坐标系
        gps_position = convertToLocalCartesian(*closest_gps);
        
        // 设置协方差矩阵
        double pos_std = gps_status_.is_rtk_fixed ? 
                        config_.rtk_position_std : config_.gps_position_std;
        
        gps_covariance = Eigen::Matrix3d::Identity() * (pos_std * pos_std);
        
        // 如果有协方差信息，使用GPS提供的值
        if (closest_gps->position_covariance_type != 0) {
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    gps_covariance(i, j) = closest_gps->position_covariance[i*3 + j];
                }
            }
        }
        
        return true;
    }
    
    /**
     * 高度漂移校正
     */
    Eigen::Vector3d correctHeightDrift(const Eigen::Vector3d& slam_position, 
                                      double timestamp) {
        if (!config_.enable_height_correction || !start_position_set_) {
            return slam_position;
        }
        
        Eigen::Vector3d gps_position;
        Eigen::Matrix3d gps_covariance;
        
        if (!getGPSConstraint(timestamp, gps_position, gps_covariance)) {
            return slam_position;
        }
        
        // 计算高度差
        double height_drift = gps_position.z() - slam_position.z();
        
        // 如果高度漂移超过阈值，进行校正
        if (std::abs(height_drift) > config_.height_correction_threshold) {
            Eigen::Vector3d corrected_position = slam_position;
            
            // 渐进式校正，避免突跳
            double correction_factor = 0.1;  // 10%的校正率
            corrected_position.z() += height_drift * correction_factor;
            
            ROS_INFO("高度漂移校正: 漂移=%.3fm, 校正后=%.3fm", 
                    height_drift, corrected_position.z());
            
            return corrected_position;
        }
        
        return slam_position;
    }
    
    /**
     * 回环检测GPS约束
     */
    bool detectGPSLoopClosure(const Eigen::Vector3d& current_slam_position,
                             double timestamp,
                             Eigen::Vector3d& loop_constraint) {
        if (!config_.enable_loop_closure_gps || !start_position_set_) {
            return false;
        }
        
        Eigen::Vector3d current_gps_position;
        Eigen::Matrix3d gps_covariance;
        
        if (!getGPSConstraint(timestamp, current_gps_position, gps_covariance)) {
            return false;
        }
        
        // 检查是否接近起始位置
        double distance_to_start = (current_gps_position - start_gps_position_).norm();
        
        if (distance_to_start < 2.0) {  // 2米范围内认为是回环
            // 计算回环约束
            Eigen::Vector3d slam_drift = current_slam_position - 
                                        (start_gps_position_ - start_gps_position_);  // 相对起点
            Eigen::Vector3d gps_drift = current_gps_position - start_gps_position_;
            
            loop_constraint = gps_drift - slam_drift;
            
            ROS_INFO("检测到GPS回环: 距离起点%.3fm, 约束向量[%.3f,%.3f,%.3f]",
                    distance_to_start, loop_constraint.x(), 
                    loop_constraint.y(), loop_constraint.z());
            
            return true;
        }
        
        return false;
    }
    
private:
    void updateGPSStatus(const sensor_msgs::NavSatFix& gps_msg) {
        gps_status_.last_update_time = gps_msg.header.stamp.toSec();
        
        // 检查RTK状态
        if (gps_msg.status.status == sensor_msgs::NavSatStatus::STATUS_GBAS_FIX) {
            gps_status_.is_rtk_fixed = true;
            gps_status_.consecutive_good_fixes++;
        } else if (gps_msg.status.status == sensor_msgs::NavSatStatus::STATUS_FIX) {
            gps_status_.is_rtk_fixed = false;
            gps_status_.consecutive_good_fixes++;
        } else {
            gps_status_.consecutive_good_fixes = 0;
        }
        
        // 更新位置标准差
        if (gps_msg.position_covariance_type != 0) {
            gps_status_.position_std = std::sqrt(gps_msg.position_covariance[0]);
        }
    }
    
    bool isGPSValid(const sensor_msgs::NavSatFix& gps_msg) {
        return gps_msg.status.status >= sensor_msgs::NavSatStatus::STATUS_FIX &&
               gps_msg.latitude != 0.0 && gps_msg.longitude != 0.0;
    }
    
    void setCoordinateOrigin(const sensor_msgs::NavSatFix& gps_msg) {
        local_cartesian_->Reset(gps_msg.latitude, gps_msg.longitude, gps_msg.altitude);
        is_origin_set_ = true;
        ROS_INFO("GPS坐标原点已设置: lat=%.8f, lon=%.8f, alt=%.3f",
                gps_msg.latitude, gps_msg.longitude, gps_msg.altitude);
    }
    
    Eigen::Vector3d convertToLocalCartesian(const sensor_msgs::NavSatFix& gps_msg) {
        if (!is_origin_set_) return Eigen::Vector3d::Zero();
        
        double x, y, z;
        local_cartesian_->Forward(gps_msg.latitude, gps_msg.longitude, 
                                 gps_msg.altitude, x, y, z);
        return Eigen::Vector3d(x, y, z);
    }
    
    sensor_msgs::NavSatFix* findClosestGPS(double timestamp) {
        if (gps_buffer_.empty()) return nullptr;
        
        double min_time_diff = std::numeric_limits<double>::max();
        sensor_msgs::NavSatFix* closest = nullptr;
        
        for (auto& gps_msg : gps_buffer_) {
            double time_diff = std::abs(gps_msg.header.stamp.toSec() - timestamp);
            if (time_diff < min_time_diff && time_diff < config_.gps_timeout) {
                min_time_diff = time_diff;
                closest = &gps_msg;
            }
        }
        
        return closest;
    }
};

/**
 * 在αLiDAR主循环中集成GPS的示例
 */
void integrateGPSIntoAlphaLidar(AlphaLidarGPSIntegration& gps_processor,
                               esekfom::esekf<state_ikfom, 12, input_ikfom>& kf,
                               double timestamp) {
    
    // 1. 获取当前SLAM位置
    state_ikfom current_state = kf.get_x();
    Eigen::Vector3d slam_position = current_state.pos;
    
    // 2. 高度漂移校正
    Eigen::Vector3d corrected_position = gps_processor.correctHeightDrift(slam_position, timestamp);
    if ((corrected_position - slam_position).norm() > 0.01) {
        // 应用校正
        current_state.pos = corrected_position;
        kf.change_x(current_state);
    }
    
    // 3. 回环检测和约束
    Eigen::Vector3d loop_constraint;
    if (gps_processor.detectGPSLoopClosure(slam_position, timestamp, loop_constraint)) {
        // 添加回环约束到优化中
        // 这里需要根据具体的优化框架实现
        ROS_INFO("应用GPS回环约束");
    }
    
    // 4. 直接GPS约束（可选）
    Eigen::Vector3d gps_position;
    Eigen::Matrix3d gps_covariance;
    if (gps_processor.getGPSConstraint(timestamp, gps_position, gps_covariance)) {
        // 计算残差
        Eigen::Vector3d residual = gps_position - slam_position;
        
        // 如果残差不太大，可以直接融合
        if (residual.norm() < 2.0) {
            // 这里需要扩展卡尔曼滤波器以支持GPS观测
            // 或者使用加权平均等简单融合方法
        }
    }
}
