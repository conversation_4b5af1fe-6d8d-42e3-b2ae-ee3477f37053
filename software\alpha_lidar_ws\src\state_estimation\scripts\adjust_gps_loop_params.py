#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS回环检测参数动态调整工具
可以在运行时动态调整参数，无需重启系统
"""

import rospy
import yaml
import sys
import os
from std_msgs.msg import String

class GPSLoopParamAdjuster:
    def __init__(self):
        rospy.init_node('gps_loop_param_adjuster', anonymous=True)
        
        # 参数命名空间
        self.gps_optimizer_ns = '/enhanced_gps_loop_closure_optimizer'
        self.slam_integration_ns = '/enhanced_slam_loop_closure_integration'
        
        print("GPS回环检测参数动态调整工具")
        print("=" * 50)
        
    def show_current_params(self):
        """显示当前参数"""
        print("\n当前GPS回环检测参数:")
        print("-" * 30)
        
        # GPS优化器参数
        print("GPS回环检测优化器:")
        gps_params = [
            'loop_closure_distance_threshold',
            'intermediate_loop_threshold', 
            'min_trajectory_length',
            'gps_quality_threshold',
            'check_interval',
            'revisit_threshold'
        ]
        
        for param in gps_params:
            try:
                value = rospy.get_param(f"{self.gps_optimizer_ns}/{param}")
                print(f"  {param}: {value}")
            except KeyError:
                print(f"  {param}: 未设置")
        
        print("\nSLAM回环检测集成:")
        slam_params = [
            'force_search_radius',
            'intermediate_search_radius',
            'start_end_score_threshold',
            'intermediate_score_threshold',
            'voxel_leaf_size',
            'max_search_candidates'
        ]
        
        for param in slam_params:
            try:
                value = rospy.get_param(f"{self.slam_integration_ns}/{param}")
                print(f"  {param}: {value}")
            except KeyError:
                print(f"  {param}: 未设置")
    
    def set_param(self, namespace, param_name, value):
        """设置参数"""
        full_param_name = f"{namespace}/{param_name}"
        try:
            # 尝试转换为适当的类型
            if '.' in str(value):
                value = float(value)
            elif str(value).isdigit() or (str(value).startswith('-') and str(value)[1:].isdigit()):
                value = int(value)
            
            rospy.set_param(full_param_name, value)
            print(f"✅ 参数设置成功: {full_param_name} = {value}")
            return True
        except Exception as e:
            print(f"❌ 参数设置失败: {e}")
            return False
    
    def load_preset(self, preset_name):
        """加载预设配置"""
        config_file = os.path.join(
            os.path.dirname(__file__), 
            '../config/gps_loop_closure_params.yaml'
        )
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if preset_name not in config:
                print(f"❌ 预设 '{preset_name}' 不存在")
                return False
            
            preset_config = config[preset_name]
            
            # 设置GPS优化器参数
            if 'enhanced_gps_loop_closure_optimizer' in preset_config:
                gps_params = preset_config['enhanced_gps_loop_closure_optimizer']
                for param, value in gps_params.items():
                    self.set_param(self.gps_optimizer_ns, param, value)
            
            # 设置SLAM集成参数
            if 'enhanced_slam_loop_closure_integration' in preset_config:
                slam_params = preset_config['enhanced_slam_loop_closure_integration']
                for param, value in slam_params.items():
                    self.set_param(self.slam_integration_ns, param, value)
            
            print(f"✅ 预设 '{preset_name}' 加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 加载预设失败: {e}")
            return False
    
    def interactive_mode(self):
        """交互式参数调整模式"""
        while True:
            print("\n" + "=" * 50)
            print("GPS回环检测参数调整菜单:")
            print("1. 显示当前参数")
            print("2. 设置GPS优化器参数")
            print("3. 设置SLAM集成参数")
            print("4. 加载预设配置")
            print("5. 快速调整常用参数")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("退出参数调整工具")
                break
            elif choice == '1':
                self.show_current_params()
            elif choice == '2':
                self.adjust_gps_params()
            elif choice == '3':
                self.adjust_slam_params()
            elif choice == '4':
                self.load_preset_interactive()
            elif choice == '5':
                self.quick_adjust()
            else:
                print("无效选择，请重试")
    
    def adjust_gps_params(self):
        """调整GPS优化器参数"""
        print("\nGPS优化器参数调整:")
        params = {
            '1': ('loop_closure_distance_threshold', '起点-终点回环距离阈值(米)'),
            '2': ('intermediate_loop_threshold', '中间回环距离阈值(米)'),
            '3': ('min_trajectory_length', '最小轨迹长度(米)'),
            '4': ('gps_quality_threshold', 'GPS质量阈值'),
            '5': ('check_interval', '检查间隔(秒)'),
            '6': ('revisit_threshold', '重访距离阈值(米)')
        }
        
        for key, (param, desc) in params.items():
            print(f"{key}. {desc} ({param})")
        
        choice = input("选择要调整的参数 (1-6): ").strip()
        if choice in params:
            param_name, desc = params[choice]
            try:
                current_value = rospy.get_param(f"{self.gps_optimizer_ns}/{param_name}")
                print(f"当前值: {current_value}")
            except:
                print("当前值: 未设置")
            
            new_value = input(f"输入新值: ").strip()
            if new_value:
                self.set_param(self.gps_optimizer_ns, param_name, new_value)
    
    def adjust_slam_params(self):
        """调整SLAM集成参数"""
        print("\nSLAM集成参数调整:")
        params = {
            '1': ('force_search_radius', '基础搜索半径(米)'),
            '2': ('intermediate_search_radius', '中间回环搜索半径(米)'),
            '3': ('start_end_score_threshold', '起点-终点匹配阈值'),
            '4': ('intermediate_score_threshold', '中间回环匹配阈值'),
            '5': ('voxel_leaf_size', '体素滤波大小(米)'),
            '6': ('max_search_candidates', '最大搜索候选数')
        }
        
        for key, (param, desc) in params.items():
            print(f"{key}. {desc} ({param})")
        
        choice = input("选择要调整的参数 (1-6): ").strip()
        if choice in params:
            param_name, desc = params[choice]
            try:
                current_value = rospy.get_param(f"{self.slam_integration_ns}/{param_name}")
                print(f"当前值: {current_value}")
            except:
                print("当前值: 未设置")
            
            new_value = input(f"输入新值: ").strip()
            if new_value:
                self.set_param(self.slam_integration_ns, param_name, new_value)
    
    def load_preset_interactive(self):
        """交互式加载预设"""
        print("\n可用预设:")
        presets = {
            '1': ('poor_gps_preset', 'GPS质量差场景'),
            '2': ('good_gps_preset', 'GPS质量好场景'),
            '3': ('high_performance_preset', '高性能计算场景'),
            '4': ('low_performance_preset', '低性能计算场景')
        }
        
        for key, (preset, desc) in presets.items():
            print(f"{key}. {desc} ({preset})")
        
        choice = input("选择预设 (1-4): ").strip()
        if choice in presets:
            preset_name, desc = presets[choice]
            print(f"加载预设: {desc}")
            self.load_preset(preset_name)
    
    def quick_adjust(self):
        """快速调整常用参数"""
        print("\n快速调整 - 适合您的GPS status=-1场景:")
        print("1. 放宽所有阈值 (适合GPS质量很差)")
        print("2. 中等阈值 (适合GPS质量一般)")
        print("3. 严格阈值 (适合GPS质量好)")
        
        choice = input("选择调整方案 (1-3): ").strip()
        
        if choice == '1':
            # 放宽阈值
            params = [
                (self.gps_optimizer_ns, 'loop_closure_distance_threshold', 12.0),
                (self.gps_optimizer_ns, 'intermediate_loop_threshold', 15.0),
                (self.gps_optimizer_ns, 'min_trajectory_length', 20.0),
                (self.gps_optimizer_ns, 'gps_quality_threshold', -1),
                (self.slam_integration_ns, 'force_search_radius', 25.0),
                (self.slam_integration_ns, 'start_end_score_threshold', 0.5),
                (self.slam_integration_ns, 'intermediate_score_threshold', 0.6)
            ]
        elif choice == '2':
            # 中等阈值
            params = [
                (self.gps_optimizer_ns, 'loop_closure_distance_threshold', 8.0),
                (self.gps_optimizer_ns, 'intermediate_loop_threshold', 10.0),
                (self.gps_optimizer_ns, 'min_trajectory_length', 30.0),
                (self.gps_optimizer_ns, 'gps_quality_threshold', -1),
                (self.slam_integration_ns, 'force_search_radius', 15.0),
                (self.slam_integration_ns, 'start_end_score_threshold', 0.35),
                (self.slam_integration_ns, 'intermediate_score_threshold', 0.45)
            ]
        elif choice == '3':
            # 严格阈值
            params = [
                (self.gps_optimizer_ns, 'loop_closure_distance_threshold', 5.0),
                (self.gps_optimizer_ns, 'intermediate_loop_threshold', 6.0),
                (self.gps_optimizer_ns, 'min_trajectory_length', 50.0),
                (self.gps_optimizer_ns, 'gps_quality_threshold', 0),
                (self.slam_integration_ns, 'force_search_radius', 10.0),
                (self.slam_integration_ns, 'start_end_score_threshold', 0.25),
                (self.slam_integration_ns, 'intermediate_score_threshold', 0.30)
            ]
        else:
            print("无效选择")
            return
        
        print(f"应用调整方案 {choice}...")
        for namespace, param, value in params:
            self.set_param(namespace, param, value)
        
        print("✅ 快速调整完成")

def main():
    if len(sys.argv) > 1:
        # 命令行模式
        adjuster = GPSLoopParamAdjuster()
        
        if sys.argv[1] == 'show':
            adjuster.show_current_params()
        elif sys.argv[1] == 'preset' and len(sys.argv) > 2:
            adjuster.load_preset(sys.argv[2])
        elif sys.argv[1] == 'set' and len(sys.argv) > 4:
            namespace = sys.argv[2]  # gps 或 slam
            param = sys.argv[3]
            value = sys.argv[4]
            
            if namespace == 'gps':
                ns = adjuster.gps_optimizer_ns
            elif namespace == 'slam':
                ns = adjuster.slam_integration_ns
            else:
                print("命名空间必须是 'gps' 或 'slam'")
                return
            
            adjuster.set_param(ns, param, value)
        else:
            print("用法:")
            print("  python3 adjust_gps_loop_params.py show")
            print("  python3 adjust_gps_loop_params.py preset poor_gps_preset")
            print("  python3 adjust_gps_loop_params.py set gps loop_closure_distance_threshold 10.0")
            print("  python3 adjust_gps_loop_params.py set slam force_search_radius 20.0")
    else:
        # 交互式模式
        try:
            adjuster = GPSLoopParamAdjuster()
            adjuster.interactive_mode()
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序异常: {e}")

if __name__ == '__main__':
    main()
