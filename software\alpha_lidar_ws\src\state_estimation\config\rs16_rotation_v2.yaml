common:
    lid_topic:  "/velodyne_points"
    imu_topic:  "/imu/data"
    encoder_topic:  "/imu/encoder"
    gps_topic:  "/rtk/gnss"     # GPS topic name - 修改为实际的topic
    time_sync_en: false         # ONLY turn on when external time synchronization is really not possible

# GPS integration parameters
gps:
    enable_correction: true     # Enable GPS height correction
    enable_loop_closure: true  # Enable GPS loop closure detection
    enable_plane_constraint: false  # Disable GPS plane constraint to avoid point cloud misalignment
    height_correction_threshold: 0.3  # Height correction threshold in meters
    correction_rate: 0.1        # Height correction rate (10%)
    loop_closure_distance: 10.0  # Loop closure detection distance in meters (设置为10米)
    loop_closure_min_distance: 20.0  # 最小轨迹长度，超过此距离才开始检测回环 (20米)

    # GPS触发的ICP回环检测参数
    enable_icp_loop_closure: true   # 启用GPS触发的ICP回环检测
    icp_trigger_distance: 20.0      # GPS触发ICP回环检测的距离 (20米，降低触发距离)
    keyframe_skip: 10               # 关键帧间隔 (每10帧保存一个关键帧)
    icp_fitness_threshold: 0.3      # ICP匹配质量阈值
    icp_max_correspondence_distance: 1.0  # ICP最大对应距离

    # GPS constraint mode: 0=disabled, 1=conservative, 2=moderate, 3=aggressive
    constraint_mode: 0          # Disabled mode (for GPS reference enhancement)

    # Enhanced GPS constraint parameters - 禁用直接约束，启用参考引导
    plane_constraint_weight: 0.0    # Weight for plane constraint (0%, 完全禁用)
    xy_correction_threshold: 999.0  # XY correction threshold in meters (设置很大值，禁用校正)
    xy_correction_rate: 0.0         # XY correction rate (0%, 禁用校正)
    constraint_window_size: 1       # GPS constraint window size (最小值)

    # GPS reference enhancement parameters - GPS参考增强参数
    enable_reference_enhancement: true  # 启用GPS参考增强
    reference_radius: 100.0             # GPS参考半径 (100米) - 距离起点100米内触发
    enhancement_radius: 100.0           # SLAM增强半径 (100米) - SLAM匹配增强范围
    start_point_return_threshold: 100.0 # 返回起点检测阈值 (100米)

# PCD save parameters
pcd_save:
    pcd_save_en: true          # Enable PCD saving
    interval: -1               # Save interval: -1 = save all to one file, >0 = save every N frames
    directory: "/home/<USER>/slam_share/aLidar/temp"  # Save directory

preprocess:
    lidar_type: 2                # 1 for Livox serials LiDAR, 2 for Velodyne LiDAR, 3 for ouster LiDAR,
    scan_line: 16
    scan_rate: 10                # only need to be set for velodyne, unit: Hz,
    blind: 0.8
    point_filter_num: 1

mapping:
    down_sample_size: 0.2
    max_iteration: 4
#    voxel_size: 1.0
#    max_layer: 4                # 4 layer, 0, 1, 2, 3
    voxel_size: 0.25
    max_layer: 2                # 4 layer, 0, 1, 2, 3
    layer_point_size: [ 5, 5, 5, 5, 5 ]
    plannar_threshold: 0.01
    max_points_size: 1000
    max_cov_points_size: 1000
    init_gravity_with_pose: true

    fov_degree:    360
    det_range:     100.0
    extrinsic_est_en:  true      # true: enable the online estimation of IMU-LiDAR extrinsic,
    extrinsic_T: [ 0.03, 0.01, -0.05 ]
    extrinsic_R: [ 1, 0, 0,
                   0, -1, 0,
                   0, 0, -1 ]

    encoder_fusion_en: true     # true: enable encoder angle fusion,
    extrinsic_T_encoder_lidar:  [-0.0042, 0.0046, 0.09994]
    extrinsic_R_encoder_lidar: [-0.0002004, 3.04e-05, 1.0,
                                0.0040013, 0.999992, -2.96e-05,
                                -0.999992, 0.0040013, -0.0002005]
    encoder_offset_deg: 120

noise_model:
    ranging_cov: 0.04
    angle_cov: 0.1
    # v2的硬件 imu cov调低一点效果好
#    acc_cov: 0.05
#    gyr_cov: 0.0025
    acc_cov: 0.1
    gyr_cov: 0.01
    #    acc_cov: 1.0
    #    gyr_cov: 0.5
#    b_acc_cov: 0.0043
#    b_gyr_cov: 0.000266
    b_acc_cov: 0.0000043
    b_gyr_cov: 0.000000266


publish:
    pub_voxel_map: false
    publish_max_voxel_layer: 2         # only publish 0,1,2 layer's plane
    path_en:  true
    publish_limit_z: 3.0
    publish_dense_skip: 2
    scan_publish_en:  true       # false: close all the point cloud output
    dense_publish_en: true # false: low down the points number in a global-frame point clouds scan.
    scan_bodyframe_pub_en: true  # true: output the point cloud scans in IMU-body-frame
    intensity_th: 2.0

pcd_save:
    pcd_save_en: true           # Enable PCD saving
    interval: -1                # how many LiDAR frames saved in each pcd file;
    # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.
    directory: "/home/<USER>/slam_share/aLidar/temp"  # Save directory
