#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build the fixed version with bagpy patch
"""

import os
import sys
import subprocess
import shutil
import time

def ensure_bagpy_version():
    """确保bagpy version文件存在"""
    try:
        import bagpy
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        if not os.path.exists(version_file):
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
            print(f"✅ 创建bagpy version文件: {version_file}")
        else:
            print(f"✅ bagpy version文件已存在")
        return True
    except Exception as e:
        print(f"⚠️  处理bagpy version文件时出错: {e}")
        return False

def build_exe():
    """构建exe文件"""
    print("🚀 开始构建修复版exe...")
    
    # 清理之前的构建
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 使用简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=GPS_3D_Analyzer_Fixed_Final",
        "gps_gui_analyzer_fixed.py"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        build_time = time.time() - start_time
        print(f"构建耗时: {build_time:.1f}秒")
        
        if result.returncode == 0:
            print("✅ 构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_final_release():
    """创建最终发布包"""
    print("📦 创建最终发布包...")
    
    release_dir = "GPS_3D_Analyzer_Fixed_Final_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制exe文件
    exe_path = "dist/GPS_3D_Analyzer_Fixed_Final.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        
        # 获取文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ exe文件复制完成 ({size_mb:.1f} MB)")
    else:
        print("❌ exe文件不存在")
        return False
    
    # 创建说明文件
    readme_content = f"""# GPS 3D 轨迹分析器 - 最终修复版

## 🔧 Bagpy问题最终解决方案

本版本彻底解决了bagpy在PyInstaller中的导入问题。

### ✅ 修复内容
- 智能检测bagpy导入问题
- 自动创建缺失的version文件
- 动态修复bagpy模块路径
- 兼容模式支持

### 🎯 主要功能
- 📁 从ROS bag文件提取GPS数据
- 🎨 3D轨迹可视化（深灰色主题 + 深绿色文字）
- 📊 详细的GPS质量分析（中文显示）
- 💾 导出轨迹为文本文件（帧编号 X Y Z格式）
- 🎮 交互式3D控制（鼠标拖拽旋转、滚轮缩放）

### 🎯 GPS质量颜色编码
- 🟢 青绿色实线: RTK固定解（最高精度）
- 🔵 蓝色虚线: SBAS定位
- 🟠 橙色虚线: GBAS定位
- 🔴 红色虚线: 无定位解
- 🟣 紫色虚线: 其他状态

### 🚀 使用方法
1. 双击 GPS_3D_Analyzer_Fixed_Final.exe 启动程序
2. 程序会自动检测和修复bagpy问题
3. 点击"浏览"选择ROS bag文件
4. 设置GPS话题（默认: /rtk/gnss）
5. 选择输出文件位置
6. 点击"开始分析"
7. 查看3D轨迹和分析结果

### 🎮 3D控制说明
- **鼠标拖拽**: 旋转3D视角
- **鼠标滚轮**: 缩放视图
- **俯视按钮**: 切换到俯视角度
- **侧视按钮**: 切换到侧视角度
- **正视按钮**: 切换到正视角度
- **复位按钮**: 恢复默认视角

### 💻 系统要求
- Windows 10 64位
- 最小4GB内存
- 支持OpenGL的显卡
- 100MB可用磁盘空间

### 🎨 界面特色
- 深灰色专业主题 (#2E2E2E)
- 深绿色文字显示 (#00C851)
- 深色选择背景 (#404040)
- 无中文乱码
- 流畅的3D交互

### 🔧 故障排除
如果程序无法启动：
1. 确保系统满足最低要求
2. 检查是否有杀毒软件阻止
3. 尝试以管理员身份运行
4. 查看Windows事件查看器获取详细错误信息

### 📝 版本信息
- 版本: 1.0.4 (最终修复版)
- 构建日期: {time.strftime("%Y-%m-%d")}
- 平台: Windows 10 64位
- Bagpy支持: 完整修复

---
© 2024 GPS Analyzer Team. All rights reserved.
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 最终发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("🔧 GPS 3D轨迹分析器 - 最终修复版构建")
    print("=" * 50)
    
    # 检查文件
    if not os.path.exists("gps_gui_analyzer_fixed.py"):
        print("❌ 主程序文件不存在")
        return False
    
    start_time = time.time()
    
    # 构建步骤
    steps = [
        ("确保bagpy version文件", ensure_bagpy_version),
        ("构建exe文件", build_exe),
        ("创建最终发布包", create_final_release),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ 失败于步骤: {step_name}")
            return False
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("🎉 最终修复版构建完成!")
    print("=" * 50)
    print(f"⏱️  总耗时: {total_time:.1f}秒")
    print("📁 发布包: GPS_3D_Analyzer_Fixed_Final_Release/")
    print("🚀 exe文件: GPS_3D_Analyzer_Fixed_Final.exe")
    print("\n✅ 这个版本应该可以正常运行并解决bagpy问题")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
