#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS质量分析器
实时分析GPS信号质量，为软约束系统提供质量评估和预测
"""

import rospy
import numpy as np
from std_msgs.msg import String, Float64
from sensor_msgs.msg import NavSatFix
import json
import threading
from collections import deque
import math

class GPSQualityAnalyzer:
    def __init__(self):
        rospy.init_node('gps_quality_analyzer', anonymous=True)
        
        # 参数配置
        self.analysis_window = rospy.get_param('~analysis_window', 100)
        self.report_interval = rospy.get_param('~report_interval', 10.0)
        self.enable_quality_prediction = rospy.get_param('~enable_quality_prediction', True)
        self.prediction_horizon = rospy.get_param('~prediction_horizon', 30)  # 预测30秒
        
        # 质量阈值
        self.excellent_threshold = rospy.get_param('~excellent_threshold', 5)
        self.good_threshold = rospy.get_param('~good_threshold', 3)
        self.fair_threshold = rospy.get_param('~fair_threshold', 1)
        
        # 数据存储
        self.gps_data_history = deque(maxlen=self.analysis_window)
        self.quality_history = deque(maxlen=self.analysis_window)
        self.position_accuracy_history = deque(maxlen=50)
        
        # 统计变量
        self.quality_stats = {
            'excellent': 0, 'good': 0, 'fair': 0, 'poor': 0
        }
        self.total_samples = 0
        
        # 质量预测模型 (简单移动平均)
        self.quality_trend = deque(maxlen=20)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        
        # 发布器
        self.quality_report_pub = rospy.Publisher('/gps_quality_report', String, queue_size=1)
        self.quality_score_pub = rospy.Publisher('/gps_quality_score', Float64, queue_size=1)
        self.quality_prediction_pub = rospy.Publisher('/gps_quality_prediction', String, queue_size=1)
        
        # 定时器
        self.analysis_timer = rospy.Timer(rospy.Duration(self.report_interval), self.analyze_and_report)
        
        rospy.loginfo("📊 GPS Quality Analyzer Started")
        rospy.loginfo("Analysis window: %d samples", self.analysis_window)
        rospy.loginfo("Report interval: %.1f seconds", self.report_interval)
    
    def classify_quality(self, status, accuracy=None):
        """GPS质量分类"""
        if status >= self.excellent_threshold:
            return 'excellent'
        elif status >= self.good_threshold:
            return 'good'
        elif status >= self.fair_threshold:
            return 'fair'
        else:
            return 'poor'
    
    def calculate_quality_score(self, status, accuracy=None):
        """计算质量分数 (0-1)"""
        # 基于状态的分数
        if status >= 5:
            base_score = 1.0
        elif status >= 3:
            base_score = 0.8
        elif status >= 1:
            base_score = 0.6
        elif status >= 0:
            base_score = 0.4
        else:
            base_score = 0.2
        
        # 如果有精度信息，进行调整
        if accuracy is not None and accuracy > 0:
            # 精度越小越好，转换为分数
            accuracy_score = max(0.0, 1.0 - accuracy / 10.0)  # 假设10米为最差精度
            base_score = (base_score + accuracy_score) / 2.0
        
        return max(0.0, min(1.0, base_score))
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        with self.lock:
            current_time = rospy.Time.now().to_sec()
            
            # 提取GPS数据
            gps_data = {
                'timestamp': current_time,
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status,
                'service': msg.status.service,
                'covariance': list(msg.position_covariance) if len(msg.position_covariance) > 0 else None
            }
            
            # 计算位置精度 (如果有协方差信息)
            position_accuracy = None
            if gps_data['covariance'] and len(gps_data['covariance']) >= 9:
                # 使用协方差矩阵的对角元素估算精度
                var_x = gps_data['covariance'][0]
                var_y = gps_data['covariance'][4]
                var_z = gps_data['covariance'][8]
                position_accuracy = math.sqrt(var_x + var_y + var_z)
                self.position_accuracy_history.append(position_accuracy)
            
            # 质量分类和评分
            quality = self.classify_quality(gps_data['status'], position_accuracy)
            quality_score = self.calculate_quality_score(gps_data['status'], position_accuracy)
            
            gps_data['quality'] = quality
            gps_data['quality_score'] = quality_score
            gps_data['position_accuracy'] = position_accuracy
            
            # 存储数据
            self.gps_data_history.append(gps_data)
            self.quality_history.append(quality)
            self.quality_trend.append(quality_score)
            
            # 更新统计
            self.quality_stats[quality] += 1
            self.total_samples += 1
            
            # 发布实时质量分数
            score_msg = Float64()
            score_msg.data = quality_score
            self.quality_score_pub.publish(score_msg)
    
    def analyze_and_report(self, event):
        """分析并报告GPS质量"""
        try:
            with self.lock:
                if len(self.gps_data_history) < 5:
                    return
                
                # 基本统计
                recent_data = list(self.gps_data_history)[-min(50, len(self.gps_data_history)):]
                recent_qualities = [d['quality'] for d in recent_data]
                recent_scores = [d['quality_score'] for d in recent_data]
                
                # 质量分布
                quality_distribution = {}
                for quality in ['excellent', 'good', 'fair', 'poor']:
                    count = recent_qualities.count(quality)
                    quality_distribution[quality] = {
                        'count': count,
                        'percentage': (count / len(recent_qualities)) * 100
                    }
                
                # 质量趋势分析
                trend_analysis = self.analyze_quality_trend(recent_scores)
                
                # 稳定性分析
                stability_analysis = self.analyze_quality_stability(recent_scores)
                
                # 位置精度分析
                accuracy_analysis = self.analyze_position_accuracy()
                
                # 生成报告
                quality_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'analyzer_type': 'gps_quality_analyzer',
                    'sample_count': len(recent_data),
                    'current_quality': recent_data[-1]['quality'] if recent_data else 'unknown',
                    'current_score': recent_data[-1]['quality_score'] if recent_data else 0.0,
                    'quality_distribution': quality_distribution,
                    'trend_analysis': trend_analysis,
                    'stability_analysis': stability_analysis,
                    'accuracy_analysis': accuracy_analysis,
                    'recommendations': self.generate_recommendations(quality_distribution, trend_analysis, stability_analysis)
                }
                
                # 发布报告
                report_msg = String()
                report_msg.data = json.dumps(quality_report, indent=2)
                self.quality_report_pub.publish(report_msg)
                
                # 质量预测
                if self.enable_quality_prediction:
                    prediction = self.predict_quality_trend()
                    prediction_msg = String()
                    prediction_msg.data = json.dumps(prediction, indent=2)
                    self.quality_prediction_pub.publish(prediction_msg)
                
                # 日志输出
                avg_score = np.mean(recent_scores) if recent_scores else 0.0
                rospy.loginfo("GPS Quality: %.2f (%.1f%% excellent, %.1f%% good, %.1f%% fair, %.1f%% poor)", 
                             avg_score,
                             quality_distribution['excellent']['percentage'],
                             quality_distribution['good']['percentage'],
                             quality_distribution['fair']['percentage'],
                             quality_distribution['poor']['percentage'])
                
        except Exception as e:
            rospy.logerr("Error in quality analysis: %s", str(e))
    
    def analyze_quality_trend(self, scores):
        """分析质量趋势"""
        if len(scores) < 10:
            return {'trend': 'insufficient_data', 'slope': 0.0, 'confidence': 0.0}
        
        # 简单线性回归分析趋势
        x = np.arange(len(scores))
        y = np.array(scores)
        
        # 计算斜率
        slope = np.polyfit(x, y, 1)[0]
        
        # 趋势分类
        if slope > 0.01:
            trend = 'improving'
        elif slope < -0.01:
            trend = 'degrading'
        else:
            trend = 'stable'
        
        # 计算置信度 (基于R²)
        correlation = np.corrcoef(x, y)[0, 1]
        confidence = abs(correlation) if not np.isnan(correlation) else 0.0
        
        return {
            'trend': trend,
            'slope': float(slope),
            'confidence': float(confidence),
            'recent_change': float(scores[-1] - scores[0]) if len(scores) > 1 else 0.0
        }
    
    def analyze_quality_stability(self, scores):
        """分析质量稳定性"""
        if len(scores) < 5:
            return {'stability': 'insufficient_data', 'variance': 0.0, 'coefficient_of_variation': 0.0}
        
        mean_score = np.mean(scores)
        variance = np.var(scores)
        std_dev = np.std(scores)
        
        # 变异系数
        cv = std_dev / mean_score if mean_score > 0 else float('inf')
        
        # 稳定性分类
        if cv < 0.1:
            stability = 'very_stable'
        elif cv < 0.2:
            stability = 'stable'
        elif cv < 0.3:
            stability = 'moderately_stable'
        else:
            stability = 'unstable'
        
        return {
            'stability': stability,
            'variance': float(variance),
            'standard_deviation': float(std_dev),
            'coefficient_of_variation': float(cv),
            'mean_score': float(mean_score)
        }
    
    def analyze_position_accuracy(self):
        """分析位置精度"""
        if len(self.position_accuracy_history) < 5:
            return {'status': 'insufficient_data'}
        
        accuracies = list(self.position_accuracy_history)
        
        return {
            'status': 'available',
            'mean_accuracy': float(np.mean(accuracies)),
            'std_accuracy': float(np.std(accuracies)),
            'min_accuracy': float(np.min(accuracies)),
            'max_accuracy': float(np.max(accuracies)),
            'recent_accuracy': float(accuracies[-1])
        }
    
    def predict_quality_trend(self):
        """预测质量趋势"""
        if len(self.quality_trend) < 10:
            return {'prediction': 'insufficient_data'}
        
        recent_trend = list(self.quality_trend)[-10:]
        
        # 简单移动平均预测
        short_term_avg = np.mean(recent_trend[-5:])
        long_term_avg = np.mean(recent_trend)
        
        # 预测方向
        if short_term_avg > long_term_avg + 0.05:
            prediction_direction = 'improving'
        elif short_term_avg < long_term_avg - 0.05:
            prediction_direction = 'degrading'
        else:
            prediction_direction = 'stable'
        
        # 预测置信度
        variance = np.var(recent_trend)
        confidence = max(0.0, 1.0 - variance * 2)  # 方差越小，置信度越高
        
        return {
            'prediction': prediction_direction,
            'predicted_score': float(short_term_avg),
            'confidence': float(confidence),
            'prediction_horizon': self.prediction_horizon,
            'timestamp': rospy.Time.now().to_sec()
        }
    
    def generate_recommendations(self, distribution, trend, stability):
        """生成建议"""
        recommendations = []
        
        # 基于质量分布的建议
        poor_percentage = distribution['poor']['percentage']
        excellent_percentage = distribution['excellent']['percentage']
        
        if poor_percentage > 30:
            recommendations.append("GPS质量较差，建议主要依赖SLAM自身的回环检测")
        elif excellent_percentage > 60:
            recommendations.append("GPS质量优秀，可以使用严格的软约束参数")
        else:
            recommendations.append("GPS质量中等，建议使用平衡的软约束参数")
        
        # 基于趋势的建议
        if trend['trend'] == 'degrading' and trend['confidence'] > 0.5:
            recommendations.append("GPS质量呈下降趋势，建议降低软约束权重")
        elif trend['trend'] == 'improving' and trend['confidence'] > 0.5:
            recommendations.append("GPS质量呈上升趋势，可以适当增加软约束权重")
        
        # 基于稳定性的建议
        if stability['stability'] == 'unstable':
            recommendations.append("GPS质量不稳定，建议使用更保守的软约束策略")
        elif stability['stability'] == 'very_stable':
            recommendations.append("GPS质量非常稳定，可以使用更积极的软约束策略")
        
        return recommendations

def main():
    try:
        analyzer = GPSQualityAnalyzer()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("GPS Quality Analyzer error: %s", str(e))

if __name__ == '__main__':
    main()
