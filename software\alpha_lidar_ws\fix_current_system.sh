#!/bin/bash

# 修复当前运行系统的脚本

echo "=========================================="
echo "🔧 修复当前运行的SLAM系统"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "检查当前系统状态..."

# 检查关键节点
if ! rosnode list | grep -q enhanced_gps_loop_optimizer; then
    echo "❌ GPS回环优化器未运行"
    echo "请先启动SLAM系统"
    exit 1
fi

echo "✅ GPS回环优化器正在运行"

if rosnode list | grep -q enhanced_slam_loop_integration; then
    echo "✅ SLAM集成模块正在运行"
else
    echo "⚠️  SLAM集成模块未运行"
fi

if rosnode list | grep -q intelligent_start_end_detector; then
    echo "✅ 智能检测器正在运行"
else
    echo "⚠️  智能检测器未运行"
fi

echo ""
echo "当前参数状态："
current_trigger=$(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 2>/dev/null || echo "未设置")
current_search=$(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius 2>/dev/null || echo "未设置")
current_threshold=$(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold 2>/dev/null || echo "未设置")

echo "GPS触发距离: ${current_trigger}米"
echo "搜索半径: ${current_search}米"
echo "匹配阈值: ${current_threshold}"
echo ""

echo "根据您的日志分析："
echo "❌ 当前GPS距离: 7.6米"
echo "❌ 当前触发距离: 20.0米 (太大)"
echo "✅ GPS质量: RTK_FIXED (很好)"
echo ""

echo "选择修复方案："
echo "1) 立即修复 - 激进参数 (触发10m, 搜索50m, 阈值0.8)"
echo "2) 立即修复 - 积极参数 (触发12m, 搜索40m, 阈值0.75)"
echo "3) 立即修复 - 保守参数 (触发15m, 搜索35m, 阈值0.7)"
echo "4) 自定义参数"
echo "5) 查看当前系统状态"
echo ""

read -p "请选择修复方案 (1-5): " choice

case $choice in
    1)
        echo "应用激进修复参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 10.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 50.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.8
        rosparam set /enhanced_gps_loop_closure_optimizer/gps_quality_threshold 0
        
        if rosnode list | grep -q intelligent_start_end_detector; then
            rosparam set /intelligent_start_end_detector/return_threshold 10.0
            rosparam set /intelligent_start_end_detector/departure_threshold 20.0
        fi
        
        echo "✅ 激进参数已应用"
        ;;
    2)
        echo "应用积极修复参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 12.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 40.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.75
        rosparam set /enhanced_gps_loop_closure_optimizer/gps_quality_threshold 0
        
        if rosnode list | grep -q intelligent_start_end_detector; then
            rosparam set /intelligent_start_end_detector/return_threshold 12.0
            rosparam set /intelligent_start_end_detector/departure_threshold 20.0
        fi
        
        echo "✅ 积极参数已应用"
        ;;
    3)
        echo "应用保守修复参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 15.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 35.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.7
        rosparam set /enhanced_gps_loop_closure_optimizer/gps_quality_threshold 0
        
        if rosnode list | grep -q intelligent_start_end_detector; then
            rosparam set /intelligent_start_end_detector/return_threshold 15.0
            rosparam set /intelligent_start_end_detector/departure_threshold 25.0
        fi
        
        echo "✅ 保守参数已应用"
        ;;
    4)
        echo "自定义参数设置："
        read -p "GPS触发距离 (米, 当前: $current_trigger): " new_trigger
        read -p "搜索半径 (米, 当前: $current_search): " new_search
        read -p "匹配阈值 (0-1, 当前: $current_threshold): " new_threshold
        
        if [ ! -z "$new_trigger" ]; then
            rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold $new_trigger
        fi
        if [ ! -z "$new_search" ]; then
            rosparam set /enhanced_slam_loop_closure_integration/force_search_radius $new_search
        fi
        if [ ! -z "$new_threshold" ]; then
            rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold $new_threshold
        fi
        
        echo "✅ 自定义参数已应用"
        ;;
    5)
        echo "当前系统状态："
        echo ""
        echo "=== GPS回环状态 ==="
        timeout 5 rostopic echo /enhanced_gps_loop_closure_status -n 1 | head -10
        
        echo ""
        echo "=== 智能检测状态 ==="
        if rosnode list | grep -q intelligent_start_end_detector; then
            timeout 3 rostopic echo /intelligent_detector_status -n 1 | head -5
        else
            echo "智能检测器未运行"
        fi
        
        echo ""
        echo "=== 质量监控 ==="
        timeout 3 rostopic echo /pointcloud_quality_report -n 1 | head -5
        
        exit 0
        ;;
    *)
        echo "无效选择，使用积极修复参数..."
        rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 12.0
        rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 40.0
        rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.75
        ;;
esac

echo ""
echo "修复后的参数："
echo "GPS触发距离: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold)米"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius)米"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold)"
echo "GPS质量要求: $(rosparam get /enhanced_gps_loop_closure_optimizer/gps_quality_threshold) (0=RTK_FIXED)"

echo ""
echo "=========================================="
echo "🎉 系统参数修复完成"
echo "=========================================="
echo ""
echo "预期效果："
echo "✅ 当GPS距离接近触发阈值时立即触发回环"
echo "✅ 更大的搜索范围提高匹配成功率"
echo "✅ 适中的匹配阈值平衡精度和成功率"
echo ""
echo "监控命令："
echo "  实时GPS状态: rostopic echo /enhanced_gps_loop_closure_status"
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo ""
echo "继续播放您的bag文件，系统现在应该能够在GPS距离较小时触发回环检测"
