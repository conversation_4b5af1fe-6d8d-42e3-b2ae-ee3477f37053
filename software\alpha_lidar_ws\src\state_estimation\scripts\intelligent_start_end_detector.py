#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能首尾回环检测器
实现您提出的优化策略：
1. 起点离开30米后开始监控
2. 实时GPS位置与起点比较
3. 质量非0的GPS也参与检测
4. 直线距离<50米时强制匹配
5. SLAM首尾帧范围全局精细匹配
"""

import rospy
import numpy as np
import math
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import String, Bool
import json
import threading
from collections import deque

class IntelligentStartEndDetector:
    def __init__(self):
        rospy.init_node('intelligent_start_end_detector', anonymous=True)
        
        # 参数配置
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        self.departure_threshold = rospy.get_param('~departure_threshold', 30.0)  # 离开起点的距离阈值
        self.return_threshold = rospy.get_param('~return_threshold', 50.0)        # 返回起点的距离阈值
        self.min_trajectory_points = rospy.get_param('~min_trajectory_points', 100)  # 最小轨迹点数
        self.gps_quality_threshold = rospy.get_param('~gps_quality_threshold', -1)   # GPS质量阈值(-1接受所有)
        
        # 状态变量
        self.start_position = None          # 起点GPS位置
        self.current_position = None        # 当前GPS位置
        self.has_departed = False           # 是否已离开起点
        self.trajectory_points = deque()    # 轨迹点历史
        self.last_force_time = 0           # 上次强制回环时间
        self.force_cooldown = 10.0         # 强制回环冷却时间(秒)
        
        # 统计信息
        self.total_checks = 0
        self.forced_detections = 0
        self.successful_returns = 0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器和发布器
        self.gps_sub = rospy.Subscriber(
            self.gps_topic, NavSatFix, self.gps_callback, queue_size=10)
        
        self.force_loop_pub = rospy.Publisher(
            '/intelligent_force_loop_closure', String, queue_size=1)
        
        self.status_pub = rospy.Publisher(
            '/intelligent_detector_status', String, queue_size=1)
        
        self.precise_match_pub = rospy.Publisher(
            '/force_precise_start_end_match', Bool, queue_size=1)
        
        # 定时器
        self.status_timer = rospy.Timer(
            rospy.Duration(30.0), self.publish_status_report)
        
        rospy.loginfo("Intelligent Start-End Detector Started")
        rospy.loginfo("Departure threshold: %.1f meters", self.departure_threshold)
        rospy.loginfo("Return threshold: %.1f meters", self.return_threshold)
        rospy.loginfo("GPS topic: %s", self.gps_topic)
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        try:
            with self.data_lock:
                # 检查GPS质量（支持质量非0的情况）
                if self.gps_quality_threshold >= 0 and msg.status.status < self.gps_quality_threshold:
                    return
                
                current_pos = {
                    'lat': msg.latitude,
                    'lon': msg.longitude,
                    'alt': msg.altitude,
                    'timestamp': msg.header.stamp.to_sec(),
                    'status': msg.status.status
                }
                
                # 设置起点位置
                if self.start_position is None:
                    self.start_position = current_pos.copy()
                    rospy.loginfo("🏁 Start position set: lat=%.6f, lon=%.6f, status=%d", 
                                 current_pos['lat'], current_pos['lon'], current_pos['status'])
                
                self.current_position = current_pos
                
                # 添加到轨迹历史
                self.trajectory_points.append(current_pos)
                if len(self.trajectory_points) > 2000:  # 限制历史长度
                    self.trajectory_points.popleft()
                
                # 执行智能检测
                self.intelligent_detection()
                
        except Exception as e:
            rospy.logerr("Error in GPS callback: %s", str(e))
    
    def calculate_gps_distance(self, pos1, pos2):
        """计算两个GPS位置之间的直线距离(米)"""
        try:
            # 使用Haversine公式计算球面距离
            lat1, lon1 = math.radians(pos1['lat']), math.radians(pos1['lon'])
            lat2, lon2 = math.radians(pos2['lat']), math.radians(pos2['lon'])
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            # 地球半径(米)
            R = 6371000
            distance = R * c
            
            return distance
            
        except Exception as e:
            rospy.logerr("Error calculating GPS distance: %s", str(e))
            return float('inf')
    
    def intelligent_detection(self):
        """智能首尾回环检测逻辑"""
        try:
            if self.start_position is None or self.current_position is None:
                return
            
            if len(self.trajectory_points) < self.min_trajectory_points:
                return
            
            # 计算当前位置与起点的直线距离
            distance_to_start = self.calculate_gps_distance(self.current_position, self.start_position)
            self.total_checks += 1
            
            # 检查是否已离开起点30米
            if not self.has_departed:
                if distance_to_start > self.departure_threshold:
                    self.has_departed = True
                    rospy.loginfo("🚀 Departed from start position! Distance: %.2f meters", distance_to_start)
                    rospy.loginfo("Now monitoring for return to start position...")
                return
            
            # 如果已离开起点，检查是否返回到50米范围内
            if self.has_departed and distance_to_start <= self.return_threshold:
                current_time = rospy.Time.now().to_sec()
                
                # 检查冷却时间
                if current_time - self.last_force_time < self.force_cooldown:
                    return
                
                rospy.logwarn("🎯 INTELLIGENT DETECTION: Approaching start position!")
                rospy.logwarn("Direct distance to start: %.2f meters (threshold: %.2f meters)", 
                             distance_to_start, self.return_threshold)
                rospy.logwarn("GPS Status - Current: %d, Start: %d", 
                             self.current_position['status'], self.start_position['status'])
                
                # 强制触发SLAM首尾帧范围的全局精细匹配
                self.force_precise_start_end_matching()
                
                self.last_force_time = current_time
                self.forced_detections += 1
                
                # 如果距离很近，认为成功返回
                if distance_to_start <= 20.0:
                    self.successful_returns += 1
                    rospy.loginfo("✅ Successful return detected! Distance: %.2f meters", distance_to_start)
                
        except Exception as e:
            rospy.logerr("Error in intelligent detection: %s", str(e))
    
    def force_precise_start_end_matching(self):
        """强制触发SLAM首尾帧范围的全局精细匹配"""
        try:
            # 发布智能强制回环信号
            force_msg = String()
            force_data = {
                'type': 'intelligent_start_end_precise_match',
                'trigger_reason': 'gps_proximity_detection',
                'distance_to_start': self.calculate_gps_distance(self.current_position, self.start_position),
                'departure_threshold': self.departure_threshold,
                'return_threshold': self.return_threshold,
                'trajectory_points': len(self.trajectory_points),
                'current_gps_status': self.current_position['status'],
                'start_gps_status': self.start_position['status'],
                'match_type': 'global_precise_start_end_frames',
                'timestamp': rospy.Time.now().to_sec()
            }
            force_msg.data = json.dumps(force_data, indent=2)
            self.force_loop_pub.publish(force_msg)
            
            # 发布精细匹配信号
            precise_msg = Bool()
            precise_msg.data = True
            self.precise_match_pub.publish(precise_msg)
            
            rospy.loginfo("🔥 FORCED PRECISE START-END MATCHING TRIGGERED!")
            rospy.loginfo("Reason: GPS proximity detection (%.2f meters)", force_data['distance_to_start'])
            rospy.loginfo("Match type: Global precise start-end frames matching")
            
        except Exception as e:
            rospy.logerr("Error forcing precise matching: %s", str(e))

    def publish_status_report(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                if self.start_position is None or self.current_position is None:
                    return

                distance_to_start = self.calculate_gps_distance(self.current_position, self.start_position)

                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'detector_type': 'intelligent_start_end_detector',
                    'status': {
                        'has_departed': self.has_departed,
                        'distance_to_start': round(distance_to_start, 2),
                        'departure_threshold': self.departure_threshold,
                        'return_threshold': self.return_threshold,
                        'trajectory_points': len(self.trajectory_points),
                        'within_return_zone': distance_to_start <= self.return_threshold
                    },
                    'statistics': {
                        'total_checks': self.total_checks,
                        'forced_detections': self.forced_detections,
                        'successful_returns': self.successful_returns
                    }
                }

                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.status_pub.publish(status_msg)

                # 控制台输出简化状态
                if self.has_departed:
                    rospy.loginfo("📍 Distance to start: %.2f meters | Status: %s",
                                 distance_to_start,
                                 "APPROACHING" if distance_to_start <= self.return_threshold else "AWAY")
                else:
                    rospy.loginfo("🏁 Waiting for departure from start position (current: %.2f meters)",
                                 distance_to_start)

        except Exception as e:
            rospy.logerr("Error publishing status report: %s", str(e))

def main():
    try:
        detector = IntelligentStartEndDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Intelligent Start-End Detector: %s", str(e))

if __name__ == '__main__':
    main()
