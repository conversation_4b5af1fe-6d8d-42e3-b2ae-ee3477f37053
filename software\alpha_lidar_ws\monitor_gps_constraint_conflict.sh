#!/bin/bash

# 监控GPS约束与SLAM匹配冲突的脚本

echo "=========================================="
echo "🔍 GPS约束与SLAM匹配冲突监控"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动解决方案系统"
    exit 1
fi

echo "选择监控模式："
echo "1) 实时约束状态和ICP质量监控"
echo "2) GPS约束开关事件监控"
echo "3) 匹配质量趋势分析"
echo "4) 系统冲突诊断"
echo "5) 约束参数实时调节"
echo ""

read -p "请选择模式 (1-5): " choice

case $choice in
    1)
        echo "🔍 实时约束状态和ICP质量监控"
        echo ""
        echo "时间     | 约束状态 | ICP质量 | 速度    | 状态"
        echo "---------|----------|---------|---------|----------"
        
        # 并行监控多个topic
        (
            # 监控约束状态
            rostopic echo /gps_constraint_control | while read line; do
                if [[ $line == *"data:"* ]]; then
                    constraint=$(echo $line | grep -o '[a-z]*')
                    echo "CONSTRAINT:$constraint:$(date '+%H:%M:%S')"
                fi
            done &
            
            # 监控ICP质量
            rostopic echo /icp_fitness_score | while read line; do
                if [[ $line == *"data:"* ]]; then
                    fitness=$(echo $line | grep -o '[0-9.]*')
                    echo "FITNESS:$fitness:$(date '+%H:%M:%S')"
                fi
            done &
            
            # 监控约束控制器状态
            rostopic echo /gps_constraint_controller_status | grep -o '"avg_velocity": [0-9.]*' | while read line; do
                velocity=$(echo $line | grep -o '[0-9.]*')
                echo "VELOCITY:$velocity:$(date '+%H:%M:%S')"
            done &
            
            wait
        ) | while read event; do
            IFS=':' read -r type value timestamp <<< "$event"
            case $type in
                CONSTRAINT)
                    if [[ $value == "true" ]]; then
                        constraint_status="✅ 启用"
                    else
                        constraint_status="❌ 禁用"
                    fi
                    echo "$timestamp | $constraint_status | ---     | ---     | 约束变化"
                    ;;
                FITNESS)
                    if (( $(echo "$value > 0.5" | bc -l) )); then
                        quality_status="❌ 差($value)"
                    elif (( $(echo "$value < 0.2" | bc -l) )); then
                        quality_status="✅ 好($value)"
                    else
                        quality_status="⚠️  中($value)"
                    fi
                    echo "$timestamp | ---      | $quality_status | ---     | 匹配质量"
                    ;;
                VELOCITY)
                    if (( $(echo "$value > 2.0" | bc -l) )); then
                        velocity_status="🚀 快(${value}m/s)"
                    else
                        velocity_status="🚶 慢(${value}m/s)"
                    fi
                    echo "$timestamp | ---      | ---     | $velocity_status | 运动状态"
                    ;;
            esac
        done
        ;;
    2)
        echo "🔄 GPS约束开关事件监控"
        echo ""
        rostopic echo /gps_constraint_control | while read line; do
            if [[ $line == *"data: true"* ]]; then
                echo "$(date '+%H:%M:%S') - ✅ GPS约束已启用"
                echo "  原因: 匹配质量改善或速度降低"
            elif [[ $line == *"data: false"* ]]; then
                echo "$(date '+%H:%M:%S') - ❌ GPS约束已禁用"
                echo "  原因: 匹配质量下降或高速运动"
            fi
            echo "----------------------------------------"
        done
        ;;
    3)
        echo "📊 匹配质量趋势分析"
        echo ""
        echo "收集最近30个ICP匹配质量数据..."
        
        # 收集数据
        fitness_data=()
        count=0
        timeout 60 rostopic echo /icp_fitness_score | while read line; do
            if [[ $line == *"data:"* ]]; then
                fitness=$(echo $line | grep -o '[0-9.]*')
                fitness_data+=($fitness)
                count=$((count + 1))
                
                if [ $count -ge 30 ]; then
                    echo "分析结果："
                    echo "样本数量: $count"
                    
                    # 计算统计信息
                    sum=0
                    min=${fitness_data[0]}
                    max=${fitness_data[0]}
                    
                    for f in "${fitness_data[@]}"; do
                        sum=$(echo "$sum + $f" | bc -l)
                        if (( $(echo "$f < $min" | bc -l) )); then
                            min=$f
                        fi
                        if (( $(echo "$f > $max" | bc -l) )); then
                            max=$f
                        fi
                    done
                    
                    avg=$(echo "scale=4; $sum / $count" | bc -l)
                    
                    echo "平均值: $avg"
                    echo "最小值: $min"
                    echo "最大值: $max"
                    
                    if (( $(echo "$avg > 0.5" | bc -l) )); then
                        echo "建议: 匹配质量较差，建议禁用GPS约束"
                    elif (( $(echo "$avg < 0.2" | bc -l) )); then
                        echo "建议: 匹配质量良好，可以启用GPS约束"
                    else
                        echo "建议: 匹配质量中等，保持当前约束状态"
                    fi
                    break
                fi
                
                echo "收集中... ($count/30) 当前值: $fitness"
            fi
        done
        ;;
    4)
        echo "🔍 系统冲突诊断"
        echo ""
        
        echo "=== GPS约束控制器状态 ==="
        timeout 5 rostopic echo /gps_constraint_controller_status -n 1
        
        echo ""
        echo "=== 当前约束状态 ==="
        constraint_status=$(timeout 3 rostopic echo /gps_constraint_control -n 1 | grep -o 'data: [a-z]*' | cut -d' ' -f2)
        if [[ $constraint_status == "true" ]]; then
            echo "✅ GPS约束当前已启用"
        else
            echo "❌ GPS约束当前已禁用"
        fi
        
        echo ""
        echo "=== 最近ICP匹配质量 ==="
        recent_fitness=$(timeout 3 rostopic echo /icp_fitness_score -n 1 | grep -o 'data: [0-9.]*' | cut -d' ' -f2)
        if [ ! -z "$recent_fitness" ]; then
            echo "当前ICP适应度: $recent_fitness"
            if (( $(echo "$recent_fitness > 0.5" | bc -l) )); then
                echo "⚠️  匹配质量较差，可能存在冲突"
            elif (( $(echo "$recent_fitness < 0.2" | bc -l) )); then
                echo "✅ 匹配质量良好"
            else
                echo "ℹ️  匹配质量中等"
            fi
        fi
        
        echo ""
        echo "=== 系统参数检查 ==="
        echo "GPS平面约束: $(rosparam get /state_estimation_node/gps/enable_plane_constraint 2>/dev/null || echo '未设置')"
        echo "约束权重: $(rosparam get /state_estimation_node/gps/plane_constraint_weight 2>/dev/null || echo '未设置')"
        echo "禁用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_disable_fitness 2>/dev/null || echo '未设置')"
        echo "启用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_enable_fitness 2>/dev/null || echo '未设置')"
        
        echo ""
        echo "=== 诊断建议 ==="
        if [[ $constraint_status == "true" ]] && (( $(echo "$recent_fitness > 0.4" | bc -l) )); then
            echo "⚠️  发现潜在冲突: GPS约束启用但ICP质量差"
            echo "建议: 调高禁用阈值或手动禁用约束"
        elif [[ $constraint_status == "false" ]] && (( $(echo "$recent_fitness < 0.3" | bc -l) )); then
            echo "ℹ️  可以考虑启用约束: ICP质量良好但约束禁用"
            echo "建议: 调低启用阈值或手动启用约束"
        else
            echo "✅ 系统状态正常，约束控制合理"
        fi
        ;;
    5)
        echo "🔧 约束参数实时调节"
        echo ""
        echo "当前参数："
        echo "GPS平面约束: $(rosparam get /state_estimation_node/gps/enable_plane_constraint 2>/dev/null)"
        echo "约束权重: $(rosparam get /state_estimation_node/gps/plane_constraint_weight 2>/dev/null)"
        echo "ICP禁用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_disable_fitness 2>/dev/null)"
        echo "ICP启用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_enable_fitness 2>/dev/null)"
        echo "速度阈值: $(rosparam get /intelligent_gps_constraint_controller/velocity_threshold 2>/dev/null)"
        echo ""
        
        echo "调节选项："
        echo "1) 调节ICP禁用阈值 (匹配质量超过此值时禁用约束)"
        echo "2) 调节ICP启用阈值 (匹配质量低于此值时启用约束)"
        echo "3) 调节速度阈值 (速度超过此值时禁用约束)"
        echo "4) 调节GPS约束权重"
        echo "5) 完全禁用GPS平面约束"
        echo "6) 重新启用GPS平面约束"
        echo ""
        
        read -p "请选择 (1-6): " param_choice
        
        case $param_choice in
            1)
                current=$(rosparam get /intelligent_gps_constraint_controller/constraint_disable_fitness 2>/dev/null)
                echo "当前ICP禁用阈值: $current"
                read -p "新的ICP禁用阈值 (0.3-0.8): " new_threshold
                rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness $new_threshold
                echo "✅ ICP禁用阈值已设置为 $new_threshold"
                ;;
            2)
                current=$(rosparam get /intelligent_gps_constraint_controller/constraint_enable_fitness 2>/dev/null)
                echo "当前ICP启用阈值: $current"
                read -p "新的ICP启用阈值 (0.1-0.3): " new_threshold
                rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness $new_threshold
                echo "✅ ICP启用阈值已设置为 $new_threshold"
                ;;
            3)
                current=$(rosparam get /intelligent_gps_constraint_controller/velocity_threshold 2>/dev/null)
                echo "当前速度阈值: $current m/s"
                read -p "新的速度阈值 (1.0-5.0 m/s): " new_threshold
                rosparam set /intelligent_gps_constraint_controller/velocity_threshold $new_threshold
                echo "✅ 速度阈值已设置为 ${new_threshold} m/s"
                ;;
            4)
                current=$(rosparam get /state_estimation_node/gps/plane_constraint_weight 2>/dev/null)
                echo "当前GPS约束权重: $current"
                read -p "新的GPS约束权重 (0.01-0.5): " new_weight
                rosparam set /state_estimation_node/gps/plane_constraint_weight $new_weight
                echo "✅ GPS约束权重已设置为 $new_weight"
                ;;
            5)
                rosparam set /state_estimation_node/gps/enable_plane_constraint false
                rostopic pub /gps_constraint_control std_msgs/Bool "data: false" -1
                echo "✅ GPS平面约束已完全禁用"
                ;;
            6)
                rosparam set /state_estimation_node/gps/enable_plane_constraint true
                rostopic pub /gps_constraint_control std_msgs/Bool "data: true" -1
                echo "✅ GPS平面约束已重新启用"
                ;;
        esac
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "监控结束"
