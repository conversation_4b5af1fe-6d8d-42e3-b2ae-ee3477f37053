#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS轨迹分析工具
从ROS bag文件中提取GPS数据，使用Open3D进行可视化分析
支持不同GPS质量的颜色编码和统计分析
"""

try:
    import rosbag
    USE_ROSBAG = True
except ImportError:
    try:
        import bagpy
        USE_ROSBAG = False
        print("⚠️  使用bagpy替代rosbag (Windows兼容模式)")
    except ImportError:
        print("❌ 错误: 需要安装rosbag或bagpy")
        print("请运行: pip install bagpy")
        exit(1)

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import sys
import os
from collections import defaultdict
import math
import pandas as pd

class GPSTrajectoryAnalyzer:
    def __init__(self):
        self.gps_data = []
        self.trajectory_points = []
        # GPS quality color mapping
        self.quality_colors = {
            -1: 'red',      # NO_FIX: Red
            0:  'green',    # RTK_FIXED: Green (Fixed solution)
            1:  'blue',     # SBAS_FIX: Blue
            2:  'yellow',   # GBAS_FIX: Yellow
            3:  'purple',   # OTHER: Purple
        }
        self.quality_names = {
            -1: "NO_FIX",
            0:  "RTK_FIXED",
            1:  "SBAS_FIX", 
            2:  "GBAS_FIX",
            3:  "OTHER"
        }
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
    def extract_gps_from_bag(self, bag_file, topic_name="/rtk/gnss"):
        """从bag文件中提取GPS数据"""
        print(f"🔍 正在从 {bag_file} 提取GPS数据...")

        if USE_ROSBAG:
            return self._extract_with_rosbag(bag_file, topic_name)
        else:
            return self._extract_with_bagpy(bag_file, topic_name)

    def _extract_with_rosbag(self, bag_file, topic_name):
        """使用rosbag提取GPS数据"""
        try:
            bag = rosbag.Bag(bag_file, 'r')
            gps_count = 0

            # 检查topic是否存在
            topics = bag.get_type_and_topic_info()[1].keys()
            if topic_name not in topics:
                print(f"❌ 错误: topic '{topic_name}' 不存在于bag文件中")
                print(f"可用的topics: {list(topics)}")
                bag.close()
                return False

            for topic, msg, t in bag.read_messages(topics=[topic_name]):
                gps_info = {
                    'timestamp': t.to_sec(),
                    'latitude': msg.latitude,
                    'longitude': msg.longitude,
                    'altitude': msg.altitude,
                    'status': msg.status.status,
                    'service': msg.status.service,
                }
                self.gps_data.append(gps_info)
                gps_count += 1

                if gps_count % 500 == 0:
                    print(f"  已提取 {gps_count} 个GPS点...")

            bag.close()
            print(f"✅ GPS数据提取完成，共 {len(self.gps_data)} 个点")

        except Exception as e:
            print(f"❌ 读取bag文件失败: {e}")
            return False

        return len(self.gps_data) > 0

    def _extract_with_bagpy(self, bag_file, topic_name):
        """使用bagpy提取GPS数据"""
        try:
            # 使用bagpy读取bag文件
            bag = bagpy.bagreader(bag_file)

            # 获取可用topics
            topics = bag.topic_table['Topics'].tolist()
            print(f"📋 发现topics: {topics}")

            # 检查topic是否存在
            if topic_name not in topics:
                print(f"❌ 错误: topic '{topic_name}' 不存在于bag文件中")
                print(f"可用的topics: {topics}")
                return False

            # 读取GPS数据
            print(f"📡 正在读取topic: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)

            # 读取CSV文件
            df = pd.read_csv(gps_csv)
            print(f"📊 CSV列名: {df.columns.tolist()}")

            gps_count = 0
            for index, row in df.iterrows():
                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 500 == 0:
                        print(f"  已提取 {gps_count} 个GPS点...")

                except (ValueError, KeyError) as e:
                    if gps_count == 0:  # 只在第一次出错时显示详细信息
                        print(f"⚠️  解析GPS数据时出错: {e}")
                        print(f"行数据: {row}")
                    continue

            print(f"✅ GPS数据提取完成，共 {len(self.gps_data)} 个点")

        except Exception as e:
            print(f"❌ 读取bag文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        return len(self.gps_data) > 0
    
    def convert_to_local_coordinates(self):
        """将GPS坐标转换为本地坐标系"""
        if not self.gps_data:
            return
            
        # 使用第一个有效GPS点作为原点
        for gps in self.gps_data:
            if gps['status'] >= 0:  # 有效GPS
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude'] 
                self.origin_alt = gps['altitude']
                break
                
        if self.origin_lat is None:
            print("⚠️  警告: 没有找到有效的GPS点")
            # 使用第一个点作为原点
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return
                
        print(f"📍 GPS原点设置为: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}, alt={self.origin_alt:.3f}")
        
        # 转换为本地坐标
        for gps in self.gps_data:
            # 简化的坐标转换（适用于小范围）
            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt
            
            # 转换为米 (简化公式，适用于小范围)
            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff
            
            point_info = {
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)
    
    def analyze_gps_quality(self):
        """分析GPS质量统计"""
        if not self.gps_data:
            return {}
            
        status_count = defaultdict(int)
        total_points = len(self.gps_data)
        
        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3  # 其他状态
            status_count[status] += 1
            
        print("\n" + "="*50)
        print("📊 GPS质量分析")
        print("="*50)
        print(f"总GPS点数: {total_points:,}")
        print("-" * 40)
        
        for status, count in sorted(status_count.items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "UNKNOWN")
            color = self.quality_colors.get(status, 'gray')
            print(f"{quality_name:12}: {count:6,d} points ({percentage:5.1f}%) - {color}")
            
        # 计算轨迹统计
        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])
            trajectory_length = self.calculate_trajectory_length()
            
            print("\n" + "="*50)
            print("📏 轨迹统计")
            print("="*50)
            print(f"轨迹总长度: {trajectory_length:.1f} 米")
            print(f"X范围: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} 米")
            print(f"Y范围: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} 米") 
            print(f"Z范围: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} 米")
            
            # 计算首尾距离
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                print(f"首尾距离: {start_end_distance:.1f} 米")
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0
                print(f"闭合误差: {closure_error:.2f}%")
            
        return status_count
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.trajectory_points) < 2:
            return 0.0
            
        total_length = 0.0
        for i in range(1, len(self.trajectory_points)):
            p1 = np.array(self.trajectory_points[i-1]['position'])
            p2 = np.array(self.trajectory_points[i]['position'])
            total_length += np.linalg.norm(p2 - p1)
            
        return total_length

    def create_trajectory_visualization(self):
        """Create 3D trajectory visualization"""
        if not self.trajectory_points:
            print("❌ No trajectory points to visualize")
            return False

        print("🎨 Creating 3D visualization...")

        # Create 3D plot - only trajectory view
        fig = plt.figure(figsize=(12, 9))
        ax = fig.add_subplot(111, projection='3d')

        # Group by GPS status
        status_groups = defaultdict(list)
        for i, point in enumerate(self.trajectory_points):
            status = point['status']
            if status not in self.quality_names:
                status = 3
            status_groups[status].append(i)

        # Plot 3D trajectory with different colors for different GPS quality
        for status, indices in status_groups.items():
            if len(indices) < 2:
                continue

            points = [self.trajectory_points[i]['position'] for i in indices]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            z_coords = [p[2] for p in points]

            color = self.quality_colors[status]
            quality_name = self.quality_names[status]

            if status == 0:  # RTK_FIXED: solid line
                ax.plot(x_coords, y_coords, z_coords,
                       color=color, linewidth=3, linestyle='-',
                       label=f'{quality_name} ({len(indices)} points)', alpha=0.9)
            else:  # Other qualities: dashed line
                ax.plot(x_coords, y_coords, z_coords,
                       color=color, linewidth=2, linestyle='--',
                       label=f'{quality_name} ({len(indices)} points)', alpha=0.8)

        # Mark start and end points
        if self.trajectory_points:
            start_pos = self.trajectory_points[0]['position']
            end_pos = self.trajectory_points[-1]['position']

            ax.scatter(start_pos[0], start_pos[1], start_pos[2],
                      c='green', s=150, marker='o', label='Start Point',
                      edgecolors='black', linewidth=2)
            ax.scatter(end_pos[0], end_pos[1], end_pos[2],
                      c='red', s=150, marker='s', label='End Point',
                      edgecolors='black', linewidth=2)

        # Set labels and title
        ax.set_xlabel('X (meters)', fontsize=12)
        ax.set_ylabel('Y (meters)', fontsize=12)
        ax.set_zlabel('Z (meters)', fontsize=12)
        ax.set_title('GPS 3D Trajectory Analysis', fontsize=14, fontweight='bold')

        # Add legend
        ax.legend(loc='upper right', fontsize=10)

        # Add grid
        ax.grid(True, alpha=0.3)

        # Set equal aspect ratio
        max_range = np.array([
            np.array([p['position'] for p in self.trajectory_points])[:, 0].max() -
            np.array([p['position'] for p in self.trajectory_points])[:, 0].min(),
            np.array([p['position'] for p in self.trajectory_points])[:, 1].max() -
            np.array([p['position'] for p in self.trajectory_points])[:, 1].min(),
            np.array([p['position'] for p in self.trajectory_points])[:, 2].max() -
            np.array([p['position'] for p in self.trajectory_points])[:, 2].min()
        ]).max() / 2.0

        mid_x = (np.array([p['position'] for p in self.trajectory_points])[:, 0].max() +
                np.array([p['position'] for p in self.trajectory_points])[:, 0].min()) * 0.5
        mid_y = (np.array([p['position'] for p in self.trajectory_points])[:, 1].max() +
                np.array([p['position'] for p in self.trajectory_points])[:, 1].min()) * 0.5
        mid_z = (np.array([p['position'] for p in self.trajectory_points])[:, 2].max() +
                np.array([p['position'] for p in self.trajectory_points])[:, 2].min()) * 0.5

        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)

        plt.tight_layout()
        return True



    def run_analysis(self, bag_file, topic_name="/rtk/gnss"):
        """运行完整的GPS分析"""
        print("🚀 GPS轨迹分析工具启动")
        print("="*60)

        # 检查文件是否存在
        if not os.path.exists(bag_file):
            print(f"❌ 错误: bag文件不存在: {bag_file}")
            return False

        # 提取GPS数据
        if not self.extract_gps_from_bag(bag_file, topic_name):
            print("❌ GPS数据提取失败")
            return False

        # 转换坐标
        self.convert_to_local_coordinates()

        # 分析质量
        quality_stats = self.analyze_gps_quality()

        # 创建可视化
        print("\n🎨 Creating 3D visualization...")
        if self.create_trajectory_visualization():
            # 显示图例信息
            self._print_legend(quality_stats)

            # 运行可视化
            print("\n🖥️  3D visualization window opened")
            print("💡 Operation tips:")
            print("   - Drag to rotate the 3D view")
            print("   - Scroll to zoom in/out")
            print("   - Close window to exit")

            plt.show()
        else:
            print("❌ Visualization creation failed")
            return False

        print("\n✅ GPS轨迹分析完成")
        return True

    def _print_legend(self, quality_stats):
        """Print legend information"""
        print("\n" + "="*50)
        print("🎨 Visualization Legend")
        print("="*50)

        for status, count in sorted(quality_stats.items()):
            if count > 0:
                quality_name = self.quality_names[status]
                line_style = "Solid line" if status == 0 else "Dashed line"
                print(f"{quality_name:12}: {line_style}")

        print("\n🔵 Marker descriptions:")
        print("   Green circle: Trajectory start point")
        print("   Red square: Trajectory end point")
        print("\n📝 Quality descriptions:")
        print("   RTK_FIXED: RTK fixed solution, highest accuracy (Green solid)")
        print("   SBAS_FIX:  SBAS differential positioning (Blue dashed)")
        print("   GBAS_FIX:  GBAS differential positioning (Yellow dashed)")
        print("   NO_FIX:    No positioning solution (Red dashed)")

def main():
    parser = argparse.ArgumentParser(
        description='GPS轨迹分析工具 - 从ROS bag文件分析GPS轨迹质量',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 gps_analyzer_tool.py data.bag
  python3 gps_analyzer_tool.py data.bag --topic /gps/fix

支持的GPS状态:
  - RTK_FIXED (0): RTK固定解，最高精度
  - SBAS_FIX (1):  SBAS差分定位
  - GBAS_FIX (2):  GBAS差分定位
  - NO_FIX (-1):   无定位解
        """
    )
    parser.add_argument('bag_file', help='ROS bag文件路径')
    parser.add_argument('--topic', default='/rtk/gnss',
                       help='GPS topic名称 (默认: /rtk/gnss)')

    args = parser.parse_args()

    # 运行分析
    analyzer = GPSTrajectoryAnalyzer()
    success = analyzer.run_analysis(args.bag_file, args.topic)

    if not success:
        print("❌ GPS轨迹分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
