/**
 * αLiDAR系统GPS集成的具体修改方案
 * 基于现有voxelMapping.cpp的修改建议
 */

// ============================================================================
// 1. 在voxelMapping.cpp中添加GPS相关的全局变量和包含文件
// ============================================================================

// 在文件开头添加GPS相关头文件
#include <sensor_msgs/NavSatFix.h>
#include <GeographicLib/LocalCartesian.hpp>

// 添加GPS相关全局变量
std::deque<sensor_msgs::NavSatFix> gps_buffer;
std::mutex gps_mutex;
std::unique_ptr<GeographicLib::LocalCartesian> local_cartesian;
bool gps_origin_set = false;
bool enable_gps_correction = true;
double gps_height_correction_threshold = 0.3;  // 30cm阈值
double gps_correction_rate = 0.1;  // 10%校正率

// GPS起始位置记录
Eigen::Vector3d start_gps_position = Eigen::Vector3d::Zero();
Eigen::Vector3d start_slam_position = Eigen::Vector3d::Zero();
bool start_positions_set = false;

// GPS状态监控
struct {
    bool is_rtk_fixed = false;
    double last_update_time = 0.0;
    int consecutive_good_fixes = 0;
    double position_std = 1.0;
} gps_status;

// ============================================================================
// 2. 添加GPS回调函数
// ============================================================================

void gps_callback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
    std::lock_guard<std::mutex> lock(gps_mutex);
    
    // 检查GPS有效性
    if (gps_msg->status.status < sensor_msgs::NavSatStatus::STATUS_FIX) {
        return;
    }
    
    // 更新GPS状态
    gps_status.last_update_time = gps_msg->header.stamp.toSec();
    gps_status.is_rtk_fixed = (gps_msg->status.status == sensor_msgs::NavSatStatus::STATUS_GBAS_FIX);
    
    if (gps_status.is_rtk_fixed) {
        gps_status.consecutive_good_fixes++;
    }
    
    // 设置坐标原点
    if (!gps_origin_set && gps_status.consecutive_good_fixes > 5) {
        local_cartesian = std::make_unique<GeographicLib::LocalCartesian>(
            gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
        gps_origin_set = true;
        ROS_INFO("GPS坐标原点已设置: lat=%.8f, lon=%.8f, alt=%.3f",
                gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
    }
    
    // 添加到缓冲区
    gps_buffer.push_back(*gps_msg);
    while (gps_buffer.size() > 500) {
        gps_buffer.pop_front();
    }
}

// ============================================================================
// 3. GPS辅助函数
// ============================================================================

Eigen::Vector3d convertGPSToLocal(const sensor_msgs::NavSatFix& gps_msg) {
    if (!gps_origin_set) return Eigen::Vector3d::Zero();
    
    double x, y, z;
    local_cartesian->Forward(gps_msg.latitude, gps_msg.longitude, gps_msg.altitude, x, y, z);
    return Eigen::Vector3d(x, y, z);
}

bool getGPSPosition(double timestamp, Eigen::Vector3d& gps_position, double& gps_std) {
    std::lock_guard<std::mutex> lock(gps_mutex);
    
    if (gps_buffer.empty() || !gps_origin_set) return false;
    
    // 查找时间戳最近的GPS数据
    double min_time_diff = std::numeric_limits<double>::max();
    sensor_msgs::NavSatFix closest_gps;
    bool found = false;
    
    for (const auto& gps_msg : gps_buffer) {
        double time_diff = std::abs(gps_msg.header.stamp.toSec() - timestamp);
        if (time_diff < min_time_diff && time_diff < 0.5) {  // 0.5秒内的数据
            min_time_diff = time_diff;
            closest_gps = gps_msg;
            found = true;
        }
    }
    
    if (!found) return false;
    
    gps_position = convertGPSToLocal(closest_gps);
    gps_std = gps_status.is_rtk_fixed ? 0.02 : 1.0;  // RTK: 2cm, 普通GPS: 1m
    
    return true;
}

// ============================================================================
// 4. 高度校正函数
// ============================================================================

Eigen::Vector3d correctHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp) {
    if (!enable_gps_correction) return slam_position;
    
    Eigen::Vector3d gps_position;
    double gps_std;
    
    if (!getGPSPosition(timestamp, gps_position, gps_std)) {
        return slam_position;
    }
    
    // 计算高度差
    double height_diff = gps_position.z() - slam_position.z();
    
    // 如果高度差超过阈值，进行校正
    if (std::abs(height_diff) > gps_height_correction_threshold) {
        Eigen::Vector3d corrected_position = slam_position;
        
        // 渐进式校正，避免突跳
        corrected_position.z() += height_diff * gps_correction_rate;
        
        ROS_INFO("GPS高度校正: SLAM=%.3f, GPS=%.3f, 校正=%.3f", 
                slam_position.z(), gps_position.z(), corrected_position.z());
        
        return corrected_position;
    }
    
    return slam_position;
}

// ============================================================================
// 5. 回环检测函数
// ============================================================================

bool detectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp,
                         Eigen::Vector3d& loop_constraint) {
    if (!start_positions_set) return false;
    
    Eigen::Vector3d current_gps_pos;
    double gps_std;
    
    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }
    
    // 检查是否接近起始位置
    double distance_to_start = (current_gps_pos - start_gps_position).norm();
    
    if (distance_to_start < 3.0) {  // 3米范围内认为是回环
        // 计算回环约束
        Eigen::Vector3d slam_drift = current_slam_pos - start_slam_position;
        Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position;
        
        loop_constraint = gps_drift - slam_drift;
        
        ROS_INFO("GPS回环检测: 距离起点%.2fm, 约束[%.3f,%.3f,%.3f]",
                distance_to_start, loop_constraint.x(), 
                loop_constraint.y(), loop_constraint.z());
        
        return loop_constraint.norm() > 0.1;  // 约束大于10cm才应用
    }
    
    return false;
}

// ============================================================================
// 6. 修改execute()函数
// ============================================================================

void execute_with_gps(){
    // 原有的execute()函数内容保持不变，在适当位置添加GPS处理
    
    if (flg_first_scan)
    {
        first_lidar_time = Measures.lidar_beg_time;
        p_imu->first_lidar_time = first_lidar_time;
        flg_first_scan = false;
        return;
    }

    double t_optimize_start = omp_get_wtime();
    p_imu->Process(Measures, kf, feats_undistort);
    state_point = kf.get_x();
    pos_lid = state_point.pos + state_point.rot * state_point.offset_T_L_I;

    if (feats_undistort->empty() || (feats_undistort == NULL)) {
        ROS_WARN("No point, skip this scan!\n");
        return;
    }

    // ========== GPS集成点1: 记录起始位置 ==========
    if (!start_positions_set && gps_origin_set && gps_status.consecutive_good_fixes > 10) {
        Eigen::Vector3d gps_pos;
        double gps_std;
        if (getGPSPosition(lidar_end_time, gps_pos, gps_std)) {
            start_gps_position = gps_pos;
            start_slam_position = state_point.pos;
            start_positions_set = true;
            ROS_INFO("起始位置已记录 - SLAM: [%.3f,%.3f,%.3f], GPS: [%.3f,%.3f,%.3f]",
                    start_slam_position.x(), start_slam_position.y(), start_slam_position.z(),
                    start_gps_position.x(), start_gps_position.y(), start_gps_position.z());
        }
    }

    // 原有的特征点处理和体素地图构建...
    // [保持原有代码不变]
    
    // ========== GPS集成点2: 状态估计后的高度校正 ==========
    // 在kf.update_iterated_dyn_share_diagonal()之后添加
    kf.update_iterated_dyn_share_diagonal();
    state_point = kf.get_x();
    
    // GPS高度校正
    Eigen::Vector3d corrected_position = correctHeightWithGPS(state_point.pos, lidar_end_time);
    if ((corrected_position - state_point.pos).norm() > 0.01) {
        state_point.pos = corrected_position;
        kf.change_x(state_point);  // 更新滤波器状态
        ROS_DEBUG("应用GPS高度校正");
    }
    
    // ========== GPS集成点3: 回环约束检测 ==========
    Eigen::Vector3d loop_constraint;
    if (detectGPSLoopClosure(state_point.pos, lidar_end_time, loop_constraint)) {
        // 简单的位置校正方法
        if (loop_constraint.norm() < 2.0) {  // 约束不能太大
            state_point.pos += loop_constraint * 0.1;  // 10%的校正
            kf.change_x(state_point);
            ROS_INFO("应用GPS回环约束校正");
        }
    }

    // 继续原有的地图更新等处理...
    // [保持原有代码不变]
}

// ============================================================================
// 7. 修改main函数
// ============================================================================

int main_with_gps(int argc, char** argv) {
    ros::init(argc, argv, "state_estimation_node");
    ros::NodeHandle nh;

    // 原有的参数读取...
    
    // 添加GPS相关参数
    nh.param<bool>("gps/enable_correction", enable_gps_correction, true);
    nh.param<double>("gps/height_correction_threshold", gps_height_correction_threshold, 0.3);
    nh.param<double>("gps/correction_rate", gps_correction_rate, 0.1);

    // 原有的订阅者...
    
    // 添加GPS订阅者
    ros::Subscriber sub_gps = nh.subscribe("/gps/fix", 200, gps_callback);
    // 或者根据实际的GPS topic名称调整，可能是：
    // ros::Subscriber sub_gps = nh.subscribe("/rtk/fix", 200, gps_callback);
    // ros::Subscriber sub_gps = nh.subscribe("/ublox_gps/fix", 200, gps_callback);

    // 原有的主循环，但调用修改后的execute函数
    signal(SIGINT, SigHandle);
    ros::Rate rate(5000);
    bool status = ros::ok();
    while (status)
    {
        if (flg_exit) break;
        ros::spinOnce();
        if(sync_packages(Measures))
        {
            execute_with_gps();  // 使用GPS增强的execute函数
        }
        status = ros::ok();
        rate.sleep();
    }
    
    return 0;
}

// ============================================================================
// 8. 配置文件修改建议 (rs16_rotation_v2.yaml)
// ============================================================================

/*
在配置文件中添加GPS相关参数：

gps:
    enable_correction: true
    height_correction_threshold: 0.3    # 高度校正阈值(米)
    correction_rate: 0.1                # 校正速率(0-1)
    loop_closure_distance: 3.0          # 回环检测距离(米)
    rtk_position_std: 0.02              # RTK位置标准差(米)
    gps_position_std: 1.0               # 普通GPS位置标准差(米)
    timeout: 0.5                        # GPS数据超时时间(秒)

common:
    gps_topic: "/gps/fix"               # GPS topic名称
    # 或者根据实际情况设置为：
    # gps_topic: "/rtk/fix"
    # gps_topic: "/ublox_gps/fix"
*/
