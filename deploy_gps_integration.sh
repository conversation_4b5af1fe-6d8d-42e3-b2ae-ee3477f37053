#!/bin/bash

# αLiDAR GPS集成自动部署脚本
# Ubuntu 20.04 + ROS Noetic

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 配置变量
WORKSPACE_PATH="$HOME/alpha_lidar_GPS/software/alpha_lidar_ws"
BACKUP_DIR="$HOME/alpha_lidar_backup/$(date +%Y%m%d_%H%M%S)"
STATE_ESTIMATION_PATH="$WORKSPACE_PATH/src/state_estimation"

echo "=========================================="
echo "αLiDAR GPS集成自动部署脚本"
echo "=========================================="

# 1. 环境检查
log_step "1. 检查环境..."

if [ ! -d "$WORKSPACE_PATH" ]; then
    log_error "工作空间不存在: $WORKSPACE_PATH"
    exit 1
fi

if [ ! -d "$STATE_ESTIMATION_PATH" ]; then
    log_error "state_estimation包不存在: $STATE_ESTIMATION_PATH"
    exit 1
fi

log_info "环境检查通过"

# 2. 安装依赖
log_step "2. 安装系统依赖..."

sudo apt update
sudo apt install -y \
    libgeographic-dev libgeographic19 \
    libeigen3-dev \
    libpcl-dev \
    libopencv-dev \
    python3-matplotlib \
    python3-numpy \
    build-essential \
    cmake

# 验证GeographicLib安装
if pkg-config --exists geographic; then
    GEOGRAPHIC_VERSION=$(pkg-config --modversion geographic)
    log_info "GeographicLib已安装: $GEOGRAPHIC_VERSION"
else
    log_error "GeographicLib安装失败"
    exit 1
fi

# 3. 备份原始代码
log_step "3. 备份原始代码..."

mkdir -p "$BACKUP_DIR"
cp -r "$STATE_ESTIMATION_PATH" "$BACKUP_DIR/"
log_info "原始代码已备份到: $BACKUP_DIR"

# 4. 修改CMakeLists.txt
log_step "4. 修改CMakeLists.txt..."

cd "$STATE_ESTIMATION_PATH"
cp CMakeLists.txt CMakeLists.txt.bak

# 检查是否已经添加了GeographicLib
if ! grep -q "geographic" CMakeLists.txt; then
    cat >> CMakeLists.txt << 'EOF'

# GPS集成相关依赖
find_package(PkgConfig REQUIRED)
pkg_check_modules(GEOGRAPHIC REQUIRED geographic)

# 添加到include目录
include_directories(${GEOGRAPHIC_INCLUDE_DIRS})
EOF

    # 修改target_link_libraries
    sed -i '/target_link_libraries(state_estimation_node/,/)/c\
target_link_libraries(state_estimation_node\
  ${catkin_LIBRARIES}\
  ${PCL_LIBRARIES}\
  ${PYTHON_LIBRARIES}\
  ${GEOGRAPHIC_LIBRARIES}\
)' CMakeLists.txt

    log_info "CMakeLists.txt已更新"
else
    log_info "CMakeLists.txt已包含GeographicLib配置"
fi

# 5. 修改package.xml
log_step "5. 修改package.xml..."

cp package.xml package.xml.bak

if ! grep -q "libgeographic-dev" package.xml; then
    sed -i '/<\/package>/i\  <build_depend>libgeographic-dev</build_depend>' package.xml
    sed -i '/<\/package>/i\  <run_depend>libgeographic-dev</run_depend>' package.xml
    log_info "package.xml已更新"
else
    log_info "package.xml已包含GeographicLib依赖"
fi

# 6. 检查GPS topic名称
log_step "6. 检查GPS topic名称..."

BAG_FILE="$HOME/datasets/UM982loop_715std_maximum_synced.bag"
if [ -f "$BAG_FILE" ]; then
    log_info "分析bag文件中的GPS topic..."
    
    # 尝试获取GPS相关的topic
    GPS_TOPICS=$(rosbag info "$BAG_FILE" 2>/dev/null | grep -i -E "(gps|rtk|fix|navsat)" | head -5 || echo "")
    
    if [ -n "$GPS_TOPICS" ]; then
        log_info "发现的GPS相关topic:"
        echo "$GPS_TOPICS"
        
        # 提取第一个可能的GPS topic名称
        GPS_TOPIC=$(echo "$GPS_TOPICS" | head -1 | awk '{print $1}' || echo "/ublox_gps/fix")
        log_info "将使用GPS topic: $GPS_TOPIC"
    else
        GPS_TOPIC="/ublox_gps/fix"
        log_warn "未能自动检测GPS topic，使用默认: $GPS_TOPIC"
    fi
else
    GPS_TOPIC="/ublox_gps/fix"
    log_warn "bag文件不存在，使用默认GPS topic: $GPS_TOPIC"
fi

# 7. 修改launch文件
log_step "7. 修改launch文件..."

cd launch
cp mapping_robosense.launch mapping_robosense.launch.bak

# 添加GPS topic重映射
if ! grep -q "gps" mapping_robosense.launch; then
    sed -i '/<\/launch>/i\    <!-- GPS topic重映射 -->' mapping_robosense.launch
    sed -i "/<\/launch>/i\    <remap from=\"/gps/fix\" to=\"$GPS_TOPIC\"/>" mapping_robosense.launch
    log_info "launch文件已更新，GPS topic: $GPS_TOPIC"
else
    log_info "launch文件已包含GPS配置"
fi

# 8. 修改配置文件
log_step "8. 修改配置文件..."

cd ../config
cp rs16_rotation_v2.yaml rs16_rotation_v2.yaml.bak

# 添加GPS配置
if ! grep -q "gps:" rs16_rotation_v2.yaml; then
    cat >> rs16_rotation_v2.yaml << 'EOF'

# GPS集成配置
gps:
    enable_correction: true
    height_correction_threshold: 0.3    # 高度校正阈值(米)
    correction_rate: 0.1                # 校正速率(0-1)
    loop_closure_distance: 3.0          # 回环检测距离(米)
    rtk_position_std: 0.02              # RTK位置标准差(米)
    gps_position_std: 1.0               # 普通GPS位置标准差(米)
    timeout: 0.5                        # GPS数据超时时间(秒)
EOF
    log_info "配置文件已更新"
else
    log_info "配置文件已包含GPS配置"
fi

# 9. 提示用户修改源代码
log_step "9. 源代码修改提示..."

echo ""
log_warn "重要提示: 需要手动修改voxelMapping.cpp源代码"
echo "有两种方式:"
echo ""
echo "方式1 (推荐): 使用提供的集成版本"
echo "  cp voxelMapping_gps_integration.cpp $STATE_ESTIMATION_PATH/src/voxelMapping.cpp"
echo ""
echo "方式2: 手动修改现有文件"
echo "  参考 alpha_lidar_gps_modification.cpp 中的修改建议"
echo "  主要修改点:"
echo "  - 添加GPS相关头文件和全局变量"
echo "  - 添加GPS回调函数和处理函数"
echo "  - 在execute()函数中添加GPS处理逻辑"
echo "  - 在main()函数中添加GPS订阅"
echo ""

read -p "是否现在使用集成版本替换源文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "voxelMapping_gps_integration.cpp" ]; then
        cp voxelMapping_gps_integration.cpp "$STATE_ESTIMATION_PATH/src/voxelMapping.cpp"
        log_info "已使用GPS集成版本替换voxelMapping.cpp"
    else
        log_error "找不到voxelMapping_gps_integration.cpp文件"
        log_info "请手动复制该文件到当前目录后重新运行"
    fi
else
    log_info "请手动修改voxelMapping.cpp文件"
fi

# 10. 编译系统
log_step "10. 编译系统..."

cd "$WORKSPACE_PATH"

# 清理之前的编译结果
rm -rf build/ devel/

# 设置ROS环境
source /opt/ros/noetic/setup.bash

# 编译
log_info "开始编译..."
if catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation" -j4; then
    log_info "✅ 编译成功!"
else
    log_error "❌ 编译失败"
    echo ""
    echo "常见编译错误解决方案:"
    echo "1. 检查GeographicLib是否正确安装"
    echo "2. 检查voxelMapping.cpp是否正确修改"
    echo "3. 检查CMakeLists.txt配置"
    echo ""
    echo "编译日志已保存，请检查错误信息"
    exit 1
fi

# 11. 创建启动脚本
log_step "11. 创建启动脚本..."

cat > "$HOME/start_alpha_lidar_gps.sh" << EOF
#!/bin/bash

# αLiDAR GPS集成启动脚本

# 设置环境
source /opt/ros/noetic/setup.bash
source $WORKSPACE_PATH/devel/setup.bash

# 检查bag文件
BAG_FILE="$BAG_FILE"
if [ ! -f "\$BAG_FILE" ]; then
    echo "错误: 找不到bag文件 \$BAG_FILE"
    echo "请确认bag文件路径正确"
    exit 1
fi

echo "=========================================="
echo "αLiDAR GPS集成系统启动"
echo "=========================================="
echo "GPS Topic: $GPS_TOPIC"
echo "Bag文件: \$BAG_FILE"
echo ""
echo "请在另一个终端中运行以下命令播放bag文件:"
echo "rosbag play \$BAG_FILE --pause"
echo "然后按空格键开始播放"
echo ""

# 启动系统
roslaunch state_estimation mapping_robosense.launch
EOF

chmod +x "$HOME/start_alpha_lidar_gps.sh"
log_info "启动脚本已创建: $HOME/start_alpha_lidar_gps.sh"

# 12. 创建监控脚本
cat > "$HOME/monitor_gps.sh" << EOF
#!/bin/bash

# GPS数据监控脚本

echo "GPS数据监控工具"
echo "=================="

# 检查GPS topic是否存在
echo "1. 检查GPS topic..."
if rostopic list | grep -q "$GPS_TOPIC"; then
    echo "✅ GPS topic存在: $GPS_TOPIC"
else
    echo "❌ GPS topic不存在: $GPS_TOPIC"
    echo "可用的topic:"
    rostopic list | grep -i -E "(gps|rtk|fix|navsat)" || echo "未找到GPS相关topic"
    exit 1
fi

# 监控GPS数据频率
echo ""
echo "2. GPS数据频率:"
timeout 10 rostopic hz $GPS_TOPIC

# 显示GPS数据样本
echo ""
echo "3. GPS数据样本:"
timeout 5 rostopic echo $GPS_TOPIC -n 1

echo ""
echo "监控完成"
EOF

chmod +x "$HOME/monitor_gps.sh"
log_info "监控脚本已创建: $HOME/monitor_gps.sh"

# 13. 部署完成总结
echo ""
echo "=========================================="
echo "部署完成总结"
echo "=========================================="

log_info "✅ GPS集成部署完成!"
echo ""
echo "生成的文件:"
echo "- 启动脚本: $HOME/start_alpha_lidar_gps.sh"
echo "- 监控脚本: $HOME/monitor_gps.sh"
echo "- 代码备份: $BACKUP_DIR"
echo ""
echo "快速启动命令 (在不同终端窗口中执行):"
echo ""
echo "窗口1: 启动roscore"
echo "  roscore"
echo ""
echo "窗口2: 启动αLiDAR系统"
echo "  $HOME/start_alpha_lidar_gps.sh"
echo ""
echo "窗口3: 播放bag文件"
echo "  cd ~/datasets"
echo "  rosbag play UM982loop_715std_maximum_synced.bag --pause"
echo "  # 按空格键开始播放"
echo ""
echo "窗口4: 监控GPS数据 (可选)"
echo "  $HOME/monitor_gps.sh"
echo ""
echo "预期看到的日志信息:"
echo "- 'GPS坐标原点已设置'"
echo "- '起始位置已记录'"
echo "- 'GPS高度校正'"
echo "- 'GPS回环检测'"
echo ""
echo "如有问题，请检查:"
echo "1. GPS topic名称是否正确: $GPS_TOPIC"
echo "2. voxelMapping.cpp是否正确修改"
echo "3. 编译是否成功"
echo ""
log_info "部署脚本执行完成!"
