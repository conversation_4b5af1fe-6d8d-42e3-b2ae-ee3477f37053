#!/bin/bash

# 点云数据同步修复脚本

echo "=========================================="
echo "🔧 点云数据同步问题修复"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查编译结果
if [ ! -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "❌ SLAM节点未编译，请先运行: ./complete_rebuild.sh"
    exit 1
fi

echo ""
echo "📊 分析bag文件数据"
echo "=================="
rosbag info "$BAG_FILE"

echo ""
echo "🔧 问题诊断和修复"
echo "================"

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo ""
echo "步骤1: 测试点云数据接收"
echo "======================"

# 先测试能否正常接收点云数据
echo "启动bag播放进行测试..."
timeout 10 rosbag play "$BAG_FILE" --clock &
TEST_BAG_PID=$!

sleep 3

echo "检查topic可用性..."
TOPICS=$(rostopic list)
echo "可用topic:"
echo "$TOPICS"

if echo "$TOPICS" | grep -q "/velodyne_points"; then
    echo "✅ 点云topic存在"
    
    echo ""
    echo "测试点云数据接收..."
    POINTCLOUD_TEST=$(timeout 5 rostopic echo /velodyne_points -n 1 2>/dev/null)
    
    if [ -n "$POINTCLOUD_TEST" ]; then
        echo "✅ 点云数据接收正常"
        
        # 分析点云数据
        WIDTH=$(echo "$POINTCLOUD_TEST" | grep "width:" | awk '{print $2}')
        HEIGHT=$(echo "$POINTCLOUD_TEST" | grep "height:" | awk '{print $2}')
        
        if [ -n "$WIDTH" ] && [ -n "$HEIGHT" ]; then
            TOTAL_POINTS=$((WIDTH * HEIGHT))
            echo "点云尺寸: ${WIDTH} x ${HEIGHT} = ${TOTAL_POINTS} 点"
            
            if [ $TOTAL_POINTS -gt 1000 ]; then
                echo "✅ 点云密度充足"
            else
                echo "⚠️  点云密度较低: ${TOTAL_POINTS}点"
            fi
        fi
    else
        echo "❌ 点云数据接收失败"
        echo "可能原因:"
        echo "1. bag文件播放速度问题"
        echo "2. topic名称不匹配"
        echo "3. 数据格式问题"
    fi
else
    echo "❌ 点云topic不存在"
fi

# 停止测试
kill $TEST_BAG_PID 2>/dev/null
sleep 2

echo ""
echo "步骤2: 创建同步启动解决方案"
echo "=========================="

# 创建专门的同步启动脚本
cat > start_slam_synchronized.sh << 'EOF'
#!/bin/bash

# SLAM同步启动脚本 - 修复版本

echo "🚀 SLAM同步启动 - 修复版本"
echo "========================="

BAG_FILE="$1"
if [ -z "$BAG_FILE" ] || [ ! -f "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/sync_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "🔧 设置SLAM参数"
echo "==============="

# 设置关键参数
rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
rosparam set /state_estimation_node/imu_topic "/imu/data"
rosparam set /state_estimation_node/gps_topic "/rtk/gnss"

# 点云处理参数
rosparam set /state_estimation_node/enable_point_cloud_validation true
rosparam set /state_estimation_node/min_points_threshold 500
rosparam set /state_estimation_node/skip_empty_scans true
rosparam set /state_estimation_node/wait_for_pointcloud true
rosparam set /state_estimation_node/pointcloud_timeout 10.0

# 处理参数 - 更保守
rosparam set /state_estimation_node/voxel_size 0.8
rosparam set /state_estimation_node/downsample_ratio 0.3
rosparam set /state_estimation_node/max_iterations 15
rosparam set /state_estimation_node/transformation_epsilon 1e-4

# 禁用可能导致崩溃的功能
rosparam set /state_estimation_node/enable_gps false
rosparam set /state_estimation_node/enable_loop_closure false
rosparam set /state_estimation_node/enable_intensity_processing false
rosparam set /state_estimation_node/enable_feature_extraction false

# 内存安全参数
rosparam set /state_estimation_node/max_points_per_scan 100000
rosparam set /state_estimation_node/enable_memory_monitoring true

echo "✅ 参数设置完成"

echo ""
echo "🎬 同步启动流程"
echo "==============="

echo "步骤1: 预启动bag播放（获取topic）"
rosbag play "$BAG_FILE" --clock --pause &
BAG_PID=$!
sleep 5

echo "步骤2: 验证topic可用性"
if ! rostopic list | grep -q "/velodyne_points"; then
    echo "❌ 点云topic不可用"
    kill $BAG_PID 2>/dev/null
    exit 1
fi
echo "✅ topic验证通过"

echo "步骤3: 启动SLAM节点"
rosrun state_estimation state_estimation_node &
SLAM_PID=$!
sleep 8

echo "步骤4: 检查SLAM节点状态"
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM节点启动失败"
    kill $BAG_PID 2>/dev/null
    exit 1
fi
echo "✅ SLAM节点运行中"

echo "步骤5: 开始数据播放"
# 停止暂停的播放
kill $BAG_PID 2>/dev/null
sleep 2

# 重新开始播放
echo "开始播放数据..."
rosbag play "$BAG_FILE" --clock --rate=0.5 &
BAG_PID=$!

echo "✅ 数据播放已开始（0.5倍速）"

echo ""
echo "🔍 监控系统状态"
echo "==============="

sleep 10

if ps -p $SLAM_PID > /dev/null; then
    echo "✅ SLAM节点运行稳定"
    
    # 检查数据流
    if timeout 5 rostopic hz /velodyne_points 2>/dev/null | grep -q "average rate"; then
        echo "✅ 点云数据流正常"
    else
        echo "⚠️  点云数据流异常"
    fi
    
    # 检查输出
    if rostopic list | grep -q "/aft_mapped_to_init"; then
        echo "✅ SLAM输出正常"
    else
        echo "⚠️  SLAM输出异常"
    fi
    
    echo ""
    echo "🎉 系统启动成功!"
    echo ""
    echo "监控命令："
    echo "  rostopic echo /aft_mapped_to_init"
    echo "  rostopic hz /velodyne_points"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    # 等待用户中断
    trap 'kill $SLAM_PID $BAG_PID 2>/dev/null; echo "系统已停止"; exit 0' SIGINT
    
    while ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; do
        sleep 5
        echo "系统运行中... $(date '+%H:%M:%S')"
    done
    
    if ! ps -p $SLAM_PID > /dev/null; then
        echo "❌ SLAM节点意外停止"
        echo "查看日志: tail ~/.ros/log/latest/state_estimation_node-*.log"
    fi
    
    if ! ps -p $BAG_PID > /dev/null; then
        echo "✅ 数据播放完成"
    fi
    
else
    echo "❌ SLAM节点崩溃"
    echo "查看崩溃日志: tail ~/.ros/log/latest/state_estimation_node-*.log"
    kill $BAG_PID 2>/dev/null
    exit 1
fi
EOF

chmod +x start_slam_synchronized.sh

echo "✅ 同步启动脚本已创建"

echo ""
echo "步骤3: 创建调试启动脚本"
echo "======================"

# 创建调试版本
cat > start_slam_debug.sh << 'EOF'
#!/bin/bash

# SLAM调试启动脚本

echo "🐛 SLAM调试启动"
echo "==============="

BAG_FILE="$1"
if [ -z "$BAG_FILE" ] || [ ! -f "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查GDB
if ! command -v gdb &> /dev/null; then
    echo "安装GDB..."
    sudo apt install -y gdb
fi

echo ""
echo "选择调试模式:"
echo "1) 使用GDB调试SLAM节点"
echo "2) 使用Valgrind检查内存问题"
echo "3) 仅输出详细日志"
echo ""

read -p "请选择 (1-3): " debug_choice

case $debug_choice in
    1)
        echo "🐛 GDB调试模式"
        echo "=============="
        
        # 启动bag播放
        rosbag play "$BAG_FILE" --clock --pause &
        BAG_PID=$!
        sleep 3
        
        echo "启动GDB调试SLAM节点..."
        echo "GDB命令提示:"
        echo "  run - 运行程序"
        echo "  bt - 显示调用栈"
        echo "  c - 继续执行"
        echo "  quit - 退出"
        echo ""
        
        # 设置参数
        rosparam set /state_estimation_node/enable_gps false
        rosparam set /state_estimation_node/skip_empty_scans true
        
        gdb --args devel/lib/state_estimation/state_estimation_node
        
        kill $BAG_PID 2>/dev/null
        ;;
        
    2)
        echo "🔍 Valgrind内存检查"
        echo "=================="
        
        if ! command -v valgrind &> /dev/null; then
            echo "安装Valgrind..."
            sudo apt install -y valgrind
        fi
        
        # 启动bag播放
        rosbag play "$BAG_FILE" --clock --pause &
        BAG_PID=$!
        sleep 3
        
        echo "启动Valgrind检查..."
        rosparam set /state_estimation_node/enable_gps false
        
        valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all \
                 --track-origins=yes --verbose \
                 devel/lib/state_estimation/state_estimation_node
        
        kill $BAG_PID 2>/dev/null
        ;;
        
    3)
        echo "📝 详细日志模式"
        echo "==============="
        
        # 设置详细日志
        export ROSCONSOLE_CONFIG_FILE=/tmp/rosconsole.conf
        cat > /tmp/rosconsole.conf << 'EOFLOG'
log4j.logger.ros=DEBUG
log4j.logger.ros.state_estimation=DEBUG
EOFLOG
        
        # 启动系统
        ./start_slam_synchronized.sh "$BAG_FILE"
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac
EOF

chmod +x start_slam_debug.sh

echo "✅ 调试启动脚本已创建"

echo ""
echo "=========================================="
echo "点云同步修复完成"
echo "=========================================="
echo ""
echo "🎯 使用方法:"
echo ""
echo "1. 正常启动:"
echo "   ./start_slam_synchronized.sh $BAG_FILE"
echo ""
echo "2. 调试启动:"
echo "   ./start_slam_debug.sh $BAG_FILE"
echo ""
echo "3. 手动分步启动:"
echo "   # 终端1: rosbag play $BAG_FILE --clock --pause"
echo "   # 终端2: rosrun state_estimation state_estimation_node"
echo "   # 终端1: 按空格键开始播放"
