#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create a complete bagpy-fixed spec file
"""

import os
import sys
from pathlib import Path

def find_bagpy_files():
    """查找bagpy的所有文件"""
    try:
        import bagpy
        bagpy_path = Path(bagpy.__file__).parent
        print(f"✅ 找到bagpy路径: {bagpy_path}")
        
        # 收集所有bagpy文件
        data_files = []
        
        # 1. 确保version文件存在
        version_file = bagpy_path / 'version'
        if not version_file.exists():
            print(f"⚠️  创建缺失的version文件: {version_file}")
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
        
        # 2. 收集所有非Python文件
        for root, dirs, files in os.walk(bagpy_path):
            for file in files:
                file_path = Path(root) / file
                # 包含所有文件，不仅仅是非Python文件
                rel_path = file_path.relative_to(bagpy_path.parent)
                data_files.append((str(file_path), str(rel_path.parent)))
                print(f"📁 添加文件: {rel_path}")
        
        return data_files
        
    except ImportError:
        print("❌ bagpy未安装，请先安装: pip install bagpy")
        return []
    except Exception as e:
        print(f"❌ 查找bagpy文件时出错: {e}")
        return []

def create_complete_spec():
    """创建完整的spec文件"""
    print("📝 创建完整的bagpy修复spec文件...")
    
    # 查找bagpy文件
    bagpy_files = find_bagpy_files()
    
    if not bagpy_files:
        print("❌ 无法找到bagpy文件")
        return False
    
    # 格式化数据文件列表
    datas_lines = []
    for src, dst in bagpy_files:
        # 使用原始字符串避免路径问题
        datas_lines.append(f"    (r'{src}', r'{dst}'),")
    
    datas_str = '\n'.join(datas_lines)
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Complete bagpy-fixed spec file

import sys
from pathlib import Path

block_cipher = None

# 完整的隐藏导入列表
hiddenimports = [
    # bagpy相关
    'bagpy',
    'bagpy.bagreader',
    'bagpy.bagpy',
    
    # ROS相关
    'rosbag',
    'rospy',
    'genpy',
    'std_msgs',
    'sensor_msgs',
    'geometry_msgs',
    'nav_msgs',
    
    # 数据处理
    'pandas',
    'numpy',
    'matplotlib',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    
    # GUI
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    
    # 系统
    'threading',
    'collections',
    'math',
    'time',
    'locale',
    'pathlib',
    'tempfile',
    'traceback',
    'csv',
    'random',
]

# 完整的数据文件列表 - 包含所有bagpy文件
datas = [
{datas_str}
]

# 二进制文件
binaries = []

a = Analysis(
    ['gps_gui_analyzer_fixed.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GPS_3D_Analyzer_BagpyFixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("GPS_3D_Analyzer_BagpyFixed.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 完整bagpy修复spec文件创建完成")
    return True

def main():
    """主函数"""
    print("🔧 创建完整的bagpy修复spec文件")
    print("=" * 50)
    
    if create_complete_spec():
        print("\n✅ Spec文件创建成功!")
        print("📁 文件: GPS_3D_Analyzer_BagpyFixed.spec")
        print("\n🚀 下一步: 运行以下命令构建:")
        print("pyinstaller --clean --noconfirm GPS_3D_Analyzer_BagpyFixed.spec")
    else:
        print("❌ Spec文件创建失败")

if __name__ == "__main__":
    main()
