#!/bin/bash

# GPS平面约束问题诊断和解决脚本

echo "=========================================="
echo "🔍 GPS平面约束问题诊断工具"
echo "=========================================="
echo ""
echo "问题描述: GPS平面约束导致SLAM点云匹配错误"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "选择诊断模式："
echo "1) 快速诊断 - 检查当前GPS约束状态"
echo "2) 深度分析 - 启动完整分析系统"
echo "3) 智能修复 - 自动调整GPS约束参数"
echo "4) 禁用GPS约束 - 临时解决方案"
echo "5) 启用智能控制 - 动态开关GPS约束"
echo ""

read -p "请选择模式 (1-5): " choice

case $choice in
    1)
        echo "🔍 快速诊断GPS约束状态..."
        echo ""
        
        echo "=== 当前GPS约束参数 ==="
        echo "平面约束启用: $(rosparam get /gps/enable_plane_constraint 2>/dev/null || echo '未设置')"
        echo "约束权重: $(rosparam get /gps/plane_constraint_weight 2>/dev/null || echo '未设置')"
        echo "校正阈值: $(rosparam get /gps/xy_correction_threshold 2>/dev/null || echo '未设置')"
        echo "校正率: $(rosparam get /gps/xy_correction_rate 2>/dev/null || echo '未设置')"
        
        echo ""
        echo "=== GPS数据质量 ==="
        timeout 5 rostopic echo /rtk/gnss -n 1 | grep -E "(status|latitude|longitude)" | head -3
        
        echo ""
        echo "=== SLAM位姿稳定性 ==="
        echo "检查最近的位姿变化..."
        timeout 10 rostopic echo /aft_mapped_to_init -n 3 | grep -A3 "position:" | head -12
        
        echo ""
        echo "=== 建议 ==="
        gps_status=$(timeout 3 rostopic echo /rtk/gnss -n 1 | grep "status:" | grep -o '[0-9-]*' | head -1)
        if [ ! -z "$gps_status" ] && [ "$gps_status" -lt 0 ]; then
            echo "⚠️  GPS质量较差 (status=$gps_status)，建议禁用平面约束"
        else
            echo "✅ GPS质量正常，可以使用平面约束"
        fi
        ;;
        
    2)
        echo "🔬 启动深度分析系统..."
        echo ""
        
        # 启动GPS约束分析器
        rosrun state_estimation gps_constraint_analyzer.py &
        ANALYZER_PID=$!
        
        echo "分析器已启动 (PID: $ANALYZER_PID)"
        echo "等待分析结果..."
        sleep 5
        
        echo ""
        echo "=== 分析结果 ==="
        timeout 30 rostopic echo /gps_constraint_analysis -n 1 | head -50
        
        echo ""
        echo "=== 优化建议 ==="
        timeout 10 rostopic echo /gps_constraint_recommendations -n 1
        
        # 清理
        kill $ANALYZER_PID 2>/dev/null
        ;;
        
    3)
        echo "🔧 智能修复GPS约束参数..."
        echo ""
        
        # 获取当前GPS状态
        gps_status=$(timeout 3 rostopic echo /rtk/gnss -n 1 | grep "status:" | grep -o '[0-9-]*' | head -1)
        
        if [ ! -z "$gps_status" ]; then
            echo "当前GPS状态: $gps_status"
            
            if [ "$gps_status" -ge 0 ]; then
                echo "GPS质量良好，设置保守约束参数..."
                rosparam set /gps/enable_plane_constraint true
                rosparam set /gps/plane_constraint_weight 0.1
                rosparam set /gps/xy_correction_threshold 0.8
                rosparam set /gps/xy_correction_rate 0.03
                echo "✅ 已设置保守约束参数"
            else
                echo "GPS质量较差，设置宽松约束参数..."
                rosparam set /gps/enable_plane_constraint true
                rosparam set /gps/plane_constraint_weight 0.05
                rosparam set /gps/xy_correction_threshold 1.5
                rosparam set /gps/xy_correction_rate 0.01
                echo "✅ 已设置宽松约束参数"
            fi
        else
            echo "无法获取GPS状态，使用默认安全参数..."
            rosparam set /gps/enable_plane_constraint false
            echo "✅ 已禁用GPS约束作为安全措施"
        fi
        ;;
        
    4)
        echo "🚫 禁用GPS平面约束..."
        echo ""
        
        # 禁用所有GPS约束
        rosparam set /gps/enable_plane_constraint false
        rosparam set /gps/plane_constraint_weight 0.0
        rosparam set /gps/enable_correction false
        rosparam set /gps/enable_loop_closure false
        
        # 发布禁用信号
        rostopic pub /gps_constraint_control std_msgs/Bool "data: false" -1
        
        echo "✅ GPS平面约束已禁用"
        echo ""
        echo "注意: 这是临时解决方案，可能影响长期精度"
        echo "建议使用智能控制模式 (选项5)"
        ;;
        
    5)
        echo "🤖 启用智能GPS约束控制..."
        echo ""
        
        # 启动智能控制器
        rosrun state_estimation smart_gps_constraint_controller.py &
        CONTROLLER_PID=$!
        
        # 启动分析器
        rosrun state_estimation gps_constraint_analyzer.py &
        ANALYZER_PID=$!
        
        echo "智能控制系统已启动:"
        echo "  控制器 PID: $CONTROLLER_PID"
        echo "  分析器 PID: $ANALYZER_PID"
        echo ""
        echo "系统将自动监控SLAM匹配质量并动态调整GPS约束"
        echo ""
        echo "监控命令:"
        echo "  实时状态: rostopic echo /smart_constraint_status"
        echo "  匹配质量: rostopic echo /slam_matching_quality"
        echo "  约束控制: rostopic echo /gps_constraint_control"
        echo ""
        echo "按任意键停止智能控制..."
        read -n 1
        
        # 清理
        kill $CONTROLLER_PID $ANALYZER_PID 2>/dev/null
        echo "智能控制系统已停止"
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "诊断完成"
echo "=========================================="
echo ""
echo "常见解决方案总结:"
echo ""
echo "1. 🚫 临时禁用GPS约束:"
echo "   rosparam set /gps/enable_plane_constraint false"
echo ""
echo "2. 🔧 调整约束参数:"
echo "   rosparam set /gps/plane_constraint_weight 0.05"
echo "   rosparam set /gps/xy_correction_threshold 1.0"
echo ""
echo "3. 🤖 使用智能控制:"
echo "   rosrun state_estimation smart_gps_constraint_controller.py"
echo ""
echo "4. 📊 持续监控:"
echo "   rostopic echo /gps_constraint_analysis"
echo ""
echo "根据您的具体情况选择合适的解决方案。"
