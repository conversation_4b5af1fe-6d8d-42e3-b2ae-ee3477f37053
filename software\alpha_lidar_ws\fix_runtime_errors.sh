#!/bin/bash

# 修复运行时错误的脚本

echo "=========================================="
echo "🔧 修复运行时错误"
echo "=========================================="

echo "步骤1: 安装Python依赖"

# 安装psutil
echo "安装psutil..."
pip3 install psutil

# 如果pip3安装失败，使用apt
if [ $? -ne 0 ]; then
    echo "使用apt安装psutil..."
    sudo apt update
    sudo apt install -y python3-psutil
fi

# 安装其他可能需要的Python包
pip3 install numpy scipy matplotlib

echo "✓ Python依赖安装完成"

echo ""
echo "步骤2: 安装ROS依赖"

sudo apt update
sudo apt install -y \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-tf2-sensor-msgs \
    ros-noetic-message-filters \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions

echo "✓ ROS依赖安装完成"

echo ""
echo "步骤3: 修复脚本错误"

# 检查并修复enhanced_gps_loop_closure_optimizer.py
if [ -f "src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py" ]; then
    echo "修复enhanced_gps_loop_closure_optimizer.py..."
    
    # 检查topic名称是否正确
    if grep -q "/force_loop_closure" src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py; then
        sed -i 's|/force_loop_closure|/intelligent_force_loop_closure|g' src/state_estimation/scripts/enhanced_gps_loop_closure_optimizer.py
        echo "✓ 修复了topic名称"
    fi
fi

# 检查并修复simple_performance_monitor.py
if [ -f "src/state_estimation/scripts/simple_performance_monitor.py" ]; then
    echo "检查simple_performance_monitor.py..."
    
    # 添加psutil导入检查
    cat > temp_fix_monitor.py << 'EOF'
#!/usr/bin/env python3
import sys
try:
    import psutil
except ImportError:
    print("Warning: psutil not available, performance monitoring disabled")
    sys.exit(0)

# 继续原来的代码...
EOF
    
    # 备份原文件
    cp src/state_estimation/scripts/simple_performance_monitor.py src/state_estimation/scripts/simple_performance_monitor.py.backup
    
    # 在文件开头添加psutil检查
    head -n 8 src/state_estimation/scripts/simple_performance_monitor.py > temp_header.py
    echo "" >> temp_header.py
    echo "# 检查psutil是否可用" >> temp_header.py
    echo "try:" >> temp_header.py
    echo "    import psutil" >> temp_header.py
    echo "except ImportError:" >> temp_header.py
    echo "    rospy.logwarn('psutil not available, performance monitoring disabled')" >> temp_header.py
    echo "    sys.exit(0)" >> temp_header.py
    echo "" >> temp_header.py
    
    tail -n +9 src/state_estimation/scripts/simple_performance_monitor.py >> temp_header.py
    mv temp_header.py src/state_estimation/scripts/simple_performance_monitor.py
    
    echo "✓ 修复了performance monitor"
fi

echo ""
echo "步骤4: 设置权限"
chmod +x src/state_estimation/scripts/*.py
echo "✓ 权限设置完成"

echo ""
echo "步骤5: 重新编译"
source /opt/ros/noetic/setup.bash
catkin_make --only-pkg-with-deps state_estimation

if [ $? -eq 0 ]; then
    echo "✓ 重新编译成功"
    source devel/setup.bash
else
    echo "⚠️  重新编译失败，但运行时错误已修复"
fi

echo ""
echo "步骤6: 验证修复"

echo "检查Python模块..."
python3 -c "import psutil; print('✓ psutil可用')" 2>/dev/null || echo "❌ psutil不可用"
python3 -c "import numpy; print('✓ numpy可用')" 2>/dev/null || echo "❌ numpy不可用"
python3 -c "import rospy; print('✓ rospy可用')" 2>/dev/null || echo "❌ rospy不可用"

echo ""
echo "检查ROS包..."
rospack find tf2_ros >/dev/null 2>&1 && echo "✓ tf2_ros可用" || echo "❌ tf2_ros不可用"
rospack find pcl_ros >/dev/null 2>&1 && echo "✓ pcl_ros可用" || echo "❌ pcl_ros不可用"

echo ""
echo "🎉 运行时错误修复完成!"
echo ""
echo "现在可以重新启动系统:"
echo "1. 完整GPS功能:"
echo "   ./start_gps_slam_robust.sh"
echo ""
echo "2. 解决GPS约束冲突:"
echo "   ./solve_gps_constraint_conflict.sh"
echo ""
echo "3. 解决首尾偏差:"
echo "   ./solve_start_end_offset.sh"

echo ""
echo "=========================================="
echo "修复完成!"
echo "=========================================="
