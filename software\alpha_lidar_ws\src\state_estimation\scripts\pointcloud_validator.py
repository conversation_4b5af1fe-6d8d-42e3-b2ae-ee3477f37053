#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点云数据验证器
验证点云数据的完整性和格式，帮助诊断SLAM崩溃问题
"""

import rospy
from sensor_msgs.msg import PointCloud2
import sensor_msgs.point_cloud2 as pc2
from std_msgs.msg import String
import json
import numpy as np
import struct

class PointCloudValidator:
    def __init__(self):
        rospy.init_node('pointcloud_validator', anonymous=True)
        
        # 参数配置
        self.lidar_topic = rospy.get_param('~lidar_topic', '/velodyne_points')
        self.validation_count = rospy.get_param('~validation_count', 10)
        self.detailed_analysis = rospy.get_param('~detailed_analysis', True)
        
        # 统计变量
        self.total_received = 0
        self.valid_clouds = 0
        self.empty_clouds = 0
        self.error_clouds = 0
        
        # 数据统计
        self.point_counts = []
        self.field_types = set()
        self.intensity_ranges = []
        
        # 订阅器
        self.pointcloud_sub = rospy.Subscriber(self.lidar_topic, PointCloud2, 
                                              self.pointcloud_callback, queue_size=1)
        
        # 发布器
        self.validation_report_pub = rospy.Publisher('/pointcloud_validation_report', 
                                                    String, queue_size=1)
        
        rospy.loginfo("🔍 点云数据验证器启动")
        rospy.loginfo("监听topic: %s", self.lidar_topic)
        rospy.loginfo("验证帧数: %d", self.validation_count)
        
    def pointcloud_callback(self, msg):
        """处理点云数据"""
        try:
            self.total_received += 1
            
            rospy.loginfo("📊 验证点云 #%d", self.total_received)
            
            # 基本信息验证
            validation_result = self.validate_basic_info(msg)
            
            # 详细数据验证
            if self.detailed_analysis and validation_result['is_valid']:
                detailed_result = self.validate_detailed_data(msg)
                validation_result.update(detailed_result)
            
            # 更新统计
            self.update_statistics(validation_result)
            
            # 发布验证报告
            self.publish_validation_report(validation_result)
            
            # 检查是否完成验证
            if self.total_received >= self.validation_count:
                self.publish_final_report()
                rospy.loginfo("✅ 点云验证完成，共验证 %d 帧", self.total_received)
                rospy.signal_shutdown("验证完成")
                
        except Exception as e:
            rospy.logerr("验证点云时出错: %s", str(e))
            self.error_clouds += 1
    
    def validate_basic_info(self, msg):
        """验证基本信息"""
        result = {
            'frame_id': self.total_received,
            'timestamp': msg.header.stamp.to_sec(),
            'width': msg.width,
            'height': msg.height,
            'total_points': msg.width * msg.height,
            'is_dense': msg.is_dense,
            'point_step': msg.point_step,
            'row_step': msg.row_step,
            'data_size': len(msg.data),
            'fields': [],
            'is_valid': True,
            'issues': []
        }
        
        # 检查字段信息
        for field in msg.fields:
            field_info = {
                'name': field.name,
                'offset': field.offset,
                'datatype': field.datatype,
                'count': field.count
            }
            result['fields'].append(field_info)
            self.field_types.add(field.name)
        
        # 验证点云尺寸
        if result['total_points'] == 0:
            result['is_valid'] = False
            result['issues'].append("点云为空")
            self.empty_clouds += 1
            rospy.logwarn("⚠️  点云 #%d 为空", self.total_received)
        elif result['total_points'] < 100:
            result['issues'].append(f"点云密度过低: {result['total_points']}点")
            rospy.logwarn("⚠️  点云 #%d 密度过低: %d点", self.total_received, result['total_points'])
        else:
            rospy.loginfo("✅ 点云 #%d 尺寸正常: %d点", self.total_received, result['total_points'])
        
        # 验证数据大小一致性
        expected_data_size = result['total_points'] * result['point_step']
        if result['data_size'] != expected_data_size:
            result['is_valid'] = False
            result['issues'].append(f"数据大小不一致: 期望{expected_data_size}, 实际{result['data_size']}")
            rospy.logerr("❌ 点云 #%d 数据大小不一致", self.total_received)
        
        # 验证必要字段
        field_names = [f['name'] for f in result['fields']]
        required_fields = ['x', 'y', 'z']
        missing_fields = [f for f in required_fields if f not in field_names]
        if missing_fields:
            result['is_valid'] = False
            result['issues'].append(f"缺少必要字段: {missing_fields}")
            rospy.logerr("❌ 点云 #%d 缺少字段: %s", self.total_received, missing_fields)
        
        return result
    
    def validate_detailed_data(self, msg):
        """详细数据验证"""
        result = {
            'coordinate_ranges': {},
            'intensity_stats': {},
            'nan_count': 0,
            'inf_count': 0
        }
        
        try:
            # 解析点云数据
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=False))
            
            if len(points) > 0:
                # 转换为numpy数组进行分析
                coords = np.array(points)
                
                # 检查NaN和Inf
                nan_mask = np.isnan(coords).any(axis=1)
                inf_mask = np.isinf(coords).any(axis=1)
                
                result['nan_count'] = int(np.sum(nan_mask))
                result['inf_count'] = int(np.sum(inf_mask))
                
                # 有效点的坐标范围
                valid_coords = coords[~(nan_mask | inf_mask)]
                if len(valid_coords) > 0:
                    result['coordinate_ranges'] = {
                        'x': {'min': float(np.min(valid_coords[:, 0])), 'max': float(np.max(valid_coords[:, 0]))},
                        'y': {'min': float(np.min(valid_coords[:, 1])), 'max': float(np.max(valid_coords[:, 1]))},
                        'z': {'min': float(np.min(valid_coords[:, 2])), 'max': float(np.max(valid_coords[:, 2]))}
                    }
                
                # 检查强度信息
                if 'intensity' in [f.name for f in msg.fields]:
                    try:
                        intensity_points = list(pc2.read_points(msg, field_names=("intensity",), skip_nans=True))
                        if intensity_points:
                            intensities = np.array([p[0] for p in intensity_points])
                            result['intensity_stats'] = {
                                'min': float(np.min(intensities)),
                                'max': float(np.max(intensities)),
                                'mean': float(np.mean(intensities)),
                                'std': float(np.std(intensities))
                            }
                            self.intensity_ranges.append(result['intensity_stats'])
                    except Exception as e:
                        rospy.logwarn("强度数据解析失败: %s", str(e))
                
                rospy.loginfo("📈 点云 #%d 详细分析: 有效点%d, NaN%d, Inf%d", 
                             self.total_received, len(valid_coords), 
                             result['nan_count'], result['inf_count'])
            
        except Exception as e:
            rospy.logerr("详细数据验证失败: %s", str(e))
            result['error'] = str(e)
        
        return result
    
    def update_statistics(self, validation_result):
        """更新统计信息"""
        if validation_result['is_valid']:
            self.valid_clouds += 1
            self.point_counts.append(validation_result['total_points'])
        
    def publish_validation_report(self, validation_result):
        """发布验证报告"""
        try:
            report = {
                'timestamp': rospy.Time.now().to_sec(),
                'frame_validation': validation_result,
                'cumulative_stats': {
                    'total_received': self.total_received,
                    'valid_clouds': self.valid_clouds,
                    'empty_clouds': self.empty_clouds,
                    'error_clouds': self.error_clouds
                }
            }
            
            report_msg = String()
            report_msg.data = json.dumps(report, indent=2)
            self.validation_report_pub.publish(report_msg)
            
        except Exception as e:
            rospy.logerr("发布验证报告失败: %s", str(e))
    
    def publish_final_report(self):
        """发布最终报告"""
        try:
            final_report = {
                'timestamp': rospy.Time.now().to_sec(),
                'validation_summary': {
                    'total_frames': self.total_received,
                    'valid_frames': self.valid_clouds,
                    'empty_frames': self.empty_clouds,
                    'error_frames': self.error_clouds,
                    'success_rate': (self.valid_clouds / self.total_received) * 100 if self.total_received > 0 else 0
                },
                'point_statistics': {
                    'min_points': int(np.min(self.point_counts)) if self.point_counts else 0,
                    'max_points': int(np.max(self.point_counts)) if self.point_counts else 0,
                    'mean_points': float(np.mean(self.point_counts)) if self.point_counts else 0,
                    'std_points': float(np.std(self.point_counts)) if self.point_counts else 0
                },
                'field_types_found': list(self.field_types),
                'intensity_analysis': {
                    'has_intensity': 'intensity' in self.field_types,
                    'intensity_ranges': self.intensity_ranges
                },
                'recommendations': self.generate_recommendations()
            }
            
            report_msg = String()
            report_msg.data = json.dumps(final_report, indent=2)
            self.validation_report_pub.publish(report_msg)
            
            # 打印总结
            rospy.loginfo("📋 点云验证总结:")
            rospy.loginfo("  总帧数: %d", self.total_received)
            rospy.loginfo("  有效帧: %d (%.1f%%)", self.valid_clouds, 
                         (self.valid_clouds / self.total_received) * 100)
            rospy.loginfo("  空帧数: %d", self.empty_clouds)
            rospy.loginfo("  错误帧: %d", self.error_clouds)
            
            if self.point_counts:
                rospy.loginfo("  平均点数: %.0f", np.mean(self.point_counts))
                rospy.loginfo("  点数范围: %d - %d", np.min(self.point_counts), np.max(self.point_counts))
            
            rospy.loginfo("  字段类型: %s", list(self.field_types))
            
        except Exception as e:
            rospy.logerr("发布最终报告失败: %s", str(e))
    
    def generate_recommendations(self):
        """生成建议"""
        recommendations = []
        
        if self.empty_clouds > 0:
            recommendations.append("检测到空点云，建议在SLAM中启用skip_empty_scans参数")
        
        if self.error_clouds > 0:
            recommendations.append("检测到数据错误，建议检查bag文件完整性")
        
        if self.point_counts:
            avg_points = np.mean(self.point_counts)
            if avg_points < 1000:
                recommendations.append("点云密度较低，建议降低voxel_size参数")
            elif avg_points > 100000:
                recommendations.append("点云密度很高，建议增加downsample_ratio参数")
        
        if 'intensity' not in self.field_types:
            recommendations.append("未检测到强度字段，建议禁用intensity_processing")
        
        success_rate = (self.valid_clouds / self.total_received) * 100 if self.total_received > 0 else 0
        if success_rate < 80:
            recommendations.append("点云数据质量较差，建议使用更保守的SLAM参数")
        
        return recommendations

def main():
    try:
        validator = PointCloudValidator()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("点云验证器错误: %s", str(e))

if __name__ == '__main__':
    main()
