# GPS轨迹分析工具

专门用于分析ROS bag文件中GPS数据的可视化工具，支持3D轨迹显示和质量分析。

## 🚀 功能特性

### 📊 数据分析
- **轨迹长度计算**: 精确计算GPS轨迹总长度
- **速度分析**: 平均速度、最大速度统计
- **GPS状态分布**: RTK固定解、SBAS定位等状态统计
- **位置精度**: X/Y/Z方向的标准差分析
- **回环检测**: 自动检测轨迹中的回环区域

### 🎨 3D可视化
- **彩色轨迹**: 根据GPS状态显示不同颜色
  - 🟢 绿色: RTK固定解 (最高精度)
  - 🔵 蓝色: SBAS定位
  - 🟡 黄色: GBAS定位
  - 🔴 红色: 无定位
- **时间渐变**: 轨迹线从蓝色(开始)到红色(结束)
- **坐标系**: 显示XYZ坐标轴
- **交互式**: 支持旋转、缩放、平移

### 📈 导出功能
- **2D轨迹图**: 保存高分辨率PNG图片
- **CSV数据**: 导出所有GPS数据点
- **质量报告**: 详细的文本分析报告

## 📦 安装

### 自动安装
```bash
chmod +x install_gps_analyzer.sh
./install_gps_analyzer.sh
```

### 手动安装
```bash
# 安装Python依赖
pip3 install --user rospkg rosbag numpy matplotlib open3d

# 设置执行权限
chmod +x gps_trajectory_analyzer.py
```

## 🛠️ 使用方法

### 基本用法
```bash
python3 gps_trajectory_analyzer.py your_bag_file.bag
```

### 指定GPS topic
```bash
python3 gps_trajectory_analyzer.py your_bag_file.bag --topic /rtk/gnss
```

### 完整功能
```bash
python3 gps_trajectory_analyzer.py your_bag_file.bag \
    --topic /rtk/gnss \
    --save-plot \
    --export-data
```

## 📋 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `bag_file` | ROS bag文件路径 | 必需 |
| `--topic` | GPS topic名称 | `/rtk/gnss` |
| `--save-plot` | 保存2D轨迹图 | 否 |
| `--export-data` | 导出CSV数据 | 否 |

## 📊 输出示例

### 控制台输出
```
GPS轨迹分析报告
========================================
总数据点: 15420
时间跨度: 1847.3 秒 (30.8 分钟)
轨迹长度: 2847.5 米
平均速度: 1.54 m/s (5.5 km/h)
最大速度: 8.32 m/s (29.9 km/h)

GPS状态分布:
  RTK_FIXED: 14892 (96.6%)
  SBAS_FIX: 528 (3.4%)

位置精度 (标准差):
  X: 0.023 m
  Y: 0.031 m
  Z: 0.045 m

颜色说明:
  绿色: RTK固定解 (最高精度)
  蓝色: SBAS定位
  黄色: GBAS定位
  红色: 无定位

轨迹线颜色: 蓝色(开始) → 红色(结束)
```

### 生成的文件
- `gps_trajectory_your_bag_file.png` - 2D轨迹图
- `gps_data_your_bag_file.csv` - GPS数据CSV文件

## 🎮 3D可视化操作

### 鼠标操作
- **左键拖拽**: 旋转视角
- **右键拖拽**: 平移视图
- **滚轮**: 缩放
- **中键**: 重置视角

### 键盘快捷键
- **Q**: 退出程序
- **R**: 重置视角
- **F**: 全屏切换

## 🔧 故障排除

### 常见问题

1. **找不到topic**
   ```
   错误: Topic /rtk/gnss 不存在于bag文件中
   ```
   **解决**: 使用 `rosbag info your_bag_file.bag` 查看可用topics

2. **Open3D显示问题**
   ```
   无法创建3D窗口
   ```
   **解决**: 确保系统支持OpenGL，或使用SSH时启用X11转发

3. **ROS依赖问题**
   ```
   ImportError: No module named 'rosbag'
   ```
   **解决**: 确保已source ROS环境并安装rosbag包

## 📝 技术细节

### GPS坐标转换
- 使用简化的平面投影将GPS坐标转换为局部坐标系
- 第一个有效GPS点作为原点
- 适用于小范围区域的轨迹分析

### 质量评估指标
- **RTK固定解比例**: 高精度GPS定位的百分比
- **位置标准差**: 轨迹点的离散程度
- **速度一致性**: 检测异常的速度变化

### 回环检测算法
- 基于欧几里得距离的简单回环检测
- 可调节的距离阈值
- 排除起始和结束区域的干扰

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License - 详见LICENSE文件
