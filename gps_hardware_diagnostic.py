#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS硬件诊断工具
专门用于诊断GPS硬件问题和提供修复建议
"""

import subprocess
import time
import os
import sys

class GPSHardwareDiagnostic:
    def __init__(self):
        self.diagnostic_results = {}
        
    def check_gps_hardware_connection(self):
        """检查GPS硬件连接"""
        print("🔍 检查GPS硬件连接...")
        
        # 检查USB设备
        try:
            result = subprocess.run(['lsusb'], capture_output=True, text=True)
            usb_devices = result.stdout
            
            gps_keywords = ['GPS', 'GNSS', 'u-blox', 'Trimble', 'Novatel']
            gps_found = any(keyword.lower() in usb_devices.lower() for keyword in gps_keywords)
            
            if gps_found:
                print("✅ 检测到GPS设备连接")
                return True
            else:
                print("❌ 未检测到GPS设备")
                return False
                
        except Exception as e:
            print(f"⚠️  无法检查USB设备: {e}")
            return None
    
    def check_serial_ports(self):
        """检查串口连接"""
        print("🔍 检查串口连接...")
        
        try:
            # 检查常见的GPS串口
            common_ports = ['/dev/ttyUSB0', '/dev/ttyUSB1', '/dev/ttyACM0', '/dev/ttyACM1']
            active_ports = []
            
            for port in common_ports:
                if os.path.exists(port):
                    active_ports.append(port)
                    print(f"✅ 发现串口: {port}")
            
            if active_ports:
                return active_ports
            else:
                print("❌ 未发现GPS串口")
                return []
                
        except Exception as e:
            print(f"⚠️  检查串口时出错: {e}")
            return []
    
    def check_gps_signal_strength(self):
        """检查GPS信号强度"""
        print("🛰️  检查GPS信号强度...")
        
        suggestions = [
            "确保GPS天线有清晰的天空视野",
            "检查天线是否正确连接",
            "远离高楼、树木等遮挡物",
            "检查是否有电磁干扰源（WiFi路由器、手机等）",
            "等待GPS冷启动完成（可能需要5-15分钟）"
        ]
        
        print("📡 GPS信号优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        
        return suggestions
    
    def check_ros_gps_node(self):
        """检查ROS GPS节点状态"""
        print("🤖 检查ROS GPS节点...")
        
        try:
            # 检查ROS节点
            result = subprocess.run(['rosnode', 'list'], capture_output=True, text=True)
            nodes = result.stdout
            
            gps_nodes = [line.strip() for line in nodes.split('\n') if 'gps' in line.lower() or 'gnss' in line.lower()]
            
            if gps_nodes:
                print("✅ 发现GPS相关节点:")
                for node in gps_nodes:
                    print(f"   - {node}")
                return gps_nodes
            else:
                print("❌ 未发现GPS节点")
                return []
                
        except Exception as e:
            print(f"⚠️  检查ROS节点时出错: {e}")
            return []
    
    def check_gps_topics(self):
        """检查GPS话题"""
        print("📡 检查GPS话题...")
        
        try:
            result = subprocess.run(['rostopic', 'list'], capture_output=True, text=True)
            topics = result.stdout
            
            gps_topics = [line.strip() for line in topics.split('\n') if 'gps' in line.lower() or 'gnss' in line.lower()]
            
            if gps_topics:
                print("✅ 发现GPS话题:")
                for topic in gps_topics:
                    print(f"   - {topic}")
                    
                    # 检查话题频率
                    try:
                        hz_result = subprocess.run(['rostopic', 'hz', topic], 
                                                 capture_output=True, text=True, timeout=5)
                        print(f"     频率: {hz_result.stdout.strip()}")
                    except:
                        print(f"     频率: 无法检测")
                
                return gps_topics
            else:
                print("❌ 未发现GPS话题")
                return []
                
        except Exception as e:
            print(f"⚠️  检查GPS话题时出错: {e}")
            return []
    
    def generate_fix_commands(self):
        """生成修复命令"""
        print("\n🔧 GPS问题修复命令:")
        
        commands = [
            "# 1. 重启GPS设备",
            "sudo systemctl restart gpsd",
            "",
            "# 2. 检查设备权限",
            "sudo chmod 666 /dev/ttyUSB0",
            "sudo chmod 666 /dev/ttyACM0",
            "",
            "# 3. 重启ROS GPS节点",
            "rosnode kill /gps_node",
            "roslaunch your_package gps.launch",
            "",
            "# 4. 检查GPS配置",
            "rosparam get /gps_node/",
            "",
            "# 5. 监控GPS数据",
            "rostopic echo /rtk/gnss",
            "",
            "# 6. 如果使用u-blox GPS，配置RTK",
            "# 确保base station和rover配置正确",
            "# 检查RTCM数据流"
        ]
        
        for cmd in commands:
            print(cmd)
        
        return commands
    
    def create_gps_test_script(self):
        """创建GPS测试脚本"""
        print("\n📝 创建GPS测试脚本...")
        
        test_script = '''#!/bin/bash
# GPS硬件测试脚本

echo "🔍 开始GPS硬件测试..."

# 1. 检查USB设备
echo "1. 检查USB设备:"
lsusb | grep -i "gps\\|gnss\\|u-blox\\|trimble\\|novatel"

# 2. 检查串口设备
echo "2. 检查串口设备:"
ls -la /dev/ttyUSB* /dev/ttyACM* 2>/dev/null

# 3. 检查ROS节点
echo "3. 检查ROS节点:"
rosnode list | grep -i "gps\\|gnss"

# 4. 检查GPS话题
echo "4. 检查GPS话题:"
rostopic list | grep -i "gps\\|gnss"

# 5. 测试GPS数据
echo "5. 测试GPS数据 (10秒):"
timeout 10 rostopic echo /rtk/gnss

# 6. 检查GPS参数
echo "6. 检查GPS参数:"
rosparam list | grep -i "gps\\|gnss"

echo "✅ GPS硬件测试完成"
'''
        
        with open("gps_hardware_test.sh", "w") as f:
            f.write(test_script)
        
        os.chmod("gps_hardware_test.sh", 0o755)
        print("✅ GPS测试脚本已保存到: gps_hardware_test.sh")
        print("   运行: ./gps_hardware_test.sh")
        
        return "gps_hardware_test.sh"
    
    def run_comprehensive_diagnostic(self):
        """运行综合诊断"""
        print("🔧 GPS硬件综合诊断")
        print("="*50)
        
        # 1. 硬件连接检查
        hw_status = self.check_gps_hardware_connection()
        
        # 2. 串口检查
        serial_ports = self.check_serial_ports()
        
        # 3. 信号强度建议
        signal_suggestions = self.check_gps_signal_strength()
        
        # 4. ROS节点检查
        ros_nodes = self.check_ros_gps_node()
        
        # 5. GPS话题检查
        gps_topics = self.check_gps_topics()
        
        # 6. 生成修复命令
        fix_commands = self.generate_fix_commands()
        
        # 7. 创建测试脚本
        test_script = self.create_gps_test_script()
        
        # 生成诊断报告
        print("\n" + "="*60)
        print("📋 GPS硬件诊断报告")
        print("="*60)
        
        print(f"硬件连接状态: {'✅ 正常' if hw_status else '❌ 异常'}")
        print(f"串口设备: {len(serial_ports)} 个")
        print(f"ROS节点: {len(ros_nodes)} 个")
        print(f"GPS话题: {len(gps_topics)} 个")
        
        print("\n🎯 主要问题:")
        if not hw_status:
            print("- GPS硬件未正确连接")
        if not serial_ports:
            print("- 未发现GPS串口设备")
        if not ros_nodes:
            print("- GPS ROS节点未运行")
        if not gps_topics:
            print("- GPS话题未发布")
        
        print("\n🔧 建议的修复步骤:")
        print("1. 检查GPS硬件连接和电源")
        print("2. 确保GPS天线位置和信号质量")
        print("3. 运行GPS测试脚本: ./gps_hardware_test.sh")
        print("4. 根据测试结果调整配置")
        print("5. 重启相关服务和节点")
        
        return {
            'hardware_status': hw_status,
            'serial_ports': serial_ports,
            'ros_nodes': ros_nodes,
            'gps_topics': gps_topics,
            'test_script': test_script
        }

def main():
    """主函数"""
    diagnostic = GPSHardwareDiagnostic()
    results = diagnostic.run_comprehensive_diagnostic()
    
    print("\n✅ GPS硬件诊断完成!")
    print("请根据诊断报告进行相应的修复操作。")

if __name__ == "__main__":
    main()
