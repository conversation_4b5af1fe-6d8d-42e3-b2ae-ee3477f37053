#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script to show green selection in Entry widgets
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

def create_demo():
    """Create demo window with green selection"""
    root = tk.Tk()
    root.title("绿色选择演示")
    root.geometry("800x600")
    root.configure(bg='#2E2E2E')
    
    # Configure style
    style = ttk.Style()
    style.theme_use('clam')
    
    # Dark theme colors
    colors = {
        'bg': '#2E2E2E',
        'fg': '#FFFFFF',
        'entry_bg': '#3C3C3C',
        'entry_fg': '#FFFFFF',
        'frame_bg': '#353535',
    }
    
    # Configure Entry style
    style.configure('Demo.TEntry', 
                   background=colors['entry_bg'], 
                   foreground=colors['entry_fg'],
                   relief='flat',
                   borderwidth=1)
    
    # Main frame
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    main_frame.configure(style='TFrame')
    
    # Title
    title_label = tk.Label(main_frame, text="绿色选择效果演示", 
                          font=('Microsoft YaHei', 16, 'bold'),
                          bg=colors['bg'], fg=colors['fg'])
    title_label.pack(pady=(0, 20))
    
    # Demo entries
    demo_frame = tk.Frame(main_frame, bg=colors['frame_bg'], relief='raised', bd=1)
    demo_frame.pack(fill=tk.X, pady=10, padx=10)
    
    # Entry 1
    tk.Label(demo_frame, text="Bag文件路径:", 
            bg=colors['frame_bg'], fg=colors['fg'],
            font=('Microsoft YaHei', 10)).pack(anchor='w', padx=10, pady=(10, 5))
    
    entry1_var = tk.StringVar(value="L:\\WORK\\alidar_AIcode\\github_alidar03\\datasets\\UM982loop_715std_maximum_synced.bag")
    entry1 = tk.Entry(demo_frame, textvariable=entry1_var, width=80,
                     bg=colors['entry_bg'], fg=colors['entry_fg'],
                     selectbackground='#00C851',  # 绿色选择背景
                     selectforeground='#FFFFFF',  # 白色选择文字
                     font=('Microsoft YaHei', 10),
                     relief='flat', bd=1)
    entry1.pack(padx=10, pady=(0, 10), fill=tk.X)
    
    # Entry 2
    tk.Label(demo_frame, text="GPS话题:", 
            bg=colors['frame_bg'], fg=colors['fg'],
            font=('Microsoft YaHei', 10)).pack(anchor='w', padx=10, pady=(10, 5))
    
    entry2_var = tk.StringVar(value="/rtk/gnss")
    entry2 = tk.Entry(demo_frame, textvariable=entry2_var, width=80,
                     bg=colors['entry_bg'], fg=colors['entry_fg'],
                     selectbackground='#00C851',  # 绿色选择背景
                     selectforeground='#FFFFFF',  # 白色选择文字
                     font=('Microsoft YaHei', 10),
                     relief='flat', bd=1)
    entry2.pack(padx=10, pady=(0, 10), fill=tk.X)
    
    # Entry 3
    tk.Label(demo_frame, text="输出文件:", 
            bg=colors['frame_bg'], fg=colors['fg'],
            font=('Microsoft YaHei', 10)).pack(anchor='w', padx=10, pady=(10, 5))
    
    entry3_var = tk.StringVar(value="UM982loop_715std_maximum_synced_轨迹.txt")
    entry3 = tk.Entry(demo_frame, textvariable=entry3_var, width=80,
                     bg=colors['entry_bg'], fg=colors['entry_fg'],
                     selectbackground='#00C851',  # 绿色选择背景
                     selectforeground='#FFFFFF',  # 白色选择文字
                     font=('Microsoft YaHei', 10),
                     relief='flat', bd=1)
    entry3.pack(padx=10, pady=(0, 15), fill=tk.X)
    
    # Text widget demo
    text_frame = tk.Frame(main_frame, bg=colors['frame_bg'], relief='raised', bd=1)
    text_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=10)
    
    tk.Label(text_frame, text="文本选择演示:", 
            bg=colors['frame_bg'], fg=colors['fg'],
            font=('Microsoft YaHei', 10)).pack(anchor='w', padx=10, pady=(10, 5))
    
    demo_text = scrolledtext.ScrolledText(
        text_frame,
        height=15,
        font=('Microsoft YaHei', 10),
        bg=colors['entry_bg'],
        fg=colors['entry_fg'],
        selectbackground='#00C851',  # 绿色选择背景
        selectforeground='#FFFFFF',  # 白色选择文字
        relief='flat',
        borderwidth=1
    )
    demo_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
    
    # Insert demo text
    demo_content = """GPS 3D 轨迹分析器 - 绿色选择演示

这是一个演示绿色选择效果的文本框。

主要特点：
• 深灰色主题界面
• 绿色文字选择背景
• 白色选择文字颜色
• 中文字体支持

使用说明：
1. 选择上方输入框中的文字，可以看到绿色选择效果
2. 选择此文本框中的文字，同样显示绿色选择
3. 绿色选择背景色：#00C851
4. 白色选择文字色：#FFFFFF

这种配色方案在深色主题下提供了良好的对比度和可读性。
绿色选择背景与深灰色界面形成鲜明对比，
使选中的文字更加突出和易于识别。

请尝试选择不同的文字内容来体验效果！"""
    
    demo_text.insert(1.0, demo_content)
    
    # Instructions
    instruction_label = tk.Label(main_frame, 
                                text="💡 提示：选择上方输入框或文本框中的文字，查看绿色选择效果", 
                                font=('Microsoft YaHei', 10),
                                bg=colors['bg'], fg='#00C851')
    instruction_label.pack(pady=10)
    
    # Auto-select some text to show the effect
    def auto_select():
        entry1.select_range(0, 20)  # Select first 20 characters
        entry1.focus()
    
    # Auto-select after 1 second
    root.after(1000, auto_select)
    
    return root

def main():
    """Main function"""
    print("启动绿色选择演示...")
    root = create_demo()
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
