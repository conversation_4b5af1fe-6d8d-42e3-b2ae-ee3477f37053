
// GPS数据集成到αLiDAR系统的建议代码

class GPSProcessor {
private:
    std::deque<sensor_msgs::NavSatFix> gps_buffer;
    std::mutex gps_mutex;
    
    // GPS坐标转换参数
    double utm_zone;
    bool is_utm_initialized = false;
    Eigen::Vector3d utm_origin;
    
public:
    void gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
        std::lock_guard<std::mutex> lock(gps_mutex);
        gps_buffer.push_back(*gps_msg);
        
        // 保持缓冲区大小
        if (gps_buffer.size() > 1000) {
            gps_buffer.pop_front();
        }
    }
    
    bool getGPSConstraint(double timestamp, Eigen::Vector3d& gps_position, 
                         Eigen::Matrix3d& gps_covariance) {
        std::lock_guard<std::mutex> lock(gps_mutex);
        
        // 查找时间戳最近的GPS数据
        auto closest_gps = findClosestGPS(timestamp);
        if (!closest_gps) return false;
        
        // 转换GPS坐标到UTM
        if (!is_utm_initialized) {
            initializeUTM(closest_gps->latitude, closest_gps->longitude);
        }
        
        gps_position = convertToUTM(closest_gps->latitude, 
                                   closest_gps->longitude, 
                                   closest_gps->altitude);
        
        // 设置GPS协方差
        if (closest_gps->position_covariance_type != 0) {
            // 使用GPS提供的协方差
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    gps_covariance(i, j) = closest_gps->position_covariance[i*3 + j];
                }
            }
        } else {
            // 使用默认协方差
            gps_covariance = Eigen::Matrix3d::Identity() * 1.0; // 1米标准差
        }
        
        return true;
    }
    
    // 在状态估计中集成GPS约束
    void integrateGPSConstraint(esekfom::esekf<state_ikfom, 12, input_ikfom>& kf,
                               double timestamp) {
        Eigen::Vector3d gps_position;
        Eigen::Matrix3d gps_covariance;
        
        if (getGPSConstraint(timestamp, gps_position, gps_covariance)) {
            // 获取当前状态估计的位置
            state_ikfom current_state = kf.get_x();
            Eigen::Vector3d estimated_position = current_state.pos;
            
            // 计算GPS约束残差
            Eigen::Vector3d residual = gps_position - estimated_position;
            
            // 如果残差过大，可能是GPS跳跃，需要谨慎处理
            if (residual.norm() > 10.0) {
                ROS_WARN("Large GPS residual: %.3f m, skipping GPS update", 
                        residual.norm());
                return;
            }
            
            // 构建GPS观测模型 H矩阵
            Eigen::MatrixXd H = Eigen::MatrixXd::Zero(3, 12);
            H.block<3, 3>(0, 0) = Eigen::Matrix3d::Identity(); // 位置部分
            
            // 更新卡尔曼滤波器
            kf.update_iterated_dyn_share_GPS(residual, H, gps_covariance);
        }
    }
};

// 在主循环中集成GPS
void execute_with_gps() {
    // ... 原有的IMU和LiDAR处理 ...
    
    // 添加GPS约束
    if (gps_processor && enable_gps_constraint) {
        gps_processor->integrateGPSConstraint(kf, lidar_end_time);
    }
    
    // ... 继续原有的地图更新等 ...
}
