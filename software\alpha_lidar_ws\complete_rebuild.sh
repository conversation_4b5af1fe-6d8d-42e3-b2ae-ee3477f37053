#!/bin/bash

# 完整重新编译脚本

echo "=========================================="
echo "🔧 完整重新编译SLAM系统"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 清理所有编译文件"
echo "======================"
rm -rf build devel install .catkin_workspace
echo "✅ 编译文件已清理"

echo ""
echo "步骤2: 检查和安装依赖"
echo "===================="

# 更新包列表
sudo apt update

# 安装ROS依赖
echo "安装ROS核心依赖..."
sudo apt install -y \
    ros-noetic-desktop-full \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-message-filters \
    ros-noetic-sensor-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-geometry-msgs

# 安装PCL依赖
echo "安装PCL依赖..."
sudo apt install -y \
    libpcl-dev \
    pcl-tools \
    libpcl1 \
    libpcl-common1.10 \
    libpcl-io1.10

# 安装其他依赖
echo "安装其他依赖..."
sudo apt install -y \
    libeigen3-dev \
    libboost-all-dev \
    libgoogle-glog-dev \
    libgflags-dev \
    build-essential \
    cmake

echo "✅ 依赖安装完成"

echo ""
echo "步骤3: 设置ROS环境"
echo "=================="
source /opt/ros/noetic/setup.bash
echo "✅ ROS环境已设置"

echo ""
echo "步骤4: 检查源码问题"
echo "=================="

# 检查关键文件
if [ ! -f "src/state_estimation/src/state_estimation_node.cpp" ]; then
    echo "❌ 主要源文件缺失"
    exit 1
fi

echo "检查CMakeLists.txt..."
if grep -q "find_package.*PCL" src/state_estimation/CMakeLists.txt; then
    echo "✅ PCL依赖配置正确"
else
    echo "⚠️  PCL依赖可能有问题"
fi

echo ""
echo "步骤5: 修复编译配置"
echo "=================="

# 创建修复的CMakeLists.txt备份
cp src/state_estimation/CMakeLists.txt src/state_estimation/CMakeLists.txt.backup

# 检查并修复CMakeLists.txt中的关键配置
cat > /tmp/cmake_fix.txt << 'EOF'
# 确保这些行存在于CMakeLists.txt中
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  sensor_msgs
  nav_msgs
  geometry_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_ros
  pcl_conversions
  message_filters
)

find_package(PCL REQUIRED)
find_package(Eigen3 REQUIRED)

include_directories(
  ${catkin_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${EIGEN3_INCLUDE_DIR}
)

link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})
EOF

echo "✅ 编译配置已检查"

echo ""
echo "步骤6: 开始编译"
echo "==============="

echo "使用单线程编译避免内存问题..."
catkin_make -DCMAKE_BUILD_TYPE=Release -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    echo ""
    echo "步骤7: 验证编译结果"
    echo "=================="
    
    # 检查可执行文件
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点编译成功"
        
        # 检查依赖
        echo "检查动态库依赖..."
        ldd devel/lib/state_estimation/state_estimation_node | grep -E "(pcl|eigen)" && echo "✅ PCL/Eigen依赖正常" || echo "⚠️  依赖可能有问题"
        
    else
        echo "❌ SLAM核心节点编译失败"
        exit 1
    fi
    
    # 检查Python脚本
    if [ -f "src/state_estimation/scripts/gps_soft_loop_detector.py" ]; then
        echo "✅ Python脚本存在"
        chmod +x src/state_estimation/scripts/*.py
    fi
    
    echo ""
    echo "🎉 重新编译完成!"
    echo ""
    echo "下一步: 使用修复的启动脚本"
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见问题解决:"
    echo "1. 内存不足 - 使用更少线程编译"
    echo "2. 依赖缺失 - 重新安装依赖"
    echo "3. 版本冲突 - 检查ROS和PCL版本"
    echo ""
    echo "详细错误信息请查看上面的输出"
    exit 1
fi

echo ""
echo "=========================================="
echo "重新编译完成"
echo "=========================================="
