#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for GPS GUI Analyzer
Quick test to verify all dependencies are working
"""

import sys
import traceback

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✅ tkinter")
    except ImportError as e:
        print(f"❌ tkinter: {e}")
        return False
    
    try:
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter modules")
    except ImportError as e:
        print(f"❌ tkinter modules: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib.pyplot")
    except ImportError as e:
        print(f"❌ matplotlib.pyplot: {e}")
        return False
    
    try:
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        print("✅ matplotlib backend")
    except ImportError as e:
        print(f"❌ matplotlib backend: {e}")
        return False
    
    try:
        from mpl_toolkits.mplot3d import Axes3D
        print("✅ mplot3d")
    except ImportError as e:
        print(f"❌ mplot3d: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy")
    except ImportError as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas")
    except ImportError as e:
        print(f"❌ pandas: {e}")
        return False
    
    try:
        import bagpy
        print("✅ bagpy")
    except ImportError as e:
        print(f"❌ bagpy: {e}")
        print("  Install with: pip install bagpy")
        return False
    
    return True

def test_gui_creation():
    """Test basic GUI creation"""
    print("\nTesting GUI creation...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create test window
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("400x300")
        
        # Add some widgets
        label = ttk.Label(root, text="Test Label")
        label.pack(pady=10)
        
        button = ttk.Button(root, text="Test Button")
        button.pack(pady=10)
        
        # Test matplotlib integration
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        from mpl_toolkits.mplot3d import Axes3D
        
        fig = plt.Figure(figsize=(5, 4), dpi=100)
        ax = fig.add_subplot(111, projection='3d')
        ax.plot([1, 2, 3], [1, 2, 3], [1, 2, 3])
        
        canvas = FigureCanvasTkAgg(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack()
        
        print("✅ GUI creation successful")
        
        # Don't show the window, just destroy it
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("GPS GUI Analyzer - Dependency Test")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print()
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed")
        print("Please install missing dependencies:")
        print("pip install -r requirements.txt")
        return False
    
    # Test GUI creation
    if not test_gui_creation():
        print("\n❌ GUI test failed")
        return False
    
    print("\n" + "=" * 50)
    print("✅ All tests passed!")
    print("✅ GPS GUI Analyzer should work correctly")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
