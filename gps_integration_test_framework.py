#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS集成测试框架
用于验证GPS集成效果和性能评估
"""

import subprocess
import time
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import yaml
import os
from pathlib import Path

class GPSIntegrationTester:
    def __init__(self, workspace_path):
        self.workspace_path = Path(workspace_path)
        self.results_dir = self.workspace_path / "gps_test_results"
        self.results_dir.mkdir(exist_ok=True)
        
        self.test_configs = {
            'baseline': {
                'name': '基线测试(无GPS)',
                'gps_enable_correction': False,
                'gps_enable_loop_closure': False
            },
            'height_correction': {
                'name': 'GPS高度校正',
                'gps_enable_correction': True,
                'gps_enable_loop_closure': False,
                'gps_correction_rate': 0.1,
                'gps_height_correction_threshold': 0.3
            },
            'full_integration': {
                'name': '完整GPS集成',
                'gps_enable_correction': True,
                'gps_enable_loop_closure': True,
                'gps_correction_rate': 0.1,
                'gps_height_correction_threshold': 0.3
            },
            'conservative': {
                'name': '保守GPS集成',
                'gps_enable_correction': True,
                'gps_enable_loop_closure': True,
                'gps_correction_rate': 0.05,
                'gps_height_correction_threshold': 0.5
            },
            'aggressive': {
                'name': '激进GPS集成',
                'gps_enable_correction': True,
                'gps_enable_loop_closure': True,
                'gps_correction_rate': 0.2,
                'gps_height_correction_threshold': 0.1
            }
        }
        
        self.test_results = {}
    
    def prepare_test_environment(self):
        """准备测试环境"""
        print("准备测试环境...")
        
        # 检查必要文件
        bag_file = self.workspace_path / "datasets" / "UM982loop_715std_maximum_synced.bag"
        if not bag_file.exists():
            raise FileNotFoundError(f"找不到数据集文件: {bag_file}")
        
        # 检查ROS工作空间
        ws_path = self.workspace_path / "alpha_lidar_GPS" / "software" / "alpha_lidar_ws"
        if not ws_path.exists():
            raise FileNotFoundError(f"找不到ROS工作空间: {ws_path}")
        
        # 编译工作空间
        print("编译ROS工作空间...")
        result = subprocess.run([
            "bash", "-c", 
            f"cd {ws_path} && source /opt/ros/noetic/setup.bash && catkin_make"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"编译失败: {result.stderr}")
            return False
        
        print("测试环境准备完成")
        return True
    
    def run_single_test(self, config_name, config_params, timeout=600):
        """运行单个测试配置"""
        print(f"\n运行测试: {config_params['name']}")
        
        # 创建测试结果目录
        test_dir = self.results_dir / config_name
        test_dir.mkdir(exist_ok=True)
        
        # 生成launch参数
        launch_args = [
            f"bag_path:={self.workspace_path}/datasets/UM982loop_715std_maximum_synced.bag"
        ]
        
        for key, value in config_params.items():
            if key != 'name' and isinstance(value, (bool, int, float)):
                launch_args.append(f"{key}:={str(value).lower()}")
        
        # 构建命令
        ws_path = self.workspace_path / "alpha_lidar_GPS" / "software" / "alpha_lidar_ws"
        cmd = [
            "bash", "-c",
            f"cd {ws_path} && "
            f"source devel/setup.bash && "
            f"timeout {timeout} roslaunch state_estimation mapping_robosense.launch " +
            " ".join(launch_args)
        ]
        
        # 运行测试
        start_time = time.time()
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout+10)
            execution_time = time.time() - start_time
            
            # 解析结果
            test_result = self.parse_test_results(result, test_dir, execution_time)
            test_result['config'] = config_params
            test_result['success'] = result.returncode == 0 or result.returncode == 124  # 124 = timeout
            
            self.test_results[config_name] = test_result
            
            print(f"测试完成: {config_params['name']} - {'成功' if test_result['success'] else '失败'}")
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"测试超时: {config_params['name']}")
            return {'success': False, 'error': 'timeout', 'config': config_params}
    
    def parse_test_results(self, result, test_dir, execution_time):
        """解析测试结果"""
        # 检查临时结果文件
        temp_results_dir = Path("/tmp/alpha_lidar")
        
        test_result = {
            'execution_time': execution_time,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'metrics': {}
        }
        
        if temp_results_dir.exists():
            # 解析轨迹文件
            trajectory_files = list(temp_results_dir.glob("*.odom"))
            if trajectory_files:
                test_result['metrics'] = self.analyze_trajectory(trajectory_files)
            
            # 解析统计文件
            stats_files = list(temp_results_dir.glob("*.stats"))
            if stats_files:
                test_result['metrics'].update(self.analyze_stats(stats_files))
            
            # 复制结果文件到测试目录
            for file in temp_results_dir.iterdir():
                if file.is_file():
                    subprocess.run(["cp", str(file), str(test_dir)])
        
        return test_result
    
    def analyze_trajectory(self, trajectory_files):
        """分析轨迹文件"""
        if not trajectory_files:
            return {}
        
        # 读取轨迹数据
        positions = []
        timestamps = []
        
        for traj_file in sorted(trajectory_files):
            timestamp = float(traj_file.stem)
            trajectory = np.loadtxt(traj_file)
            
            if trajectory.size >= 12:  # 确保有足够的数据
                position = trajectory[:3, 3] if trajectory.ndim == 2 else trajectory[:3]
                positions.append(position)
                timestamps.append(timestamp)
        
        if len(positions) < 2:
            return {}
        
        positions = np.array(positions)
        timestamps = np.array(timestamps)
        
        # 计算关键指标
        metrics = {
            'trajectory_length': len(positions),
            'duration': timestamps[-1] - timestamps[0],
            'start_position': positions[0],
            'end_position': positions[-1],
            'start_end_distance': np.linalg.norm(positions[-1] - positions[0]),
            'start_end_height_diff': abs(positions[-1][2] - positions[0][2]),
            'total_distance': self.calculate_total_distance(positions),
            'height_std': np.std(positions[:, 2]),
            'height_range': np.max(positions[:, 2]) - np.min(positions[:, 2])
        }
        
        return metrics
    
    def analyze_stats(self, stats_files):
        """分析统计文件"""
        if not stats_files:
            return {}
        
        latencies = []
        fov_coverage = []
        
        for stats_file in stats_files:
            try:
                stats = np.loadtxt(stats_file)
                if len(stats) >= 4:
                    fov_raw, fov_alpha, latency_opt, latency_update = stats[:4]
                    latencies.append(latency_opt + latency_update)
                    fov_coverage.append(fov_alpha)
            except:
                continue
        
        if not latencies:
            return {}
        
        return {
            'avg_latency': np.mean(latencies),
            'max_latency': np.max(latencies),
            'avg_fov_coverage': np.mean(fov_coverage) if fov_coverage else 0
        }
    
    def calculate_total_distance(self, positions):
        """计算轨迹总长度"""
        if len(positions) < 2:
            return 0
        
        distances = np.linalg.norm(np.diff(positions, axis=0), axis=1)
        return np.sum(distances)
    
    def run_all_tests(self):
        """运行所有测试配置"""
        print("开始运行所有测试配置...")
        
        if not self.prepare_test_environment():
            print("测试环境准备失败")
            return False
        
        for config_name, config_params in self.test_configs.items():
            try:
                self.run_single_test(config_name, config_params)
                time.sleep(5)  # 测试间隔
            except Exception as e:
                print(f"测试 {config_name} 失败: {e}")
                self.test_results[config_name] = {
                    'success': False, 
                    'error': str(e), 
                    'config': config_params
                }
        
        return True
    
    def generate_comparison_report(self):
        """生成对比报告"""
        print("\n生成对比报告...")
        
        # 创建对比表格
        comparison_data = []
        
        for config_name, result in self.test_results.items():
            if not result.get('success', False):
                continue
            
            metrics = result.get('metrics', {})
            row = {
                '配置名称': result['config']['name'],
                '首尾距离(m)': metrics.get('start_end_distance', 0),
                '首尾高度差(m)': metrics.get('start_end_height_diff', 0),
                '高度标准差(m)': metrics.get('height_std', 0),
                '平均延迟(s)': metrics.get('avg_latency', 0),
                '执行时间(s)': result.get('execution_time', 0)
            }
            comparison_data.append(row)
        
        if not comparison_data:
            print("没有成功的测试结果用于对比")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(comparison_data)
        
        # 保存CSV
        csv_path = self.results_dir / "comparison_results.csv"
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 生成Markdown报告
        report_path = self.results_dir / "comparison_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# GPS集成测试对比报告\n\n")
            f.write("## 测试结果对比\n\n")
            f.write(df.to_markdown(index=False))
            f.write("\n\n## 关键发现\n\n")
            
            # 分析最佳配置
            if len(df) > 1:
                best_height = df.loc[df['首尾高度差(m)'].idxmin()]
                best_latency = df.loc[df['平均延迟(s)'].idxmin()]
                
                f.write(f"- **高度精度最佳**: {best_height['配置名称']} "
                       f"(首尾高度差: {best_height['首尾高度差(m)']:.3f}m)\n")
                f.write(f"- **实时性最佳**: {best_latency['配置名称']} "
                       f"(平均延迟: {best_latency['平均延迟(s)']:.3f}s)\n")
        
        print(f"对比报告已保存: {report_path}")
        
        # 生成可视化图表
        self.generate_comparison_plots(df)
    
    def generate_comparison_plots(self, df):
        """生成对比图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 首尾高度差对比
        axes[0, 0].bar(df['配置名称'], df['首尾高度差(m)'])
        axes[0, 0].set_title('首尾高度差对比')
        axes[0, 0].set_ylabel('高度差 (m)')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 高度标准差对比
        axes[0, 1].bar(df['配置名称'], df['高度标准差(m)'])
        axes[0, 1].set_title('高度标准差对比')
        axes[0, 1].set_ylabel('标准差 (m)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 平均延迟对比
        axes[1, 0].bar(df['配置名称'], df['平均延迟(s)'] * 1000)  # 转换为毫秒
        axes[1, 0].set_title('平均延迟对比')
        axes[1, 0].set_ylabel('延迟 (ms)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 综合性能雷达图
        categories = ['高度精度', '实时性能', '系统稳定性']
        
        # 归一化指标 (越小越好的指标需要取倒数)
        for idx, row in df.iterrows():
            values = [
                1 / (1 + row['首尾高度差(m)']),  # 高度精度
                1 / (1 + row['平均延迟(s)'] * 1000),  # 实时性能
                1 / (1 + row['高度标准差(m)'])  # 系统稳定性
            ]
            
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False)
            values += values[:1]  # 闭合图形
            angles = np.concatenate((angles, [angles[0]]))
            
            axes[1, 1].plot(angles, values, 'o-', label=row['配置名称'])
            axes[1, 1].fill(angles, values, alpha=0.25)
        
        axes[1, 1].set_xticks(angles[:-1])
        axes[1, 1].set_xticklabels(categories)
        axes[1, 1].set_title('综合性能对比')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plot_path = self.results_dir / "comparison_plots.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"对比图表已保存: {plot_path}")
    
    def cleanup(self):
        """清理临时文件"""
        temp_dir = Path("/tmp/alpha_lidar")
        if temp_dir.exists():
            subprocess.run(["rm", "-rf", str(temp_dir)])

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='GPS集成测试框架')
    parser.add_argument('--workspace', type=str, 
                       default='L:/WORK/alidar_AIcode/github_alidar03',
                       help='工作空间路径')
    parser.add_argument('--config', type=str, choices=['all'] + list(GPSIntegrationTester({}).test_configs.keys()),
                       default='all', help='测试配置')
    
    args = parser.parse_args()
    
    tester = GPSIntegrationTester(args.workspace)
    
    try:
        if args.config == 'all':
            tester.run_all_tests()
            tester.generate_comparison_report()
        else:
            config_params = tester.test_configs[args.config]
            tester.run_single_test(args.config, config_params)
        
        print("\n测试完成!")
        print(f"结果保存在: {tester.results_dir}")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
