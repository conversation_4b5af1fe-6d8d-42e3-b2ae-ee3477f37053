#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analyzer GUI - Enhanced Version
Complete GUI application with gray theme, interactive 3D controls, and Chinese analysis
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pandas as pd
import os
import sys
import threading
import time
from collections import defaultdict
import math

# 设置matplotlib灰度主题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
plt.rcParams['figure.facecolor'] = '#f0f0f0'
plt.rcParams['axes.facecolor'] = '#ffffff'

class GPSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPS 3D Trajectory Analyzer")
        self.root.geometry("1600x1000")

        # 设置字体编码
        self.setup_fonts()

        # 设置灰度主题
        self.setup_gray_theme()
        
        # Variables
        self.bag_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.gps_topic = tk.StringVar(value="/rtk/gnss")
        self.is_processing = False
        self.gps_data = []
        self.trajectory_points = []
        
        # 3D视图控制变量
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        
        # GPS quality settings
        self.quality_colors = {
            -1: '#CC0000',  # 深红色 - NO_FIX
            0:  '#008800',  # 深绿色 - RTK_FIXED
            1:  '#0066CC',  # 深蓝色 - SBAS_FIX
            2:  '#CC8800',  # 橙色 - GBAS_FIX
            3:  '#8800CC',  # 紫色 - OTHER
        }
        self.quality_names = {
            -1: "No Fix",
            0:  "RTK Fixed",
            1:  "SBAS Fix",
            2:  "GBAS Fix",
            3:  "Other"
        }
        
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
        self.setup_gui()

    def setup_fonts(self):
        """设置字体以避免乱码"""
        # 设置默认字体
        self.default_font = ('Arial', 10)
        self.title_font = ('Arial', 14, 'bold')
        self.mono_font = ('Consolas', 9)

        # 尝试使用中文字体
        try:
            import tkinter.font as tkFont
            available_fonts = tkFont.families()

            # 优先选择支持中文的字体
            chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS']
            for font in chinese_fonts:
                if font in available_fonts:
                    self.default_font = (font, 10)
                    self.title_font = (font, 14, 'bold')
                    break
        except:
            pass

    def setup_gray_theme(self):
        """设置灰度主题"""
        # 灰度主题配色
        self.colors = {
            'bg': '#f0f0f0',           # 浅灰背景
            'fg': '#333333',           # 深灰前景
            'select_bg': '#d0d0d0',    # 选中背景
            'select_fg': '#000000',    # 选中前景
            'button_bg': '#e0e0e0',    # 按钮背景
            'button_fg': '#333333',    # 按钮前景
            'entry_bg': '#ffffff',     # 输入框背景
            'entry_fg': '#333333',     # 输入框前景
            'frame_bg': '#e8e8e8',     # 框架背景
            'accent': '#4a90e2',       # 强调色（蓝色）
            'success': '#28a745',      # 成功色（绿色）
            'warning': '#ffc107',      # 警告色（黄色）
            'error': '#dc3545',        # 错误色（红色）
        }

        # 配置根窗口
        self.root.configure(bg=self.colors['bg'])

        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')

        # 配置各种控件样式
        style.configure('Gray.TFrame', background=self.colors['frame_bg'])
        style.configure('Gray.TLabel', background=self.colors['bg'], foreground=self.colors['fg'], font=self.default_font)
        style.configure('Gray.TButton', background=self.colors['button_bg'], foreground=self.colors['button_fg'], font=self.default_font)
        style.configure('Gray.TEntry', background=self.colors['entry_bg'], foreground=self.colors['entry_fg'], font=self.default_font)
        style.configure('Accent.TButton', background=self.colors['accent'], foreground='white', font=self.default_font)
        style.configure('Success.TLabel', background=self.colors['bg'], foreground=self.colors['success'], font=self.default_font)
        style.configure('Warning.TLabel', background=self.colors['bg'], foreground=self.colors['warning'], font=self.default_font)
        style.configure('Error.TLabel', background=self.colors['bg'], foreground=self.colors['error'], font=self.default_font)

        # 配置LabelFrame
        style.configure('Gray.TLabelframe', background=self.colors['bg'], foreground=self.colors['fg'])
        style.configure('Gray.TLabelframe.Label', background=self.colors['bg'], foreground=self.colors['fg'], font=self.default_font)
        
    def setup_gui(self):
        """Setup the GUI layout"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10", style='Gray.TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="GPS 3D Trajectory Analyzer",
                               font=self.title_font, style='Gray.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control panel (left side)
        self.setup_control_panel(main_frame)
        
        # Visualization and analysis panel (right side)
        self.setup_visualization_panel(main_frame)
        
        # Terminal output (bottom)
        self.setup_terminal_panel(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="Control Panel", padding="15", style='Gray.TLabelframe')
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))

        # File selection
        ttk.Label(control_frame, text="Bag File:", style='Gray.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.bag_file_path, width=35, style='Gray.TEntry').grid(row=0, column=1, padx=8)
        ttk.Button(control_frame, text="Browse", command=self.browse_bag_file, style='Gray.TButton').grid(row=0, column=2, padx=8)

        # GPS Topic
        ttk.Label(control_frame, text="GPS Topic:", style='Gray.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.gps_topic, width=35, style='Gray.TEntry').grid(row=1, column=1, padx=8)

        # Output file
        ttk.Label(control_frame, text="Output File:", style='Gray.TLabel').grid(row=2, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.output_file_path, width=35, style='Gray.TEntry').grid(row=2, column=1, padx=8)
        ttk.Button(control_frame, text="Browse", command=self.browse_output_file, style='Gray.TButton').grid(row=2, column=2, padx=8)
        
        # Control buttons
        button_frame = ttk.Frame(control_frame, style='Gray.TFrame')
        button_frame.grid(row=3, column=0, columnspan=3, pady=25)

        self.start_button = ttk.Button(button_frame, text="Start Analysis",
                                      command=self.start_analysis, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=8)

        self.stop_button = ttk.Button(button_frame, text="Stop Analysis",
                                     command=self.stop_analysis, state='disabled', style='Gray.TButton')
        self.stop_button.pack(side=tk.LEFT, padx=8)

        # 3D视图控制按钮
        view_frame = ttk.LabelFrame(control_frame, text="3D View Control", padding="10", style='Gray.TLabelframe')
        view_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 10))

        # 视角控制
        ttk.Label(view_frame, text="View Control:", style='Gray.TLabel').grid(row=0, column=0, columnspan=3, pady=5)

        view_buttons_frame = ttk.Frame(view_frame, style='Gray.TFrame')
        view_buttons_frame.grid(row=1, column=0, columnspan=3, pady=5)

        ttk.Button(view_buttons_frame, text="Top", command=self.view_top, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(view_buttons_frame, text="Side", command=self.view_side, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(view_buttons_frame, text="Front", command=self.view_front, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(view_buttons_frame, text="Reset", command=self.reset_view, style='Gray.TButton').pack(side=tk.LEFT, padx=2)

        # 缩放控制
        zoom_frame = ttk.Frame(view_frame, style='Gray.TFrame')
        zoom_frame.grid(row=2, column=0, columnspan=3, pady=10)

        ttk.Label(zoom_frame, text="Zoom:", style='Gray.TLabel').pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="In", command=self.zoom_in, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Out", command=self.zoom_out, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Fit", command=self.zoom_fit, style='Gray.TButton').pack(side=tk.LEFT, padx=2)
        
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)

        # Status label
        self.status_label = ttk.Label(control_frame, text="Ready", style='Success.TLabel')
        self.status_label.grid(row=6, column=0, columnspan=3, pady=8)

    def setup_visualization_panel(self, parent):
        """Setup visualization and analysis panel"""
        viz_frame = ttk.LabelFrame(parent, text="3D Visualization & Analysis Results", padding="15", style='Gray.TLabelframe')
        viz_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        viz_frame.columnconfigure(0, weight=2)
        viz_frame.columnconfigure(1, weight=1)
        viz_frame.rowconfigure(0, weight=1)

        # 3D Plot frame
        plot_frame = ttk.Frame(viz_frame, style='Gray.TFrame')
        plot_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        plot_frame.columnconfigure(0, weight=1)
        plot_frame.rowconfigure(0, weight=1)

        # Create matplotlib figure with gray theme
        self.fig = plt.Figure(figsize=(10, 8), dpi=100, facecolor='#f0f0f0')
        self.ax = self.fig.add_subplot(111, projection='3d')
        self.ax.set_facecolor('#ffffff')

        # 设置3D图表样式
        self.ax.set_title('GPS 3D Trajectory Visualization', color='black', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X (meters)', color='black', fontsize=12)
        self.ax.set_ylabel('Y (meters)', color='black', fontsize=12)
        self.ax.set_zlabel('Z (meters)', color='black', fontsize=12)

        # 设置坐标轴颜色
        self.ax.tick_params(colors='black')
        self.ax.xaxis.label.set_color('black')
        self.ax.yaxis.label.set_color('black')
        self.ax.zaxis.label.set_color('black')

        # Canvas for matplotlib
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 绑定鼠标滚轮事件
        self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        # 添加导航工具栏
        toolbar_frame = ttk.Frame(plot_frame, style='Gray.TFrame')
        toolbar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.config(bg=self.colors['frame_bg'])
        self.toolbar.update()

        # Analysis results frame
        analysis_frame = ttk.LabelFrame(viz_frame, text="Analysis Results", padding="15", style='Gray.TLabelframe')
        analysis_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Analysis text widget with gray theme
        self.analysis_text = scrolledtext.ScrolledText(
            analysis_frame,
            width=45,
            height=30,
            font=self.default_font,
            bg=self.colors['entry_bg'],
            fg=self.colors['entry_fg'],
            insertbackground=self.colors['fg'],
            selectbackground=self.colors['select_bg'],
            selectforeground=self.colors['select_fg']
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True)

    def setup_terminal_panel(self, parent):
        """Setup terminal output panel"""
        terminal_frame = ttk.LabelFrame(parent, text="Terminal Output", padding="15", style='Gray.TLabelframe')
        terminal_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        terminal_frame.columnconfigure(0, weight=1)
        terminal_frame.rowconfigure(0, weight=1)

        # Terminal text widget with gray theme
        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            height=10,
            font=self.mono_font,
            bg='#2a2a2a',
            fg='#00ff00',
            insertbackground='#00ff00',
            selectbackground='#404040',
            selectforeground='#ffffff'
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True)

    # 3D视图控制方法
    def on_scroll(self, event):
        """处理鼠标滚轮缩放"""
        if event.inaxes == self.ax:
            if event.button == 'up':
                self.zoom_in()
            elif event.button == 'down':
                self.zoom_out()

    def on_mouse_press(self, event):
        """处理鼠标按下事件"""
        if event.inaxes == self.ax:
            self.mouse_pressed = True
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def on_mouse_motion(self, event):
        """处理鼠标移动事件"""
        if hasattr(self, 'mouse_pressed') and self.mouse_pressed and event.inaxes == self.ax:
            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y

            # 更新视角
            self.view_azimuth += dx * 0.5
            self.view_elevation += dy * 0.5

            # 限制仰角范围
            self.view_elevation = max(-90, min(90, self.view_elevation))

            # 更新3D视图
            self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
            self.canvas.draw()

            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def view_top(self):
        """俯视图"""
        self.view_elevation = 90
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_side(self):
        """侧视图"""
        self.view_elevation = 0
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_front(self):
        """正视图"""
        self.view_elevation = 0
        self.view_azimuth = 90
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def reset_view(self):
        """复位视图"""
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.zoom_fit()

    def zoom_in(self):
        """放大"""
        self.zoom_factor *= 0.8
        self.apply_zoom()

    def zoom_out(self):
        """缩小"""
        self.zoom_factor *= 1.25
        self.apply_zoom()

    def zoom_fit(self):
        """适应窗口"""
        if hasattr(self, 'original_limits'):
            self.zoom_factor = 1.0
            self.apply_zoom()

    def apply_zoom(self):
        """应用缩放"""
        if hasattr(self, 'original_limits'):
            xlim, ylim, zlim = self.original_limits

            # 计算中心点
            x_center = (xlim[0] + xlim[1]) / 2
            y_center = (ylim[0] + ylim[1]) / 2
            z_center = (zlim[0] + zlim[1]) / 2

            # 计算新的范围
            x_range = (xlim[1] - xlim[0]) * self.zoom_factor / 2
            y_range = (ylim[1] - ylim[0]) * self.zoom_factor / 2
            z_range = (zlim[1] - zlim[0]) * self.zoom_factor / 2

            # 设置新的限制
            self.ax.set_xlim(x_center - x_range, x_center + x_range)
            self.ax.set_ylim(y_center - y_range, y_center + y_range)
            self.ax.set_zlim(z_center - z_range, z_center + z_range)

            self.canvas.draw()

    def log_message(self, message):
        """添加消息到终端输出"""
        timestamp = time.strftime("%H:%M:%S")
        self.terminal_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.terminal_text.see(tk.END)
        self.root.update_idletasks()

    def browse_bag_file(self):
        """Browse for bag file"""
        filename = filedialog.askopenfilename(
            title="Select ROS Bag File",
            filetypes=[("Bag files", "*.bag"), ("All files", "*.*")]
        )
        if filename:
            self.bag_file_path.set(filename)
            # 自动生成输出文件名
            base_name = os.path.splitext(os.path.basename(filename))[0]
            output_path = os.path.join(os.path.dirname(filename), f"{base_name}_trajectory.txt")
            self.output_file_path.set(output_path)

    def browse_output_file(self):
        """Browse for output file"""
        filename = filedialog.asksaveasfilename(
            title="Save Trajectory File",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.output_file_path.set(filename)

    def start_analysis(self):
        """Start GPS analysis"""
        if not self.bag_file_path.get():
            messagebox.showerror("Error", "Please select a bag file")
            return

        if not self.output_file_path.get():
            messagebox.showerror("Error", "Please specify output file path")
            return

        self.is_processing = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress.start()
        self.status_label.config(text="Processing...", style='Warning.TLabel')

        # 清除之前的结果
        self.ax.clear()
        self.setup_3d_plot_style()
        self.canvas.draw()
        self.analysis_text.delete(1.0, tk.END)
        self.terminal_text.delete(1.0, tk.END)

        # 在单独线程中开始处理
        self.processing_thread = threading.Thread(target=self.process_gps_data)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def stop_analysis(self):
        """Stop GPS analysis"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Stopped", style='Error.TLabel')
        self.log_message("Analysis stopped by user")

    def setup_3d_plot_style(self):
        """设置3D图表样式"""
        self.ax.set_facecolor('#ffffff')
        self.ax.set_title('GPS 3D Trajectory Visualization', color='black', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X (meters)', color='black', fontsize=12)
        self.ax.set_ylabel('Y (meters)', color='black', fontsize=12)
        self.ax.set_zlabel('Z (meters)', color='black', fontsize=12)

        # 设置坐标轴颜色
        self.ax.tick_params(colors='black')
        self.ax.xaxis.label.set_color('black')
        self.ax.yaxis.label.set_color('black')
        self.ax.zaxis.label.set_color('black')

        # 设置网格
        self.ax.grid(True, alpha=0.3, color='gray')

    def process_gps_data(self):
        """在后台线程中处理GPS数据"""
        try:
            self.log_message("Starting GPS data extraction...")

            # 提取GPS数据
            if not self.extract_gps_from_bag():
                return

            if not self.is_processing:
                return

            self.log_message("Converting to local coordinates...")
            self.convert_to_local_coordinates()

            if not self.is_processing:
                return

            self.log_message("Analyzing GPS quality...")
            analysis = self.analyze_gps_quality()

            if not self.is_processing:
                return

            self.log_message("Creating 3D visualization...")
            self.root.after(0, self.update_visualization, analysis)

            if not self.is_processing:
                return

            self.log_message("Saving trajectory to file...")
            self.save_trajectory_file()

            # 完成
            self.root.after(0, self.analysis_complete)

        except Exception as e:
            self.log_message(f"Error during processing: {str(e)}")
            self.root.after(0, self.analysis_error, str(e))

    def extract_gps_from_bag(self):
        """从bag文件中提取GPS数据"""
        try:
            import bagpy
        except ImportError:
            self.log_message("错误: 未安装bagpy。请运行: pip install bagpy")
            return False

        try:
            bag_file = self.bag_file_path.get()
            topic_name = self.gps_topic.get()

            self.log_message(f"读取bag文件: {os.path.basename(bag_file)}")
            bag = bagpy.bagreader(bag_file)

            self.log_message(f"提取话题: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            df = pd.read_csv(gps_csv)

            self.gps_data = []
            gps_count = 0

            for index, row in df.iterrows():
                if not self.is_processing:
                    return False

                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 1000 == 0:
                        self.log_message(f"已提取 {gps_count} 个GPS点...")

                except (ValueError, KeyError):
                    continue

            self.log_message(f"GPS数据提取完成: {len(self.gps_data)} 个点")
            return len(self.gps_data) > 0

        except Exception as e:
            self.log_message(f"读取bag文件失败: {str(e)}")
            return False

    def convert_to_local_coordinates(self):
        """转换GPS坐标到本地坐标系"""
        if not self.gps_data:
            return

        # 寻找原点
        for gps in self.gps_data:
            if gps['status'] >= 0:
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude']
                self.origin_alt = gps['altitude']
                break

        if self.origin_lat is None:
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return

        self.log_message(f"GPS原点: 纬度={self.origin_lat:.8f}, 经度={self.origin_lon:.8f}")

        # 转换为本地坐标
        self.trajectory_points = []
        for i, gps in enumerate(self.gps_data):
            if not self.is_processing:
                return

            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt

            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff

            point_info = {
                'frame_id': i + 1,  # 帧编号从1开始
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)

    def analyze_gps_quality(self):
        """分析GPS质量统计"""
        status_count = defaultdict(int)
        total_points = len(self.gps_data)

        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3
            status_count[status] += 1

        # 计算轨迹统计
        trajectory_length = 0.0
        start_end_distance = 0.0
        closure_error = 0.0

        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])

            # 计算轨迹长度
            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                trajectory_length += np.linalg.norm(p2 - p1)

            # 计算首尾距离
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0

        return {
            'status_count': status_count,
            'total_points': total_points,
            'trajectory_length': trajectory_length,
            'start_end_distance': start_end_distance,
            'closure_error': closure_error,
            'positions': np.array([p['position'] for p in self.trajectory_points]) if self.trajectory_points else None
        }

    def update_visualization(self, analysis):
        """更新3D可视化和分析结果"""
        try:
            # 清除之前的图表
            self.ax.clear()
            self.setup_3d_plot_style()

            # 按GPS状态分组
            status_groups = defaultdict(list)
            for i, point in enumerate(self.trajectory_points):
                status = point['status']
                if status not in self.quality_names:
                    status = 3
                status_groups[status].append(i)

            # 绘制3D轨迹，不同颜色表示不同GPS质量
            for status, indices in status_groups.items():
                if len(indices) < 2:
                    continue

                points = [self.trajectory_points[i]['position'] for i in indices]
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                z_coords = [p[2] for p in points]

                color = self.quality_colors[status]
                quality_name = self.quality_names[status]

                # RTK固定解用粗实线，其他用虚线
                if status == 0:  # RTK_FIXED
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=4, linestyle='-',
                               alpha=0.9, label=f'{quality_name}')
                else:
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=2.5, linestyle='--',
                               alpha=0.8, label=f'{quality_name}')

            # 标记起点和终点
            if self.trajectory_points:
                start_pos = self.trajectory_points[0]['position']
                end_pos = self.trajectory_points[-1]['position']

                self.ax.scatter(start_pos[0], start_pos[1], start_pos[2],
                              c='#00ff00', s=200, marker='o', edgecolors='white', linewidth=2,
                              label='起点', alpha=1.0, zorder=10)
                self.ax.scatter(end_pos[0], end_pos[1], end_pos[2],
                              c='#ff0000', s=200, marker='s', edgecolors='white', linewidth=2,
                              label='终点', alpha=1.0, zorder=10)

            # 添加图例
            legend = self.ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
            legend.get_frame().set_facecolor('#f0f0f0')
            legend.get_frame().set_edgecolor('black')
            for text in legend.get_texts():
                text.set_color('black')

            # 设置相等的坐标轴比例并保存原始限制
            if analysis['positions'] is not None:
                positions = analysis['positions']
                max_range = np.array([positions[:, 0].max() - positions[:, 0].min(),
                                    positions[:, 1].max() - positions[:, 1].min(),
                                    positions[:, 2].max() - positions[:, 2].min()]).max() / 2.0

                mid_x = (positions[:, 0].max() + positions[:, 0].min()) * 0.5
                mid_y = (positions[:, 1].max() + positions[:, 1].min()) * 0.5
                mid_z = (positions[:, 2].max() + positions[:, 2].min()) * 0.5

                self.ax.set_xlim(mid_x - max_range, mid_x + max_range)
                self.ax.set_ylim(mid_y - max_range, mid_y + max_range)
                self.ax.set_zlim(mid_z - max_range, mid_z + max_range)

                # 保存原始限制用于缩放
                self.original_limits = (
                    (mid_x - max_range, mid_x + max_range),
                    (mid_y - max_range, mid_y + max_range),
                    (mid_z - max_range, mid_z + max_range)
                )

            # 设置初始视角
            self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)

            # 更新画布
            self.canvas.draw()

            # 更新分析结果
            self.update_analysis_text(analysis)

        except Exception as e:
            self.log_message(f"更新可视化时出错: {str(e)}")

    def update_analysis_text(self, analysis):
        """更新分析结果文本（中文显示）"""
        self.analysis_text.delete(1.0, tk.END)

        # 格式化分析文本
        text_lines = []
        text_lines.append("=" * 45)
        text_lines.append("GPS Quality Analysis")
        text_lines.append("=" * 45)
        text_lines.append("")

        # GPS质量统计
        text_lines.append("GPS Quality Distribution:")
        text_lines.append("-" * 35)
        total_points = analysis['total_points']

        for status, count in sorted(analysis['status_count'].items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "未知")
            color_name = {
                '#CC0000': 'Red',
                '#008800': 'Green',
                '#0066CC': 'Blue',
                '#CC8800': 'Orange',
                '#8800CC': 'Purple'
            }.get(self.quality_colors.get(status), 'Gray')

            line_style = "Solid" if status == 0 else "Dashed"
            text_lines.append(f"{quality_name:12}: {count:6,d} points")
            text_lines.append(f"{'':14} ({percentage:5.1f}%)")
            text_lines.append(f"{'':14} {color_name} {line_style}")
            text_lines.append("")

        text_lines.append("=" * 45)
        text_lines.append("Trajectory Statistics")
        text_lines.append("=" * 45)
        text_lines.append("")

        # 轨迹统计
        text_lines.append(f"Total Points:      {total_points:,}")
        text_lines.append(f"Trajectory Length: {analysis['trajectory_length']:.1f} m")
        text_lines.append(f"Start-End Distance: {analysis['start_end_distance']:.1f} m")
        text_lines.append(f"Closure Error:     {analysis['closure_error']:.3f}%")
        text_lines.append("")

        # 坐标范围
        if analysis['positions'] is not None:
            positions = analysis['positions']
            text_lines.append("Coordinate Ranges:")
            text_lines.append("-" * 25)
            text_lines.append(f"X: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} m")
            text_lines.append(f"Y: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} m")
            text_lines.append(f"Z: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} m")
            text_lines.append("")

        # GPS原点
        text_lines.append("GPS Origin:")
        text_lines.append("-" * 18)
        text_lines.append(f"Latitude:  {self.origin_lat:.8f}")
        text_lines.append(f"Longitude: {self.origin_lon:.8f}")
        text_lines.append(f"Altitude:  {self.origin_alt:.3f} m")
        text_lines.append("")

        # 质量评估
        text_lines.append("Quality Assessment:")
        text_lines.append("-" * 20)
        if analysis['closure_error'] < 0.1:
            text_lines.append("Closure Error: EXCELLENT")
        elif analysis['closure_error'] < 0.5:
            text_lines.append("Closure Error: GOOD")
        elif analysis['closure_error'] < 1.0:
            text_lines.append("Closure Error: FAIR")
        else:
            text_lines.append("Closure Error: POOR")

        rtk_percentage = analysis['status_count'].get(0, 0) / total_points * 100
        if rtk_percentage > 95:
            text_lines.append("GPS Quality: EXCELLENT")
        elif rtk_percentage > 80:
            text_lines.append("GPS Quality: GOOD")
        elif rtk_percentage > 50:
            text_lines.append("GPS Quality: FAIR")
        else:
            text_lines.append("GPS Quality: POOR")

        # 输出文件信息
        text_lines.append("")
        text_lines.append("Output File:")
        text_lines.append("-" * 18)
        output_file = self.output_file_path.get()
        text_lines.append(f"Path: {os.path.basename(output_file)}")
        text_lines.append(f"Format: Frame_ID X Y Z")
        text_lines.append(f"Points: {len(self.trajectory_points)}")

        # 3D控制说明
        text_lines.append("")
        text_lines.append("3D Controls:")
        text_lines.append("-" * 15)
        text_lines.append("* Mouse Drag: Rotate view")
        text_lines.append("* Scroll Wheel: Zoom")
        text_lines.append("* Left Panel: View controls")

        # 插入文本
        analysis_text = "\n".join(text_lines)
        self.analysis_text.insert(1.0, analysis_text)

    def save_trajectory_file(self):
        """保存轨迹到文本文件，格式: 帧编号 X Y Z"""
        try:
            output_file = self.output_file_path.get()

            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入文件头
                f.write("# GPS轨迹数据\n")
                f.write("# 格式: 帧编号 X Y Z\n")
                f.write("# 坐标系: 本地ENU (东-北-上)\n")
                f.write(f"# 原点: 纬度={self.origin_lat:.8f}, 经度={self.origin_lon:.8f}, 高度={self.origin_alt:.3f}\n")
                f.write("# 单位: 米\n")
                f.write("#\n")

                # 写入轨迹点
                for point in self.trajectory_points:
                    frame_id = point['frame_id']
                    x, y, z = point['position']
                    f.write(f"{frame_id} {x:.6f} {y:.6f} {z:.6f}\n")

            self.log_message(f"Trajectory saved to: {os.path.basename(output_file)}")
            self.log_message(f"Total points saved: {len(self.trajectory_points)}")

        except Exception as e:
            self.log_message(f"Error saving trajectory file: {str(e)}")

    def analysis_complete(self):
        """Handle analysis completion"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Analysis Complete", style='Success.TLabel')
        self.log_message("GPS trajectory analysis completed successfully!")

        # 显示完成消息
        messagebox.showinfo("Analysis Complete",
                           f"GPS trajectory analysis completed!\n\n"
                           f"Points processed: {len(self.trajectory_points):,}\n"
                           f"Output file: {os.path.basename(self.output_file_path.get())}")

    def analysis_error(self, error_msg):
        """Handle analysis error"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Error", style='Error.TLabel')
        messagebox.showerror("Analysis Error", f"An error occurred during analysis:\n\n{error_msg}")

# Import GPS analysis modules
try:
    import bagpy
    USE_BAGPY = True
except ImportError:
    USE_BAGPY = False

def main():
    """主函数运行GUI应用程序"""
    # 检查依赖
    if not USE_BAGPY:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Missing Dependency",
                           "bagpy is required but not installed.\n\n"
                           "Please install it using:\n"
                           "pip install bagpy")
        return

    # 创建并运行GUI
    root = tk.Tk()
    app = GPSAnalyzerGUI(root)

    # 窗口居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass

    root.mainloop()

if __name__ == "__main__":
    main()
