#!/bin/bash

# GPS约束和强度处理参数调节脚本

echo "=========================================="
echo "🔧 GPS约束和强度处理参数调节"
echo "=========================================="

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "❌ ROS未运行，请先启动SLAM系统"
    exit 1
fi

echo "选择调节类型："
echo "1) GPS平面约束参数"
echo "2) 强度处理控制"
echo "3) 智能约束控制参数"
echo "4) 查看当前参数"
echo "5) 一键优化配置"
echo ""

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🔧 GPS平面约束参数调节"
        echo "================================"
        
        # 显示当前参数
        echo "当前GPS约束参数："
        echo "  启用状态: $(rosparam get /state_estimation_node/gps/enable_plane_constraint 2>/dev/null || echo '未设置')"
        echo "  约束权重: $(rosparam get /state_estimation_node/gps/plane_constraint_weight 2>/dev/null || echo '未设置')"
        echo "  校正率:   $(rosparam get /state_estimation_node/gps/xy_correction_rate 2>/dev/null || echo '未设置')"
        echo "  高度阈值: $(rosparam get /state_estimation_node/gps/height_threshold 2>/dev/null || echo '未设置')"
        echo ""
        
        echo "调节选项："
        echo "1) 完全禁用GPS约束"
        echo "2) 启用弱约束 (权重0.01)"
        echo "3) 启用中等约束 (权重0.05)"
        echo "4) 启用标准约束 (权重0.1)"
        echo "5) 自定义约束参数"
        echo ""
        
        read -p "请选择 (1-5): " gps_choice
        
        case $gps_choice in
            1)
                echo "禁用GPS约束..."
                rosparam set /state_estimation_node/gps/enable_plane_constraint false
                echo "✅ GPS约束已禁用"
                ;;
            2)
                echo "设置弱约束..."
                rosparam set /state_estimation_node/gps/enable_plane_constraint true
                rosparam set /state_estimation_node/gps/plane_constraint_weight 0.01
                rosparam set /state_estimation_node/gps/xy_correction_rate 0.005
                echo "✅ 弱GPS约束已设置"
                ;;
            3)
                echo "设置中等约束..."
                rosparam set /state_estimation_node/gps/enable_plane_constraint true
                rosparam set /state_estimation_node/gps/plane_constraint_weight 0.05
                rosparam set /state_estimation_node/gps/xy_correction_rate 0.02
                echo "✅ 中等GPS约束已设置"
                ;;
            4)
                echo "设置标准约束..."
                rosparam set /state_estimation_node/gps/enable_plane_constraint true
                rosparam set /state_estimation_node/gps/plane_constraint_weight 0.1
                rosparam set /state_estimation_node/gps/xy_correction_rate 0.05
                echo "✅ 标准GPS约束已设置"
                ;;
            5)
                echo "自定义约束参数："
                read -p "约束权重 (0.0-0.5): " weight
                read -p "校正率 (0.0-0.1): " rate
                read -p "高度阈值 (0.1-1.0): " height
                
                rosparam set /state_estimation_node/gps/enable_plane_constraint true
                rosparam set /state_estimation_node/gps/plane_constraint_weight $weight
                rosparam set /state_estimation_node/gps/xy_correction_rate $rate
                rosparam set /state_estimation_node/gps/height_threshold $height
                echo "✅ 自定义GPS约束已设置"
                ;;
        esac
        ;;
        
    2)
        echo ""
        echo "🚫 强度处理控制"
        echo "========================"
        
        # 显示当前状态
        echo "当前强度处理状态："
        echo "  强度保存: $(rosparam get /intensity_preserving_pcd_saver/enable_intensity_preservation 2>/dev/null || echo '未设置')"
        echo "  强度监控: $(rosparam get /intensity_quality_monitor/enable_monitoring 2>/dev/null || echo '未设置')"
        echo "  强度分析: $(rosparam get /simple_intensity_analyzer/enable_analysis 2>/dev/null || echo '未设置')"
        echo ""
        
        echo "控制选项："
        echo "1) 完全禁用强度处理"
        echo "2) 仅启用强度保存"
        echo "3) 启用强度保存和监控"
        echo "4) 启用所有强度功能"
        echo "5) 停止强度处理节点"
        echo ""
        
        read -p "请选择 (1-5): " intensity_choice
        
        case $intensity_choice in
            1)
                echo "禁用所有强度处理..."
                rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation false
                rosparam set /intensity_quality_monitor/enable_monitoring false
                rosparam set /simple_intensity_analyzer/enable_analysis false
                echo "✅ 所有强度处理已禁用"
                ;;
            2)
                echo "仅启用强度保存..."
                rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation true
                rosparam set /intensity_quality_monitor/enable_monitoring false
                rosparam set /simple_intensity_analyzer/enable_analysis false
                echo "✅ 仅强度保存已启用"
                ;;
            3)
                echo "启用强度保存和监控..."
                rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation true
                rosparam set /intensity_quality_monitor/enable_monitoring true
                rosparam set /simple_intensity_analyzer/enable_analysis false
                echo "✅ 强度保存和监控已启用"
                ;;
            4)
                echo "启用所有强度功能..."
                rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation true
                rosparam set /intensity_quality_monitor/enable_monitoring true
                rosparam set /simple_intensity_analyzer/enable_analysis true
                echo "✅ 所有强度功能已启用"
                ;;
            5)
                echo "停止强度处理节点..."
                rosnode kill /intensity_preserving_pcd_saver 2>/dev/null && echo "✅ 强度保存节点已停止" || echo "⚠️  强度保存节点未运行"
                rosnode kill /intensity_quality_monitor 2>/dev/null && echo "✅ 强度监控节点已停止" || echo "⚠️  强度监控节点未运行"
                rosnode kill /simple_intensity_analyzer 2>/dev/null && echo "✅ 强度分析节点已停止" || echo "⚠️  强度分析节点未运行"
                ;;
        esac
        ;;
        
    3)
        echo ""
        echo "⚙️  智能约束控制参数"
        echo "============================"
        
        # 显示当前参数
        echo "当前智能约束参数："
        echo "  ICP禁用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_disable_fitness 2>/dev/null || echo '未设置')"
        echo "  ICP启用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_enable_fitness 2>/dev/null || echo '未设置')"
        echo "  速度阈值:    $(rosparam get /intelligent_gps_constraint_controller/velocity_threshold 2>/dev/null || echo '未设置')"
        echo "  冷却时间:    $(rosparam get /intelligent_gps_constraint_controller/constraint_cooldown 2>/dev/null || echo '未设置')"
        echo ""
        
        echo "预设配置："
        echo "1) 保守配置 (匹配质量优先)"
        echo "2) 平衡配置 (默认推荐)"
        echo "3) 激进配置 (GPS约束优先)"
        echo "4) 自定义配置"
        echo ""
        
        read -p "请选择 (1-4): " smart_choice
        
        case $smart_choice in
            1)
                echo "设置保守配置..."
                rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.3
                rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.1
                rosparam set /intelligent_gps_constraint_controller/velocity_threshold 1.5
                rosparam set /intelligent_gps_constraint_controller/constraint_cooldown 8.0
                echo "✅ 保守配置已设置"
                ;;
            2)
                echo "设置平衡配置..."
                rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.4
                rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.15
                rosparam set /intelligent_gps_constraint_controller/velocity_threshold 2.0
                rosparam set /intelligent_gps_constraint_controller/constraint_cooldown 5.0
                echo "✅ 平衡配置已设置"
                ;;
            3)
                echo "设置激进配置..."
                rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.6
                rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.25
                rosparam set /intelligent_gps_constraint_controller/velocity_threshold 3.0
                rosparam set /intelligent_gps_constraint_controller/constraint_cooldown 3.0
                echo "✅ 激进配置已设置"
                ;;
            4)
                echo "自定义配置："
                read -p "ICP禁用阈值 (0.2-0.8): " disable_thresh
                read -p "ICP启用阈值 (0.05-0.3): " enable_thresh
                read -p "速度阈值 (1.0-5.0 m/s): " vel_thresh
                read -p "冷却时间 (2.0-10.0 s): " cooldown
                
                rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness $disable_thresh
                rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness $enable_thresh
                rosparam set /intelligent_gps_constraint_controller/velocity_threshold $vel_thresh
                rosparam set /intelligent_gps_constraint_controller/constraint_cooldown $cooldown
                echo "✅ 自定义配置已设置"
                ;;
        esac
        ;;
        
    4)
        echo ""
        echo "📊 当前系统参数"
        echo "========================"
        
        echo ""
        echo "GPS约束参数："
        echo "  启用状态: $(rosparam get /state_estimation_node/gps/enable_plane_constraint 2>/dev/null || echo '未设置')"
        echo "  约束权重: $(rosparam get /state_estimation_node/gps/plane_constraint_weight 2>/dev/null || echo '未设置')"
        echo "  校正率:   $(rosparam get /state_estimation_node/gps/xy_correction_rate 2>/dev/null || echo '未设置')"
        echo "  高度阈值: $(rosparam get /state_estimation_node/gps/height_threshold 2>/dev/null || echo '未设置')"
        
        echo ""
        echo "强度处理参数："
        echo "  强度保存: $(rosparam get /intensity_preserving_pcd_saver/enable_intensity_preservation 2>/dev/null || echo '未设置')"
        echo "  强度监控: $(rosparam get /intensity_quality_monitor/enable_monitoring 2>/dev/null || echo '未设置')"
        echo "  强度分析: $(rosparam get /simple_intensity_analyzer/enable_analysis 2>/dev/null || echo '未设置')"
        
        echo ""
        echo "智能约束参数："
        echo "  ICP禁用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_disable_fitness 2>/dev/null || echo '未设置')"
        echo "  ICP启用阈值: $(rosparam get /intelligent_gps_constraint_controller/constraint_enable_fitness 2>/dev/null || echo '未设置')"
        echo "  速度阈值:    $(rosparam get /intelligent_gps_constraint_controller/velocity_threshold 2>/dev/null || echo '未设置') m/s"
        echo "  冷却时间:    $(rosparam get /intelligent_gps_constraint_controller/constraint_cooldown 2>/dev/null || echo '未设置') s"
        
        echo ""
        echo "运行中的节点："
        rosnode list | grep -E "(state_estimation|intensity|intelligent)" | while read node; do
            echo "  ✅ $node"
        done
        ;;
        
    5)
        echo ""
        echo "🚀 一键优化配置"
        echo "========================"
        
        echo "应用推荐的优化配置..."
        
        # GPS约束优化
        rosparam set /state_estimation_node/gps/enable_plane_constraint true
        rosparam set /state_estimation_node/gps/plane_constraint_weight 0.03
        rosparam set /state_estimation_node/gps/xy_correction_rate 0.01
        rosparam set /state_estimation_node/gps/height_threshold 0.5
        
        # 禁用强度处理以提高性能
        rosparam set /intensity_preserving_pcd_saver/enable_intensity_preservation true
        rosparam set /intensity_quality_monitor/enable_monitoring false
        rosparam set /simple_intensity_analyzer/enable_analysis false
        
        # 智能约束优化
        rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.35
        rosparam set /intelligent_gps_constraint_controller/constraint_enable_fitness 0.12
        rosparam set /intelligent_gps_constraint_controller/velocity_threshold 2.5
        rosparam set /intelligent_gps_constraint_controller/constraint_cooldown 4.0
        
        # 智能检测优化
        rosparam set /intelligent_start_end_detector/return_threshold 40.0
        rosparam set /intelligent_start_end_detector/departure_threshold 25.0
        
        echo "✅ 优化配置已应用"
        echo ""
        echo "优化配置说明："
        echo "  🎯 GPS约束: 很弱约束，减少对SLAM的干扰"
        echo "  🚫 强度处理: 仅保存，不分析，提高性能"
        echo "  ⚙️  智能约束: 平衡配置，适应性强"
        echo "  📍 智能检测: 更敏感的触发阈值"
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "参数调节完成!"
echo "=========================================="
