#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build console version to see error messages
"""

import os
import sys
import subprocess
import shutil
import time

def build_console_exe():
    """构建控制台版本exe文件"""
    print("🚀 开始构建控制台版exe...")
    
    # 清理之前的构建
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 使用控制台模式的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console",  # 使用控制台模式而不是windowed
        "--name=GPS_3D_Analyzer_Console",
        "gps_gui_analyzer_fixed.py"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        build_time = time.time() - start_time
        print(f"构建耗时: {build_time:.1f}秒")
        
        if result.returncode == 0:
            print("✅ 控制台版构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_console_release():
    """创建控制台版发布包"""
    print("📦 创建控制台版发布包...")
    
    release_dir = "GPS_3D_Analyzer_Console_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制exe文件
    exe_path = "dist/GPS_3D_Analyzer_Console.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        
        # 获取文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ 控制台版exe文件复制完成 ({size_mb:.1f} MB)")
    else:
        print("❌ exe文件不存在")
        return False
    
    # 创建说明文件
    readme_content = f"""# GPS 3D 轨迹分析器 - 控制台调试版

## 🔧 用于调试的控制台版本

本版本显示控制台窗口，可以查看详细的错误信息和调试输出。

### 🚀 使用方法
1. 双击 GPS_3D_Analyzer_Console.exe 启动程序
2. 会显示控制台窗口和GUI窗口
3. 如果有错误，会在控制台窗口中显示详细信息
4. 可以查看bagpy修复过程的详细日志

### 🔍 调试信息
- bagpy导入状态
- version文件创建过程
- 错误详细信息
- 程序运行日志

### 📝 版本信息
- 版本: 1.0.5 (控制台调试版)
- 构建日期: {time.strftime("%Y-%m-%d")}
- 模式: 控制台 + GUI

---
用于调试bagpy问题的特殊版本
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 控制台版发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("🔧 GPS 3D轨迹分析器 - 控制台调试版构建")
    print("=" * 50)
    
    start_time = time.time()
    
    # 构建步骤
    steps = [
        ("构建控制台版exe", build_console_exe),
        ("创建控制台版发布包", create_console_release),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ 失败于步骤: {step_name}")
            return False
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("🎉 控制台调试版构建完成!")
    print("=" * 50)
    print(f"⏱️  总耗时: {total_time:.1f}秒")
    print("📁 发布包: GPS_3D_Analyzer_Console_Release/")
    print("🚀 exe文件: GPS_3D_Analyzer_Console.exe")
    print("\n✅ 这个版本会显示详细的错误信息")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
