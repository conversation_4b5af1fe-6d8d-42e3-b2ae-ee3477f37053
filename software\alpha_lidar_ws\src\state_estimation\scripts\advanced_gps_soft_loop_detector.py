#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度优化的GPS软约束回环检测器
专门处理GPS质量差的问题，通过多层质量评估和智能融合实现鲁棒的回环检测
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
import json
import threading
from collections import deque
import math
from scipy.spatial.distance import cdist
from scipy import stats

class AdvancedGPSSoftLoopDetector:
    def __init__(self):
        rospy.init_node('advanced_gps_soft_loop_detector', anonymous=True)
        
        # 深度优化参数
        self.gps_quality_weights = {
            4: 1.0,    # RTK Fixed - 最高质量
            3: 0.8,    # RTK Float - 高质量  
            2: 0.6,    # DGPS - 中等质量
            1: 0.4,    # GPS SPS - 低质量
            0: 0.2,    # Invalid - 很低质量
            -1: 0.1    # No GPS - 最低质量但仍可用
        }
        
        # 多层阈值配置
        self.proximity_thresholds = {
            'high_quality': rospy.get_param('~high_quality_threshold', 15.0),    # 高质量GPS
            'medium_quality': rospy.get_param('~medium_quality_threshold', 25.0), # 中等质量GPS
            'low_quality': rospy.get_param('~low_quality_threshold', 40.0),      # 低质量GPS
            'adaptive': rospy.get_param('~adaptive_threshold', 30.0)             # 自适应阈值
        }
        
        # 高级参数
        self.trajectory_consistency_window = rospy.get_param('~consistency_window', 10)
        self.gps_noise_estimation_window = rospy.get_param('~noise_window', 20)
        self.min_confidence_score = rospy.get_param('~min_confidence', 0.3)
        self.temporal_smoothing_factor = rospy.get_param('~smoothing_factor', 0.7)
        self.heading_consistency_threshold = rospy.get_param('~heading_threshold', 45.0)
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.slam_trajectory = deque(maxlen=2000)
        self.gps_quality_history = deque(maxlen=500)
        self.noise_estimates = deque(maxlen=100)
        
        # 状态变量
        self.current_gps_noise_level = 10.0  # 初始噪声估计
        self.adaptive_threshold = self.proximity_thresholds['adaptive']
        self.last_loop_time = 0
        self.total_detections = 0
        self.quality_based_detections = {'high': 0, 'medium': 0, 'low': 0}
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=20)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=20)
        
        # 发布器
        self.loop_trigger_pub = rospy.Publisher('/advanced_gps_loop_trigger', String, queue_size=1)
        self.confidence_pub = rospy.Publisher('/gps_loop_confidence', Float64, queue_size=1)
        self.quality_assessment_pub = rospy.Publisher('/gps_quality_assessment', String, queue_size=1)
        self.adaptive_threshold_pub = rospy.Publisher('/adaptive_threshold_status', String, queue_size=1)
        
        # 定时器
        self.detection_timer = rospy.Timer(rospy.Duration(1.5), self.advanced_loop_detection)
        self.quality_timer = rospy.Timer(rospy.Duration(5.0), self.update_quality_assessment)
        self.status_timer = rospy.Timer(rospy.Duration(15.0), self.publish_comprehensive_status)
        
        rospy.loginfo("🎯 Advanced GPS Soft Loop Detector Started")
        rospy.loginfo("Quality-based thresholds: High=%.1fm, Medium=%.1fm, Low=%.1fm", 
                     self.proximity_thresholds['high_quality'],
                     self.proximity_thresholds['medium_quality'], 
                     self.proximity_thresholds['low_quality'])
    
    def gps_callback(self, msg):
        """深度优化的GPS数据处理"""
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # GPS质量评估
            gps_quality = self.evaluate_gps_quality(msg)
            quality_weight = self.gps_quality_weights.get(msg.status.status, 0.1)
            
            # 坐标转换
            if len(self.gps_trajectory) == 0:
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                local_x, local_y = 0.0, 0.0
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            # 噪声估计
            noise_estimate = self.estimate_gps_noise(local_x, local_y, current_time)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'timestamp': current_time,
                'raw_quality': msg.status.status,
                'quality_score': gps_quality,
                'quality_weight': quality_weight,
                'noise_estimate': noise_estimate,
                'covariance': self.extract_covariance(msg),
                'satellites': getattr(msg.status, 'satellites_used', 0)
            }
            
            self.gps_trajectory.append(gps_point)
            self.gps_quality_history.append(gps_quality)
            
            # 更新噪声估计
            if noise_estimate > 0:
                self.noise_estimates.append(noise_estimate)
                self.update_noise_level()
    
    def evaluate_gps_quality(self, msg):
        """多维度GPS质量评估"""
        base_score = max(0.1, self.gps_quality_weights.get(msg.status.status, 0.1))
        
        # 考虑协方差信息
        if hasattr(msg, 'position_covariance') and len(msg.position_covariance) >= 9:
            # 提取位置不确定性
            pos_var = msg.position_covariance[0] + msg.position_covariance[4]  # x和y方差
            if pos_var > 0:
                uncertainty_factor = max(0.1, 1.0 / (1.0 + pos_var))
                base_score *= uncertainty_factor
        
        # 考虑卫星数量
        if hasattr(msg.status, 'satellites_used') and msg.status.satellites_used > 0:
            sat_factor = min(1.0, msg.status.satellites_used / 8.0)  # 8颗卫星为满分
            base_score *= (0.5 + 0.5 * sat_factor)
        
        return min(1.0, base_score)
    
    def extract_covariance(self, msg):
        """提取GPS协方差信息"""
        if hasattr(msg, 'position_covariance') and len(msg.position_covariance) >= 9:
            return {
                'xx': msg.position_covariance[0],
                'yy': msg.position_covariance[4],
                'xy': msg.position_covariance[1]
            }
        return {'xx': 100.0, 'yy': 100.0, 'xy': 0.0}  # 默认大不确定性
    
    def estimate_gps_noise(self, x, y, timestamp):
        """估计GPS噪声水平"""
        if len(self.gps_trajectory) < 3:
            return -1
        
        # 计算最近几个点的位置变化
        recent_points = list(self.gps_trajectory)[-self.gps_noise_estimation_window:]
        if len(recent_points) < 3:
            return -1
        
        # 计算位置变化的标准差
        positions = np.array([[p['x'], p['y']] for p in recent_points])
        if len(positions) < 3:
            return -1
        
        # 计算相邻点间距离的变化
        distances = []
        for i in range(1, len(positions)):
            dist = np.linalg.norm(positions[i] - positions[i-1])
            distances.append(dist)
        
        if len(distances) > 1:
            noise_estimate = np.std(distances)
            return noise_estimate
        
        return -1
    
    def update_noise_level(self):
        """更新当前噪声水平估计"""
        if len(self.noise_estimates) >= 5:
            # 使用中位数减少异常值影响
            self.current_gps_noise_level = np.median(list(self.noise_estimates))
            
            # 更新自适应阈值
            base_threshold = self.proximity_thresholds['adaptive']
            noise_factor = max(1.0, self.current_gps_noise_level / 5.0)
            self.adaptive_threshold = base_threshold * noise_factor
    
    def pose_callback(self, msg):
        """SLAM位姿数据处理"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            self.slam_trajectory.append(slam_point)
    
    def gps_to_local(self, lat, lon):
        """高精度GPS坐标转换"""
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 使用更精确的转换
        R_earth = 6378137.0  # 地球半径
        lat_rad = math.radians(self.origin_lat)
        
        x = lon_diff * R_earth * math.cos(lat_rad) * math.pi / 180.0
        y = lat_diff * R_earth * math.pi / 180.0
        
        return x, y
    
    def advanced_loop_detection(self, event):
        """深度优化的回环检测"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查数据充足性
                if len(self.gps_trajectory) < 20 or len(self.slam_trajectory) < 20:
                    return
                
                # 检查时间间隔
                if current_time - self.last_loop_time < 20.0:
                    return
                
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 多层质量检测
                loop_candidates = self.find_quality_based_candidates(current_gps)
                
                if loop_candidates:
                    best_candidate = self.advanced_candidate_validation(current_slam, loop_candidates)
                    
                    if best_candidate and best_candidate['confidence'] >= self.min_confidence_score:
                        self.trigger_advanced_loop_closure(current_slam, best_candidate)
                        
        except Exception as e:
            rospy.logerr("Error in advanced loop detection: %s", str(e))
    
    def find_quality_based_candidates(self, current_gps):
        """基于质量的候选点查找"""
        candidates = []
        current_time = current_gps['timestamp']
        current_quality = current_gps['quality_score']
        
        # 根据当前GPS质量选择阈值
        if current_quality >= 0.8:
            threshold = self.proximity_thresholds['high_quality']
            quality_level = 'high'
        elif current_quality >= 0.5:
            threshold = self.proximity_thresholds['medium_quality']
            quality_level = 'medium'
        else:
            threshold = max(self.proximity_thresholds['low_quality'], self.adaptive_threshold)
            quality_level = 'low'
        
        # 搜索历史点
        for i, gps_point in enumerate(self.gps_trajectory[:-20]):
            time_gap = current_time - gps_point['timestamp']
            if time_gap < 30.0:  # 最小时间间隔
                continue
            
            # 计算加权距离
            raw_distance = self.calculate_distance(current_gps, gps_point)
            
            # 质量加权
            quality_factor = (current_gps['quality_weight'] + gps_point['quality_weight']) / 2.0
            weighted_distance = raw_distance / max(0.1, quality_factor)
            
            # 噪声补偿
            noise_compensation = (current_gps['noise_estimate'] + gps_point['noise_estimate']) / 2.0
            if noise_compensation > 0:
                adjusted_threshold = threshold + noise_compensation * 2.0
            else:
                adjusted_threshold = threshold
            
            if weighted_distance <= adjusted_threshold:
                confidence = self.calculate_candidate_confidence(
                    current_gps, gps_point, raw_distance, quality_factor, time_gap)
                
                candidates.append({
                    'gps_point': gps_point,
                    'slam_index': i,
                    'raw_distance': raw_distance,
                    'weighted_distance': weighted_distance,
                    'quality_factor': quality_factor,
                    'confidence': confidence,
                    'time_gap': time_gap,
                    'quality_level': quality_level
                })
        
        # 按置信度排序
        candidates.sort(key=lambda x: x['confidence'], reverse=True)
        return candidates[:3]  # 返回最佳3个候选
    
    def calculate_candidate_confidence(self, current_gps, historical_gps, distance, quality_factor, time_gap):
        """计算候选点置信度"""
        # 距离置信度 (距离越近置信度越高)
        distance_confidence = max(0.0, 1.0 - distance / 50.0)
        
        # 质量置信度
        quality_confidence = quality_factor
        
        # 时间置信度 (时间间隔适中最好)
        optimal_time = 120.0  # 2分钟
        time_confidence = max(0.0, 1.0 - abs(time_gap - optimal_time) / optimal_time)
        
        # 噪声置信度
        avg_noise = (current_gps['noise_estimate'] + historical_gps['noise_estimate']) / 2.0
        if avg_noise > 0:
            noise_confidence = max(0.0, 1.0 - avg_noise / 20.0)
        else:
            noise_confidence = 0.5
        
        # 综合置信度
        total_confidence = (
            distance_confidence * 0.3 +
            quality_confidence * 0.3 +
            time_confidence * 0.2 +
            noise_confidence * 0.2
        )
        
        return min(1.0, total_confidence)
    
    def advanced_candidate_validation(self, current_slam, candidates):
        """高级候选验证"""
        best_candidate = None
        best_score = 0.0
        
        for candidate in candidates:
            slam_index = candidate['slam_index']
            if slam_index >= len(self.slam_trajectory):
                continue
            
            historical_slam = self.slam_trajectory[slam_index]
            
            # SLAM轨迹一致性检查
            slam_distance = self.calculate_distance(current_slam, historical_slam)
            trajectory_consistency = self.check_trajectory_consistency(
                current_slam, historical_slam, candidate['time_gap'])
            
            # 航向一致性
            heading_consistency = self.check_heading_consistency(current_slam, historical_slam)
            
            # 综合评分
            gps_score = candidate['confidence']
            slam_score = max(0.0, 1.0 - slam_distance / 30.0)
            consistency_score = trajectory_consistency
            heading_score = heading_consistency
            
            total_score = (
                gps_score * 0.4 +
                slam_score * 0.3 +
                consistency_score * 0.2 +
                heading_score * 0.1
            )
            
            if total_score > best_score and total_score >= self.min_confidence_score:
                best_score = total_score
                best_candidate = {
                    'candidate': candidate,
                    'historical_slam': historical_slam,
                    'slam_distance': slam_distance,
                    'confidence': total_score,
                    'gps_confidence': gps_score,
                    'slam_confidence': slam_score,
                    'consistency_score': consistency_score,
                    'heading_score': heading_score
                }
        
        return best_candidate

    def check_trajectory_consistency(self, current_slam, historical_slam, time_gap):
        """检查轨迹一致性"""
        try:
            # 获取轨迹片段
            current_segment = self.get_trajectory_segment(len(self.slam_trajectory) - 1,
                                                        self.trajectory_consistency_window)
            historical_index = self.find_slam_index_by_time(historical_slam['timestamp'])
            historical_segment = self.get_trajectory_segment(historical_index,
                                                           self.trajectory_consistency_window)

            if len(current_segment) < 3 or len(historical_segment) < 3:
                return 0.5  # 默认中等一致性

            # 计算轨迹形状相似度
            shape_similarity = self.calculate_trajectory_shape_similarity(current_segment, historical_segment)

            # 计算速度一致性
            velocity_consistency = self.calculate_velocity_consistency(current_segment, historical_segment)

            return (shape_similarity + velocity_consistency) / 2.0

        except Exception as e:
            rospy.logwarn("Error checking trajectory consistency: %s", str(e))
            return 0.5

    def check_heading_consistency(self, current_slam, historical_slam):
        """检查航向一致性"""
        try:
            def quat_to_yaw(q):
                return math.atan2(2.0 * (q['w'] * q['z'] + q['x'] * q['y']),
                                1.0 - 2.0 * (q['y'] * q['y'] + q['z'] * q['z']))

            current_yaw = quat_to_yaw(current_slam['orientation'])
            historical_yaw = quat_to_yaw(historical_slam['orientation'])

            angle_diff = abs(current_yaw - historical_yaw)
            angle_diff = min(angle_diff, 2*math.pi - angle_diff)
            angle_diff_deg = math.degrees(angle_diff)

            # 航向一致性评分
            if angle_diff_deg <= self.heading_consistency_threshold:
                return max(0.0, 1.0 - angle_diff_deg / self.heading_consistency_threshold)
            else:
                return 0.0

        except Exception as e:
            rospy.logwarn("Error checking heading consistency: %s", str(e))
            return 0.5

    def get_trajectory_segment(self, center_index, window_size):
        """获取轨迹片段"""
        start_idx = max(0, center_index - window_size // 2)
        end_idx = min(len(self.slam_trajectory), center_index + window_size // 2)
        return list(self.slam_trajectory)[start_idx:end_idx]

    def find_slam_index_by_time(self, target_time):
        """根据时间戳查找SLAM索引"""
        best_idx = 0
        min_time_diff = float('inf')

        for i, slam_point in enumerate(self.slam_trajectory):
            time_diff = abs(slam_point['timestamp'] - target_time)
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                best_idx = i

        return best_idx

    def calculate_trajectory_shape_similarity(self, segment1, segment2):
        """计算轨迹形状相似度"""
        if len(segment1) < 3 or len(segment2) < 3:
            return 0.5

        # 提取位置序列
        pos1 = np.array([[p['x'], p['y']] for p in segment1])
        pos2 = np.array([[p['x'], p['y']] for p in segment2])

        # 计算相对位置变化
        if len(pos1) > 1 and len(pos2) > 1:
            diff1 = np.diff(pos1, axis=0)
            diff2 = np.diff(pos2, axis=0)

            # 计算方向向量的相似度
            if len(diff1) > 0 and len(diff2) > 0:
                # 归一化方向向量
                norm1 = np.linalg.norm(diff1, axis=1, keepdims=True)
                norm2 = np.linalg.norm(diff2, axis=1, keepdims=True)

                norm1[norm1 == 0] = 1
                norm2[norm2 == 0] = 1

                dir1 = diff1 / norm1
                dir2 = diff2 / norm2

                # 计算平均相似度
                min_len = min(len(dir1), len(dir2))
                if min_len > 0:
                    similarities = []
                    for i in range(min_len):
                        dot_product = np.dot(dir1[i], dir2[i])
                        similarity = (dot_product + 1) / 2  # 归一化到[0,1]
                        similarities.append(similarity)

                    return np.mean(similarities)

        return 0.5

    def calculate_velocity_consistency(self, segment1, segment2):
        """计算速度一致性"""
        if len(segment1) < 2 or len(segment2) < 2:
            return 0.5

        # 计算平均速度
        def calc_avg_velocity(segment):
            velocities = []
            for i in range(1, len(segment)):
                dt = segment[i]['timestamp'] - segment[i-1]['timestamp']
                if dt > 0:
                    dx = segment[i]['x'] - segment[i-1]['x']
                    dy = segment[i]['y'] - segment[i-1]['y']
                    velocity = math.sqrt(dx*dx + dy*dy) / dt
                    velocities.append(velocity)
            return np.mean(velocities) if velocities else 0.0

        vel1 = calc_avg_velocity(segment1)
        vel2 = calc_avg_velocity(segment2)

        if vel1 == 0 and vel2 == 0:
            return 1.0

        max_vel = max(vel1, vel2)
        if max_vel == 0:
            return 1.0

        velocity_diff = abs(vel1 - vel2) / max_vel
        return max(0.0, 1.0 - velocity_diff)

    def trigger_advanced_loop_closure(self, current_pose, best_candidate):
        """触发高级回环检测"""
        try:
            self.last_loop_time = rospy.Time.now().to_sec()
            self.total_detections += 1

            # 统计质量级别
            quality_level = best_candidate['candidate']['quality_level']
            self.quality_based_detections[quality_level] += 1

            # 构建详细的回环数据
            loop_data = {
                'type': 'advanced_gps_soft_loop_closure',
                'timestamp': self.last_loop_time,
                'quality_level': quality_level,
                'confidence_scores': {
                    'overall': best_candidate['confidence'],
                    'gps': best_candidate['gps_confidence'],
                    'slam': best_candidate['slam_confidence'],
                    'consistency': best_candidate['consistency_score'],
                    'heading': best_candidate['heading_score']
                },
                'current_pose': {
                    'x': current_pose['x'],
                    'y': current_pose['y'],
                    'z': current_pose['z'],
                    'timestamp': current_pose['timestamp']
                },
                'historical_pose': {
                    'x': best_candidate['historical_slam']['x'],
                    'y': best_candidate['historical_slam']['y'],
                    'z': best_candidate['historical_slam']['z'],
                    'timestamp': best_candidate['historical_slam']['timestamp']
                },
                'gps_metrics': {
                    'raw_distance': best_candidate['candidate']['raw_distance'],
                    'weighted_distance': best_candidate['candidate']['weighted_distance'],
                    'quality_factor': best_candidate['candidate']['quality_factor'],
                    'current_noise_level': self.current_gps_noise_level,
                    'adaptive_threshold': self.adaptive_threshold
                },
                'slam_metrics': {
                    'slam_distance': best_candidate['slam_distance'],
                    'time_gap': best_candidate['candidate']['time_gap']
                }
            }

            # 发布回环触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.loop_trigger_pub.publish(trigger_msg)

            # 发布置信度
            confidence_msg = Float64()
            confidence_msg.data = best_candidate['confidence']
            self.confidence_pub.publish(confidence_msg)

            rospy.loginfo("🎯 Advanced GPS Soft Loop Closure Triggered!")
            rospy.loginfo("Quality: %s, Confidence: %.3f, GPS: %.2fm, SLAM: %.2fm",
                         quality_level, best_candidate['confidence'],
                         best_candidate['candidate']['raw_distance'],
                         best_candidate['slam_distance'])

        except Exception as e:
            rospy.logerr("Error triggering advanced loop closure: %s", str(e))

    def calculate_distance(self, point1, point2):
        """计算两点间距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        return math.sqrt(dx*dx + dy*dy)

    def update_quality_assessment(self, event):
        """更新质量评估"""
        try:
            with self.data_lock:
                if len(self.gps_quality_history) < 10:
                    return

                recent_quality = list(self.gps_quality_history)[-20:]
                avg_quality = np.mean(recent_quality)
                quality_std = np.std(recent_quality)

                assessment = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'average_quality': avg_quality,
                    'quality_stability': 1.0 / (1.0 + quality_std),
                    'current_noise_level': self.current_gps_noise_level,
                    'adaptive_threshold': self.adaptive_threshold,
                    'quality_distribution': {
                        'high': sum(1 for q in recent_quality if q >= 0.8) / len(recent_quality),
                        'medium': sum(1 for q in recent_quality if 0.5 <= q < 0.8) / len(recent_quality),
                        'low': sum(1 for q in recent_quality if q < 0.5) / len(recent_quality)
                    }
                }

                assessment_msg = String()
                assessment_msg.data = json.dumps(assessment, indent=2)
                self.quality_assessment_pub.publish(assessment_msg)

        except Exception as e:
            rospy.logerr("Error updating quality assessment: %s", str(e))

    def publish_comprehensive_status(self, event):
        """发布综合状态报告"""
        try:
            with self.data_lock:
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'detector_type': 'advanced_gps_soft_loop_detector',
                    'statistics': {
                        'total_detections': self.total_detections,
                        'quality_based_detections': self.quality_based_detections,
                        'gps_points': len(self.gps_trajectory),
                        'slam_points': len(self.slam_trajectory),
                        'current_noise_level': self.current_gps_noise_level,
                        'adaptive_threshold': self.adaptive_threshold
                    },
                    'current_quality': {
                        'average': np.mean(list(self.gps_quality_history)[-10:]) if len(self.gps_quality_history) >= 10 else 0.0,
                        'latest': self.gps_quality_history[-1] if self.gps_quality_history else 0.0
                    },
                    'parameters': {
                        'proximity_thresholds': self.proximity_thresholds,
                        'min_confidence_score': self.min_confidence_score,
                        'heading_consistency_threshold': self.heading_consistency_threshold
                    }
                }

                # 发布自适应阈值状态
                threshold_status = {
                    'current_threshold': self.adaptive_threshold,
                    'noise_level': self.current_gps_noise_level,
                    'base_threshold': self.proximity_thresholds['adaptive']
                }

                threshold_msg = String()
                threshold_msg.data = json.dumps(threshold_status)
                self.adaptive_threshold_pub.publish(threshold_msg)

                rospy.loginfo("Advanced GPS Soft Loop: %d detections, noise=%.2fm, threshold=%.1fm",
                             self.total_detections, self.current_gps_noise_level, self.adaptive_threshold)

        except Exception as e:
            rospy.logerr("Error publishing comprehensive status: %s", str(e))

def main():
    try:
        detector = AdvancedGPSSoftLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Advanced GPS Soft Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
