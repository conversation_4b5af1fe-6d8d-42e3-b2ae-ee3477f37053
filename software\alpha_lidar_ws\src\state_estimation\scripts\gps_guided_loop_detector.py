#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS引导的软约束回环检测器
不直接修改SLAM位置，而是通过GPS信息智能触发回环检测
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
import json
import threading
from collections import deque
import math

class GPSGuidedLoopDetector:
    def __init__(self):
        rospy.init_node('gps_guided_loop_detector', anonymous=True)
        
        # 参数配置
        self.gps_proximity_threshold = rospy.get_param('~gps_proximity_threshold', 20.0)  # GPS接近阈值
        self.trajectory_similarity_threshold = rospy.get_param('~trajectory_similarity_threshold', 15.0)  # 轨迹相似度阈值
        self.min_time_gap = rospy.get_param('~min_time_gap', 30.0)  # 最小时间间隔
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 50.0)  # 最小轨迹长度
        self.heading_similarity_threshold = rospy.get_param('~heading_similarity_threshold', 30.0)  # 航向相似度阈值(度)
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=1000)
        self.slam_trajectory = deque(maxlen=1000)
        self.trajectory_timestamps = deque(maxlen=1000)
        
        # 状态变量
        self.last_loop_detection_time = 0
        self.total_loop_detections = 0
        self.successful_loops = 0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=10)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=10)
        self.path_sub = rospy.Subscriber('/path', Path, self.path_callback, queue_size=1)
        
        # 发布器
        self.loop_trigger_pub = rospy.Publisher('/gps_guided_loop_trigger', String, queue_size=1)
        self.loop_candidate_pub = rospy.Publisher('/loop_candidate_poses', String, queue_size=1)
        self.status_pub = rospy.Publisher('/gps_guided_loop_status', String, queue_size=1)
        
        # 定时器
        self.detection_timer = rospy.Timer(rospy.Duration(2.0), self.detect_loop_opportunities)
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("GPS Guided Loop Detector Started")
        rospy.loginfo("GPS proximity threshold: %.1f meters", self.gps_proximity_threshold)
        rospy.loginfo("Trajectory similarity threshold: %.1f meters", self.trajectory_similarity_threshold)
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        if msg.status.status < 0:  # 接受所有GPS数据，包括质量差的
            return
            
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # 转换GPS坐标为局部坐标
            if len(self.gps_trajectory) == 0:
                # 第一个GPS点作为原点
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                local_x, local_y = 0.0, 0.0
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'timestamp': current_time,
                'quality': msg.status.status
            }
            
            self.gps_trajectory.append(gps_point)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            self.slam_trajectory.append(slam_point)
            self.trajectory_timestamps.append(current_time)
    
    def path_callback(self, msg):
        """处理路径数据"""
        # 可以用于更精确的轨迹分析
        pass
    
    def gps_to_local(self, lat, lon):
        """GPS坐标转换为局部坐标"""
        # 简化的坐标转换
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 近似转换 (适用于小范围)
        x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
        y = lat_diff * 110540.0
        
        return x, y
    
    def calculate_distance(self, point1, point2):
        """计算两点间距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        return math.sqrt(dx*dx + dy*dy)
    
    def calculate_heading_similarity(self, pose1, pose2):
        """计算两个位姿的航向相似度"""
        # 从四元数计算航向角
        def quat_to_yaw(q):
            return math.atan2(2.0 * (q['w'] * q['z'] + q['x'] * q['y']),
                            1.0 - 2.0 * (q['y'] * q['y'] + q['z'] * q['z']))
        
        yaw1 = quat_to_yaw(pose1['orientation'])
        yaw2 = quat_to_yaw(pose2['orientation'])
        
        # 计算角度差
        angle_diff = abs(yaw1 - yaw2)
        angle_diff = min(angle_diff, 2*math.pi - angle_diff)  # 取较小角度
        
        return math.degrees(angle_diff)
    
    def detect_loop_opportunities(self, event):
        """检测回环机会"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()
                
                # 检查是否有足够的数据
                if len(self.gps_trajectory) < 10 or len(self.slam_trajectory) < 10:
                    return
                
                # 检查时间间隔
                if current_time - self.last_loop_detection_time < self.min_time_gap:
                    return
                
                # 获取当前位置
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 检查轨迹长度
                if self.calculate_trajectory_length() < self.min_trajectory_length:
                    return
                
                # 寻找GPS接近的历史位置
                loop_candidates = self.find_gps_proximity_candidates(current_gps)
                
                if loop_candidates:
                    # 进一步验证回环候选
                    best_candidate = self.validate_loop_candidates(current_slam, loop_candidates)
                    
                    if best_candidate:
                        self.trigger_loop_closure(current_slam, best_candidate)
                        
        except Exception as e:
            rospy.logerr("Error in loop detection: %s", str(e))
    
    def find_gps_proximity_candidates(self, current_gps):
        """寻找GPS接近的候选位置"""
        candidates = []
        current_time = current_gps['timestamp']
        
        for i, gps_point in enumerate(self.gps_trajectory[:-10]):  # 排除最近的10个点
            # 检查时间间隔
            if current_time - gps_point['timestamp'] < self.min_time_gap:
                continue
            
            # 检查GPS距离
            distance = self.calculate_distance(current_gps, gps_point)
            if distance <= self.gps_proximity_threshold:
                candidates.append({
                    'gps_point': gps_point,
                    'slam_index': i,
                    'gps_distance': distance,
                    'time_gap': current_time - gps_point['timestamp']
                })
        
        # 按GPS距离排序
        candidates.sort(key=lambda x: x['gps_distance'])
        return candidates[:5]  # 返回最近的5个候选
    
    def validate_loop_candidates(self, current_slam, candidates):
        """验证回环候选"""
        best_candidate = None
        best_score = float('inf')
        
        for candidate in candidates:
            slam_index = candidate['slam_index']
            if slam_index >= len(self.slam_trajectory):
                continue
                
            historical_slam = self.slam_trajectory[slam_index]
            
            # 计算SLAM轨迹距离
            slam_distance = self.calculate_distance(current_slam, historical_slam)
            
            # 计算航向相似度
            heading_diff = self.calculate_heading_similarity(current_slam, historical_slam)
            
            # 综合评分
            distance_score = slam_distance
            heading_score = heading_diff / 180.0 * 50.0  # 归一化航向差异
            time_score = min(candidate['time_gap'] / 300.0, 1.0) * 10.0  # 时间间隔奖励
            
            total_score = distance_score + heading_score - time_score
            
            # 检查阈值
            if (slam_distance <= self.trajectory_similarity_threshold and 
                heading_diff <= self.heading_similarity_threshold and
                total_score < best_score):
                
                best_score = total_score
                best_candidate = {
                    'candidate': candidate,
                    'historical_slam': historical_slam,
                    'slam_distance': slam_distance,
                    'heading_diff': heading_diff,
                    'score': total_score
                }
        
        return best_candidate
    
    def trigger_loop_closure(self, current_pose, best_candidate):
        """触发回环检测"""
        try:
            self.last_loop_detection_time = rospy.Time.now().to_sec()
            self.total_loop_detections += 1
            
            # 构建回环触发消息
            loop_data = {
                'type': 'gps_guided_loop_closure',
                'timestamp': self.last_loop_detection_time,
                'current_pose': {
                    'x': current_pose['x'],
                    'y': current_pose['y'],
                    'z': current_pose['z']
                },
                'historical_pose': {
                    'x': best_candidate['historical_slam']['x'],
                    'y': best_candidate['historical_slam']['y'],
                    'z': best_candidate['historical_slam']['z']
                },
                'gps_distance': best_candidate['candidate']['gps_distance'],
                'slam_distance': best_candidate['slam_distance'],
                'heading_difference': best_candidate['heading_diff'],
                'confidence_score': 1.0 / (1.0 + best_candidate['score']),
                'time_gap': best_candidate['candidate']['time_gap']
            }
            
            # 发布回环触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.loop_trigger_pub.publish(trigger_msg)
            
            # 发布候选位姿
            candidate_msg = String()
            candidate_msg.data = json.dumps({
                'current_pose': loop_data['current_pose'],
                'target_pose': loop_data['historical_pose']
            })
            self.loop_candidate_pub.publish(candidate_msg)
            
            rospy.loginfo("🎯 GPS Guided Loop Closure Triggered!")
            rospy.loginfo("GPS distance: %.2f m, SLAM distance: %.2f m, Heading diff: %.1f°", 
                         best_candidate['candidate']['gps_distance'],
                         best_candidate['slam_distance'],
                         best_candidate['heading_diff'])
            
        except Exception as e:
            rospy.logerr("Error triggering loop closure: %s", str(e))
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.slam_trajectory) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(1, len(self.slam_trajectory)):
            total_length += self.calculate_distance(self.slam_trajectory[i-1], self.slam_trajectory[i])
        
        return total_length
    
    def publish_status(self, event):
        """发布状态报告"""
        try:
            with self.data_lock:
                status_report = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'detector_type': 'gps_guided_loop_detector',
                    'statistics': {
                        'total_detections': self.total_loop_detections,
                        'successful_loops': self.successful_loops,
                        'gps_points': len(self.gps_trajectory),
                        'slam_points': len(self.slam_trajectory),
                        'trajectory_length': self.calculate_trajectory_length()
                    },
                    'parameters': {
                        'gps_proximity_threshold': self.gps_proximity_threshold,
                        'trajectory_similarity_threshold': self.trajectory_similarity_threshold,
                        'min_time_gap': self.min_time_gap,
                        'heading_similarity_threshold': self.heading_similarity_threshold
                    }
                }
                
                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.status_pub.publish(status_msg)
                
                if len(self.gps_trajectory) > 0:
                    rospy.loginfo("GPS Guided Loop Detector: %d GPS points, %d SLAM points, %.1f m trajectory", 
                                 len(self.gps_trajectory), len(self.slam_trajectory), 
                                 self.calculate_trajectory_length())
                
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        detector = GPSGuidedLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in GPS Guided Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
