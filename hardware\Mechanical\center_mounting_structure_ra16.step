ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME(
  '/home/<USER>/Code/alpha_lidar/1_hardware/Mechanical/center_adapter.step'
  ,'2024-09-13T03:16:09',('Author'),(''),
  'Open CASCADE STEP processor 7.3','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('base_rs16_001','base_rs16_001','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#5413);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#194,#456,#538,#578,#661,#2164,#2191,#2218,
    #2245,#2301,#2356,#2411,#2466,#2521,#2556,#3348,#3715,#3742,#3797,
    #3847,#3897,#3947,#3974,#3981,#4020,#4027,#4034,#4154,#4203,#4210,
    #4259,#4286,#4313,#4320,#4444,#4493,#4542,#4591,#4640,#4689,#4738,
    #4790,#4845,#4872,#4899,#4926,#4981,#5036,#5091,#5146,#5201,#5251,
    #5306,#5336,#5363,#5373,#5385,#5389,#5393,#5397,#5401,#5405,#5409));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#84,#112,#140,#168));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(32.5,10.5,17.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(32.5,10.5,47.));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(32.5,10.5,7.));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(32.5,-10.25071429154,7.));
#35 = DIRECTION('',(-1.,0.,0.));
#36 = DIRECTION('',(0.,1.,0.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(20.750714291545,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(0.,-1.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(32.5,10.5,7.));
#47 = DIRECTION('',(-0.,-1.,-0.));
#48 = DIRECTION('',(-1.,0.,0.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(0.,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(0.,-1.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#22,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(32.5,0.5,7.));
#59 = SURFACE_CURVE('',#60,(#65,#72),.PCURVE_S1.);
#60 = CIRCLE('',#61,10.);
#61 = AXIS2_PLACEMENT_3D('',#62,#63,#64);
#62 = CARTESIAN_POINT('',(32.5,0.5,17.));
#63 = DIRECTION('',(-1.,0.,0.));
#64 = DIRECTION('',(0.,0.,-1.));
#65 = PCURVE('',#32,#66);
#66 = DEFINITIONAL_REPRESENTATION('',(#67),#71);
#67 = CIRCLE('',#68,10.);
#68 = AXIS2_PLACEMENT_2D('',#69,#70);
#69 = CARTESIAN_POINT('',(10.750714291545,-10.));
#70 = DIRECTION('',(0.,1.));
#71 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#72 = PCURVE('',#73,#78);
#73 = CYLINDRICAL_SURFACE('',#74,10.);
#74 = AXIS2_PLACEMENT_3D('',#75,#76,#77);
#75 = CARTESIAN_POINT('',(32.5,0.5,17.));
#76 = DIRECTION('',(-1.,0.,0.));
#77 = DIRECTION('',(0.,1.,0.));
#78 = DEFINITIONAL_REPRESENTATION('',(#79),#83);
#79 = LINE('',#80,#81);
#80 = CARTESIAN_POINT('',(-4.712388980385,0.));
#81 = VECTOR('',#82,1.);
#82 = DIRECTION('',(1.,0.));
#83 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#84 = ORIENTED_EDGE('',*,*,#85,.F.);
#85 = EDGE_CURVE('',#86,#57,#88,.T.);
#86 = VERTEX_POINT('',#87);
#87 = CARTESIAN_POINT('',(32.5,-10.25071429154,7.));
#88 = SURFACE_CURVE('',#89,(#93,#100),.PCURVE_S1.);
#89 = LINE('',#90,#91);
#90 = CARTESIAN_POINT('',(32.5,-10.25071429154,7.));
#91 = VECTOR('',#92,1.);
#92 = DIRECTION('',(0.,1.,0.));
#93 = PCURVE('',#32,#94);
#94 = DEFINITIONAL_REPRESENTATION('',(#95),#99);
#95 = LINE('',#96,#97);
#96 = CARTESIAN_POINT('',(0.,0.));
#97 = VECTOR('',#98,1.);
#98 = DIRECTION('',(1.,0.));
#99 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#100 = PCURVE('',#101,#106);
#101 = PLANE('',#102);
#102 = AXIS2_PLACEMENT_3D('',#103,#104,#105);
#103 = CARTESIAN_POINT('',(1.457E-14,2.379060296882,7.));
#104 = DIRECTION('',(0.,0.,1.));
#105 = DIRECTION('',(1.,0.,0.));
#106 = DEFINITIONAL_REPRESENTATION('',(#107),#111);
#107 = LINE('',#108,#109);
#108 = CARTESIAN_POINT('',(32.5,-12.62977458842));
#109 = VECTOR('',#110,1.);
#110 = DIRECTION('',(0.,1.));
#111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#112 = ORIENTED_EDGE('',*,*,#113,.T.);
#113 = EDGE_CURVE('',#86,#114,#116,.T.);
#114 = VERTEX_POINT('',#115);
#115 = CARTESIAN_POINT('',(32.5,-10.25071429154,22.));
#116 = SURFACE_CURVE('',#117,(#121,#128),.PCURVE_S1.);
#117 = LINE('',#118,#119);
#118 = CARTESIAN_POINT('',(32.5,-10.25071429154,7.));
#119 = VECTOR('',#120,1.);
#120 = DIRECTION('',(0.,0.,1.));
#121 = PCURVE('',#32,#122);
#122 = DEFINITIONAL_REPRESENTATION('',(#123),#127);
#123 = LINE('',#124,#125);
#124 = CARTESIAN_POINT('',(0.,0.));
#125 = VECTOR('',#126,1.);
#126 = DIRECTION('',(0.,-1.));
#127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#128 = PCURVE('',#129,#134);
#129 = CYLINDRICAL_SURFACE('',#130,54.5);
#130 = AXIS2_PLACEMENT_3D('',#131,#132,#133);
#131 = CARTESIAN_POINT('',(0.,-54.,7.));
#132 = DIRECTION('',(-0.,-0.,-1.));
#133 = DIRECTION('',(1.,0.,0.));
#134 = DEFINITIONAL_REPRESENTATION('',(#135),#139);
#135 = LINE('',#136,#137);
#136 = CARTESIAN_POINT('',(-0.931874526209,0.));
#137 = VECTOR('',#138,1.);
#138 = DIRECTION('',(-0.,-1.));
#139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#140 = ORIENTED_EDGE('',*,*,#141,.T.);
#141 = EDGE_CURVE('',#114,#142,#144,.T.);
#142 = VERTEX_POINT('',#143);
#143 = CARTESIAN_POINT('',(32.5,-10.25071429154,47.));
#144 = SURFACE_CURVE('',#145,(#149,#156),.PCURVE_S1.);
#145 = LINE('',#146,#147);
#146 = CARTESIAN_POINT('',(32.5,-10.25071429154,7.));
#147 = VECTOR('',#148,1.);
#148 = DIRECTION('',(0.,0.,1.));
#149 = PCURVE('',#32,#150);
#150 = DEFINITIONAL_REPRESENTATION('',(#151),#155);
#151 = LINE('',#152,#153);
#152 = CARTESIAN_POINT('',(0.,0.));
#153 = VECTOR('',#154,1.);
#154 = DIRECTION('',(0.,-1.));
#155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#156 = PCURVE('',#157,#162);
#157 = CYLINDRICAL_SURFACE('',#158,54.5);
#158 = AXIS2_PLACEMENT_3D('',#159,#160,#161);
#159 = CARTESIAN_POINT('',(0.,-54.,7.));
#160 = DIRECTION('',(0.,0.,-1.));
#161 = DIRECTION('',(1.,0.,0.));
#162 = DEFINITIONAL_REPRESENTATION('',(#163),#167);
#163 = LINE('',#164,#165);
#164 = CARTESIAN_POINT('',(5.35131078097,0.));
#165 = VECTOR('',#166,1.);
#166 = DIRECTION('',(0.,-1.));
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#168 = ORIENTED_EDGE('',*,*,#169,.T.);
#169 = EDGE_CURVE('',#142,#24,#170,.T.);
#170 = SURFACE_CURVE('',#171,(#175,#182),.PCURVE_S1.);
#171 = LINE('',#172,#173);
#172 = CARTESIAN_POINT('',(32.5,-10.25071429154,47.));
#173 = VECTOR('',#174,1.);
#174 = DIRECTION('',(0.,1.,0.));
#175 = PCURVE('',#32,#176);
#176 = DEFINITIONAL_REPRESENTATION('',(#177),#181);
#177 = LINE('',#178,#179);
#178 = CARTESIAN_POINT('',(0.,-40.));
#179 = VECTOR('',#180,1.);
#180 = DIRECTION('',(1.,0.));
#181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#182 = PCURVE('',#183,#188);
#183 = CYLINDRICAL_SURFACE('',#184,30.);
#184 = AXIS2_PLACEMENT_3D('',#185,#186,#187);
#185 = CARTESIAN_POINT('',(2.5,-10.25071429154,47.));
#186 = DIRECTION('',(0.,1.,0.));
#187 = DIRECTION('',(1.,0.,0.));
#188 = DEFINITIONAL_REPRESENTATION('',(#189),#193);
#189 = LINE('',#190,#191);
#190 = CARTESIAN_POINT('',(-0.,0.));
#191 = VECTOR('',#192,1.);
#192 = DIRECTION('',(-0.,1.));
#193 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#194 = ADVANCED_FACE('',(#195,#328,#359,#394,#425),#44,.F.);
#195 = FACE_BOUND('',#196,.F.);
#196 = EDGE_LOOP('',(#197,#198,#222,#250,#279,#307));
#197 = ORIENTED_EDGE('',*,*,#21,.T.);
#198 = ORIENTED_EDGE('',*,*,#199,.T.);
#199 = EDGE_CURVE('',#24,#200,#202,.T.);
#200 = VERTEX_POINT('',#201);
#201 = CARTESIAN_POINT('',(2.5,10.5,77.));
#202 = SURFACE_CURVE('',#203,(#208,#215),.PCURVE_S1.);
#203 = CIRCLE('',#204,30.);
#204 = AXIS2_PLACEMENT_3D('',#205,#206,#207);
#205 = CARTESIAN_POINT('',(2.5,10.5,47.));
#206 = DIRECTION('',(0.,-1.,0.));
#207 = DIRECTION('',(0.,0.,1.));
#208 = PCURVE('',#44,#209);
#209 = DEFINITIONAL_REPRESENTATION('',(#210),#214);
#210 = CIRCLE('',#211,30.);
#211 = AXIS2_PLACEMENT_2D('',#212,#213);
#212 = CARTESIAN_POINT('',(30.,-40.));
#213 = DIRECTION('',(0.,-1.));
#214 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#215 = PCURVE('',#183,#216);
#216 = DEFINITIONAL_REPRESENTATION('',(#217),#221);
#217 = LINE('',#218,#219);
#218 = CARTESIAN_POINT('',(4.712388980385,20.750714291545));
#219 = VECTOR('',#220,1.);
#220 = DIRECTION('',(-1.,0.));
#221 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#222 = ORIENTED_EDGE('',*,*,#223,.T.);
#223 = EDGE_CURVE('',#200,#224,#226,.T.);
#224 = VERTEX_POINT('',#225);
#225 = CARTESIAN_POINT('',(-2.5,10.5,77.));
#226 = SURFACE_CURVE('',#227,(#231,#238),.PCURVE_S1.);
#227 = LINE('',#228,#229);
#228 = CARTESIAN_POINT('',(32.5,10.5,77.));
#229 = VECTOR('',#230,1.);
#230 = DIRECTION('',(-1.,0.,0.));
#231 = PCURVE('',#44,#232);
#232 = DEFINITIONAL_REPRESENTATION('',(#233),#237);
#233 = LINE('',#234,#235);
#234 = CARTESIAN_POINT('',(0.,-70.));
#235 = VECTOR('',#236,1.);
#236 = DIRECTION('',(1.,0.));
#237 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#238 = PCURVE('',#239,#244);
#239 = PLANE('',#240);
#240 = AXIS2_PLACEMENT_3D('',#241,#242,#243);
#241 = CARTESIAN_POINT('',(1.457E-14,2.379060296882,77.));
#242 = DIRECTION('',(0.,0.,1.));
#243 = DIRECTION('',(1.,0.,0.));
#244 = DEFINITIONAL_REPRESENTATION('',(#245),#249);
#245 = LINE('',#246,#247);
#246 = CARTESIAN_POINT('',(32.5,8.120939703118));
#247 = VECTOR('',#248,1.);
#248 = DIRECTION('',(-1.,0.));
#249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#250 = ORIENTED_EDGE('',*,*,#251,.T.);
#251 = EDGE_CURVE('',#224,#252,#254,.T.);
#252 = VERTEX_POINT('',#253);
#253 = CARTESIAN_POINT('',(-32.5,10.5,47.));
#254 = SURFACE_CURVE('',#255,(#260,#267),.PCURVE_S1.);
#255 = CIRCLE('',#256,30.);
#256 = AXIS2_PLACEMENT_3D('',#257,#258,#259);
#257 = CARTESIAN_POINT('',(-2.5,10.5,47.));
#258 = DIRECTION('',(0.,-1.,0.));
#259 = DIRECTION('',(0.,0.,-1.));
#260 = PCURVE('',#44,#261);
#261 = DEFINITIONAL_REPRESENTATION('',(#262),#266);
#262 = CIRCLE('',#263,30.);
#263 = AXIS2_PLACEMENT_2D('',#264,#265);
#264 = CARTESIAN_POINT('',(35.,-40.));
#265 = DIRECTION('',(0.,1.));
#266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#267 = PCURVE('',#268,#273);
#268 = CYLINDRICAL_SURFACE('',#269,30.);
#269 = AXIS2_PLACEMENT_3D('',#270,#271,#272);
#270 = CARTESIAN_POINT('',(-2.5,10.5,47.));
#271 = DIRECTION('',(0.,-1.,0.));
#272 = DIRECTION('',(0.,0.,1.));
#273 = DEFINITIONAL_REPRESENTATION('',(#274),#278);
#274 = LINE('',#275,#276);
#275 = CARTESIAN_POINT('',(-3.14159265359,0.));
#276 = VECTOR('',#277,1.);
#277 = DIRECTION('',(1.,0.));
#278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#279 = ORIENTED_EDGE('',*,*,#280,.F.);
#280 = EDGE_CURVE('',#281,#252,#283,.T.);
#281 = VERTEX_POINT('',#282);
#282 = CARTESIAN_POINT('',(-32.5,10.5,17.));
#283 = SURFACE_CURVE('',#284,(#288,#295),.PCURVE_S1.);
#284 = LINE('',#285,#286);
#285 = CARTESIAN_POINT('',(-32.5,10.5,7.));
#286 = VECTOR('',#287,1.);
#287 = DIRECTION('',(0.,0.,1.));
#288 = PCURVE('',#44,#289);
#289 = DEFINITIONAL_REPRESENTATION('',(#290),#294);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(65.,0.));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(0.,-1.));
#294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#295 = PCURVE('',#296,#301);
#296 = PLANE('',#297);
#297 = AXIS2_PLACEMENT_3D('',#298,#299,#300);
#298 = CARTESIAN_POINT('',(-32.5,10.5,7.));
#299 = DIRECTION('',(1.,0.,0.));
#300 = DIRECTION('',(0.,-1.,0.));
#301 = DEFINITIONAL_REPRESENTATION('',(#302),#306);
#302 = LINE('',#303,#304);
#303 = CARTESIAN_POINT('',(0.,0.));
#304 = VECTOR('',#305,1.);
#305 = DIRECTION('',(0.,-1.));
#306 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#307 = ORIENTED_EDGE('',*,*,#308,.F.);
#308 = EDGE_CURVE('',#22,#281,#309,.T.);
#309 = SURFACE_CURVE('',#310,(#314,#321),.PCURVE_S1.);
#310 = LINE('',#311,#312);
#311 = CARTESIAN_POINT('',(32.5,10.5,17.));
#312 = VECTOR('',#313,1.);
#313 = DIRECTION('',(-1.,0.,0.));
#314 = PCURVE('',#44,#315);
#315 = DEFINITIONAL_REPRESENTATION('',(#316),#320);
#316 = LINE('',#317,#318);
#317 = CARTESIAN_POINT('',(0.,-10.));
#318 = VECTOR('',#319,1.);
#319 = DIRECTION('',(1.,0.));
#320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#321 = PCURVE('',#73,#322);
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#327);
#323 = LINE('',#324,#325);
#324 = CARTESIAN_POINT('',(0.,0.));
#325 = VECTOR('',#326,1.);
#326 = DIRECTION('',(0.,1.));
#327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#328 = FACE_BOUND('',#329,.F.);
#329 = EDGE_LOOP('',(#330));
#330 = ORIENTED_EDGE('',*,*,#331,.F.);
#331 = EDGE_CURVE('',#332,#332,#334,.T.);
#332 = VERTEX_POINT('',#333);
#333 = CARTESIAN_POINT('',(18.845508075689,10.5,38.));
#334 = SURFACE_CURVE('',#335,(#340,#347),.PCURVE_S1.);
#335 = CIRCLE('',#336,1.525);
#336 = AXIS2_PLACEMENT_3D('',#337,#338,#339);
#337 = CARTESIAN_POINT('',(17.320508075689,10.5,38.));
#338 = DIRECTION('',(0.,-1.,-2.2E-16));
#339 = DIRECTION('',(1.,0.,0.));
#340 = PCURVE('',#44,#341);
#341 = DEFINITIONAL_REPRESENTATION('',(#342),#346);
#342 = CIRCLE('',#343,1.525);
#343 = AXIS2_PLACEMENT_2D('',#344,#345);
#344 = CARTESIAN_POINT('',(15.179491924311,-31.));
#345 = DIRECTION('',(-1.,0.));
#346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#347 = PCURVE('',#348,#353);
#348 = CYLINDRICAL_SURFACE('',#349,1.525);
#349 = AXIS2_PLACEMENT_3D('',#350,#351,#352);
#350 = CARTESIAN_POINT('',(17.320508075689,-54.,38.));
#351 = DIRECTION('',(0.,-1.,-2.2E-16));
#352 = DIRECTION('',(1.,0.,0.));
#353 = DEFINITIONAL_REPRESENTATION('',(#354),#358);
#354 = LINE('',#355,#356);
#355 = CARTESIAN_POINT('',(0.,-64.5));
#356 = VECTOR('',#357,1.);
#357 = DIRECTION('',(1.,0.));
#358 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#359 = FACE_BOUND('',#360,.F.);
#360 = EDGE_LOOP('',(#361));
#361 = ORIENTED_EDGE('',*,*,#362,.T.);
#362 = EDGE_CURVE('',#363,#363,#365,.T.);
#363 = VERTEX_POINT('',#364);
#364 = CARTESIAN_POINT('',(-11.5,10.5,48.));
#365 = SURFACE_CURVE('',#366,(#371,#382),.PCURVE_S1.);
#366 = CIRCLE('',#367,11.5);
#367 = AXIS2_PLACEMENT_3D('',#368,#369,#370);
#368 = CARTESIAN_POINT('',(0.,10.5,48.));
#369 = DIRECTION('',(0.,1.,-0.));
#370 = DIRECTION('',(-1.,0.,0.));
#371 = PCURVE('',#44,#372);
#372 = DEFINITIONAL_REPRESENTATION('',(#373),#381);
#373 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#374,#375,#376,#377,#378,#379
,#380),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#374 = CARTESIAN_POINT('',(44.,-41.));
#375 = CARTESIAN_POINT('',(44.,-60.91858428704));
#376 = CARTESIAN_POINT('',(26.75,-50.95929214352));
#377 = CARTESIAN_POINT('',(9.5,-41.));
#378 = CARTESIAN_POINT('',(26.75,-31.04070785647));
#379 = CARTESIAN_POINT('',(44.,-21.08141571295));
#380 = CARTESIAN_POINT('',(44.,-41.));
#381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#382 = PCURVE('',#383,#388);
#383 = CYLINDRICAL_SURFACE('',#384,11.5);
#384 = AXIS2_PLACEMENT_3D('',#385,#386,#387);
#385 = CARTESIAN_POINT('',(0.,10.5,48.));
#386 = DIRECTION('',(0.,1.,-2.2E-16));
#387 = DIRECTION('',(-1.,0.,0.));
#388 = DEFINITIONAL_REPRESENTATION('',(#389),#393);
#389 = LINE('',#390,#391);
#390 = CARTESIAN_POINT('',(0.,0.));
#391 = VECTOR('',#392,1.);
#392 = DIRECTION('',(1.,0.));
#393 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#394 = FACE_BOUND('',#395,.F.);
#395 = EDGE_LOOP('',(#396));
#396 = ORIENTED_EDGE('',*,*,#397,.F.);
#397 = EDGE_CURVE('',#398,#398,#400,.T.);
#398 = VERTEX_POINT('',#399);
#399 = CARTESIAN_POINT('',(1.525,10.5,68.));
#400 = SURFACE_CURVE('',#401,(#406,#413),.PCURVE_S1.);
#401 = CIRCLE('',#402,1.525);
#402 = AXIS2_PLACEMENT_3D('',#403,#404,#405);
#403 = CARTESIAN_POINT('',(0.,10.5,68.));
#404 = DIRECTION('',(0.,-1.,-2.2E-16));
#405 = DIRECTION('',(1.,0.,0.));
#406 = PCURVE('',#44,#407);
#407 = DEFINITIONAL_REPRESENTATION('',(#408),#412);
#408 = CIRCLE('',#409,1.525);
#409 = AXIS2_PLACEMENT_2D('',#410,#411);
#410 = CARTESIAN_POINT('',(32.5,-61.));
#411 = DIRECTION('',(-1.,0.));
#412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#413 = PCURVE('',#414,#419);
#414 = CYLINDRICAL_SURFACE('',#415,1.525);
#415 = AXIS2_PLACEMENT_3D('',#416,#417,#418);
#416 = CARTESIAN_POINT('',(0.,-54.,68.));
#417 = DIRECTION('',(0.,-1.,-2.2E-16));
#418 = DIRECTION('',(1.,0.,0.));
#419 = DEFINITIONAL_REPRESENTATION('',(#420),#424);
#420 = LINE('',#421,#422);
#421 = CARTESIAN_POINT('',(0.,-64.5));
#422 = VECTOR('',#423,1.);
#423 = DIRECTION('',(1.,0.));
#424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#425 = FACE_BOUND('',#426,.F.);
#426 = EDGE_LOOP('',(#427));
#427 = ORIENTED_EDGE('',*,*,#428,.F.);
#428 = EDGE_CURVE('',#429,#429,#431,.T.);
#429 = VERTEX_POINT('',#430);
#430 = CARTESIAN_POINT('',(-15.79550807568,10.5,38.));
#431 = SURFACE_CURVE('',#432,(#437,#444),.PCURVE_S1.);
#432 = CIRCLE('',#433,1.525);
#433 = AXIS2_PLACEMENT_3D('',#434,#435,#436);
#434 = CARTESIAN_POINT('',(-17.32050807568,10.5,38.));
#435 = DIRECTION('',(0.,-1.,-2.2E-16));
#436 = DIRECTION('',(1.,0.,0.));
#437 = PCURVE('',#44,#438);
#438 = DEFINITIONAL_REPRESENTATION('',(#439),#443);
#439 = CIRCLE('',#440,1.525);
#440 = AXIS2_PLACEMENT_2D('',#441,#442);
#441 = CARTESIAN_POINT('',(49.820508075689,-31.));
#442 = DIRECTION('',(-1.,0.));
#443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#444 = PCURVE('',#445,#450);
#445 = CYLINDRICAL_SURFACE('',#446,1.525);
#446 = AXIS2_PLACEMENT_3D('',#447,#448,#449);
#447 = CARTESIAN_POINT('',(-17.32050807568,-54.,38.));
#448 = DIRECTION('',(0.,-1.,-2.2E-16));
#449 = DIRECTION('',(1.,0.,0.));
#450 = DEFINITIONAL_REPRESENTATION('',(#451),#455);
#451 = LINE('',#452,#453);
#452 = CARTESIAN_POINT('',(0.,-64.5));
#453 = VECTOR('',#454,1.);
#454 = DIRECTION('',(1.,0.));
#455 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#456 = ADVANCED_FACE('',(#457),#73,.T.);
#457 = FACE_BOUND('',#458,.T.);
#458 = EDGE_LOOP('',(#459,#460,#483,#511,#537));
#459 = ORIENTED_EDGE('',*,*,#56,.T.);
#460 = ORIENTED_EDGE('',*,*,#461,.T.);
#461 = EDGE_CURVE('',#57,#462,#464,.T.);
#462 = VERTEX_POINT('',#463);
#463 = CARTESIAN_POINT('',(-9.E-15,0.5,7.));
#464 = SURFACE_CURVE('',#465,(#469,#476),.PCURVE_S1.);
#465 = LINE('',#466,#467);
#466 = CARTESIAN_POINT('',(32.5,0.5,7.));
#467 = VECTOR('',#468,1.);
#468 = DIRECTION('',(-1.,0.,0.));
#469 = PCURVE('',#73,#470);
#470 = DEFINITIONAL_REPRESENTATION('',(#471),#475);
#471 = LINE('',#472,#473);
#472 = CARTESIAN_POINT('',(1.570796326795,0.));
#473 = VECTOR('',#474,1.);
#474 = DIRECTION('',(0.,1.));
#475 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#476 = PCURVE('',#101,#477);
#477 = DEFINITIONAL_REPRESENTATION('',(#478),#482);
#478 = LINE('',#479,#480);
#479 = CARTESIAN_POINT('',(32.5,-1.879060296882));
#480 = VECTOR('',#481,1.);
#481 = DIRECTION('',(-1.,0.));
#482 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#483 = ORIENTED_EDGE('',*,*,#484,.T.);
#484 = EDGE_CURVE('',#462,#485,#487,.T.);
#485 = VERTEX_POINT('',#486);
#486 = CARTESIAN_POINT('',(-32.5,0.5,7.));
#487 = SURFACE_CURVE('',#488,(#492,#499),.PCURVE_S1.);
#488 = LINE('',#489,#490);
#489 = CARTESIAN_POINT('',(32.5,0.5,7.));
#490 = VECTOR('',#491,1.);
#491 = DIRECTION('',(-1.,0.,0.));
#492 = PCURVE('',#73,#493);
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#498);
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(1.570796326795,0.));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(0.,1.));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = PCURVE('',#500,#505);
#500 = PLANE('',#501);
#501 = AXIS2_PLACEMENT_3D('',#502,#503,#504);
#502 = CARTESIAN_POINT('',(1.457E-14,2.379060296882,7.));
#503 = DIRECTION('',(0.,0.,1.));
#504 = DIRECTION('',(1.,0.,0.));
#505 = DEFINITIONAL_REPRESENTATION('',(#506),#510);
#506 = LINE('',#507,#508);
#507 = CARTESIAN_POINT('',(32.5,-1.879060296882));
#508 = VECTOR('',#509,1.);
#509 = DIRECTION('',(-1.,0.));
#510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#511 = ORIENTED_EDGE('',*,*,#512,.F.);
#512 = EDGE_CURVE('',#281,#485,#513,.T.);
#513 = SURFACE_CURVE('',#514,(#519,#526),.PCURVE_S1.);
#514 = CIRCLE('',#515,10.);
#515 = AXIS2_PLACEMENT_3D('',#516,#517,#518);
#516 = CARTESIAN_POINT('',(-32.5,0.5,17.));
#517 = DIRECTION('',(-1.,0.,0.));
#518 = DIRECTION('',(0.,0.,-1.));
#519 = PCURVE('',#73,#520);
#520 = DEFINITIONAL_REPRESENTATION('',(#521),#525);
#521 = LINE('',#522,#523);
#522 = CARTESIAN_POINT('',(-4.712388980385,65.));
#523 = VECTOR('',#524,1.);
#524 = DIRECTION('',(1.,0.));
#525 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#526 = PCURVE('',#296,#527);
#527 = DEFINITIONAL_REPRESENTATION('',(#528),#536);
#528 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#529,#530,#531,#532,#533,#534
,#535),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#529 = CARTESIAN_POINT('',(10.,1.7763568394E-15));
#530 = CARTESIAN_POINT('',(27.320508075689,1.7763568394E-15));
#531 = CARTESIAN_POINT('',(18.660254037844,-15.));
#532 = CARTESIAN_POINT('',(10.,-30.));
#533 = CARTESIAN_POINT('',(1.339745962156,-15.));
#534 = CARTESIAN_POINT('',(-7.320508075689,-1.42108547152E-14));
#535 = CARTESIAN_POINT('',(10.,1.7763568394E-15));
#536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#537 = ORIENTED_EDGE('',*,*,#308,.F.);
#538 = ADVANCED_FACE('',(#539),#101,.F.);
#539 = FACE_BOUND('',#540,.F.);
#540 = EDGE_LOOP('',(#541,#542,#577));
#541 = ORIENTED_EDGE('',*,*,#461,.T.);
#542 = ORIENTED_EDGE('',*,*,#543,.T.);
#543 = EDGE_CURVE('',#462,#86,#544,.T.);
#544 = SURFACE_CURVE('',#545,(#550,#561),.PCURVE_S1.);
#545 = CIRCLE('',#546,54.5);
#546 = AXIS2_PLACEMENT_3D('',#547,#548,#549);
#547 = CARTESIAN_POINT('',(0.,-54.,7.));
#548 = DIRECTION('',(0.,0.,-1.));
#549 = DIRECTION('',(1.,0.,0.));
#550 = PCURVE('',#101,#551);
#551 = DEFINITIONAL_REPRESENTATION('',(#552),#560);
#552 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#553,#554,#555,#556,#557,#558
,#559),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#553 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#554 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#555 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#556 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#557 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#558 = CARTESIAN_POINT('',(54.5,38.017708715621));
#559 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#561 = PCURVE('',#562,#567);
#562 = PLANE('',#563);
#563 = AXIS2_PLACEMENT_3D('',#564,#565,#566);
#564 = CARTESIAN_POINT('',(-3.21E-15,-54.,7.));
#565 = DIRECTION('',(0.,0.,1.));
#566 = DIRECTION('',(1.,0.,0.));
#567 = DEFINITIONAL_REPRESENTATION('',(#568),#576);
#568 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#569,#570,#571,#572,#573,#574
,#575),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#569 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#570 = CARTESIAN_POINT('',(54.5,-94.3967690125));
#571 = CARTESIAN_POINT('',(-27.25,-47.19838450625));
#572 = CARTESIAN_POINT('',(-109.,-1.754865011071E-14));
#573 = CARTESIAN_POINT('',(-27.25,47.198384506252));
#574 = CARTESIAN_POINT('',(54.5,94.396769012504));
#575 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#577 = ORIENTED_EDGE('',*,*,#85,.T.);
#578 = ADVANCED_FACE('',(#579),#129,.T.);
#579 = FACE_BOUND('',#580,.F.);
#580 = EDGE_LOOP('',(#581,#605,#633,#660));
#581 = ORIENTED_EDGE('',*,*,#582,.F.);
#582 = EDGE_CURVE('',#583,#86,#585,.T.);
#583 = VERTEX_POINT('',#584);
#584 = CARTESIAN_POINT('',(51.,-34.78412114942,7.));
#585 = SURFACE_CURVE('',#586,(#591,#598),.PCURVE_S1.);
#586 = CIRCLE('',#587,54.5);
#587 = AXIS2_PLACEMENT_3D('',#588,#589,#590);
#588 = CARTESIAN_POINT('',(0.,-54.,7.));
#589 = DIRECTION('',(0.,0.,1.));
#590 = DIRECTION('',(1.,0.,0.));
#591 = PCURVE('',#129,#592);
#592 = DEFINITIONAL_REPRESENTATION('',(#593),#597);
#593 = LINE('',#594,#595);
#594 = CARTESIAN_POINT('',(-0.,0.));
#595 = VECTOR('',#596,1.);
#596 = DIRECTION('',(-1.,0.));
#597 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#598 = PCURVE('',#562,#599);
#599 = DEFINITIONAL_REPRESENTATION('',(#600),#604);
#600 = CIRCLE('',#601,54.5);
#601 = AXIS2_PLACEMENT_2D('',#602,#603);
#602 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#603 = DIRECTION('',(1.,0.));
#604 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#605 = ORIENTED_EDGE('',*,*,#606,.F.);
#606 = EDGE_CURVE('',#607,#583,#609,.T.);
#607 = VERTEX_POINT('',#608);
#608 = CARTESIAN_POINT('',(51.000000000001,-34.78412114942,22.));
#609 = SURFACE_CURVE('',#610,(#614,#621),.PCURVE_S1.);
#610 = LINE('',#611,#612);
#611 = CARTESIAN_POINT('',(51.,-34.78412114942,7.));
#612 = VECTOR('',#613,1.);
#613 = DIRECTION('',(-0.,-0.,-1.));
#614 = PCURVE('',#129,#615);
#615 = DEFINITIONAL_REPRESENTATION('',(#616),#620);
#616 = LINE('',#617,#618);
#617 = CARTESIAN_POINT('',(-0.360332001536,0.));
#618 = VECTOR('',#619,1.);
#619 = DIRECTION('',(-0.,1.));
#620 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#621 = PCURVE('',#622,#627);
#622 = PLANE('',#623);
#623 = AXIS2_PLACEMENT_3D('',#624,#625,#626);
#624 = CARTESIAN_POINT('',(51.,-104.,22.));
#625 = DIRECTION('',(1.,0.,0.));
#626 = DIRECTION('',(0.,1.,0.));
#627 = DEFINITIONAL_REPRESENTATION('',(#628),#632);
#628 = LINE('',#629,#630);
#629 = CARTESIAN_POINT('',(69.215878850576,-15.));
#630 = VECTOR('',#631,1.);
#631 = DIRECTION('',(0.,-1.));
#632 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#633 = ORIENTED_EDGE('',*,*,#634,.T.);
#634 = EDGE_CURVE('',#607,#114,#635,.T.);
#635 = SURFACE_CURVE('',#636,(#641,#648),.PCURVE_S1.);
#636 = CIRCLE('',#637,54.5);
#637 = AXIS2_PLACEMENT_3D('',#638,#639,#640);
#638 = CARTESIAN_POINT('',(0.,-54.,22.));
#639 = DIRECTION('',(0.,0.,1.));
#640 = DIRECTION('',(1.,0.,0.));
#641 = PCURVE('',#129,#642);
#642 = DEFINITIONAL_REPRESENTATION('',(#643),#647);
#643 = LINE('',#644,#645);
#644 = CARTESIAN_POINT('',(-0.,-15.));
#645 = VECTOR('',#646,1.);
#646 = DIRECTION('',(-1.,0.));
#647 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#648 = PCURVE('',#649,#654);
#649 = PLANE('',#650);
#650 = AXIS2_PLACEMENT_3D('',#651,#652,#653);
#651 = CARTESIAN_POINT('',(-3.21E-15,-54.,22.));
#652 = DIRECTION('',(0.,0.,1.));
#653 = DIRECTION('',(1.,0.,0.));
#654 = DEFINITIONAL_REPRESENTATION('',(#655),#659);
#655 = CIRCLE('',#656,54.5);
#656 = AXIS2_PLACEMENT_2D('',#657,#658);
#657 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#658 = DIRECTION('',(1.,0.));
#659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#660 = ORIENTED_EDGE('',*,*,#113,.F.);
#661 = ADVANCED_FACE('',(#662,#1072,#1435,#1678,#1921),#157,.F.);
#662 = FACE_BOUND('',#663,.F.);
#663 = EDGE_LOOP('',(#664,#665,#689,#712,#879,#907));
#664 = ORIENTED_EDGE('',*,*,#141,.F.);
#665 = ORIENTED_EDGE('',*,*,#666,.T.);
#666 = EDGE_CURVE('',#114,#667,#669,.T.);
#667 = VERTEX_POINT('',#668);
#668 = CARTESIAN_POINT('',(-32.5,-10.25071429154,22.));
#669 = SURFACE_CURVE('',#670,(#675,#682),.PCURVE_S1.);
#670 = CIRCLE('',#671,54.5);
#671 = AXIS2_PLACEMENT_3D('',#672,#673,#674);
#672 = CARTESIAN_POINT('',(0.,-54.,22.));
#673 = DIRECTION('',(0.,0.,1.));
#674 = DIRECTION('',(1.,0.,0.));
#675 = PCURVE('',#157,#676);
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(6.28318530718,-15.));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(-1.,0.));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = PCURVE('',#649,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = CIRCLE('',#685,54.5);
#685 = AXIS2_PLACEMENT_2D('',#686,#687);
#686 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#687 = DIRECTION('',(1.,0.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = ORIENTED_EDGE('',*,*,#690,.T.);
#690 = EDGE_CURVE('',#667,#691,#693,.T.);
#691 = VERTEX_POINT('',#692);
#692 = CARTESIAN_POINT('',(-32.5,-10.25071429154,47.));
#693 = SURFACE_CURVE('',#694,(#698,#705),.PCURVE_S1.);
#694 = LINE('',#695,#696);
#695 = CARTESIAN_POINT('',(-32.5,-10.25071429154,7.));
#696 = VECTOR('',#697,1.);
#697 = DIRECTION('',(0.,0.,1.));
#698 = PCURVE('',#157,#699);
#699 = DEFINITIONAL_REPRESENTATION('',(#700),#704);
#700 = LINE('',#701,#702);
#701 = CARTESIAN_POINT('',(4.073467179799,0.));
#702 = VECTOR('',#703,1.);
#703 = DIRECTION('',(0.,-1.));
#704 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#705 = PCURVE('',#296,#706);
#706 = DEFINITIONAL_REPRESENTATION('',(#707),#711);
#707 = LINE('',#708,#709);
#708 = CARTESIAN_POINT('',(20.750714291545,0.));
#709 = VECTOR('',#710,1.);
#710 = DIRECTION('',(0.,-1.));
#711 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#712 = ORIENTED_EDGE('',*,*,#713,.F.);
#713 = EDGE_CURVE('',#714,#691,#716,.T.);
#714 = VERTEX_POINT('',#715);
#715 = CARTESIAN_POINT('',(-2.5,0.442630355265,77.));
#716 = SURFACE_CURVE('',#717,(#769,#824),.PCURVE_S1.);
#717 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#718,#719,#720,#721,#722,#723,
    #724,#725,#726,#727,#728,#729,#730,#731,#732,#733,#734,#735,#736,
    #737,#738,#739,#740,#741,#742,#743,#744,#745,#746,#747,#748,#749,
    #750,#751,#752,#753,#754,#755,#756,#757,#758,#759,#760,#761,#762,
    #763,#764,#765,#766,#767,#768),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#718 = CARTESIAN_POINT('',(-2.5,0.442630355265,77.));
#719 = CARTESIAN_POINT('',(-4.030660929134,0.372342562448,77.));
#720 = CARTESIAN_POINT('',(-5.557001109984,0.252966991495,
    76.910745745415));
#721 = CARTESIAN_POINT('',(-7.061308492277,8.554897016592E-02,
    76.733119403804));
#722 = CARTESIAN_POINT('',(-8.530578302969,-0.126134094958,
    76.47229489662));
#723 = CARTESIAN_POINT('',(-9.955307741387,-0.37682911179,
    76.135341716942));
#724 = CARTESIAN_POINT('',(-11.32888259906,-0.660654028833,
    75.730168330638));
#725 = CARTESIAN_POINT('',(-12.64711777331,-0.971698897506,
    75.264718067734));
#726 = CARTESIAN_POINT('',(-14.54049072935,-1.471415661953,
    74.486258331184));
#727 = CARTESIAN_POINT('',(-15.15901865798,-1.64394674869,
    74.212681704278));
#728 = CARTESIAN_POINT('',(-15.76339422341,-1.821421374862,
    73.926431468466));
#729 = CARTESIAN_POINT('',(-16.35360572471,-2.003236564164,
    73.628274282646));
#730 = CARTESIAN_POINT('',(-16.92971056446,-2.188821102092,
    73.318921578342));
#731 = CARTESIAN_POINT('',(-17.49182561213,-2.377639823615,
    72.999022767967));
#732 = CARTESIAN_POINT('',(-18.04011907114,-2.569196342707,
    72.669159043424));
#733 = CARTESIAN_POINT('',(-18.97425151859,-2.907844947631,
    72.076340565811));
#734 = CARTESIAN_POINT('',(-19.36609077284,-3.053924157999,
    71.817573365186));
#735 = CARTESIAN_POINT('',(-19.75040477387,-3.201079091574,
    71.553752177765));
#736 = CARTESIAN_POINT('',(-20.12727864309,-3.349124839632,
    71.28507860003));
#737 = CARTESIAN_POINT('',(-20.4967993623,-3.497884234948,
    71.011739694144));
#738 = CARTESIAN_POINT('',(-20.8590556745,-3.647187738741,
    70.733907871742));
#739 = CARTESIAN_POINT('',(-21.21413798467,-3.796873327624,
    70.451740777716));
#740 = CARTESIAN_POINT('',(-21.82388569338,-4.059543063463,
    69.949996600047));
#741 = CARTESIAN_POINT('',(-22.08162473526,-4.172427516671,
    69.732241863336));
#742 = CARTESIAN_POINT('',(-22.33539342004,-4.285373620522,
    69.512178445189));
#743 = CARTESIAN_POINT('',(-22.58522878304,-4.398316747341,
    69.289864578195));
#744 = CARTESIAN_POINT('',(-22.831166861,-4.51119376144,69.065355246206)
  );
#745 = CARTESIAN_POINT('',(-23.07324269206,-4.623943019111,
    68.838702184339));
#746 = CARTESIAN_POINT('',(-23.31149031578,-4.736504368628,
    68.609953878974));
#747 = CARTESIAN_POINT('',(-24.18004184364,-5.152585207726,
    67.754939484556));
#748 = CARTESIAN_POINT('',(-24.78634408163,-5.454529683584,
    67.115764259527));
#749 = CARTESIAN_POINT('',(-25.36555195311,-5.753510394634,
    66.462487557475));
#750 = CARTESIAN_POINT('',(-25.91823216873,-6.048419251835,
    65.795870883169));
#751 = CARTESIAN_POINT('',(-26.44484953777,-6.338177476018,
    65.116591843667));
#752 = CARTESIAN_POINT('',(-26.94578937857,-6.621732102613,
    64.425252118847));
#753 = CARTESIAN_POINT('',(-27.42136848529,-6.898053775369,
    63.722381140143));
#754 = CARTESIAN_POINT('',(-28.43471008533,-7.501115043567,
    62.11633190706));
#755 = CARTESIAN_POINT('',(-28.95831680645,-7.823193813645,
    61.207031462939));
#756 = CARTESIAN_POINT('',(-29.44312870504,-8.130399025932,
    60.281467669563));
#757 = CARTESIAN_POINT('',(-29.88935297581,-8.420744494947,
    59.340594035556));
#758 = CARTESIAN_POINT('',(-30.29700155261,-8.692254043407,
    58.385407925692));
#759 = CARTESIAN_POINT('',(-30.66594883901,-8.942999223851,
    57.416975081429));
#760 = CARTESIAN_POINT('',(-30.99597573493,-9.171151681559,
    56.436454790012));
#761 = CARTESIAN_POINT('',(-31.58818385939,-9.586352083804,
    54.417801970103));
#762 = CARTESIAN_POINT('',(-31.84747921698,-9.771614541471,
    53.378829835292));
#763 = CARTESIAN_POINT('',(-32.06430990485,-9.928946637832,
    52.329828156404));
#764 = CARTESIAN_POINT('',(-32.23829162124,-10.05667742523,
    51.272534310477));
#765 = CARTESIAN_POINT('',(-32.3690692971,-10.15344886378,
    50.208816373613));
#766 = CARTESIAN_POINT('',(-32.45635980941,-10.21829598982,
    49.140684763867));
#767 = CARTESIAN_POINT('',(-32.5,-10.25071429154,48.070303346272));
#768 = CARTESIAN_POINT('',(-32.5,-10.25071429154,47.));
#769 = PCURVE('',#157,#770);
#770 = DEFINITIONAL_REPRESENTATION('',(#771),#823);
#771 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#772,#773,#774,#775,#776,#777,
    #778,#779,#780,#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,
    #791,#792,#793,#794,#795,#796,#797,#798,#799,#800,#801,#802,#803,
    #804,#805,#806,#807,#808,#809,#810,#811,#812,#813,#814,#815,#816,
    #817,#818,#819,#820,#821,#822),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#772 = CARTESIAN_POINT('',(4.666501318344,-70.));
#773 = CARTESIAN_POINT('',(4.638386201217,-70.));
#774 = CARTESIAN_POINT('',(4.610308963863,-69.91074574541));
#775 = CARTESIAN_POINT('',(4.58256127389,-69.7331194038));
#776 = CARTESIAN_POINT('',(4.555351183766,-69.47229489662));
#777 = CARTESIAN_POINT('',(4.528831703011,-69.13534171694));
#778 = CARTESIAN_POINT('',(4.503113104397,-68.73016833063));
#779 = CARTESIAN_POINT('',(4.478269707071,-68.26471806773));
#780 = CARTESIAN_POINT('',(4.442339287751,-67.48625833118));
#781 = CARTESIAN_POINT('',(4.430558053857,-67.21268170427));
#782 = CARTESIAN_POINT('',(4.419002114107,-66.92643146846));
#783 = CARTESIAN_POINT('',(4.407672276975,-66.62827428264));
#784 = CARTESIAN_POINT('',(4.396568362091,-66.31892157834));
#785 = CARTESIAN_POINT('',(4.385689316432,-65.99902276796));
#786 = CARTESIAN_POINT('',(4.375033305211,-65.66915904342));
#787 = CARTESIAN_POINT('',(4.356801693849,-65.07634056581));
#788 = CARTESIAN_POINT('',(4.349128923035,-64.81757336518));
#789 = CARTESIAN_POINT('',(4.341578514307,-64.55375217776));
#790 = CARTESIAN_POINT('',(4.334149529238,-64.28507860003));
#791 = CARTESIAN_POINT('',(4.326841036624,-64.01173969414));
#792 = CARTESIAN_POINT('',(4.319652107431,-63.73390787174));
#793 = CARTESIAN_POINT('',(4.31258180974,-63.45174077771));
#794 = CARTESIAN_POINT('',(4.300399820521,-62.94999660004));
#795 = CARTESIAN_POINT('',(4.295237055849,-62.73224186333));
#796 = CARTESIAN_POINT('',(4.290140533724,-62.51217844518));
#797 = CARTESIAN_POINT('',(4.285109904533,-62.28986457819));
#798 = CARTESIAN_POINT('',(4.280144844993,-62.0653552462));
#799 = CARTESIAN_POINT('',(4.275245058153,-61.83870218433));
#800 = CARTESIAN_POINT('',(4.2704102734,-61.60995387897));
#801 = CARTESIAN_POINT('',(4.252739252829,-60.75493948455));
#802 = CARTESIAN_POINT('',(4.240312675742,-60.11576425952));
#803 = CARTESIAN_POINT('',(4.228354677077,-59.46248755747));
#804 = CARTESIAN_POINT('',(4.216862395042,-58.79587088316));
#805 = CARTESIAN_POINT('',(4.205835252749,-58.11659184366));
#806 = CARTESIAN_POINT('',(4.195274472552,-57.42525211884));
#807 = CARTESIAN_POINT('',(4.185182796164,-56.72238114014));
#808 = CARTESIAN_POINT('',(4.163545854221,-55.11633190706));
#809 = CARTESIAN_POINT('',(4.15226752564,-54.20703146293));
#810 = CARTESIAN_POINT('',(4.14173782768,-53.28146766956));
#811 = CARTESIAN_POINT('',(4.131970843424,-52.34059403555));
#812 = CARTESIAN_POINT('',(4.122984702534,-51.38540792569));
#813 = CARTESIAN_POINT('',(4.114800033086,-50.41697508142));
#814 = CARTESIAN_POINT('',(4.107438498859,-49.43645479001));
#815 = CARTESIAN_POINT('',(4.094167713647,-47.4178019701));
#816 = CARTESIAN_POINT('',(4.088320628189,-46.37882983529));
#817 = CARTESIAN_POINT('',(4.083405255123,-45.3298281564));
#818 = CARTESIAN_POINT('',(4.079445045127,-44.27253431047));
#819 = CARTESIAN_POINT('',(4.076459944281,-43.20881637361));
#820 = CARTESIAN_POINT('',(4.07446467729,-42.14068476386));
#821 = CARTESIAN_POINT('',(4.073467179799,-41.07030334627));
#822 = CARTESIAN_POINT('',(4.073467179799,-40.));
#823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#824 = PCURVE('',#268,#825);
#825 = DEFINITIONAL_REPRESENTATION('',(#826),#878);
#826 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#827,#828,#829,#830,#831,#832,
    #833,#834,#835,#836,#837,#838,#839,#840,#841,#842,#843,#844,#845,
    #846,#847,#848,#849,#850,#851,#852,#853,#854,#855,#856,#857,#858,
    #859,#860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870,#871,
    #872,#873,#874,#875,#876,#877),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.205710434843,0.308923960068,0.386031825793,0.444028291047,
    0.600885272323,0.796884324289,1.),.UNSPECIFIED.);
#827 = CARTESIAN_POINT('',(0.,10.057369644735));
#828 = CARTESIAN_POINT('',(5.102203097114E-02,10.127657437552));
#829 = CARTESIAN_POINT('',(0.10190003412,10.247033008506));
#830 = CARTESIAN_POINT('',(0.152246027461,10.414451029829));
#831 = CARTESIAN_POINT('',(0.201824029114,10.626134094965));
#832 = CARTESIAN_POINT('',(0.250476345728,10.876829111785));
#833 = CARTESIAN_POINT('',(0.298101406377,11.160654028835));
#834 = CARTESIAN_POINT('',(0.344641426314,11.471698897506));
#835 = CARTESIAN_POINT('',(0.412880065679,11.971415661953));
#836 = CARTESIAN_POINT('',(0.435416538587,12.14394674869));
#837 = CARTESIAN_POINT('',(0.457695407563,12.321421374862));
#838 = CARTESIAN_POINT('',(0.479722902974,12.503236564164));
#839 = CARTESIAN_POINT('',(0.501506484055,12.688821102092));
#840 = CARTESIAN_POINT('',(0.523054810611,12.877639823614));
#841 = CARTESIAN_POINT('',(0.544377786891,13.069196342707));
#842 = CARTESIAN_POINT('',(0.58125652477,13.407844947631));
#843 = CARTESIAN_POINT('',(0.596906366042,13.553924157999));
#844 = CARTESIAN_POINT('',(0.612440658259,13.701079091574));
#845 = CARTESIAN_POINT('',(0.627863791904,13.849124839632));
#846 = CARTESIAN_POINT('',(0.643180129723,13.997884234948));
#847 = CARTESIAN_POINT('',(0.65839404531,14.147187738741));
#848 = CARTESIAN_POINT('',(0.673509961693,14.296873327624));
#849 = CARTESIAN_POINT('',(0.699831467224,14.559543063463));
#850 = CARTESIAN_POINT('',(0.71107757939,14.672427516671));
#851 = CARTESIAN_POINT('',(0.722272585838,14.785373620522));
#852 = CARTESIAN_POINT('',(0.733418318419,14.898316747341));
#853 = CARTESIAN_POINT('',(0.744516581402,15.01119376144));
#854 = CARTESIAN_POINT('',(0.755569151477,15.123943019111));
#855 = CARTESIAN_POINT('',(0.766577777754,15.236504368628));
#856 = CARTESIAN_POINT('',(0.80720386914,15.652585207726));
#857 = CARTESIAN_POINT('',(0.836553069178,15.954529683584));
#858 = CARTESIAN_POINT('',(0.865628145104,16.253510394634));
#859 = CARTESIAN_POINT('',(0.894461061752,16.548419251835));
#860 = CARTESIAN_POINT('',(0.923080817859,16.838177476018));
#861 = CARTESIAN_POINT('',(0.951514555006,17.121732102613));
#862 = CARTESIAN_POINT('',(0.979788343189,17.398053775369));
#863 = CARTESIAN_POINT('',(1.043088820083,18.001115043567));
#864 = CARTESIAN_POINT('',(1.078036321246,18.323193813645));
#865 = CARTESIAN_POINT('',(1.112818465381,18.630399025932));
#866 = CARTESIAN_POINT('',(1.14747498225,18.920744494947));
#867 = CARTESIAN_POINT('',(1.182039122671,19.192254043407));
#868 = CARTESIAN_POINT('',(1.216538985523,19.442999223851));
#869 = CARTESIAN_POINT('',(1.250998061565,19.671151681559));
#870 = CARTESIAN_POINT('',(1.32112231375,20.086352083804));
#871 = CARTESIAN_POINT('',(1.356787271832,20.271614541471));
#872 = CARTESIAN_POINT('',(1.392443830184,20.428946637832));
#873 = CARTESIAN_POINT('',(1.428101688253,20.556677425236));
#874 = CARTESIAN_POINT('',(1.463766579988,20.653448863782));
#875 = CARTESIAN_POINT('',(1.499440169945,20.718295989828));
#876 = CARTESIAN_POINT('',(1.535119548586,20.750714291545));
#877 = CARTESIAN_POINT('',(1.570796326795,20.750714291545));
#878 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#879 = ORIENTED_EDGE('',*,*,#880,.T.);
#880 = EDGE_CURVE('',#714,#881,#883,.T.);
#881 = VERTEX_POINT('',#882);
#882 = CARTESIAN_POINT('',(2.5,0.442630355265,77.));
#883 = SURFACE_CURVE('',#884,(#889,#896),.PCURVE_S1.);
#884 = CIRCLE('',#885,54.5);
#885 = AXIS2_PLACEMENT_3D('',#886,#887,#888);
#886 = CARTESIAN_POINT('',(0.,-54.,77.));
#887 = DIRECTION('',(0.,0.,-1.));
#888 = DIRECTION('',(1.,0.,0.));
#889 = PCURVE('',#157,#890);
#890 = DEFINITIONAL_REPRESENTATION('',(#891),#895);
#891 = LINE('',#892,#893);
#892 = CARTESIAN_POINT('',(0.,-70.));
#893 = VECTOR('',#894,1.);
#894 = DIRECTION('',(1.,0.));
#895 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#896 = PCURVE('',#239,#897);
#897 = DEFINITIONAL_REPRESENTATION('',(#898),#906);
#898 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#899,#900,#901,#902,#903,#904
,#905),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM(
  '') );
#899 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#900 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#901 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#902 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#903 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#904 = CARTESIAN_POINT('',(54.5,38.017708715621));
#905 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#907 = ORIENTED_EDGE('',*,*,#908,.F.);
#908 = EDGE_CURVE('',#142,#881,#909,.T.);
#909 = SURFACE_CURVE('',#910,(#962,#1017),.PCURVE_S1.);
#910 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#911,#912,#913,#914,#915,#916,
    #917,#918,#919,#920,#921,#922,#923,#924,#925,#926,#927,#928,#929,
    #930,#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,#942,
    #943,#944,#945,#946,#947,#948,#949,#950,#951,#952,#953,#954,#955,
    #956,#957,#958,#959,#960,#961),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,
    9),(0.,0.203115675711,0.399114727677,0.555971708952,0.613968174207,
    0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#911 = CARTESIAN_POINT('',(32.5,-10.25071429154,47.));
#912 = CARTESIAN_POINT('',(32.5,-10.25071429154,48.070303346272));
#913 = CARTESIAN_POINT('',(32.456359809417,-10.21829598982,
    49.140684763866));
#914 = CARTESIAN_POINT('',(32.369069297108,-10.15344886378,
    50.208816373612));
#915 = CARTESIAN_POINT('',(32.238291621244,-10.05667742523,
    51.272534310476));
#916 = CARTESIAN_POINT('',(32.064309904859,-9.928946637833,
    52.329828156402));
#917 = CARTESIAN_POINT('',(31.847479216987,-9.771614541471,
    53.378829835289));
#918 = CARTESIAN_POINT('',(31.588183859399,-9.586352083805,
    54.417801970101));
#919 = CARTESIAN_POINT('',(30.995975734933,-9.171151681559,
    56.43645479001));
#920 = CARTESIAN_POINT('',(30.665948839013,-8.942999223851,
    57.416975081426));
#921 = CARTESIAN_POINT('',(30.297001552611,-8.692254043408,
    58.385407925689));
#922 = CARTESIAN_POINT('',(29.88935297582,-8.420744494948,
    59.340594035554));
#923 = CARTESIAN_POINT('',(29.443128705041,-8.130399025933,
    60.28146766956));
#924 = CARTESIAN_POINT('',(28.958316806459,-7.823193813646,
    61.207031462936));
#925 = CARTESIAN_POINT('',(28.434710085336,-7.501115043568,
    62.116331907058));
#926 = CARTESIAN_POINT('',(27.421368485292,-6.89805377537,63.72238114014
    ));
#927 = CARTESIAN_POINT('',(26.945789378574,-6.621732102614,
    64.425252118845));
#928 = CARTESIAN_POINT('',(26.444849537776,-6.338177476019,
    65.116591843665));
#929 = CARTESIAN_POINT('',(25.918232168734,-6.048419251836,
    65.795870883167));
#930 = CARTESIAN_POINT('',(25.365551953118,-5.753510394635,
    66.462487557473));
#931 = CARTESIAN_POINT('',(24.786344081639,-5.454529683585,
    67.115764259524));
#932 = CARTESIAN_POINT('',(24.180041843647,-5.152585207727,
    67.754939484554));
#933 = CARTESIAN_POINT('',(23.311490315783,-4.736504368629,
    68.609953878972));
#934 = CARTESIAN_POINT('',(23.073242692068,-4.623943019112,
    68.838702184337));
#935 = CARTESIAN_POINT('',(22.831166861009,-4.511193761441,
    69.065355246204));
#936 = CARTESIAN_POINT('',(22.585228783049,-4.398316747342,
    69.289864578193));
#937 = CARTESIAN_POINT('',(22.335393420043,-4.285373620523,
    69.512178445187));
#938 = CARTESIAN_POINT('',(22.081624735262,-4.172427516672,
    69.732241863334));
#939 = CARTESIAN_POINT('',(21.82388569339,-4.059543063464,
    69.949996600045));
#940 = CARTESIAN_POINT('',(21.214137984677,-3.796873327625,
    70.451740777714));
#941 = CARTESIAN_POINT('',(20.859055674512,-3.647187738742,
    70.733907871741));
#942 = CARTESIAN_POINT('',(20.496799362309,-3.497884234949,
    71.011739694142));
#943 = CARTESIAN_POINT('',(20.127278643099,-3.349124839633,
    71.285078600028));
#944 = CARTESIAN_POINT('',(19.750404773881,-3.201079091575,
    71.553752177764));
#945 = CARTESIAN_POINT('',(19.366090772846,-3.053924158,71.817573365184)
  );
#946 = CARTESIAN_POINT('',(18.974251518598,-2.907844947632,
    72.076340565809));
#947 = CARTESIAN_POINT('',(18.040119071149,-2.569196342708,
    72.669159043423));
#948 = CARTESIAN_POINT('',(17.49182561214,-2.377639823615,
    72.999022767965));
#949 = CARTESIAN_POINT('',(16.929710564467,-2.188821102093,
    73.318921578341));
#950 = CARTESIAN_POINT('',(16.35360572472,-2.003236564165,
    73.628274282645));
#951 = CARTESIAN_POINT('',(15.763394223414,-1.821421374863,
    73.926431468465));
#952 = CARTESIAN_POINT('',(15.159018657988,-1.643946748691,
    74.212681704276));
#953 = CARTESIAN_POINT('',(14.540490729356,-1.471415661954,
    74.486258331183));
#954 = CARTESIAN_POINT('',(12.647117773316,-0.971698897506,
    75.264718067734));
#955 = CARTESIAN_POINT('',(11.328882599073,-0.660654028837,
    75.730168330636));
#956 = CARTESIAN_POINT('',(9.955307741368,-0.376829111783,
    76.135341716939));
#957 = CARTESIAN_POINT('',(8.530578302995,-0.126134094966,
    76.472294896628));
#958 = CARTESIAN_POINT('',(7.061308492262,8.554897017086E-02,
    76.733119403796));
#959 = CARTESIAN_POINT('',(5.557001109992,0.252966991493,76.910745745418
    ));
#960 = CARTESIAN_POINT('',(4.030660929134,0.372342562448,77.));
#961 = CARTESIAN_POINT('',(2.5,0.442630355265,77.));
#962 = PCURVE('',#157,#963);
#963 = DEFINITIONAL_REPRESENTATION('',(#964),#1016);
#964 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#965,#966,#967,#968,#969,#970,
    #971,#972,#973,#974,#975,#976,#977,#978,#979,#980,#981,#982,#983,
    #984,#985,#986,#987,#988,#989,#990,#991,#992,#993,#994,#995,#996,
    #997,#998,#999,#1000,#1001,#1002,#1003,#1004,#1005,#1006,#1007,#1008
    ,#1009,#1010,#1011,#1012,#1013,#1014,#1015),.UNSPECIFIED.,.F.,.F.,(9
    ,7,7,7,7,7,7,9),(0.,0.203115675711,0.399114727677,0.555971708952,
    0.613968174207,0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#965 = CARTESIAN_POINT('',(5.35131078097,-40.));
#966 = CARTESIAN_POINT('',(5.35131078097,-41.07030334627));
#967 = CARTESIAN_POINT('',(5.350313283479,-42.14068476386));
#968 = CARTESIAN_POINT('',(5.348318016488,-43.20881637361));
#969 = CARTESIAN_POINT('',(5.345332915642,-44.27253431047));
#970 = CARTESIAN_POINT('',(5.341372705646,-45.3298281564));
#971 = CARTESIAN_POINT('',(5.33645733258,-46.37882983528));
#972 = CARTESIAN_POINT('',(5.330610247123,-47.4178019701));
#973 = CARTESIAN_POINT('',(5.31733946191,-49.43645479001));
#974 = CARTESIAN_POINT('',(5.309977927683,-50.41697508142));
#975 = CARTESIAN_POINT('',(5.301793258235,-51.38540792568));
#976 = CARTESIAN_POINT('',(5.292807117345,-52.34059403555));
#977 = CARTESIAN_POINT('',(5.283040133089,-53.28146766956));
#978 = CARTESIAN_POINT('',(5.272510435129,-54.20703146293));
#979 = CARTESIAN_POINT('',(5.261232106549,-55.11633190705));
#980 = CARTESIAN_POINT('',(5.239595164605,-56.72238114014));
#981 = CARTESIAN_POINT('',(5.229503488218,-57.42525211884));
#982 = CARTESIAN_POINT('',(5.21894270802,-58.11659184366));
#983 = CARTESIAN_POINT('',(5.207915565727,-58.79587088316));
#984 = CARTESIAN_POINT('',(5.196423283693,-59.46248755747));
#985 = CARTESIAN_POINT('',(5.184465285027,-60.11576425952));
#986 = CARTESIAN_POINT('',(5.17203870794,-60.75493948455));
#987 = CARTESIAN_POINT('',(5.15436768737,-61.60995387897));
#988 = CARTESIAN_POINT('',(5.149532902616,-61.83870218433));
#989 = CARTESIAN_POINT('',(5.144633115777,-62.0653552462));
#990 = CARTESIAN_POINT('',(5.139668056236,-62.28986457819));
#991 = CARTESIAN_POINT('',(5.134637427045,-62.51217844518));
#992 = CARTESIAN_POINT('',(5.129540904921,-62.73224186333));
#993 = CARTESIAN_POINT('',(5.124378140249,-62.94999660004));
#994 = CARTESIAN_POINT('',(5.112196151029,-63.45174077771));
#995 = CARTESIAN_POINT('',(5.105125853338,-63.73390787174));
#996 = CARTESIAN_POINT('',(5.097936924145,-64.01173969414));
#997 = CARTESIAN_POINT('',(5.090628431531,-64.28507860002));
#998 = CARTESIAN_POINT('',(5.083199446462,-64.55375217776));
#999 = CARTESIAN_POINT('',(5.075649037735,-64.81757336518));
#1000 = CARTESIAN_POINT('',(5.067976266921,-65.0763405658));
#1001 = CARTESIAN_POINT('',(5.049744655558,-65.66915904342));
#1002 = CARTESIAN_POINT('',(5.039088644337,-65.99902276796));
#1003 = CARTESIAN_POINT('',(5.028209598679,-66.31892157834));
#1004 = CARTESIAN_POINT('',(5.017105683795,-66.62827428264));
#1005 = CARTESIAN_POINT('',(5.005775846662,-66.92643146846));
#1006 = CARTESIAN_POINT('',(4.994219906912,-67.21268170427));
#1007 = CARTESIAN_POINT('',(4.982438673019,-67.48625833118));
#1008 = CARTESIAN_POINT('',(4.946508253699,-68.26471806773));
#1009 = CARTESIAN_POINT('',(4.921664856372,-68.73016833063));
#1010 = CARTESIAN_POINT('',(4.895946257758,-69.13534171694));
#1011 = CARTESIAN_POINT('',(4.869426777003,-69.47229489661));
#1012 = CARTESIAN_POINT('',(4.842216686879,-69.7331194038));
#1013 = CARTESIAN_POINT('',(4.814468996906,-69.91074574541));
#1014 = CARTESIAN_POINT('',(4.786391759552,-70.));
#1015 = CARTESIAN_POINT('',(4.758276642425,-70.));
#1016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1017 = PCURVE('',#183,#1018);
#1018 = DEFINITIONAL_REPRESENTATION('',(#1019),#1071);
#1019 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1020,#1021,#1022,#1023,#1024,
    #1025,#1026,#1027,#1028,#1029,#1030,#1031,#1032,#1033,#1034,#1035,
    #1036,#1037,#1038,#1039,#1040,#1041,#1042,#1043,#1044,#1045,#1046,
    #1047,#1048,#1049,#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,
    #1058,#1059,#1060,#1061,#1062,#1063,#1064,#1065,#1066,#1067,#1068,
    #1069,#1070),.UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,9),(0.,
    0.203115675711,0.399114727677,0.555971708952,0.613968174207,
    0.691076039931,0.794289565156,1.),.UNSPECIFIED.);
#1020 = CARTESIAN_POINT('',(0.,0.));
#1021 = CARTESIAN_POINT('',(-3.567677820905E-02,-3.2E-16));
#1022 = CARTESIAN_POINT('',(-7.135615685022E-02,3.241830171733E-02));
#1023 = CARTESIAN_POINT('',(-0.107029746806,9.726542776283E-02));
#1024 = CARTESIAN_POINT('',(-0.142694638542,0.194036866309));
#1025 = CARTESIAN_POINT('',(-0.178352496611,0.321767653713));
#1026 = CARTESIAN_POINT('',(-0.214009054963,0.479099750074));
#1027 = CARTESIAN_POINT('',(-0.249674013045,0.664362207741));
#1028 = CARTESIAN_POINT('',(-0.319798265229,1.079562609986));
#1029 = CARTESIAN_POINT('',(-0.354257341271,1.307715067694));
#1030 = CARTESIAN_POINT('',(-0.388757204124,1.558460248137));
#1031 = CARTESIAN_POINT('',(-0.423321344545,1.829969796598));
#1032 = CARTESIAN_POINT('',(-0.457977861413,2.120315265612));
#1033 = CARTESIAN_POINT('',(-0.492760005549,2.427520477899));
#1034 = CARTESIAN_POINT('',(-0.527707506712,2.749599247977));
#1035 = CARTESIAN_POINT('',(-0.591007983606,3.352660516175));
#1036 = CARTESIAN_POINT('',(-0.619281771788,3.628982188931));
#1037 = CARTESIAN_POINT('',(-0.647715508936,3.912536815526));
#1038 = CARTESIAN_POINT('',(-0.676335265043,4.202295039709));
#1039 = CARTESIAN_POINT('',(-0.70516818169,4.49720389691));
#1040 = CARTESIAN_POINT('',(-0.734243257617,4.79618460796));
#1041 = CARTESIAN_POINT('',(-0.763592457655,5.098129083819));
#1042 = CARTESIAN_POINT('',(-0.804218549041,5.514209922916));
#1043 = CARTESIAN_POINT('',(-0.815227175318,5.626771272434));
#1044 = CARTESIAN_POINT('',(-0.826279745393,5.739520530104));
#1045 = CARTESIAN_POINT('',(-0.837378008376,5.852397544203));
#1046 = CARTESIAN_POINT('',(-0.848523740956,5.965340671023));
#1047 = CARTESIAN_POINT('',(-0.859718747405,6.078286774873));
#1048 = CARTESIAN_POINT('',(-0.87096485957,6.191171228081));
#1049 = CARTESIAN_POINT('',(-0.897286365102,6.45384096392));
#1050 = CARTESIAN_POINT('',(-0.912402281485,6.603526552803));
#1051 = CARTESIAN_POINT('',(-0.927616197072,6.752830056596));
#1052 = CARTESIAN_POINT('',(-0.942932534891,6.901589451912));
#1053 = CARTESIAN_POINT('',(-0.958355668536,7.04963519997));
#1054 = CARTESIAN_POINT('',(-0.973889960753,7.196790133545));
#1055 = CARTESIAN_POINT('',(-0.989539802025,7.342869343913));
#1056 = CARTESIAN_POINT('',(-1.026418539904,7.681517948838));
#1057 = CARTESIAN_POINT('',(-1.047741516184,7.87307446793));
#1058 = CARTESIAN_POINT('',(-1.069289842739,8.061893189453));
#1059 = CARTESIAN_POINT('',(-1.091073423821,8.24747772738));
#1060 = CARTESIAN_POINT('',(-1.113100919231,8.429292916682));
#1061 = CARTESIAN_POINT('',(-1.135379788208,8.606767542854));
#1062 = CARTESIAN_POINT('',(-1.157916261116,8.779298629591));
#1063 = CARTESIAN_POINT('',(-1.226154900481,9.279015394039));
#1064 = CARTESIAN_POINT('',(-1.272694920417,9.590060262708));
#1065 = CARTESIAN_POINT('',(-1.320319981067,9.873885179762));
#1066 = CARTESIAN_POINT('',(-1.368972297682,10.124580196579));
#1067 = CARTESIAN_POINT('',(-1.418550299333,10.336263261716));
#1068 = CARTESIAN_POINT('',(-1.468896292674,10.503681283039));
#1069 = CARTESIAN_POINT('',(-1.519774295824,10.623056853993));
#1070 = CARTESIAN_POINT('',(-1.570796326795,10.69334464681));
#1071 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1072 = FACE_BOUND('',#1073,.F.);
#1073 = EDGE_LOOP('',(#1074));
#1074 = ORIENTED_EDGE('',*,*,#1075,.T.);
#1075 = EDGE_CURVE('',#1076,#1076,#1078,.T.);
#1076 = VERTEX_POINT('',#1077);
#1077 = CARTESIAN_POINT('',(12.5,-0.952851160501,48.));
#1078 = SURFACE_CURVE('',#1079,(#1194,#1312),.PCURVE_S1.);
#1079 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1080,#1081,#1082,#1083,#1084,
    #1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093,#1094,#1095,
    #1096,#1097,#1098,#1099,#1100,#1101,#1102,#1103,#1104,#1105,#1106,
    #1107,#1108,#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,
    #1118,#1119,#1120,#1121,#1122,#1123,#1124,#1125,#1126,#1127,#1128,
    #1129,#1130,#1131,#1132,#1133,#1134,#1135,#1136,#1137,#1138,#1139,
    #1140,#1141,#1142,#1143,#1144,#1145,#1146,#1147,#1148,#1149,#1150,
    #1151,#1152,#1153,#1154,#1155,#1156,#1157,#1158,#1159,#1160,#1161,
    #1162,#1163,#1164,#1165,#1166,#1167,#1168,#1169,#1170,#1171,#1172,
    #1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,#1181,#1182,#1183,
    #1184,#1185,#1186,#1187,#1188,#1189,#1190,#1191,#1192,#1193),
  .UNSPECIFIED.,.T.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1080 = CARTESIAN_POINT('',(12.5,-0.952851160501,48.));
#1081 = CARTESIAN_POINT('',(12.499999909589,-0.952851139196,
    48.904114140039));
#1082 = CARTESIAN_POINT('',(12.425265160332,-0.935240668578,
    49.807650674079));
#1083 = CARTESIAN_POINT('',(12.275957423428,-0.9000579471,
    50.707065824205));
#1084 = CARTESIAN_POINT('',(12.051394838466,-0.847764380282,
    51.598655100542));
#1085 = CARTESIAN_POINT('',(11.75014137157,-0.779260029068,
    52.478298145331));
#1086 = CARTESIAN_POINT('',(11.370093454465,-0.695968834364,
    53.341133058411));
#1087 = CARTESIAN_POINT('',(10.908574634951,-0.599977157853,
    54.181148664497));
#1088 = CARTESIAN_POINT('',(10.195135818988,-0.461805696229,
    55.238695277594));
#1089 = CARTESIAN_POINT('',(10.019855898132,-0.428479643512,
    55.483884718713));
#1090 = CARTESIAN_POINT('',(9.83650434716,-0.394312616173,
    55.726043723121));
#1091 = CARTESIAN_POINT('',(9.644975121383,-0.359394460704,
    55.964942032543));
#1092 = CARTESIAN_POINT('',(9.445160948855,-0.323823687404,
    56.200326897825));
#1093 = CARTESIAN_POINT('',(9.236953288338,-0.287707882825,
    56.431923063419));
#1094 = CARTESIAN_POINT('',(9.020242287253,-0.25116412222,
    56.659432751863));
#1095 = CARTESIAN_POINT('',(8.34397675117,-0.140582662333,
    57.329027484635));
#1096 = CARTESIAN_POINT('',(7.857656986928,-6.549680325049E-02,
    57.758737191655));
#1097 = CARTESIAN_POINT('',(7.335538636076,9.811703660894E-03,
    58.168268783632));
#1098 = CARTESIAN_POINT('',(6.777439745624,8.403744130329E-02,
    58.553983239712));
#1099 = CARTESIAN_POINT('',(6.183441718545,0.155701495626,
    58.911983228553));
#1100 = CARTESIAN_POINT('',(5.553934595573,0.223174872901,
    59.238084753802));
#1101 = CARTESIAN_POINT('',(4.889681120648,0.284719905734,
    59.527775720381));
#1102 = CARTESIAN_POINT('',(3.229545799466,0.412789584026,
    60.118725548863));
#1103 = CARTESIAN_POINT('',(2.199228013877,0.472678224634,
    60.384215148611));
#1104 = CARTESIAN_POINT('',(1.121083616872,0.511925778123,
    60.552211970852));
#1105 = CARTESIAN_POINT('',(1.805267304125E-02,0.525866962114,
    60.610625188365));
#1106 = CARTESIAN_POINT('',(-1.085285401569,0.512681170804,
    60.555418985537));
#1107 = CARTESIAN_POINT('',(-2.164326438081,0.474119848101,
    60.390458988252));
#1108 = CARTESIAN_POINT('',(-3.196059927325,0.414798550425,
    60.127681408691));
#1109 = CARTESIAN_POINT('',(-4.885244290277,0.285477744666,
    59.531499847059));
#1110 = CARTESIAN_POINT('',(-5.574179247914,0.221614458625,
    59.231036236934));
#1111 = CARTESIAN_POINT('',(-6.225707543517,0.151387506907,
    58.891465203811));
#1112 = CARTESIAN_POINT('',(-6.839014087289,7.673323737325E-02,
    58.517805324824));
#1113 = CARTESIAN_POINT('',(-7.413709152059,-5.40763979501E-04,
    58.114686539006));
#1114 = CARTESIAN_POINT('',(-7.94975049928,-7.880366489962E-02,
    57.686401019982));
#1115 = CARTESIAN_POINT('',(-8.447388814502,-0.156627234129,
    57.236937801062));
#1116 = CARTESIAN_POINT('',(-9.129113280445,-0.269562474576,
    56.54455330358));
#1117 = CARTESIAN_POINT('',(-9.342482970575,-0.305985617715,
    56.31480294644));
#1118 = CARTESIAN_POINT('',(-9.547358007748,-0.341934536985,
    56.0810638975));
#1119 = CARTESIAN_POINT('',(-9.743849072063,-0.37729460916,
    55.843627807166));
#1120 = CARTESIAN_POINT('',(-9.932065087349,-0.411960598067,
    55.602764175395));
#1121 = CARTESIAN_POINT('',(-10.11211326979,-0.445836228428,
    55.358720367014));
#1122 = CARTESIAN_POINT('',(-10.28409917658,-0.478833759704,
    55.111721627029));
#1123 = CARTESIAN_POINT('',(-10.5842116175,-0.537455259662,
    54.654766507357));
#1124 = CARTESIAN_POINT('',(-10.71481732761,-0.56337748126,
    54.44566970031));
#1125 = CARTESIAN_POINT('',(-10.8399996858,-0.58859406791,
    54.234801535187));
#1126 = CARTESIAN_POINT('',(-10.95981192223,-0.613062286175,
    54.02227612743));
#1127 = CARTESIAN_POINT('',(-11.07430473845,-0.636742671401,
    53.808200850911));
#1128 = CARTESIAN_POINT('',(-11.1835263495,-0.659598873122,
    53.592676341297));
#1129 = CARTESIAN_POINT('',(-11.28752252611,-0.681597500461,
    53.375796499406));
#1130 = CARTESIAN_POINT('',(-11.6679614625,-0.762873783634,
    52.535916502908));
#1131 = CARTESIAN_POINT('',(-11.90752787011,-0.815832654387,
    51.903807900781));
#1132 = CARTESIAN_POINT('',(-12.10593693146,-0.860855538321,
    51.263511520529));
#1133 = CARTESIAN_POINT('',(-12.26392151342,-0.89739408059,
    50.616937252549));
#1134 = CARTESIAN_POINT('',(-12.38204089582,-0.925055384981,
    49.965782451233));
#1135 = CARTESIAN_POINT('',(-12.46067239602,-0.943584005921,
    49.31159780962));
#1136 = CARTESIAN_POINT('',(-12.5,-0.952851160501,48.655852702793));
#1137 = CARTESIAN_POINT('',(-12.5,-0.952851160501,47.508195572538));
#1138 = CARTESIAN_POINT('',(-12.47788599844,-0.947640228224,
    47.016416374048));
#1139 = CARTESIAN_POINT('',(-12.43366208205,-0.937219334434,
    46.525289912022));
#1140 = CARTESIAN_POINT('',(-12.36727098336,-0.921629459992,
    46.035448154778));
#1141 = CARTESIAN_POINT('',(-12.27859643154,-0.900952156765,
    45.547541963904));
#1142 = CARTESIAN_POINT('',(-12.16746493692,-0.875311375061,
    45.062255568575));
#1143 = CARTESIAN_POINT('',(-12.0336470093,-0.844876557592,
    44.580321081748));
#1144 = CARTESIAN_POINT('',(-11.74550337689,-0.780536787075,
    43.702253095002));
#1145 = CARTESIAN_POINT('',(-11.59803113899,-0.74799674638,
    43.304898949897));
#1146 = CARTESIAN_POINT('',(-11.43426144843,-0.712373322371,
    42.910893844407));
#1147 = CARTESIAN_POINT('',(-11.25397993223,-0.67381554405,
    42.520702052818));
#1148 = CARTESIAN_POINT('',(-11.05694135291,-0.632498547313,
    42.13483830325));
#1149 = CARTESIAN_POINT('',(-10.84287346924,-0.588627097341,
    41.753877178705));
#1150 = CARTESIAN_POINT('',(-10.61148089689,-0.542439110991,
    41.378462518103));
#1151 = CARTESIAN_POINT('',(-10.19513581898,-0.461805696229,
    40.761304722406));
#1152 = CARTESIAN_POINT('',(-10.01985589813,-0.428479643512,
    40.516115281286));
#1153 = CARTESIAN_POINT('',(-9.83650434716,-0.394312616173,
    40.273956276879));
#1154 = CARTESIAN_POINT('',(-9.644975121383,-0.359394460704,
    40.035057967457));
#1155 = CARTESIAN_POINT('',(-9.445160948855,-0.323823687404,
    39.799673102175));
#1156 = CARTESIAN_POINT('',(-9.236953288338,-0.287707882825,
    39.568076936581));
#1157 = CARTESIAN_POINT('',(-9.020242287253,-0.25116412222,
    39.340567248137));
#1158 = CARTESIAN_POINT('',(-8.343976752064,-0.140582662479,
    38.67097251625));
#1159 = CARTESIAN_POINT('',(-7.85765698198,-6.549680263879E-02,
    38.241262805037));
#1160 = CARTESIAN_POINT('',(-7.335538637945,9.811703504305E-03,
    37.831731217056));
#1161 = CARTESIAN_POINT('',(-6.777439751745,8.403744058148E-02,
    37.446016764093));
#1162 = CARTESIAN_POINT('',(-6.183441718079,0.15570149559,37.08801677178
    ));
#1163 = CARTESIAN_POINT('',(-5.553934589019,0.22317487361,
    36.761915242552));
#1164 = CARTESIAN_POINT('',(-4.889681123242,0.284719905534,
    36.472224280542));
#1165 = CARTESIAN_POINT('',(-3.229545799467,0.412789584026,
    35.881274451137));
#1166 = CARTESIAN_POINT('',(-2.199228013874,0.472678224634,
    35.615784851389));
#1167 = CARTESIAN_POINT('',(-1.121083616877,0.511925778123,
    35.447788029149));
#1168 = CARTESIAN_POINT('',(-1.805267303461E-02,0.525866962114,
    35.389374811634));
#1169 = CARTESIAN_POINT('',(1.085285401565,0.512681170804,
    35.444581014464));
#1170 = CARTESIAN_POINT('',(2.164326438082,0.474119848101,
    35.609541011748));
#1171 = CARTESIAN_POINT('',(3.196059927325,0.414798550425,
    35.872318591309));
#1172 = CARTESIAN_POINT('',(4.885244289625,0.285477744716,
    36.468500152711));
#1173 = CARTESIAN_POINT('',(5.574179247621,0.221614458667,36.76896376286
    ));
#1174 = CARTESIAN_POINT('',(6.225707543766,0.151387506892,
    37.108534796243));
#1175 = CARTESIAN_POINT('',(6.839014087775,7.67332373108E-02,
    37.48219467548));
#1176 = CARTESIAN_POINT('',(7.413709152354,-5.407640299211E-04,
    37.885313461285));
#1177 = CARTESIAN_POINT('',(7.949750499142,-7.8803664885E-02,
    38.313598979975));
#1178 = CARTESIAN_POINT('',(8.447388814105,-0.156627234063,
    38.763062198535));
#1179 = CARTESIAN_POINT('',(9.129113280445,-0.269562474577,
    39.455446696421));
#1180 = CARTESIAN_POINT('',(9.342482970575,-0.305985617715,
    39.68519705356));
#1181 = CARTESIAN_POINT('',(9.547358007748,-0.341934536985,39.9189361025
    ));
#1182 = CARTESIAN_POINT('',(9.743849072063,-0.37729460916,
    40.156372192834));
#1183 = CARTESIAN_POINT('',(9.93206508735,-0.411960598067,
    40.397235824605));
#1184 = CARTESIAN_POINT('',(10.112113269795,-0.445836228429,
    40.641279632986));
#1185 = CARTESIAN_POINT('',(10.28409917658,-0.478833759704,
    40.888278372971));
#1186 = CARTESIAN_POINT('',(10.971922409692,-0.613187502278,
    41.935567005313));
#1187 = CARTESIAN_POINT('',(11.414803441972,-0.70578196017,
    42.761533598928));
#1188 = CARTESIAN_POINT('',(11.779677480905,-0.785981140689,
    43.608874416303));
#1189 = CARTESIAN_POINT('',(12.069014334003,-0.851868278441,
    44.472013379685));
#1190 = CARTESIAN_POINT('',(12.284749408084,-0.90212968299,
    45.34640690342));
#1191 = CARTESIAN_POINT('',(12.428202065788,-0.935932720183,
    46.228179183713));
#1192 = CARTESIAN_POINT('',(12.499999911383,-0.952851139619,
    47.113829181122));
#1193 = CARTESIAN_POINT('',(12.5,-0.952851160501,48.));
#1194 = PCURVE('',#157,#1195);
#1195 = DEFINITIONAL_REPRESENTATION('',(#1196),#1311);
#1196 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1197,#1198,#1199,#1200,#1201,
    #1202,#1203,#1204,#1205,#1206,#1207,#1208,#1209,#1210,#1211,#1212,
    #1213,#1214,#1215,#1216,#1217,#1218,#1219,#1220,#1221,#1222,#1223,
    #1224,#1225,#1226,#1227,#1228,#1229,#1230,#1231,#1232,#1233,#1234,
    #1235,#1236,#1237,#1238,#1239,#1240,#1241,#1242,#1243,#1244,#1245,
    #1246,#1247,#1248,#1249,#1250,#1251,#1252,#1253,#1254,#1255,#1256,
    #1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,#1265,#1266,#1267,
    #1268,#1269,#1270,#1271,#1272,#1273,#1274,#1275,#1276,#1277,#1278,
    #1279,#1280,#1281,#1282,#1283,#1284,#1285,#1286,#1287,#1288,#1289,
    #1290,#1291,#1292,#1293,#1294,#1295,#1296,#1297,#1298,#1299,#1300,
    #1301,#1302,#1303,#1304,#1305,#1306,#1307,#1308,#1309,#1310),
  .UNSPECIFIED.,.T.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1197 = CARTESIAN_POINT('',(4.943806821493,-41.));
#1198 = CARTESIAN_POINT('',(4.943806819789,-41.90411414003));
#1199 = CARTESIAN_POINT('',(4.942397984145,-42.80765067407));
#1200 = CARTESIAN_POINT('',(4.9395833581,-43.7070658242));
#1201 = CARTESIAN_POINT('',(4.935352718547,-44.59865510054));
#1202 = CARTESIAN_POINT('',(4.929684185426,-45.47829814533));
#1203 = CARTESIAN_POINT('',(4.922545840497,-46.34113305841));
#1204 = CARTESIAN_POINT('',(4.913897194623,-47.18114866449));
#1205 = CARTESIAN_POINT('',(4.900563332904,-48.23869527759));
#1206 = CARTESIAN_POINT('',(4.897289591734,-48.48388471871));
#1207 = CARTESIAN_POINT('',(4.893867468041,-48.72604372312));
#1208 = CARTESIAN_POINT('',(4.890295300515,-48.96494203254));
#1209 = CARTESIAN_POINT('',(4.886571413174,-49.20032689782));
#1210 = CARTESIAN_POINT('',(4.882694110505,-49.43192306341));
#1211 = CARTESIAN_POINT('',(4.878661672616,-49.65943275186));
#1212 = CARTESIAN_POINT('',(4.866088334726,-50.32902748463));
#1213 = CARTESIAN_POINT('',(4.857059665,-50.75873719165));
#1214 = CARTESIAN_POINT('',(4.847381183787,-51.16826878363));
#1215 = CARTESIAN_POINT('',(4.837052001822,-51.55398323971));
#1216 = CARTESIAN_POINT('',(4.826075700221,-51.91198322855));
#1217 = CARTESIAN_POINT('',(4.814460869112,-52.2380847538));
#1218 = CARTESIAN_POINT('',(4.802221982687,-52.52777572038));
#1219 = CARTESIAN_POINT('',(4.771670278015,-53.11872554886));
#1220 = CARTESIAN_POINT('',(4.752736851374,-53.38421514861));
#1221 = CARTESIAN_POINT('',(4.732949112584,-53.55221197085));
#1222 = CARTESIAN_POINT('',(4.712720055115,-53.61062518836));
#1223 = CARTESIAN_POINT('',(4.692485552211,-53.55541898553));
#1224 = CARTESIAN_POINT('',(4.672681858641,-53.39045898825));
#1225 = CARTESIAN_POINT('',(4.653723120563,-53.12768140869));
#1226 = CARTESIAN_POINT('',(4.62263821395,-52.53149984705));
#1227 = CARTESIAN_POINT('',(4.609944643147,-52.23103623693));
#1228 = CARTESIAN_POINT('',(4.597922885618,-51.89146520381));
#1229 = CARTESIAN_POINT('',(4.586588452459,-51.51780532482));
#1230 = CARTESIAN_POINT('',(4.575950143301,-51.114686539));
#1231 = CARTESIAN_POINT('',(4.566011114792,-50.68640101998));
#1232 = CARTESIAN_POINT('',(4.556769535788,-50.23693780106));
#1233 = CARTESIAN_POINT('',(4.544090349716,-49.54455330358));
#1234 = CARTESIAN_POINT('',(4.540118724581,-49.31480294644));
#1235 = CARTESIAN_POINT('',(4.536302184254,-49.0810638975));
#1236 = CARTESIAN_POINT('',(4.532638995402,-48.84362780716));
#1237 = CARTESIAN_POINT('',(4.52912745615,-48.60276417539));
#1238 = CARTESIAN_POINT('',(4.525765891267,-48.35872036701));
#1239 = CARTESIAN_POINT('',(4.522552647361,-48.11172162702));
#1240 = CARTESIAN_POINT('',(4.51694192868,-47.65476650735));
#1241 = CARTESIAN_POINT('',(4.514498759201,-47.44566970031));
#1242 = CARTESIAN_POINT('',(4.512155712174,-47.23480153518));
#1243 = CARTESIAN_POINT('',(4.509911962478,-47.02227612743));
#1244 = CARTESIAN_POINT('',(4.507766725825,-46.80820085091));
#1245 = CARTESIAN_POINT('',(4.505719257255,-46.59267634129));
#1246 = CARTESIAN_POINT('',(4.503768849636,-46.3757964994));
#1247 = CARTESIAN_POINT('',(4.496630795865,-45.5359165029));
#1248 = CARTESIAN_POINT('',(4.492129070333,-44.90380790078));
#1249 = CARTESIAN_POINT('',(4.488396060035,-44.26351152052));
#1250 = CARTESIAN_POINT('',(4.485420765852,-43.61693725254));
#1251 = CARTESIAN_POINT('',(4.483194803658,-42.96578245123));
#1252 = CARTESIAN_POINT('',(4.481712510625,-42.31159780962));
#1253 = CARTESIAN_POINT('',(4.480971139276,-41.65585270279));
#1254 = CARTESIAN_POINT('',(4.480971139276,-40.50819557253));
#1255 = CARTESIAN_POINT('',(4.481388013794,-40.01641637404));
#1256 = CARTESIAN_POINT('',(4.482221685532,-39.52528991202));
#1257 = CARTESIAN_POINT('',(4.483473005139,-39.03544815477));
#1258 = CARTESIAN_POINT('',(4.485143705869,-38.5475419639));
#1259 = CARTESIAN_POINT('',(4.487236373593,-38.06225556857));
#1260 = CARTESIAN_POINT('',(4.489754432193,-37.58032108174));
#1261 = CARTESIAN_POINT('',(4.495171670619,-36.702253095));
#1262 = CARTESIAN_POINT('',(4.497942662027,-36.30489894989));
#1263 = CARTESIAN_POINT('',(4.501017857363,-35.9108938444));
#1264 = CARTESIAN_POINT('',(4.504400542874,-35.52070205281));
#1265 = CARTESIAN_POINT('',(4.508094490554,-35.13483830325));
#1266 = CARTESIAN_POINT('',(4.512103896475,-34.7538771787));
#1267 = CARTESIAN_POINT('',(4.516433319122,-34.3784625181));
#1268 = CARTESIAN_POINT('',(4.524214627865,-33.7613047224));
#1269 = CARTESIAN_POINT('',(4.527488369035,-33.51611528128));
#1270 = CARTESIAN_POINT('',(4.530910492728,-33.27395627687));
#1271 = CARTESIAN_POINT('',(4.534482660254,-33.03505796745));
#1272 = CARTESIAN_POINT('',(4.538206547596,-32.79967310217));
#1273 = CARTESIAN_POINT('',(4.542083850265,-32.56807693658));
#1274 = CARTESIAN_POINT('',(4.546116288153,-32.34056724813));
#1275 = CARTESIAN_POINT('',(4.558689626027,-31.67097251625));
#1276 = CARTESIAN_POINT('',(4.567718295861,-31.24126280503));
#1277 = CARTESIAN_POINT('',(4.577396776948,-30.83173121705));
#1278 = CARTESIAN_POINT('',(4.587725958834,-30.44601676409));
#1279 = CARTESIAN_POINT('',(4.598702260557,-30.08801677178));
#1280 = CARTESIAN_POINT('',(4.610317091779,-29.76191524255));
#1281 = CARTESIAN_POINT('',(4.622555978034,-29.47222428054));
#1282 = CARTESIAN_POINT('',(4.653107682755,-28.88127445113));
#1283 = CARTESIAN_POINT('',(4.672041109395,-28.61578485138));
#1284 = CARTESIAN_POINT('',(4.691828848185,-28.44778802915));
#1285 = CARTESIAN_POINT('',(4.712057905654,-28.38937481163));
#1286 = CARTESIAN_POINT('',(4.732292408558,-28.44458101446));
#1287 = CARTESIAN_POINT('',(4.752096102128,-28.60954101174));
#1288 = CARTESIAN_POINT('',(4.771054840206,-28.8723185913));
#1289 = CARTESIAN_POINT('',(4.802139746808,-29.46850015271));
#1290 = CARTESIAN_POINT('',(4.814833317617,-29.76896376286));
#1291 = CARTESIAN_POINT('',(4.826855075156,-30.10853479624));
#1292 = CARTESIAN_POINT('',(4.83818950832,-30.48219467548));
#1293 = CARTESIAN_POINT('',(4.848827817474,-30.88531346128));
#1294 = CARTESIAN_POINT('',(4.858766845975,-31.31359897997));
#1295 = CARTESIAN_POINT('',(4.868008424974,-31.76306219853));
#1296 = CARTESIAN_POINT('',(4.880687611054,-32.45544669642));
#1297 = CARTESIAN_POINT('',(4.884659236188,-32.68519705356));
#1298 = CARTESIAN_POINT('',(4.888475776516,-32.9189361025));
#1299 = CARTESIAN_POINT('',(4.892138965367,-33.15637219283));
#1300 = CARTESIAN_POINT('',(4.89565050462,-33.3972358246));
#1301 = CARTESIAN_POINT('',(4.899012069502,-33.64127963298));
#1302 = CARTESIAN_POINT('',(4.902225313408,-33.88827837297));
#1303 = CARTESIAN_POINT('',(4.91508443598,-34.93556700531));
#1304 = CARTESIAN_POINT('',(4.923385676619,-35.76153359892));
#1305 = CARTESIAN_POINT('',(4.930239970826,-36.6088744163));
#1306 = CARTESIAN_POINT('',(4.935684664084,-37.47201337968));
#1307 = CARTESIAN_POINT('',(4.939749097492,-38.34640690342));
#1308 = CARTESIAN_POINT('',(4.942453348103,-39.22817918371));
#1309 = CARTESIAN_POINT('',(4.943806819823,-40.11382918112));
#1310 = CARTESIAN_POINT('',(4.943806821493,-41.));
#1311 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1312 = PCURVE('',#1313,#1318);
#1313 = CYLINDRICAL_SURFACE('',#1314,12.5);
#1314 = AXIS2_PLACEMENT_3D('',#1315,#1316,#1317);
#1315 = CARTESIAN_POINT('',(0.,-54.,48.));
#1316 = DIRECTION('',(0.,-1.,-2.2E-16));
#1317 = DIRECTION('',(1.,0.,0.));
#1318 = DEFINITIONAL_REPRESENTATION('',(#1319),#1434);
#1319 = B_SPLINE_CURVE_WITH_KNOTS('',8,(#1320,#1321,#1322,#1323,#1324,
    #1325,#1326,#1327,#1328,#1329,#1330,#1331,#1332,#1333,#1334,#1335,
    #1336,#1337,#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,#1346,
    #1347,#1348,#1349,#1350,#1351,#1352,#1353,#1354,#1355,#1356,#1357,
    #1358,#1359,#1360,#1361,#1362,#1363,#1364,#1365,#1366,#1367,#1368,
    #1369,#1370,#1371,#1372,#1373,#1374,#1375,#1376,#1377,#1378,#1379,
    #1380,#1381,#1382,#1383,#1384,#1385,#1386,#1387,#1388,#1389,#1390,
    #1391,#1392,#1393,#1394,#1395,#1396,#1397,#1398,#1399,#1400,#1401,
    #1402,#1403,#1404,#1405,#1406,#1407,#1408,#1409,#1410,#1411,#1412,
    #1413,#1414,#1415,#1416,#1417,#1418,#1419,#1420,#1421,#1422,#1423,
    #1424,#1425,#1426,#1427,#1428,#1429,#1430,#1431,#1432,#1433),
  .UNSPECIFIED.,.F.,.F.,(9,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,9),(0.,
    0.106640928828,0.139311852142,0.204695586646,0.294870350494,
    0.36267553769,0.395413455501,0.422574345859,0.499984150597,
    0.558031459903,0.606662178322,0.639334931457,0.704722327821,
    0.794902141632,0.86271112599,0.89545087713,1.),.UNSPECIFIED.);
#1320 = CARTESIAN_POINT('',(0.,-53.04714883949));
#1321 = CARTESIAN_POINT('',(7.232913120308E-02,-53.0471488608));
#1322 = CARTESIAN_POINT('',(0.144612022183,-53.06475933142));
#1323 = CARTESIAN_POINT('',(0.217141941226,-53.0999420529));
#1324 = CARTESIAN_POINT('',(0.29019565168,-53.15223561971));
#1325 = CARTESIAN_POINT('',(0.364053571052,-53.22073997093));
#1326 = CARTESIAN_POINT('',(0.439006395881,-53.30403116563));
#1327 = CARTESIAN_POINT('',(0.515364502387,-53.40002284214));
#1328 = CARTESIAN_POINT('',(0.617420166665,-53.53819430377));
#1329 = CARTESIAN_POINT('',(0.641523132334,-53.57152035648));
#1330 = CARTESIAN_POINT('',(0.665807153264,-53.60568738382));
#1331 = CARTESIAN_POINT('',(0.690283822909,-53.64060553929));
#1332 = CARTESIAN_POINT('',(0.714964752076,-53.67617631259));
#1333 = CARTESIAN_POINT('',(0.73986181619,-53.71229211717));
#1334 = CARTESIAN_POINT('',(0.76498740256,-53.74883587778));
#1335 = CARTESIAN_POINT('',(0.841121691559,-53.85941733766));
#1336 = CARTESIAN_POINT('',(0.892955428345,-53.93450319675));
#1337 = CARTESIAN_POINT('',(0.945887679011,-54.00981170366));
#1338 = CARTESIAN_POINT('',(0.999958889119,-54.0840374413));
#1339 = CARTESIAN_POINT('',(1.055218891319,-54.15570149562));
#1340 = CARTESIAN_POINT('',(1.111728762655,-54.2231748729));
#1341 = CARTESIAN_POINT('',(1.169563786647,-54.28471990573));
#1342 = CARTESIAN_POINT('',(1.3105380194,-54.41278958402));
#1343 = CARTESIAN_POINT('',(1.39531307727,-54.47267822463));
#1344 = CARTESIAN_POINT('',(1.481908390876,-54.51192577812));
#1345 = CARTESIAN_POINT('',(1.569365101385,-54.52586696211));
#1346 = CARTESIAN_POINT('',(1.656832618598,-54.5126811708));
#1347 = CARTESIAN_POINT('',(1.743460474208,-54.4741198481));
#1348 = CARTESIAN_POINT('',(1.828290322267,-54.41479855042));
#1349 = CARTESIAN_POINT('',(1.971594781742,-54.28547774466));
#1350 = CARTESIAN_POINT('',(2.031567614287,-54.22161445862));
#1351 = CARTESIAN_POINT('',(2.090114016173,-54.1513875069));
#1352 = CARTESIAN_POINT('',(2.147319902345,-54.07673323737));
#1353 = CARTESIAN_POINT('',(2.203254888559,-53.99945923602));
#1354 = CARTESIAN_POINT('',(2.257975891105,-53.9211963351));
#1355 = CARTESIAN_POINT('',(2.311529355193,-53.84337276587));
#1356 = CARTESIAN_POINT('',(2.389263035505,-53.73043752542));
#1357 = CARTESIAN_POINT('',(2.414336130543,-53.69401438228));
#1358 = CARTESIAN_POINT('',(2.4391845108,-53.65806546301));
#1359 = CARTESIAN_POINT('',(2.463820540906,-53.62270539084));
#1360 = CARTESIAN_POINT('',(2.488256081089,-53.58803940193));
#1361 = CARTESIAN_POINT('',(2.512502731814,-53.55416377157));
#1362 = CARTESIAN_POINT('',(2.536572078413,-53.52116624029));
#1363 = CARTESIAN_POINT('',(2.580307681015,-53.46254474033));
#1364 = CARTESIAN_POINT('',(2.600025330471,-53.43662251874));
#1365 = CARTESIAN_POINT('',(2.619635134541,-53.41140593209));
#1366 = CARTESIAN_POINT('',(2.639143114956,-53.38693771382));
#1367 = CARTESIAN_POINT('',(2.658555133982,-53.36325732859));
#1368 = CARTESIAN_POINT('',(2.677876963656,-53.34040112687));
#1369 = CARTESIAN_POINT('',(2.697114355044,-53.31840249953));
#1370 = CARTESIAN_POINT('',(2.77087644581,-53.23712621636));
#1371 = CARTESIAN_POINT('',(2.824847692022,-53.18416734561));
#1372 = CARTESIAN_POINT('',(2.87830496241,-53.13914446167));
#1373 = CARTESIAN_POINT('',(2.931357589863,-53.10260591941));
#1374 = CARTESIAN_POINT('',(2.98411002512,-53.07494461501));
#1375 = CARTESIAN_POINT('',(3.036664783535,-53.05641599407));
#1376 = CARTESIAN_POINT('',(3.089124437366,-53.04714883949));
#1377 = CARTESIAN_POINT('',(3.180937007787,-53.04714883949));
#1378 = CARTESIAN_POINT('',(3.220279349187,-53.05235977177));
#1379 = CARTESIAN_POINT('',(3.259662258579,-53.06278066556));
#1380 = CARTESIAN_POINT('',(3.299127985908,-53.07837054));
#1381 = CARTESIAN_POINT('',(3.338718820558,-53.09904784323));
#1382 = CARTESIAN_POINT('',(3.378477587615,-53.12468862493));
#1383 = CARTESIAN_POINT('',(3.418448270141,-53.1551234424));
#1384 = CARTESIAN_POINT('',(3.492379271151,-53.21946321292));
#1385 = CARTESIAN_POINT('',(3.52626139518,-53.25200325362));
#1386 = CARTESIAN_POINT('',(3.560353872704,-53.28762667762));
#1387 = CARTESIAN_POINT('',(3.594687639415,-53.32618445595));
#1388 = CARTESIAN_POINT('',(3.629294145598,-53.36750145268));
#1389 = CARTESIAN_POINT('',(3.664205678215,-53.41137290265));
#1390 = CARTESIAN_POINT('',(3.699455682986,-53.457560889));
#1391 = CARTESIAN_POINT('',(3.759012820255,-53.53819430377));
#1392 = CARTESIAN_POINT('',(3.783115785924,-53.57152035648));
#1393 = CARTESIAN_POINT('',(3.807399806853,-53.60568738382));
#1394 = CARTESIAN_POINT('',(3.831876476499,-53.64060553929));
#1395 = CARTESIAN_POINT('',(3.856557405666,-53.67617631259));
#1396 = CARTESIAN_POINT('',(3.88145446978,-53.71229211717));
#1397 = CARTESIAN_POINT('',(3.90658005615,-53.74883587778));
#1398 = CARTESIAN_POINT('',(3.982714345048,-53.85941733752));
#1399 = CARTESIAN_POINT('',(4.034548082418,-53.93450319736));
#1400 = CARTESIAN_POINT('',(4.087480332443,-54.0098117035));
#1401 = CARTESIAN_POINT('',(4.141551542125,-54.08403744058));
#1402 = CARTESIAN_POINT('',(4.196811544928,-54.15570149559));
#1403 = CARTESIAN_POINT('',(4.253321416852,-54.22317487361));
#1404 = CARTESIAN_POINT('',(4.311156440016,-54.28471990553));
#1405 = CARTESIAN_POINT('',(4.452130672989,-54.41278958402));
#1406 = CARTESIAN_POINT('',(4.536905730861,-54.47267822463));
#1407 = CARTESIAN_POINT('',(4.623501044465,-54.51192577812));
#1408 = CARTESIAN_POINT('',(4.710957754976,-54.52586696211));
#1409 = CARTESIAN_POINT('',(4.798425272187,-54.5126811708));
#1410 = CARTESIAN_POINT('',(4.885053127798,-54.4741198481));
#1411 = CARTESIAN_POINT('',(4.969882975857,-54.41479855042));
#1412 = CARTESIAN_POINT('',(5.113187435276,-54.28547774471));
#1413 = CARTESIAN_POINT('',(5.173160267849,-54.22161445866));
#1414 = CARTESIAN_POINT('',(5.231706669783,-54.15138750689));
#1415 = CARTESIAN_POINT('',(5.288912555979,-54.07673323731));
#1416 = CARTESIAN_POINT('',(5.344847542181,-53.99945923597));
#1417 = CARTESIAN_POINT('',(5.399568544685,-53.92119633511));
#1418 = CARTESIAN_POINT('',(5.453122008737,-53.84337276593));
#1419 = CARTESIAN_POINT('',(5.530855689095,-53.73043752542));
#1420 = CARTESIAN_POINT('',(5.555928784132,-53.69401438228));
#1421 = CARTESIAN_POINT('',(5.58077716439,-53.65806546301));
#1422 = CARTESIAN_POINT('',(5.605413194496,-53.62270539084));
#1423 = CARTESIAN_POINT('',(5.629848734679,-53.58803940193));
#1424 = CARTESIAN_POINT('',(5.654095385404,-53.55416377157));
#1425 = CARTESIAN_POINT('',(5.678164732003,-53.52116624029));
#1426 = CARTESIAN_POINT('',(5.778401708157,-53.38681249772));
#1427 = CARTESIAN_POINT('',(5.853082305777,-53.29421803983));
#1428 = CARTESIAN_POINT('',(5.926443989849,-53.21401885931));
#1429 = CARTESIAN_POINT('',(5.998777324806,-53.14813172155));
#1430 = CARTESIAN_POINT('',(6.070354851269,-53.09787031701));
#1431 = CARTESIAN_POINT('',(6.14143966809,-53.06406727981));
#1432 = CARTESIAN_POINT('',(6.212291641669,-53.04714886038));
#1433 = CARTESIAN_POINT('',(6.28318530718,-53.04714883949));
#1434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1435 = FACE_BOUND('',#1436,.F.);
#1436 = EDGE_LOOP('',(#1437));
#1437 = ORIENTED_EDGE('',*,*,#1438,.T.);
#1438 = EDGE_CURVE('',#1439,#1439,#1441,.T.);
#1439 = VERTEX_POINT('',#1440);
#1440 = CARTESIAN_POINT('',(20.320508075689,-3.429979715785,38.));
#1441 = SURFACE_CURVE('',#1442,(#1517,#1595),.PCURVE_S1.);
#1442 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1443,#1444,#1445,#1446,#1447,
    #1448,#1449,#1450,#1451,#1452,#1453,#1454,#1455,#1456,#1457,#1458,
    #1459,#1460,#1461,#1462,#1463,#1464,#1465,#1466,#1467,#1468,#1469,
    #1470,#1471,#1472,#1473,#1474,#1475,#1476,#1477,#1478,#1479,#1480,
    #1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488,#1489,#1490,#1491,
    #1492,#1493,#1494,#1495,#1496,#1497,#1498,#1499,#1500,#1501,#1502,
    #1503,#1504,#1505,#1506,#1507,#1508,#1509,#1510,#1511,#1512,#1513,
    #1514,#1515,#1516),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1443 = CARTESIAN_POINT('',(20.320508075689,-3.429979715785,38.));
#1444 = CARTESIAN_POINT('',(20.320508045685,-3.429979703729,
    38.30004235822));
#1445 = CARTESIAN_POINT('',(20.285498683018,-3.415911933692,
    38.600119319529));
#1446 = CARTESIAN_POINT('',(20.215462024456,-3.387769096459,
    38.896941813699));
#1447 = CARTESIAN_POINT('',(20.110123579602,-3.345618571507,
    39.187084404773));
#1448 = CARTESIAN_POINT('',(19.969053061397,-3.28964182196,
    39.466760764424));
#1449 = CARTESIAN_POINT('',(19.791781583076,-3.220202360234,
    39.731519738803));
#1450 = CARTESIAN_POINT('',(19.411645445764,-3.073878989805,
    40.165862218238));
#1451 = CARTESIAN_POINT('',(19.222491637874,-3.001798286447,
    40.344307144701));
#1452 = CARTESIAN_POINT('',(19.011037077591,-2.922217552034,
    40.507200053127));
#1453 = CARTESIAN_POINT('',(18.778442044322,-2.835996705026,
    40.650692470807));
#1454 = CARTESIAN_POINT('',(18.526495126581,-2.744249959916,
    40.771029071684));
#1455 = CARTESIAN_POINT('',(18.257619988982,-2.648281675422,
    40.864563330308));
#1456 = CARTESIAN_POINT('',(17.691643930458,-2.450550089858,
    40.991073968074));
#1457 = CARTESIAN_POINT('',(17.396136466755,-2.349352541974,
    41.023549203447));
#1458 = CARTESIAN_POINT('',(17.095137855766,-2.248578125761,
    41.021511967389));
#1459 = CARTESIAN_POINT('',(16.796056511442,-2.150843232564,
    40.983884737171));
#1460 = CARTESIAN_POINT('',(16.506274448207,-2.058362483095,
    40.912084835909));
#1461 = CARTESIAN_POINT('',(16.23243681893,-1.972816362698,
    40.809959322795));
#1462 = CARTESIAN_POINT('',(15.786123550286,-1.835938315982,
    40.586991385794));
#1463 = CARTESIAN_POINT('',(15.604541023002,-1.781168146667,
    40.475906238966));
#1464 = CARTESIAN_POINT('',(15.435771460009,-1.731035668419,
    40.352335969763));
#1465 = CARTESIAN_POINT('',(15.2803552825,-1.685496824968,
    40.218060964963));
#1466 = CARTESIAN_POINT('',(15.138640460344,-1.644464956839,
    40.074799950063));
#1467 = CARTESIAN_POINT('',(15.010792391939,-1.607823536165,
    39.924221256156));
#1468 = CARTESIAN_POINT('',(14.817213896347,-1.552816870888,
    39.658842187914));
#1469 = CARTESIAN_POINT('',(14.744270287953,-1.532241791868,
    39.546806973416));
#1470 = CARTESIAN_POINT('',(14.677901840979,-1.51364136302,
    39.43224760279));
#1471 = CARTESIAN_POINT('',(14.618035754308,-1.496955595955,
    39.315526293854));
#1472 = CARTESIAN_POINT('',(14.564599758913,-1.482130991356,
    39.196971267331));
#1473 = CARTESIAN_POINT('',(14.517525528956,-1.469121069644,
    39.076882503385));
#1474 = CARTESIAN_POINT('',(14.431885622016,-1.445524993489,
    38.822011294041));
#1475 = CARTESIAN_POINT('',(14.394644518115,-1.435312140514,
    38.68695231598));
#1476 = CARTESIAN_POINT('',(14.364942832196,-1.42719529401,
    38.550708977322));
#1477 = CARTESIAN_POINT('',(14.34271345551,-1.421134494576,
    38.413599645208));
#1478 = CARTESIAN_POINT('',(14.327909899491,-1.417102973557,
    38.27592175169));
#1479 = CARTESIAN_POINT('',(14.320508075689,-1.415087254478,
    38.137960904865));
#1480 = CARTESIAN_POINT('',(14.320508075689,-1.415087254478,
    37.812185077994));
#1481 = CARTESIAN_POINT('',(14.334226055017,-1.418822925982,
    37.624368544516));
#1482 = CARTESIAN_POINT('',(14.361661833231,-1.426294848161,
    37.43726224769));
#1483 = CARTESIAN_POINT('',(14.40289997548,-1.437549467777,
    37.251590519093));
#1484 = CARTESIAN_POINT('',(14.458098115899,-1.45267824125,
    37.068133126846));
#1485 = CARTESIAN_POINT('',(14.527475281005,-1.471816776732,
    36.88776822871));
#1486 = CARTESIAN_POINT('',(14.644924410989,-1.504501077345,
    36.640816043229));
#1487 = CARTESIAN_POINT('',(14.680872291973,-1.514531905343,
    36.570780351629));
#1488 = CARTESIAN_POINT('',(14.719158952376,-1.525247381975,
    36.501478528318));
#1489 = CARTESIAN_POINT('',(14.759799209607,-1.536659176944,
    36.432985247702));
#1490 = CARTESIAN_POINT('',(14.802807602146,-1.548779707855,
    36.365379580667));
#1491 = CARTESIAN_POINT('',(14.848198389552,-1.561622140209,
    36.298744994568));
#1492 = CARTESIAN_POINT('',(15.015272032645,-1.609094454688,
    36.069479212101));
#1493 = CARTESIAN_POINT('',(15.149795757707,-1.647660257108,
    35.911968963967));
#1494 = CARTESIAN_POINT('',(15.299559910298,-1.691057012567,
    35.762547645353));
#1495 = CARTESIAN_POINT('',(15.464346498798,-1.739412339871,
    35.623163559514));
#1496 = CARTESIAN_POINT('',(15.643708975933,-1.792814750688,
    35.495812942091));
#1497 = CARTESIAN_POINT('',(15.836962266044,-1.851297406886,
    35.382552931292));
#1498 = CARTESIAN_POINT('',(16.339232639894,-2.006005675798,
    35.146202433847));
#1499 = CARTESIAN_POINT('',(16.662169622396,-2.107626085766,
    35.040254151874));
#1500 = CARTESIAN_POINT('',(17.003648540614,-2.217823085258,
    34.976715069704));
#1501 = CARTESIAN_POINT('',(17.353003114192,-2.333786495438,
    34.960364031603));
#1502 = CARTESIAN_POINT('',(17.698668679296,-2.451800251396,
    34.991681473462));
#1503 = CARTESIAN_POINT('',(18.029608095958,-2.56762118394,
    35.066789943049));
#1504 = CARTESIAN_POINT('',(18.552078362014,-2.754291036716,
    35.254890813163));
#1505 = CARTESIAN_POINT('',(18.756184824584,-2.828403689704,
    35.350027399374));
#1506 = CARTESIAN_POINT('',(18.94784191528,-2.899041214166,
    35.460762923036));
#1507 = CARTESIAN_POINT('',(19.126125259647,-2.965631598,35.585135037344
    ));
#1508 = CARTESIAN_POINT('',(19.290360440102,-3.027692311971,
    35.721216930226));
#1509 = CARTESIAN_POINT('',(19.440105930748,-3.084842948233,
    35.867112807818));
#1510 = CARTESIAN_POINT('',(19.789739082417,-3.219402336751,
    36.265434042187));
#1511 = CARTESIAN_POINT('',(19.967698487679,-3.289104336075,
    36.530556810045));
#1512 = CARTESIAN_POINT('',(20.109314614574,-3.345294872017,
    36.810689346199));
#1513 = CARTESIAN_POINT('',(20.215058783059,-3.38760706127,
    37.101350444293));
#1514 = CARTESIAN_POINT('',(20.2853643431,-3.415857952226,
    37.398730049706));
#1515 = CARTESIAN_POINT('',(20.320508045627,-3.429979703706,
    37.699382507555));
#1516 = CARTESIAN_POINT('',(20.320508075689,-3.429979715785,38.));
#1517 = PCURVE('',#157,#1518);
#1518 = DEFINITIONAL_REPRESENTATION('',(#1519),#1594);
#1519 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1520,#1521,#1522,#1523,#1524,
    #1525,#1526,#1527,#1528,#1529,#1530,#1531,#1532,#1533,#1534,#1535,
    #1536,#1537,#1538,#1539,#1540,#1541,#1542,#1543,#1544,#1545,#1546,
    #1547,#1548,#1549,#1550,#1551,#1552,#1553,#1554,#1555,#1556,#1557,
    #1558,#1559,#1560,#1561,#1562,#1563,#1564,#1565,#1566,#1567,#1568,
    #1569,#1570,#1571,#1572,#1573,#1574,#1575,#1576,#1577,#1578,#1579,
    #1580,#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,#1589,#1590,
    #1591,#1592,#1593),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1520 = CARTESIAN_POINT('',(5.094471212436,-31.));
#1521 = CARTESIAN_POINT('',(5.094471211843,-31.30004235822));
#1522 = CARTESIAN_POINT('',(5.093778917098,-31.60011931952));
#1523 = CARTESIAN_POINT('',(5.092393972479,-31.8969418137));
#1524 = CARTESIAN_POINT('',(5.090312165257,-32.18708440477));
#1525 = CARTESIAN_POINT('',(5.087527411637,-32.46676076442));
#1526 = CARTESIAN_POINT('',(5.084034142344,-32.7315197388));
#1527 = CARTESIAN_POINT('',(5.076560281115,-33.16586221823));
#1528 = CARTESIAN_POINT('',(5.072846131788,-33.3443071447));
#1529 = CARTESIAN_POINT('',(5.068700613054,-33.50720005312));
#1530 = CARTESIAN_POINT('',(5.064149141374,-33.6506924708));
#1531 = CARTESIAN_POINT('',(5.059229440421,-33.77102907168));
#1532 = CARTESIAN_POINT('',(5.053991236127,-33.8645633303));
#1533 = CARTESIAN_POINT('',(5.042990831688,-33.99107396807));
#1534 = CARTESIAN_POINT('',(5.03725965745,-34.02354920344));
#1535 = CARTESIAN_POINT('',(5.031435655125,-34.02151196738));
#1536 = CARTESIAN_POINT('',(5.025662608733,-33.98388473717));
#1537 = CARTESIAN_POINT('',(5.020081469967,-33.9120848359));
#1538 = CARTESIAN_POINT('',(5.014817526907,-33.80995932279));
#1539 = CARTESIAN_POINT('',(5.00625182145,-33.58699138579));
#1540 = CARTESIAN_POINT('',(5.002771807091,-33.47590623896));
#1541 = CARTESIAN_POINT('',(4.999541426372,-33.35233596976));
#1542 = CARTESIAN_POINT('',(4.996569887773,-33.21806096496));
#1543 = CARTESIAN_POINT('',(4.993862831969,-33.07479995006));
#1544 = CARTESIAN_POINT('',(4.991422559437,-32.92422125615));
#1545 = CARTESIAN_POINT('',(4.987730044553,-32.65884218791));
#1546 = CARTESIAN_POINT('',(4.986339407353,-32.54680697341));
#1547 = CARTESIAN_POINT('',(4.985074718938,-32.43224760279));
#1548 = CARTESIAN_POINT('',(4.983934391901,-32.31552629385));
#1549 = CARTESIAN_POINT('',(4.982916883362,-32.19697126733));
#1550 = CARTESIAN_POINT('',(4.982020756504,-32.07688250338));
#1551 = CARTESIAN_POINT('',(4.980390827636,-31.82201129404));
#1552 = CARTESIAN_POINT('',(4.979682276036,-31.68695231598));
#1553 = CARTESIAN_POINT('',(4.979117307551,-31.55070897732));
#1554 = CARTESIAN_POINT('',(4.978694540588,-31.4135996452));
#1555 = CARTESIAN_POINT('',(4.97841302307,-31.27592175169));
#1556 = CARTESIAN_POINT('',(4.978272263798,-31.13796090486));
#1557 = CARTESIAN_POINT('',(4.978272263798,-30.81218507799));
#1558 = CARTESIAN_POINT('',(4.978533135585,-30.62436854451));
#1559 = CARTESIAN_POINT('',(4.979054880078,-30.43726224769));
#1560 = CARTESIAN_POINT('',(4.979839216793,-30.25159051909));
#1561 = CARTESIAN_POINT('',(4.980889377262,-30.06813312684));
#1562 = CARTESIAN_POINT('',(4.982209898443,-29.88776822871));
#1563 = CARTESIAN_POINT('',(4.984446817823,-29.64081604322));
#1564 = CARTESIAN_POINT('',(4.985131609317,-29.57078035162));
#1565 = CARTESIAN_POINT('',(4.985861111496,-29.50147852831));
#1566 = CARTESIAN_POINT('',(4.986635644418,-29.4329852477));
#1567 = CARTESIAN_POINT('',(4.987455527325,-29.36537958066));
#1568 = CARTESIAN_POINT('',(4.988321078645,-29.29874499456));
#1569 = CARTESIAN_POINT('',(4.991507998659,-29.0694792121));
#1570 = CARTESIAN_POINT('',(4.994075747048,-28.91196896396));
#1571 = CARTESIAN_POINT('',(4.996936735645,-28.76254764535));
#1572 = CARTESIAN_POINT('',(5.000087797067,-28.62316355951));
#1573 = CARTESIAN_POINT('',(5.003521573496,-28.49581294209));
#1574 = CARTESIAN_POINT('',(5.007226271946,-28.38255293129));
#1575 = CARTESIAN_POINT('',(5.016869519943,-28.14620243384));
#1576 = CARTESIAN_POINT('',(5.023081302051,-28.04025415187));
#1577 = CARTESIAN_POINT('',(5.029664855214,-27.9767150697));
#1578 = CARTESIAN_POINT('',(5.036418500878,-27.9603640316));
#1579 = CARTESIAN_POINT('',(5.043120123817,-27.99168147346));
#1580 = CARTESIAN_POINT('',(5.049553409081,-28.06678994304));
#1581 = CARTESIAN_POINT('',(5.059733519487,-28.25489081316));
#1582 = CARTESIAN_POINT('',(5.063717786028,-28.35002739937));
#1583 = CARTESIAN_POINT('',(5.067465608834,-28.46076292303));
#1584 = CARTESIAN_POINT('',(5.070957548546,-28.58513503734));
#1585 = CARTESIAN_POINT('',(5.074178982444,-28.72121693022));
#1586 = CARTESIAN_POINT('',(5.077119901361,-28.86711280781));
#1587 = CARTESIAN_POINT('',(5.083993893649,-29.26543404218));
#1588 = CARTESIAN_POINT('',(5.087500672207,-29.53055681004));
#1589 = CARTESIAN_POINT('',(5.090296177671,-29.81068934619));
#1590 = CARTESIAN_POINT('',(5.092385998552,-30.10135044429));
#1591 = CARTESIAN_POINT('',(5.093776260586,-30.3987300497));
#1592 = CARTESIAN_POINT('',(5.094471211842,-30.69938250755));
#1593 = CARTESIAN_POINT('',(5.094471212436,-31.));
#1594 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1595 = PCURVE('',#1596,#1601);
#1596 = CYLINDRICAL_SURFACE('',#1597,3.);
#1597 = AXIS2_PLACEMENT_3D('',#1598,#1599,#1600);
#1598 = CARTESIAN_POINT('',(17.320508075689,-54.,38.));
#1599 = DIRECTION('',(0.,-1.,-2.2E-16));
#1600 = DIRECTION('',(1.,0.,0.));
#1601 = DEFINITIONAL_REPRESENTATION('',(#1602),#1677);
#1602 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1603,#1604,#1605,#1606,#1607,
    #1608,#1609,#1610,#1611,#1612,#1613,#1614,#1615,#1616,#1617,#1618,
    #1619,#1620,#1621,#1622,#1623,#1624,#1625,#1626,#1627,#1628,#1629,
    #1630,#1631,#1632,#1633,#1634,#1635,#1636,#1637,#1638,#1639,#1640,
    #1641,#1642,#1643,#1644,#1645,#1646,#1647,#1648,#1649,#1650,#1651,
    #1652,#1653,#1654,#1655,#1656,#1657,#1658,#1659,#1660,#1661,#1662,
    #1663,#1664,#1665,#1666,#1667,#1668,#1669,#1670,#1671,#1672,#1673,
    #1674,#1675,#1676),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.123981883518,0.220474534574,0.317175980728,0.391292407653,
    0.443041154273,0.499984690519,0.57750553477,0.608601011299,
    0.686221646619,0.797665246534,0.875812536332,1.),.UNSPECIFIED.);
#1603 = CARTESIAN_POINT('',(0.,-50.57002028421));
#1604 = CARTESIAN_POINT('',(0.100014119407,-50.57002029627));
#1605 = CARTESIAN_POINT('',(0.200039675558,-50.5840880663));
#1606 = CARTESIAN_POINT('',(0.300614988949,-50.61223090354));
#1607 = CARTESIAN_POINT('',(0.402232044324,-50.65438142849));
#1608 = CARTESIAN_POINT('',(0.505421761045,-50.71035817804));
#1609 = CARTESIAN_POINT('',(0.610748802244,-50.77979763976));
#1610 = CARTESIAN_POINT('',(0.803147945328,-50.92612101019));
#1611 = CARTESIAN_POINT('',(0.889433061186,-50.99820171355));
#1612 = CARTESIAN_POINT('',(0.977687487709,-51.07778244796));
#1613 = CARTESIAN_POINT('',(1.067884733127,-51.16400329497));
#1614 = CARTESIAN_POINT('',(1.160064055144,-51.25575004008));
#1615 = CARTESIAN_POINT('',(1.254332143065,-51.35171832457));
#1616 = CARTESIAN_POINT('',(1.447646464053,-51.54944991014));
#1617 = CARTESIAN_POINT('',(1.546138089096,-51.65064745802));
#1618 = CARTESIAN_POINT('',(1.645386305597,-51.75142187423));
#1619 = CARTESIAN_POINT('',(1.744620331801,-51.84915676743));
#1620 = CARTESIAN_POINT('',(1.843089971547,-51.9416375169));
#1621 = CARTESIAN_POINT('',(1.939960099014,-52.0271836373));
#1622 = CARTESIAN_POINT('',(2.106263113852,-52.16406168401));
#1623 = CARTESIAN_POINT('',(2.176958162849,-52.21883185333));
#1624 = CARTESIAN_POINT('',(2.246305773449,-52.26896433158));
#1625 = CARTESIAN_POINT('',(2.314382170268,-52.31450317503));
#1626 = CARTESIAN_POINT('',(2.381238370611,-52.35553504316));
#1627 = CARTESIAN_POINT('',(2.446903712243,-52.39217646383));
#1628 = CARTESIAN_POINT('',(2.556396829133,-52.44718312911));
#1629 = CARTESIAN_POINT('',(2.600897009314,-52.46775820813));
#1630 = CARTESIAN_POINT('',(2.644934170132,-52.48635863698));
#1631 = CARTESIAN_POINT('',(2.688558971827,-52.50304440404));
#1632 = CARTESIAN_POINT('',(2.73181947123,-52.51786900864));
#1633 = CARTESIAN_POINT('',(2.774762127689,-52.53087893035));
#1634 = CARTESIAN_POINT('',(2.864386979781,-52.55447500651));
#1635 = CARTESIAN_POINT('',(2.91101590729,-52.56468785948));
#1636 = CARTESIAN_POINT('',(2.957387572042,-52.57280470599));
#1637 = CARTESIAN_POINT('',(3.003567250358,-52.57886550542));
#1638 = CARTESIAN_POINT('',(3.049618756581,-52.58289702644));
#1639 = CARTESIAN_POINT('',(3.095605685301,-52.58491274552));
#1640 = CARTESIAN_POINT('',(3.204197627592,-52.58491274552));
#1641 = CARTESIAN_POINT('',(3.266802962355,-52.58117707401));
#1642 = CARTESIAN_POINT('',(3.329572745722,-52.57370515183));
#1643 = CARTESIAN_POINT('',(3.392665917731,-52.56245053222));
#1644 = CARTESIAN_POINT('',(3.456242369806,-52.54732175875));
#1645 = CARTESIAN_POINT('',(3.520469043963,-52.52818322326));
#1646 = CARTESIAN_POINT('',(3.611621955813,-52.49549892265));
#1647 = CARTESIAN_POINT('',(3.637850919763,-52.48546809465));
#1648 = CARTESIAN_POINT('',(3.664222656748,-52.47475261802));
#1649 = CARTESIAN_POINT('',(3.690747233106,-52.46334082305));
#1650 = CARTESIAN_POINT('',(3.717435048631,-52.45122029214));
#1651 = CARTESIAN_POINT('',(3.744296836564,-52.43837785979));
#1652 = CARTESIAN_POINT('',(3.838858046151,-52.39090554531));
#1653 = CARTESIAN_POINT('',(3.907698991031,-52.35233974289));
#1654 = CARTESIAN_POINT('',(3.977855076864,-52.30894298743));
#1655 = CARTESIAN_POINT('',(4.04935102893,-52.26058766012));
#1656 = CARTESIAN_POINT('',(4.122238543156,-52.20718524931));
#1657 = CARTESIAN_POINT('',(4.196600134377,-52.14870259311));
#1658 = CARTESIAN_POINT('',(4.381633790802,-51.9939943242));
#1659 = CARTESIAN_POINT('',(4.494071386901,-51.89237391423));
#1660 = CARTESIAN_POINT('',(4.608201267477,-51.78217691474));
#1661 = CARTESIAN_POINT('',(4.722838650826,-51.66621350456));
#1662 = CARTESIAN_POINT('',(4.836888293591,-51.5481997486));
#1663 = CARTESIAN_POINT('',(4.94916053828,-51.43237881606));
#1664 = CARTESIAN_POINT('',(5.134260246021,-51.24570896328));
#1665 = CARTESIAN_POINT('',(5.20901570686,-51.17159631029));
#1666 = CARTESIAN_POINT('',(5.282353157261,-51.10095878583));
#1667 = CARTESIAN_POINT('',(5.354355609859,-51.034368402));
#1668 = CARTESIAN_POINT('',(5.42507919296,-50.97230768802));
#1669 = CARTESIAN_POINT('',(5.494556477075,-50.91515705176));
#1670 = CARTESIAN_POINT('',(5.671224133202,-50.78059766324));
#1671 = CARTESIAN_POINT('',(5.776773639789,-50.71089566392));
#1672 = CARTESIAN_POINT('',(5.88017350666,-50.65470512798));
#1673 = CARTESIAN_POINT('',(5.981991650552,-50.61239293873));
#1674 = CARTESIAN_POINT('',(6.08276208956,-50.58414204777));
#1675 = CARTESIAN_POINT('',(6.182979476365,-50.57002029629));
#1676 = CARTESIAN_POINT('',(6.28318530718,-50.57002028421));
#1677 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1678 = FACE_BOUND('',#1679,.F.);
#1679 = EDGE_LOOP('',(#1680));
#1680 = ORIENTED_EDGE('',*,*,#1681,.T.);
#1681 = EDGE_CURVE('',#1682,#1682,#1684,.T.);
#1682 = VERTEX_POINT('',#1683);
#1683 = CARTESIAN_POINT('',(-14.32050807568,-1.415087254478,38.));
#1684 = SURFACE_CURVE('',#1685,(#1760,#1838),.PCURVE_S1.);
#1685 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1686,#1687,#1688,#1689,#1690,
    #1691,#1692,#1693,#1694,#1695,#1696,#1697,#1698,#1699,#1700,#1701,
    #1702,#1703,#1704,#1705,#1706,#1707,#1708,#1709,#1710,#1711,#1712,
    #1713,#1714,#1715,#1716,#1717,#1718,#1719,#1720,#1721,#1722,#1723,
    #1724,#1725,#1726,#1727,#1728,#1729,#1730,#1731,#1732,#1733,#1734,
    #1735,#1736,#1737,#1738,#1739,#1740,#1741,#1742,#1743,#1744,#1745,
    #1746,#1747,#1748,#1749,#1750,#1751,#1752,#1753,#1754,#1755,#1756,
    #1757,#1758,#1759),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1686 = CARTESIAN_POINT('',(-14.32050807568,-1.415087254478,38.));
#1687 = CARTESIAN_POINT('',(-14.320508102,-1.415087261644,
    38.263131799246));
#1688 = CARTESIAN_POINT('',(-14.34743377952,-1.422419869114,
    38.526290318888));
#1689 = CARTESIAN_POINT('',(-14.40129715482,-1.437088863549,
    38.78753477559));
#1690 = CARTESIAN_POINT('',(-14.48243182887,-1.459277219249,
    39.044820745698));
#1691 = CARTESIAN_POINT('',(-14.59142360374,-1.489332754013,
    39.295823777876));
#1692 = CARTESIAN_POINT('',(-14.72901527254,-1.527757442543,
    39.537706703784));
#1693 = CARTESIAN_POINT('',(-15.01527203264,-1.609094454688,
    39.930520787899));
#1694 = CARTESIAN_POINT('',(-15.1497957577,-1.647660257108,
    40.088031036033));
#1695 = CARTESIAN_POINT('',(-15.29955991029,-1.691057012567,
    40.237452354648));
#1696 = CARTESIAN_POINT('',(-15.46434649879,-1.739412339871,
    40.376836440487));
#1697 = CARTESIAN_POINT('',(-15.64370897593,-1.792814750688,
    40.504187057909));
#1698 = CARTESIAN_POINT('',(-15.83696226604,-1.851297406886,
    40.617447068708));
#1699 = CARTESIAN_POINT('',(-16.33923263989,-2.006005675798,
    40.853797566153));
#1700 = CARTESIAN_POINT('',(-16.66216962239,-2.107626085766,
    40.959745848126));
#1701 = CARTESIAN_POINT('',(-17.00364854061,-2.217823085259,
    41.023284930295));
#1702 = CARTESIAN_POINT('',(-17.35300311419,-2.333786495437,
    41.039635968398));
#1703 = CARTESIAN_POINT('',(-17.69866867929,-2.451800251396,
    41.008318526538));
#1704 = CARTESIAN_POINT('',(-18.02960809595,-2.56762118394,
    40.933210056951));
#1705 = CARTESIAN_POINT('',(-18.55207836201,-2.754291036716,
    40.745109186837));
#1706 = CARTESIAN_POINT('',(-18.75618482458,-2.828403689704,
    40.649972600626));
#1707 = CARTESIAN_POINT('',(-18.94784191528,-2.899041214166,
    40.539237076964));
#1708 = CARTESIAN_POINT('',(-19.12612525964,-2.965631598,40.414864962656
    ));
#1709 = CARTESIAN_POINT('',(-19.2903604401,-3.027692311971,
    40.278783069773));
#1710 = CARTESIAN_POINT('',(-19.44010593074,-3.084842948233,
    40.132887192183));
#1711 = CARTESIAN_POINT('',(-19.67509060921,-3.175278865119,
    39.865179777322));
#1712 = CARTESIAN_POINT('',(-19.76712123703,-3.210960757547,
    39.746801547014));
#1713 = CARTESIAN_POINT('',(-19.85124427644,-3.243788149983,
    39.624488433172));
#1714 = CARTESIAN_POINT('',(-19.92748355252,-3.27370465753,39.4987678828
    ));
#1715 = CARTESIAN_POINT('',(-19.99587311471,-3.30066667689,
    39.370123975433));
#1716 = CARTESIAN_POINT('',(-20.05645208458,-3.324641511962,
    39.239005241359));
#1717 = CARTESIAN_POINT('',(-20.16979989818,-3.369639396557,
    38.953158259286));
#1718 = CARTESIAN_POINT('',(-20.22013160567,-3.389718253871,
    38.797770492405));
#1719 = CARTESIAN_POINT('',(-20.26032199133,-3.405809755169,
    38.640272678355));
#1720 = CARTESIAN_POINT('',(-20.29042537845,-3.417891630092,
    38.481224502098));
#1721 = CARTESIAN_POINT('',(-20.31048052088,-3.425950331994,
    38.321155289792));
#1722 = CARTESIAN_POINT('',(-20.32050807568,-3.429979715785,
    38.160577464184));
#1723 = CARTESIAN_POINT('',(-20.32050807568,-3.429979715785,
    37.838960134466));
#1724 = CARTESIAN_POINT('',(-20.31042268855,-3.425927092958,
    37.677919926001));
#1725 = CARTESIAN_POINT('',(-20.29025187906,-3.417821913284,
    37.517392721751));
#1726 = CARTESIAN_POINT('',(-20.25997474969,-3.405670391841,
    37.357895397146));
#1727 = CARTESIAN_POINT('',(-20.21955210805,-3.389486236135,
    37.199961986914));
#1728 = CARTESIAN_POINT('',(-20.16892903453,-3.369291989426,
    37.044157316089));
#1729 = CARTESIAN_POINT('',(-20.05546856524,-3.324252550336,
    36.758945197059));
#1730 = CARTESIAN_POINT('',(-19.99524224826,-3.300418723233,
    36.628830306039));
#1731 = CARTESIAN_POINT('',(-19.92732031073,-3.273642147161,
    36.501157806191));
#1732 = CARTESIAN_POINT('',(-19.85166452585,-3.243954858226,
    36.37636653838));
#1733 = CARTESIAN_POINT('',(-19.76824158935,-3.211399479955,
    36.254929870651));
#1734 = CARTESIAN_POINT('',(-19.67702808076,-3.176031029022,
    36.13736323278));
#1735 = CARTESIAN_POINT('',(-19.41164544592,-3.073878989868,
    35.834137781948));
#1736 = CARTESIAN_POINT('',(-19.22249163732,-3.001798286243,
    35.655692854896));
#1737 = CARTESIAN_POINT('',(-19.0110370781,-2.922217552219,
    35.49279994708));
#1738 = CARTESIAN_POINT('',(-18.77844204463,-2.835996705142,
    35.349307529457));
#1739 = CARTESIAN_POINT('',(-18.52649512576,-2.744249959625,
    35.228970927977));
#1740 = CARTESIAN_POINT('',(-18.25761998939,-2.648281675565,
    35.135436669784));
#1741 = CARTESIAN_POINT('',(-17.69164393045,-2.450550089858,
    35.008926031926));
#1742 = CARTESIAN_POINT('',(-17.39613646675,-2.349352541974,
    34.976450796552));
#1743 = CARTESIAN_POINT('',(-17.09513785576,-2.248578125761,
    34.978488032611));
#1744 = CARTESIAN_POINT('',(-16.79605651144,-2.150843232563,
    35.016115262829));
#1745 = CARTESIAN_POINT('',(-16.5062744482,-2.058362483096,
    35.087915164091));
#1746 = CARTESIAN_POINT('',(-16.23243681893,-1.972816362698,
    35.190040677205));
#1747 = CARTESIAN_POINT('',(-15.78612355028,-1.835938315982,
    35.413008614206));
#1748 = CARTESIAN_POINT('',(-15.604541023,-1.781168146667,
    35.524093761034));
#1749 = CARTESIAN_POINT('',(-15.43577146,-1.731035668419,35.647664030237
    ));
#1750 = CARTESIAN_POINT('',(-15.2803552825,-1.685496824968,
    35.781939035037));
#1751 = CARTESIAN_POINT('',(-15.13864046034,-1.644464956839,
    35.925200049937));
#1752 = CARTESIAN_POINT('',(-15.01079239193,-1.607823536165,
    36.075778743843));
#1753 = CARTESIAN_POINT('',(-14.72959268924,-1.527918700277,
    36.461278769876));
#1754 = CARTESIAN_POINT('',(-14.59180478095,-1.489437867024,
    36.703298747836));
#1755 = CARTESIAN_POINT('',(-14.48265877873,-1.459339284431,
    36.954459746131));
#1756 = CARTESIAN_POINT('',(-14.40141012422,-1.437119630017,
    37.21191740122));
#1757 = CARTESIAN_POINT('',(-14.34747142353,-1.422430120239,
    37.473341867979));
#1758 = CARTESIAN_POINT('',(-14.32050810202,-1.415087261649,
    37.736684322692));
#1759 = CARTESIAN_POINT('',(-14.32050807568,-1.415087254478,38.));
#1760 = PCURVE('',#157,#1761);
#1761 = DEFINITIONAL_REPRESENTATION('',(#1762),#1837);
#1762 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1763,#1764,#1765,#1766,#1767,
    #1768,#1769,#1770,#1771,#1772,#1773,#1774,#1775,#1776,#1777,#1778,
    #1779,#1780,#1781,#1782,#1783,#1784,#1785,#1786,#1787,#1788,#1789,
    #1790,#1791,#1792,#1793,#1794,#1795,#1796,#1797,#1798,#1799,#1800,
    #1801,#1802,#1803,#1804,#1805,#1806,#1807,#1808,#1809,#1810,#1811,
    #1812,#1813,#1814,#1815,#1816,#1817,#1818,#1819,#1820,#1821,#1822,
    #1823,#1824,#1825,#1826,#1827,#1828,#1829,#1830,#1831,#1832,#1833,
    #1834,#1835,#1836),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1763 = CARTESIAN_POINT('',(4.446505696971,-31.));
#1764 = CARTESIAN_POINT('',(4.446505696471,-31.26313179924));
#1765 = CARTESIAN_POINT('',(4.445993654875,-31.52629031888));
#1766 = CARTESIAN_POINT('',(4.44496934153,-31.78753477559));
#1767 = CARTESIAN_POINT('',(4.443425966294,-32.04482074569));
#1768 = CARTESIAN_POINT('',(4.44135148324,-32.29582377787));
#1769 = CARTESIAN_POINT('',(4.438730293141,-32.53770670378));
#1770 = CARTESIAN_POINT('',(4.43326996211,-32.93052078789));
#1771 = CARTESIAN_POINT('',(4.430702213721,-33.08803103603));
#1772 = CARTESIAN_POINT('',(4.427841225125,-33.23745235464));
#1773 = CARTESIAN_POINT('',(4.424690163702,-33.37683644048));
#1774 = CARTESIAN_POINT('',(4.421256387274,-33.5041870579));
#1775 = CARTESIAN_POINT('',(4.417551688824,-33.6174470687));
#1776 = CARTESIAN_POINT('',(4.407908440826,-33.85379756615));
#1777 = CARTESIAN_POINT('',(4.401696658719,-33.95974584812));
#1778 = CARTESIAN_POINT('',(4.395113105556,-34.02328493029));
#1779 = CARTESIAN_POINT('',(4.388359459892,-34.03963596839));
#1780 = CARTESIAN_POINT('',(4.381657836953,-34.00831852653));
#1781 = CARTESIAN_POINT('',(4.375224551688,-33.93321005695));
#1782 = CARTESIAN_POINT('',(4.365044441282,-33.74510918683));
#1783 = CARTESIAN_POINT('',(4.361060174742,-33.64997260062));
#1784 = CARTESIAN_POINT('',(4.357312351936,-33.53923707696));
#1785 = CARTESIAN_POINT('',(4.353820412223,-33.41486496265));
#1786 = CARTESIAN_POINT('',(4.350598978325,-33.27878306977));
#1787 = CARTESIAN_POINT('',(4.347658059408,-33.13288719218));
#1788 = CARTESIAN_POINT('',(4.34303812317,-32.86517977732));
#1789 = CARTESIAN_POINT('',(4.341227013213,-32.74680154701));
#1790 = CARTESIAN_POINT('',(4.339570114548,-32.62448843317));
#1791 = CARTESIAN_POINT('',(4.338067386862,-32.4987678828));
#1792 = CARTESIAN_POINT('',(4.336718536117,-32.37012397543));
#1793 = CARTESIAN_POINT('',(4.335523112483,-32.23900524135));
#1794 = CARTESIAN_POINT('',(4.333285442589,-31.95315825928));
#1795 = CARTESIAN_POINT('',(4.332291151358,-31.7977704924));
#1796 = CARTESIAN_POINT('',(4.331496802279,-31.64027267835));
#1797 = CARTESIAN_POINT('',(4.330901620477,-31.48122450209));
#1798 = CARTESIAN_POINT('',(4.330505038839,-31.32115528979));
#1799 = CARTESIAN_POINT('',(4.330306748333,-31.16057746418));
#1800 = CARTESIAN_POINT('',(4.330306748333,-30.83896013446));
#1801 = CARTESIAN_POINT('',(4.330506182448,-30.677919926));
#1802 = CARTESIAN_POINT('',(4.330905051352,-30.51739272175));
#1803 = CARTESIAN_POINT('',(4.33150366768,-30.35789539714));
#1804 = CARTESIAN_POINT('',(4.332302604912,-30.19996198691));
#1805 = CARTESIAN_POINT('',(4.333302646248,-30.04415731608));
#1806 = CARTESIAN_POINT('',(4.335542518697,-29.75894519705));
#1807 = CARTESIAN_POINT('',(4.336730973596,-29.62883030603));
#1808 = CARTESIAN_POINT('',(4.338070594064,-29.50115780619));
#1809 = CARTESIAN_POINT('',(4.339561819088,-29.37636653838));
#1810 = CARTESIAN_POINT('',(4.341204936454,-29.25492987065));
#1811 = CARTESIAN_POINT('',(4.342999988433,-29.13736323278));
#1812 = CARTESIAN_POINT('',(4.348217679651,-28.83413778194));
#1813 = CARTESIAN_POINT('',(4.351931828992,-28.65569285489));
#1814 = CARTESIAN_POINT('',(4.356077347705,-28.49279994708));
#1815 = CARTESIAN_POINT('',(4.360628819389,-28.34930752945));
#1816 = CARTESIAN_POINT('',(4.365548520364,-28.22897092797));
#1817 = CARTESIAN_POINT('',(4.370786724635,-28.13543666978));
#1818 = CARTESIAN_POINT('',(4.381787129082,-28.00892603192));
#1819 = CARTESIAN_POINT('',(4.387518303319,-27.97645079655));
#1820 = CARTESIAN_POINT('',(4.393342305644,-27.97848803261));
#1821 = CARTESIAN_POINT('',(4.399115352036,-28.01611526282));
#1822 = CARTESIAN_POINT('',(4.404696490802,-28.08791516409));
#1823 = CARTESIAN_POINT('',(4.409960433863,-28.1900406772));
#1824 = CARTESIAN_POINT('',(4.418526139319,-28.4130086142));
#1825 = CARTESIAN_POINT('',(4.422006153678,-28.52409376103));
#1826 = CARTESIAN_POINT('',(4.425236534397,-28.64766403023));
#1827 = CARTESIAN_POINT('',(4.428208072996,-28.78193903503));
#1828 = CARTESIAN_POINT('',(4.4309151288,-28.92520004993));
#1829 = CARTESIAN_POINT('',(4.433355401333,-29.07577874384));
#1830 = CARTESIAN_POINT('',(4.438719293041,-29.46127876987));
#1831 = CARTESIAN_POINT('',(4.441344228145,-29.70329874783));
#1832 = CARTESIAN_POINT('',(4.443421649167,-29.95445974613));
#1833 = CARTESIAN_POINT('',(4.444967193201,-30.21191740122));
#1834 = CARTESIAN_POINT('',(4.445992939006,-30.47334186797));
#1835 = CARTESIAN_POINT('',(4.446505696471,-30.73668432269));
#1836 = CARTESIAN_POINT('',(4.446505696971,-31.));
#1837 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1838 = PCURVE('',#1839,#1844);
#1839 = CYLINDRICAL_SURFACE('',#1840,3.);
#1840 = AXIS2_PLACEMENT_3D('',#1841,#1842,#1843);
#1841 = CARTESIAN_POINT('',(-17.32050807568,-54.,38.));
#1842 = DIRECTION('',(0.,-1.,-2.2E-16));
#1843 = DIRECTION('',(1.,0.,0.));
#1844 = DEFINITIONAL_REPRESENTATION('',(#1845),#1920);
#1845 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1846,#1847,#1848,#1849,#1850,
    #1851,#1852,#1853,#1854,#1855,#1856,#1857,#1858,#1859,#1860,#1861,
    #1862,#1863,#1864,#1865,#1866,#1867,#1868,#1869,#1870,#1871,#1872,
    #1873,#1874,#1875,#1876,#1877,#1878,#1879,#1880,#1881,#1882,#1883,
    #1884,#1885,#1886,#1887,#1888,#1889,#1890,#1891,#1892,#1893,#1894,
    #1895,#1896,#1897,#1898,#1899,#1900,#1901,#1902,#1903,#1904,#1905,
    #1906,#1907,#1908,#1909,#1910,#1911,#1912,#1913,#1914,#1915,#1916,
    #1917,#1918,#1919),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.108664689595,0.18629663912,0.297756483372,0.37591516414,
    0.433763315489,0.500082593673,0.566592845877,0.624012434842,
    0.720493997526,0.817184331173,0.891292240961,1.),.UNSPECIFIED.);
#1846 = CARTESIAN_POINT('',(0.,-52.58491274552));
#1847 = CARTESIAN_POINT('',(8.771059974874E-02,-52.58491273835));
#1848 = CARTESIAN_POINT('',(0.175430674758,-52.57758013088));
#1849 = CARTESIAN_POINT('',(0.263612327933,-52.56291113645));
#1850 = CARTESIAN_POINT('',(0.35268440216,-52.54072278075));
#1851 = CARTESIAN_POINT('',(0.443075222705,-52.51066724598));
#1852 = CARTESIAN_POINT('',(0.535248302453,-52.47224255745));
#1853 = CARTESIAN_POINT('',(0.697265392561,-52.39090554531));
#1854 = CARTESIAN_POINT('',(0.766106337441,-52.35233974289));
#1855 = CARTESIAN_POINT('',(0.836262423274,-52.30894298743));
#1856 = CARTESIAN_POINT('',(0.90775837534,-52.26058766012));
#1857 = CARTESIAN_POINT('',(0.980645889566,-52.20718524931));
#1858 = CARTESIAN_POINT('',(1.055007480787,-52.14870259311));
#1859 = CARTESIAN_POINT('',(1.240041137212,-51.9939943242));
#1860 = CARTESIAN_POINT('',(1.352478733311,-51.89237391423));
#1861 = CARTESIAN_POINT('',(1.466608613887,-51.78217691474));
#1862 = CARTESIAN_POINT('',(1.581245997236,-51.66621350456));
#1863 = CARTESIAN_POINT('',(1.695295640001,-51.5481997486));
#1864 = CARTESIAN_POINT('',(1.80756788469,-51.43237881606));
#1865 = CARTESIAN_POINT('',(1.992667592431,-51.24570896328));
#1866 = CARTESIAN_POINT('',(2.06742305327,-51.17159631029));
#1867 = CARTESIAN_POINT('',(2.140760503672,-51.10095878583));
#1868 = CARTESIAN_POINT('',(2.212762956269,-51.034368402));
#1869 = CARTESIAN_POINT('',(2.28348653937,-50.97230768802));
#1870 = CARTESIAN_POINT('',(2.352963823485,-50.91515705176));
#1871 = CARTESIAN_POINT('',(2.471700253859,-50.82472113488));
#1872 = CARTESIAN_POINT('',(2.521592857756,-50.78903924245));
#1873 = CARTESIAN_POINT('',(2.570942460347,-50.75621185001));
#1874 = CARTESIAN_POINT('',(2.619810229942,-50.72629534247));
#1875 = CARTESIAN_POINT('',(2.668253048541,-50.69933332311));
#1876 = CARTESIAN_POINT('',(2.716324936981,-50.67535848803));
#1877 = CARTESIAN_POINT('',(2.818824922895,-50.63036060344));
#1878 = CARTESIAN_POINT('',(2.873157921603,-50.61028174612));
#1879 = CARTESIAN_POINT('',(2.927166438366,-50.59419024483));
#1880 = CARTESIAN_POINT('',(2.9809339881,-50.5821083699));
#1881 = CARTESIAN_POINT('',(3.034540942187,-50.574049668));
#1882 = CARTESIAN_POINT('',(3.088066832195,-50.57002028421));
#1883 = CARTESIAN_POINT('',(3.195272608768,-50.57002028421));
#1884 = CARTESIAN_POINT('',(3.248952625234,-50.57407290704));
#1885 = CARTESIAN_POINT('',(3.302714414947,-50.58217808671));
#1886 = CARTESIAN_POINT('',(3.356638192244,-50.59432960815));
#1887 = CARTESIAN_POINT('',(3.410805019631,-50.61051376386));
#1888 = CARTESIAN_POINT('',(3.465299153552,-50.63070801057));
#1889 = CARTESIAN_POINT('',(3.567616324335,-50.67574744966));
#1890 = CARTESIAN_POINT('',(3.615336756159,-50.69958127676));
#1891 = CARTESIAN_POINT('',(3.66342313902,-50.72635785283));
#1892 = CARTESIAN_POINT('',(3.711928335217,-50.75604514177));
#1893 = CARTESIAN_POINT('',(3.760907988983,-50.78860052004));
#1894 = CARTESIAN_POINT('',(3.810421899506,-50.82396897097));
#1895 = CARTESIAN_POINT('',(3.944740598835,-50.92612101013));
#1896 = CARTESIAN_POINT('',(4.031025715013,-50.99820171375));
#1897 = CARTESIAN_POINT('',(4.119280141108,-51.07778244778));
#1898 = CARTESIAN_POINT('',(4.209477386578,-51.16400329485));
#1899 = CARTESIAN_POINT('',(4.301656709036,-51.25575004037));
#1900 = CARTESIAN_POINT('',(4.395924796514,-51.35171832443));
#1901 = CARTESIAN_POINT('',(4.589239117642,-51.54944991014));
#1902 = CARTESIAN_POINT('',(4.687730742687,-51.65064745802));
#1903 = CARTESIAN_POINT('',(4.786978959186,-51.75142187423));
#1904 = CARTESIAN_POINT('',(4.886212985392,-51.84915676743));
#1905 = CARTESIAN_POINT('',(4.984682625136,-51.9416375169));
#1906 = CARTESIAN_POINT('',(5.081552752604,-52.0271836373));
#1907 = CARTESIAN_POINT('',(5.247855767442,-52.16406168401));
#1908 = CARTESIAN_POINT('',(5.318550816439,-52.21883185333));
#1909 = CARTESIAN_POINT('',(5.387898427039,-52.26896433158));
#1910 = CARTESIAN_POINT('',(5.455974823858,-52.31450317503));
#1911 = CARTESIAN_POINT('',(5.522831024201,-52.35553504316));
#1912 = CARTESIAN_POINT('',(5.588496365833,-52.39217646383));
#1913 = CARTESIAN_POINT('',(5.747550354872,-52.47208129972));
#1914 = CARTESIAN_POINT('',(5.839794069457,-52.51056213297));
#1915 = CARTESIAN_POINT('',(5.93025181004,-52.54066071556));
#1916 = CARTESIAN_POINT('',(6.019388065895,-52.56288036998));
#1917 = CARTESIAN_POINT('',(6.107632025669,-52.57756987976));
#1918 = CARTESIAN_POINT('',(6.195413414743,-52.58491273835));
#1919 = CARTESIAN_POINT('',(6.28318530718,-52.58491274552));
#1920 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1921 = FACE_BOUND('',#1922,.F.);
#1922 = EDGE_LOOP('',(#1923));
#1923 = ORIENTED_EDGE('',*,*,#1924,.T.);
#1924 = EDGE_CURVE('',#1925,#1925,#1927,.T.);
#1925 = VERTEX_POINT('',#1926);
#1926 = CARTESIAN_POINT('',(3.,0.417368550859,68.));
#1927 = SURFACE_CURVE('',#1928,(#2003,#2081),.PCURVE_S1.);
#1928 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#1929,#1930,#1931,#1932,#1933,
    #1934,#1935,#1936,#1937,#1938,#1939,#1940,#1941,#1942,#1943,#1944,
    #1945,#1946,#1947,#1948,#1949,#1950,#1951,#1952,#1953,#1954,#1955,
    #1956,#1957,#1958,#1959,#1960,#1961,#1962,#1963,#1964,#1965,#1966,
    #1967,#1968,#1969,#1970,#1971,#1972,#1973,#1974,#1975,#1976,#1977,
    #1978,#1979,#1980,#1981,#1982,#1983,#1984,#1985,#1986,#1987,#1988,
    #1989,#1990,#1991,#1992,#1993,#1994,#1995,#1996,#1997,#1998,#1999,
    #2000,#2001,#2002),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#1929 = CARTESIAN_POINT('',(3.,0.417368550859,68.));
#1930 = CARTESIAN_POINT('',(2.999999969832,0.417368552522,
    68.301675404984));
#1931 = CARTESIAN_POINT('',(2.964609026852,0.419319616309,
    68.603006772432));
#1932 = CARTESIAN_POINT('',(2.893965029493,0.423214284332,
    68.901768828789));
#1933 = CARTESIAN_POINT('',(2.787174533175,0.428955875812,
    69.195466360299));
#1934 = CARTESIAN_POINT('',(2.642558277606,0.436350297716,
    69.480833492903));
#1935 = CARTESIAN_POINT('',(2.45793211932,0.44501933438,69.75320895181)
  );
#1936 = CARTESIAN_POINT('',(2.063103655172,0.461195888245,70.19242226762
    ));
#1937 = CARTESIAN_POINT('',(1.870492538865,0.468482054394,
    70.369973548066));
#1938 = CARTESIAN_POINT('',(1.653071166507,0.475859619699,
    70.533365155146));
#1939 = CARTESIAN_POINT('',(1.411549498806,0.482906790071,
    70.677688286879));
#1940 = CARTESIAN_POINT('',(1.147450079878,0.48912317489,70.798028403946
    ));
#1941 = CARTESIAN_POINT('',(0.863169063463,0.493995677802,
    70.889438139478));
#1942 = CARTESIAN_POINT('',(0.224252530968,0.500586176271,
    71.011317986418));
#1943 = CARTESIAN_POINT('',(-0.125543171186,0.501749582478,
    71.031199639289));
#1944 = CARTESIAN_POINT('',(-0.476230923152,0.500170834836,
    71.002771098859));
#1945 = CARTESIAN_POINT('',(-0.816040368012,0.495869619363,
    70.926994407233));
#1946 = CARTESIAN_POINT('',(-1.133902307673,0.489512375875,
    70.809287379311));
#1947 = CARTESIAN_POINT('',(-1.421079579336,0.482048154056,
    70.65930263254));
#1948 = CARTESIAN_POINT('',(-1.768256496164,0.471391905819,
    70.426252124426));
#1949 = CARTESIAN_POINT('',(-1.860114170785,0.468373943648,
    70.358300223116));
#1950 = CARTESIAN_POINT('',(-1.947616057246,0.465323584161,
    70.28732280723));
#1951 = CARTESIAN_POINT('',(-2.030781955822,0.462270251616,
    70.213631848085));
#1952 = CARTESIAN_POINT('',(-2.109643477642,0.459240533206,
    70.137515686202));
#1953 = CARTESIAN_POINT('',(-2.184243541196,0.456258076803,
    70.059239286154));
#1954 = CARTESIAN_POINT('',(-2.356015139287,0.449145888983,
    69.863547674376));
#1955 = CARTESIAN_POINT('',(-2.448693949109,0.445087947171,
    69.744040340302));
#1956 = CARTESIAN_POINT('',(-2.53288203845,0.441229079813,
    69.621172052502));
#1957 = CARTESIAN_POINT('',(-2.60879050453,0.437617115477,
    69.495490215457));
#1958 = CARTESIAN_POINT('',(-2.676621417658,0.434290021871,
    69.367455665698));
#1959 = CARTESIAN_POINT('',(-2.736557437259,0.431277632965,
    69.237458262713));
#1960 = CARTESIAN_POINT('',(-2.849715614724,0.42547976182,
    68.952089515876));
#1961 = CARTESIAN_POINT('',(-2.900119012591,0.42281745858,
    68.796121169862));
#1962 = CARTESIAN_POINT('',(-2.940195687485,0.420653946984,
    68.638482316552));
#1963 = CARTESIAN_POINT('',(-2.970128011531,0.419015302945,
    68.479646679161));
#1964 = CARTESIAN_POINT('',(-2.990041558612,0.417917639872,
    68.320031637863));
#1965 = CARTESIAN_POINT('',(-3.,0.417368550859,68.160023038284));
#1966 = CARTESIAN_POINT('',(-3.,0.417368550859,67.835060926139));
#1967 = CARTESIAN_POINT('',(-2.989420292702,0.417951906668,
    67.670138642564));
#1968 = CARTESIAN_POINT('',(-2.968264887483,0.419118001206,
    67.505647305867));
#1969 = CARTESIAN_POINT('',(-2.936461732819,0.420858303487,
    67.342008846501));
#1970 = CARTESIAN_POINT('',(-2.893868883146,0.423154637924,
    67.179681958478));
#1971 = CARTESIAN_POINT('',(-2.840280665723,0.425977453635,
    67.019191089058));
#1972 = CARTESIAN_POINT('',(-2.721727737606,0.43202265182,
    66.730270355158));
#1973 = CARTESIAN_POINT('',(-2.660297204776,0.435093103089,
    66.601064761824));
#1974 = CARTESIAN_POINT('',(-2.590986628064,0.438471303214,
    66.473877926516));
#1975 = CARTESIAN_POINT('',(-2.513611765767,0.442126595468,
    66.349105631054));
#1976 = CARTESIAN_POINT('',(-2.427970378255,0.446020099095,
    66.227215667185));
#1977 = CARTESIAN_POINT('',(-2.333852850821,0.450102997674,
    66.10876334277));
#1978 = CARTESIAN_POINT('',(-2.063103655173,0.461195888245,
    65.807577732381));
#1979 = CARTESIAN_POINT('',(-1.870492538865,0.468482054394,
    65.630026451934));
#1980 = CARTESIAN_POINT('',(-1.653071166506,0.475859619699,
    65.466634844853));
#1981 = CARTESIAN_POINT('',(-1.411549498805,0.482906790071,
    65.322311713121));
#1982 = CARTESIAN_POINT('',(-1.147450079878,0.48912317489,
    65.201971596054));
#1983 = CARTESIAN_POINT('',(-0.863169063464,0.493995677802,
    65.110561860522));
#1984 = CARTESIAN_POINT('',(-0.224252531464,0.500586176266,
    64.988682013677));
#1985 = CARTESIAN_POINT('',(0.125543171656,0.501749582491,
    64.968800360484));
#1986 = CARTESIAN_POINT('',(0.476230923588,0.50017083482,64.997228901381
    ));
#1987 = CARTESIAN_POINT('',(0.816040367584,0.495869619363,
    65.073005592842));
#1988 = CARTESIAN_POINT('',(1.133902307266,0.489512375893,
    65.190712620276));
#1989 = CARTESIAN_POINT('',(1.421079579744,0.482048154044,
    65.340697367734));
#1990 = CARTESIAN_POINT('',(1.768256476943,0.471391906409,
    65.573747862672));
#1991 = CARTESIAN_POINT('',(1.860114169321,0.468373943742,
    65.641699773714));
#1992 = CARTESIAN_POINT('',(1.947616070627,0.465323583732,
    65.712677202445));
#1993 = CARTESIAN_POINT('',(2.03078196731,0.462270251167,65.786368163767
    ));
#1994 = CARTESIAN_POINT('',(2.109643475215,0.459240533243,
    65.862484313752));
#1995 = CARTESIAN_POINT('',(2.184243528544,0.456258077327,
    65.940760699432));
#1996 = CARTESIAN_POINT('',(2.474385204854,0.444244786892,
    66.271305986966));
#1997 = CARTESIAN_POINT('',(2.6532959987,0.435800590103,66.540544407481)
  );
#1998 = CARTESIAN_POINT('',(2.793517914856,0.42861473049,66.822122033229
    ));
#1999 = CARTESIAN_POINT('',(2.897114192032,0.423040658421,
    67.111646544826));
#2000 = CARTESIAN_POINT('',(2.965662538079,0.419261540179,
    67.406017948946));
#2001 = CARTESIAN_POINT('',(2.999999970285,0.417368552497,
    67.702848976135));
#2002 = CARTESIAN_POINT('',(3.,0.417368550859,68.));
#2003 = PCURVE('',#157,#2004);
#2004 = DEFINITIONAL_REPRESENTATION('',(#2005),#2080);
#2005 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2006,#2007,#2008,#2009,#2010,
    #2011,#2012,#2013,#2014,#2015,#2016,#2017,#2018,#2019,#2020,#2021,
    #2022,#2023,#2024,#2025,#2026,#2027,#2028,#2029,#2030,#2031,#2032,
    #2033,#2034,#2035,#2036,#2037,#2038,#2039,#2040,#2041,#2042,#2043,
    #2044,#2045,#2046,#2047,#2048,#2049,#2050,#2051,#2052,#2053,#2054,
    #2055,#2056,#2057,#2058,#2059,#2060,#2061,#2062,#2063,#2064,#2065,
    #2066,#2067,#2068,#2069,#2070,#2071,#2072,#2073,#2074,#2075,#2076,
    #2077,#2078,#2079),.UNSPECIFIED.,.T.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#2006 = CARTESIAN_POINT('',(4.767462688522,-61.));
#2007 = CARTESIAN_POINT('',(4.767462687968,-61.30167540498));
#2008 = CARTESIAN_POINT('',(4.766812326879,-61.60300677243));
#2009 = CARTESIAN_POINT('',(4.765514138064,-61.90176882878));
#2010 = CARTESIAN_POINT('',(4.763551852125,-62.19546636029));
#2011 = CARTESIAN_POINT('',(4.760894895079,-62.4808334929));
#2012 = CARTESIAN_POINT('',(4.757503587222,-62.75320895181));
#2013 = CARTESIAN_POINT('',(4.750252950328,-63.19242226762));
#2014 = CARTESIAN_POINT('',(4.746716291797,-63.36997354806));
#2015 = CARTESIAN_POINT('',(4.742724660827,-63.53336515514));
#2016 = CARTESIAN_POINT('',(4.738291288821,-63.67768828687));
#2017 = CARTESIAN_POINT('',(4.733444230811,-63.79802840394));
#2018 = CARTESIAN_POINT('',(4.728227421597,-63.88943813947));
#2019 = CARTESIAN_POINT('',(4.716503558799,-64.01131798641));
#2020 = CARTESIAN_POINT('',(4.710085409349,-64.03119963928));
#2021 = CARTESIAN_POINT('',(4.703651028338,-64.00277109886));
#2022 = CARTESIAN_POINT('',(4.697415834757,-63.92699440723));
#2023 = CARTESIAN_POINT('',(4.691582507732,-63.80928737931));
#2024 = CARTESIAN_POINT('',(4.686311479483,-63.65930263254));
#2025 = CARTESIAN_POINT('',(4.679938260771,-63.42625212442));
#2026 = CARTESIAN_POINT('',(4.678251893344,-63.35830022311));
#2027 = CARTESIAN_POINT('',(4.676645383796,-63.28732280723));
#2028 = CARTESIAN_POINT('',(4.67511838019,-63.21363184808));
#2029 = CARTESIAN_POINT('',(4.673670315223,-63.1375156862));
#2030 = CARTESIAN_POINT('',(4.672300414636,-63.05923928615));
#2031 = CARTESIAN_POINT('',(4.669145941704,-62.86354767437));
#2032 = CARTESIAN_POINT('',(4.667443788496,-62.7440403403));
#2033 = CARTESIAN_POINT('',(4.665897435835,-62.6211720525));
#2034 = CARTESIAN_POINT('',(4.664503047061,-62.49549021545));
#2035 = CARTESIAN_POINT('',(4.663256948212,-62.36745566569));
#2036 = CARTESIAN_POINT('',(4.662155817213,-62.23745826271));
#2037 = CARTESIAN_POINT('',(4.6600767968,-61.95208951587));
#2038 = CARTESIAN_POINT('',(4.659150675622,-61.79612116986));
#2039 = CARTESIAN_POINT('',(4.658414253761,-61.63848231655));
#2040 = CARTESIAN_POINT('',(4.657864214439,-61.47964667916));
#2041 = CARTESIAN_POINT('',(4.657498273369,-61.32003163786));
#2042 = CARTESIAN_POINT('',(4.657315272247,-61.16002303828));
#2043 = CARTESIAN_POINT('',(4.657315272247,-60.83506092613));
#2044 = CARTESIAN_POINT('',(4.657509690045,-60.67013864256));
#2045 = CARTESIAN_POINT('',(4.657898452116,-60.50564730586));
#2046 = CARTESIAN_POINT('',(4.658482869241,-60.3420088465));
#2047 = CARTESIAN_POINT('',(4.659265523551,-60.17968195847));
#2048 = CARTESIAN_POINT('',(4.660250155587,-60.01919108905));
#2049 = CARTESIAN_POINT('',(4.662428264855,-59.73027035515));
#2050 = CARTESIAN_POINT('',(4.663556837017,-59.60106476182));
#2051 = CARTESIAN_POINT('',(4.66483009894,-59.47387792651));
#2052 = CARTESIAN_POINT('',(4.666251401337,-59.34910563105));
#2053 = CARTESIAN_POINT('',(4.667824421024,-59.22721566718));
#2054 = CARTESIAN_POINT('',(4.669552967427,-59.10876334277));
#2055 = CARTESIAN_POINT('',(4.674525010442,-58.80757773238));
#2056 = CARTESIAN_POINT('',(4.678061668972,-58.63002645193));
#2057 = CARTESIAN_POINT('',(4.682053299942,-58.46663484485));
#2058 = CARTESIAN_POINT('',(4.686486671948,-58.32231171312));
#2059 = CARTESIAN_POINT('',(4.691333729958,-58.20197159605));
#2060 = CARTESIAN_POINT('',(4.696550539172,-58.11056186052));
#2061 = CARTESIAN_POINT('',(4.708274401961,-57.98868201367));
#2062 = CARTESIAN_POINT('',(4.714692551429,-57.96880036048));
#2063 = CARTESIAN_POINT('',(4.721126932439,-57.99722890138));
#2064 = CARTESIAN_POINT('',(4.727362126004,-58.07300559284));
#2065 = CARTESIAN_POINT('',(4.73319545303,-58.19071262027));
#2066 = CARTESIAN_POINT('',(4.738466481294,-58.34069736773));
#2067 = CARTESIAN_POINT('',(4.744839699645,-58.57374786267));
#2068 = CARTESIAN_POINT('',(4.746526067399,-58.64169977371));
#2069 = CARTESIAN_POINT('',(4.748132577219,-58.71267720244));
#2070 = CARTESIAN_POINT('',(4.74965958079,-58.78636816376));
#2071 = CARTESIAN_POINT('',(4.751107645502,-58.86248431375));
#2072 = CARTESIAN_POINT('',(4.752477545901,-58.94076069943));
#2073 = CARTESIAN_POINT('',(4.757805807817,-59.27130598696));
#2074 = CARTESIAN_POINT('',(4.761092174158,-59.54054440748));
#2075 = CARTESIAN_POINT('',(4.763668412478,-59.82212203322));
#2076 = CARTESIAN_POINT('',(4.765572008639,-60.11164654482));
#2077 = CARTESIAN_POINT('',(4.766831686705,-60.40601794894));
#2078 = CARTESIAN_POINT('',(4.767462687976,-60.70284897613));
#2079 = CARTESIAN_POINT('',(4.767462688522,-61.));
#2080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2081 = PCURVE('',#2082,#2087);
#2082 = CYLINDRICAL_SURFACE('',#2083,3.);
#2083 = AXIS2_PLACEMENT_3D('',#2084,#2085,#2086);
#2084 = CARTESIAN_POINT('',(0.,-54.,68.));
#2085 = DIRECTION('',(0.,-1.,-2.2E-16));
#2086 = DIRECTION('',(1.,0.,0.));
#2087 = DEFINITIONAL_REPRESENTATION('',(#2088),#2163);
#2088 = B_SPLINE_CURVE_WITH_KNOTS('',7,(#2089,#2090,#2091,#2092,#2093,
    #2094,#2095,#2096,#2097,#2098,#2099,#2100,#2101,#2102,#2103,#2104,
    #2105,#2106,#2107,#2108,#2109,#2110,#2111,#2112,#2113,#2114,#2115,
    #2116,#2117,#2118,#2119,#2120,#2121,#2122,#2123,#2124,#2125,#2126,
    #2127,#2128,#2129,#2130,#2131,#2132,#2133,#2134,#2135,#2136,#2137,
    #2138,#2139,#2140,#2141,#2142,#2143,#2144,#2145,#2146,#2147,#2148,
    #2149,#2150,#2151,#2152,#2153,#2154,#2155,#2156,#2157,#2158,#2159,
    #2160,#2161,#2162),.UNSPECIFIED.,.F.,.F.,(8,6,6,6,6,6,6,6,6,6,6,6,8)
  ,(0.,0.128095702561,0.222919538633,0.329356370111,0.370166130396,
    0.428940486402,0.497590742456,0.56834998922,0.626952770147,
    0.722694838484,0.8301623572,0.871367293004,1.),.UNSPECIFIED.);
#2089 = CARTESIAN_POINT('',(0.,-54.41736855085));
#2090 = CARTESIAN_POINT('',(0.100558468328,-54.41736855252));
#2091 = CARTESIAN_POINT('',(0.201002027823,-54.4193196163));
#2092 = CARTESIAN_POINT('',(0.302251297155,-54.42321428433));
#2093 = CARTESIAN_POINT('',(0.40511389169,-54.42895587581));
#2094 = CARTESIAN_POINT('',(0.510445044941,-54.43635029771));
#2095 = CARTESIAN_POINT('',(0.619108432665,-54.44501933438));
#2096 = CARTESIAN_POINT('',(0.815972134213,-54.46119588824));
#2097 = CARTESIAN_POINT('',(0.90290874239,-54.46848205439));
#2098 = CARTESIAN_POINT('',(0.992821338449,-54.47585961969));
#2099 = CARTESIAN_POINT('',(1.085628608031,-54.48290679007));
#2100 = CARTESIAN_POINT('',(1.181357706856,-54.48912317489));
#2101 = CARTESIAN_POINT('',(1.280149684488,-54.4939956778));
#2102 = CARTESIAN_POINT('',(1.496962207852,-54.50058617627));
#2103 = CARTESIAN_POINT('',(1.612736968494,-54.50174958247));
#2104 = CARTESIAN_POINT('',(1.728250627632,-54.50017083483));
#2105 = CARTESIAN_POINT('',(1.842378609009,-54.49586961936));
#2106 = CARTESIAN_POINT('',(1.953885566946,-54.48951237587));
#2107 = CARTESIAN_POINT('',(2.061229755628,-54.48204815405));
#2108 = CARTESIAN_POINT('',(2.200611067507,-54.47139190581));
#2109 = CARTESIAN_POINT('',(2.238657882334,-54.46837394364));
#2110 = CARTESIAN_POINT('',(2.276155319147,-54.46532358416));
#2111 = CARTESIAN_POINT('',(2.313132474971,-54.46227025161));
#2112 = CARTESIAN_POINT('',(2.349615447365,-54.4592405332));
#2113 = CARTESIAN_POINT('',(2.385628923445,-54.4562580768));
#2114 = CARTESIAN_POINT('',(2.472424141459,-54.44914588898));
#2115 = CARTESIAN_POINT('',(2.52274221575,-54.44508794717));
#2116 = CARTESIAN_POINT('',(2.572254214675,-54.44122907981));
#2117 = CARTESIAN_POINT('',(2.621054948465,-54.43761711547));
#2118 = CARTESIAN_POINT('',(2.669234291286,-54.43429002187));
#2119 = CARTESIAN_POINT('',(2.716879657862,-54.43127763296));
#2120 = CARTESIAN_POINT('',(2.819208156552,-54.42547976182));
#2121 = CARTESIAN_POINT('',(2.873729915203,-54.42281745858));
#2122 = CARTESIAN_POINT('',(2.927773716383,-54.42065394698));
#2123 = CARTESIAN_POINT('',(2.981462504303,-54.41901530294));
#2124 = CARTESIAN_POINT('',(3.034915524425,-54.41791763987));
#2125 = CARTESIAN_POINT('',(3.088251640828,-54.41736855085));
#2126 = CARTESIAN_POINT('',(3.196572344877,-54.41736855085));
#2127 = CARTESIAN_POINT('',(3.251546336094,-54.41795190666));
#2128 = CARTESIAN_POINT('',(3.306648370688,-54.4191180012));
#2129 = CARTESIAN_POINT('',(3.362008743141,-54.42085830348));
#2130 = CARTESIAN_POINT('',(3.417758180445,-54.42315463792));
#2131 = CARTESIAN_POINT('',(3.474031723575,-54.42597745363));
#2132 = CARTESIAN_POINT('',(3.578131028324,-54.43202265182));
#2133 = CARTESIAN_POINT('',(3.62574881089,-54.43509310308));
#2134 = CARTESIAN_POINT('',(3.67391287984,-54.43847130321));
#2135 = CARTESIAN_POINT('',(3.722710212757,-54.44212659546));
#2136 = CARTESIAN_POINT('',(3.772230317841,-54.44602009909));
#2137 = CARTESIAN_POINT('',(3.822567710118,-54.45010299767));
#2138 = CARTESIAN_POINT('',(3.957564787803,-54.46119588824));
#2139 = CARTESIAN_POINT('',(4.04450139598,-54.46848205439));
#2140 = CARTESIAN_POINT('',(4.134413992039,-54.47585961969));
#2141 = CARTESIAN_POINT('',(4.227221261621,-54.48290679007));
#2142 = CARTESIAN_POINT('',(4.322950360446,-54.48912317489));
#2143 = CARTESIAN_POINT('',(4.421742338078,-54.4939956778));
#2144 = CARTESIAN_POINT('',(4.638554861273,-54.50058617626));
#2145 = CARTESIAN_POINT('',(4.754329622252,-54.50174958249));
#2146 = CARTESIAN_POINT('',(4.86984328134,-54.50017083482));
#2147 = CARTESIAN_POINT('',(4.983971262504,-54.49586961936));
#2148 = CARTESIAN_POINT('',(5.095478220347,-54.48951237589));
#2149 = CARTESIAN_POINT('',(5.202822409381,-54.48204815404));
#2150 = CARTESIAN_POINT('',(5.34220371338,-54.4713919064));
#2151 = CARTESIAN_POINT('',(5.380250534847,-54.46837394374));
#2152 = CARTESIAN_POINT('',(5.417747978255,-54.46532358373));
#2153 = CARTESIAN_POINT('',(5.454725134082,-54.46227025116));
#2154 = CARTESIAN_POINT('',(5.491208100326,-54.45924053324));
#2155 = CARTESIAN_POINT('',(5.527221570641,-54.45625807732));
#2156 = CARTESIAN_POINT('',(5.673828520394,-54.44424478689));
#2157 = CARTESIAN_POINT('',(5.780618336297,-54.4358005901));
#2158 = CARTESIAN_POINT('',(5.884226831582,-54.42861473049));
#2159 = CARTESIAN_POINT('',(5.985479521324,-54.42304065842));
#2160 = CARTESIAN_POINT('',(6.085191489599,-54.41926154017));
#2161 = CARTESIAN_POINT('',(6.184134965891,-54.41736855249));
#2162 = CARTESIAN_POINT('',(6.28318530718,-54.41736855085));
#2163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2164 = ADVANCED_FACE('',(#2165),#183,.T.);
#2165 = FACE_BOUND('',#2166,.F.);
#2166 = EDGE_LOOP('',(#2167,#2168,#2189,#2190));
#2167 = ORIENTED_EDGE('',*,*,#908,.T.);
#2168 = ORIENTED_EDGE('',*,*,#2169,.T.);
#2169 = EDGE_CURVE('',#881,#200,#2170,.T.);
#2170 = SURFACE_CURVE('',#2171,(#2175,#2182),.PCURVE_S1.);
#2171 = LINE('',#2172,#2173);
#2172 = CARTESIAN_POINT('',(2.5,-10.25071429154,77.));
#2173 = VECTOR('',#2174,1.);
#2174 = DIRECTION('',(0.,1.,0.));
#2175 = PCURVE('',#183,#2176);
#2176 = DEFINITIONAL_REPRESENTATION('',(#2177),#2181);
#2177 = LINE('',#2178,#2179);
#2178 = CARTESIAN_POINT('',(-1.570796326795,0.));
#2179 = VECTOR('',#2180,1.);
#2180 = DIRECTION('',(-0.,1.));
#2181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2182 = PCURVE('',#239,#2183);
#2183 = DEFINITIONAL_REPRESENTATION('',(#2184),#2188);
#2184 = LINE('',#2185,#2186);
#2185 = CARTESIAN_POINT('',(2.5,-12.62977458842));
#2186 = VECTOR('',#2187,1.);
#2187 = DIRECTION('',(0.,1.));
#2188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2189 = ORIENTED_EDGE('',*,*,#199,.F.);
#2190 = ORIENTED_EDGE('',*,*,#169,.F.);
#2191 = ADVANCED_FACE('',(#2192),#239,.T.);
#2192 = FACE_BOUND('',#2193,.T.);
#2193 = EDGE_LOOP('',(#2194,#2195,#2216,#2217));
#2194 = ORIENTED_EDGE('',*,*,#223,.T.);
#2195 = ORIENTED_EDGE('',*,*,#2196,.T.);
#2196 = EDGE_CURVE('',#224,#714,#2197,.T.);
#2197 = SURFACE_CURVE('',#2198,(#2202,#2209),.PCURVE_S1.);
#2198 = LINE('',#2199,#2200);
#2199 = CARTESIAN_POINT('',(-2.5,10.5,77.));
#2200 = VECTOR('',#2201,1.);
#2201 = DIRECTION('',(0.,-1.,0.));
#2202 = PCURVE('',#239,#2203);
#2203 = DEFINITIONAL_REPRESENTATION('',(#2204),#2208);
#2204 = LINE('',#2205,#2206);
#2205 = CARTESIAN_POINT('',(-2.5,8.120939703118));
#2206 = VECTOR('',#2207,1.);
#2207 = DIRECTION('',(0.,-1.));
#2208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2209 = PCURVE('',#268,#2210);
#2210 = DEFINITIONAL_REPRESENTATION('',(#2211),#2215);
#2211 = LINE('',#2212,#2213);
#2212 = CARTESIAN_POINT('',(0.,0.));
#2213 = VECTOR('',#2214,1.);
#2214 = DIRECTION('',(0.,1.));
#2215 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2216 = ORIENTED_EDGE('',*,*,#880,.T.);
#2217 = ORIENTED_EDGE('',*,*,#2169,.T.);
#2218 = ADVANCED_FACE('',(#2219),#268,.T.);
#2219 = FACE_BOUND('',#2220,.T.);
#2220 = EDGE_LOOP('',(#2221,#2222,#2243,#2244));
#2221 = ORIENTED_EDGE('',*,*,#251,.T.);
#2222 = ORIENTED_EDGE('',*,*,#2223,.T.);
#2223 = EDGE_CURVE('',#252,#691,#2224,.T.);
#2224 = SURFACE_CURVE('',#2225,(#2229,#2236),.PCURVE_S1.);
#2225 = LINE('',#2226,#2227);
#2226 = CARTESIAN_POINT('',(-32.5,10.5,47.));
#2227 = VECTOR('',#2228,1.);
#2228 = DIRECTION('',(0.,-1.,0.));
#2229 = PCURVE('',#268,#2230);
#2230 = DEFINITIONAL_REPRESENTATION('',(#2231),#2235);
#2231 = LINE('',#2232,#2233);
#2232 = CARTESIAN_POINT('',(1.570796326795,0.));
#2233 = VECTOR('',#2234,1.);
#2234 = DIRECTION('',(0.,1.));
#2235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2236 = PCURVE('',#296,#2237);
#2237 = DEFINITIONAL_REPRESENTATION('',(#2238),#2242);
#2238 = LINE('',#2239,#2240);
#2239 = CARTESIAN_POINT('',(0.,-40.));
#2240 = VECTOR('',#2241,1.);
#2241 = DIRECTION('',(1.,0.));
#2242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2243 = ORIENTED_EDGE('',*,*,#713,.F.);
#2244 = ORIENTED_EDGE('',*,*,#2196,.F.);
#2245 = ADVANCED_FACE('',(#2246),#296,.F.);
#2246 = FACE_BOUND('',#2247,.F.);
#2247 = EDGE_LOOP('',(#2248,#2249,#2250,#2251,#2279,#2300));
#2248 = ORIENTED_EDGE('',*,*,#280,.T.);
#2249 = ORIENTED_EDGE('',*,*,#2223,.T.);
#2250 = ORIENTED_EDGE('',*,*,#690,.F.);
#2251 = ORIENTED_EDGE('',*,*,#2252,.F.);
#2252 = EDGE_CURVE('',#2253,#667,#2255,.T.);
#2253 = VERTEX_POINT('',#2254);
#2254 = CARTESIAN_POINT('',(-32.5,-10.25071429154,7.));
#2255 = SURFACE_CURVE('',#2256,(#2260,#2267),.PCURVE_S1.);
#2256 = LINE('',#2257,#2258);
#2257 = CARTESIAN_POINT('',(-32.5,-10.25071429154,7.));
#2258 = VECTOR('',#2259,1.);
#2259 = DIRECTION('',(0.,0.,1.));
#2260 = PCURVE('',#296,#2261);
#2261 = DEFINITIONAL_REPRESENTATION('',(#2262),#2266);
#2262 = LINE('',#2263,#2264);
#2263 = CARTESIAN_POINT('',(20.750714291545,0.));
#2264 = VECTOR('',#2265,1.);
#2265 = DIRECTION('',(0.,-1.));
#2266 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2267 = PCURVE('',#2268,#2273);
#2268 = CYLINDRICAL_SURFACE('',#2269,54.5);
#2269 = AXIS2_PLACEMENT_3D('',#2270,#2271,#2272);
#2270 = CARTESIAN_POINT('',(0.,-54.,7.));
#2271 = DIRECTION('',(-0.,-0.,-1.));
#2272 = DIRECTION('',(1.,0.,0.));
#2273 = DEFINITIONAL_REPRESENTATION('',(#2274),#2278);
#2274 = LINE('',#2275,#2276);
#2275 = CARTESIAN_POINT('',(-2.20971812738,0.));
#2276 = VECTOR('',#2277,1.);
#2277 = DIRECTION('',(-0.,-1.));
#2278 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2279 = ORIENTED_EDGE('',*,*,#2280,.F.);
#2280 = EDGE_CURVE('',#485,#2253,#2281,.T.);
#2281 = SURFACE_CURVE('',#2282,(#2286,#2293),.PCURVE_S1.);
#2282 = LINE('',#2283,#2284);
#2283 = CARTESIAN_POINT('',(-32.5,10.5,7.));
#2284 = VECTOR('',#2285,1.);
#2285 = DIRECTION('',(0.,-1.,0.));
#2286 = PCURVE('',#296,#2287);
#2287 = DEFINITIONAL_REPRESENTATION('',(#2288),#2292);
#2288 = LINE('',#2289,#2290);
#2289 = CARTESIAN_POINT('',(0.,0.));
#2290 = VECTOR('',#2291,1.);
#2291 = DIRECTION('',(1.,0.));
#2292 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2293 = PCURVE('',#500,#2294);
#2294 = DEFINITIONAL_REPRESENTATION('',(#2295),#2299);
#2295 = LINE('',#2296,#2297);
#2296 = CARTESIAN_POINT('',(-32.5,8.120939703118));
#2297 = VECTOR('',#2298,1.);
#2298 = DIRECTION('',(0.,-1.));
#2299 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2300 = ORIENTED_EDGE('',*,*,#512,.F.);
#2301 = ADVANCED_FACE('',(#2302),#348,.F.);
#2302 = FACE_BOUND('',#2303,.F.);
#2303 = EDGE_LOOP('',(#2304,#2333,#2354,#2355));
#2304 = ORIENTED_EDGE('',*,*,#2305,.F.);
#2305 = EDGE_CURVE('',#2306,#2306,#2308,.T.);
#2306 = VERTEX_POINT('',#2307);
#2307 = CARTESIAN_POINT('',(18.845508075689,4.,38.));
#2308 = SURFACE_CURVE('',#2309,(#2314,#2321),.PCURVE_S1.);
#2309 = CIRCLE('',#2310,1.525);
#2310 = AXIS2_PLACEMENT_3D('',#2311,#2312,#2313);
#2311 = CARTESIAN_POINT('',(17.320508075689,4.,38.));
#2312 = DIRECTION('',(0.,-1.,-2.2E-16));
#2313 = DIRECTION('',(1.,0.,0.));
#2314 = PCURVE('',#348,#2315);
#2315 = DEFINITIONAL_REPRESENTATION('',(#2316),#2320);
#2316 = LINE('',#2317,#2318);
#2317 = CARTESIAN_POINT('',(0.,-58.));
#2318 = VECTOR('',#2319,1.);
#2319 = DIRECTION('',(1.,0.));
#2320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2321 = PCURVE('',#2322,#2327);
#2322 = PLANE('',#2323);
#2323 = AXIS2_PLACEMENT_3D('',#2324,#2325,#2326);
#2324 = CARTESIAN_POINT('',(17.320508075689,4.,38.));
#2325 = DIRECTION('',(0.,-1.,-2.2E-16));
#2326 = DIRECTION('',(0.,2.2E-16,-1.));
#2327 = DEFINITIONAL_REPRESENTATION('',(#2328),#2332);
#2328 = CIRCLE('',#2329,1.525);
#2329 = AXIS2_PLACEMENT_2D('',#2330,#2331);
#2330 = CARTESIAN_POINT('',(3.55E-15,0.));
#2331 = DIRECTION('',(0.,1.));
#2332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2333 = ORIENTED_EDGE('',*,*,#2334,.T.);
#2334 = EDGE_CURVE('',#2306,#332,#2335,.T.);
#2335 = SEAM_CURVE('',#2336,(#2340,#2347),.PCURVE_S1.);
#2336 = LINE('',#2337,#2338);
#2337 = CARTESIAN_POINT('',(18.845508075689,-54.,38.));
#2338 = VECTOR('',#2339,1.);
#2339 = DIRECTION('',(0.,1.,2.2E-16));
#2340 = PCURVE('',#348,#2341);
#2341 = DEFINITIONAL_REPRESENTATION('',(#2342),#2346);
#2342 = LINE('',#2343,#2344);
#2343 = CARTESIAN_POINT('',(6.28318530718,0.));
#2344 = VECTOR('',#2345,1.);
#2345 = DIRECTION('',(0.,-1.));
#2346 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2347 = PCURVE('',#348,#2348);
#2348 = DEFINITIONAL_REPRESENTATION('',(#2349),#2353);
#2349 = LINE('',#2350,#2351);
#2350 = CARTESIAN_POINT('',(0.,0.));
#2351 = VECTOR('',#2352,1.);
#2352 = DIRECTION('',(0.,-1.));
#2353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2354 = ORIENTED_EDGE('',*,*,#331,.T.);
#2355 = ORIENTED_EDGE('',*,*,#2334,.F.);
#2356 = ADVANCED_FACE('',(#2357),#383,.F.);
#2357 = FACE_BOUND('',#2358,.F.);
#2358 = EDGE_LOOP('',(#2359,#2382,#2409,#2410));
#2359 = ORIENTED_EDGE('',*,*,#2360,.T.);
#2360 = EDGE_CURVE('',#363,#2361,#2363,.T.);
#2361 = VERTEX_POINT('',#2362);
#2362 = CARTESIAN_POINT('',(-11.5,8.7,48.));
#2363 = SEAM_CURVE('',#2364,(#2368,#2375),.PCURVE_S1.);
#2364 = LINE('',#2365,#2366);
#2365 = CARTESIAN_POINT('',(-11.5,10.5,48.));
#2366 = VECTOR('',#2367,1.);
#2367 = DIRECTION('',(0.,-1.,2.2E-16));
#2368 = PCURVE('',#383,#2369);
#2369 = DEFINITIONAL_REPRESENTATION('',(#2370),#2374);
#2370 = LINE('',#2371,#2372);
#2371 = CARTESIAN_POINT('',(0.,0.));
#2372 = VECTOR('',#2373,1.);
#2373 = DIRECTION('',(0.,-1.));
#2374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2375 = PCURVE('',#383,#2376);
#2376 = DEFINITIONAL_REPRESENTATION('',(#2377),#2381);
#2377 = LINE('',#2378,#2379);
#2378 = CARTESIAN_POINT('',(6.28318530718,0.));
#2379 = VECTOR('',#2380,1.);
#2380 = DIRECTION('',(0.,-1.));
#2381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2382 = ORIENTED_EDGE('',*,*,#2383,.T.);
#2383 = EDGE_CURVE('',#2361,#2361,#2384,.T.);
#2384 = SURFACE_CURVE('',#2385,(#2390,#2397),.PCURVE_S1.);
#2385 = CIRCLE('',#2386,11.5);
#2386 = AXIS2_PLACEMENT_3D('',#2387,#2388,#2389);
#2387 = CARTESIAN_POINT('',(0.,8.7,48.));
#2388 = DIRECTION('',(0.,1.,-0.));
#2389 = DIRECTION('',(-1.,0.,0.));
#2390 = PCURVE('',#383,#2391);
#2391 = DEFINITIONAL_REPRESENTATION('',(#2392),#2396);
#2392 = LINE('',#2393,#2394);
#2393 = CARTESIAN_POINT('',(0.,-1.8));
#2394 = VECTOR('',#2395,1.);
#2395 = DIRECTION('',(1.,0.));
#2396 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2397 = PCURVE('',#2398,#2403);
#2398 = PLANE('',#2399);
#2399 = AXIS2_PLACEMENT_3D('',#2400,#2401,#2402);
#2400 = CARTESIAN_POINT('',(7.1E-16,8.7,48.));
#2401 = DIRECTION('',(4.1E-16,1.,-7.1E-16));
#2402 = DIRECTION('',(0.,7.1E-16,1.));
#2403 = DEFINITIONAL_REPRESENTATION('',(#2404),#2408);
#2404 = CIRCLE('',#2405,11.5);
#2405 = AXIS2_PLACEMENT_2D('',#2406,#2407);
#2406 = CARTESIAN_POINT('',(-7.11E-15,-7.1E-16));
#2407 = DIRECTION('',(0.,-1.));
#2408 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2409 = ORIENTED_EDGE('',*,*,#2360,.F.);
#2410 = ORIENTED_EDGE('',*,*,#362,.F.);
#2411 = ADVANCED_FACE('',(#2412),#414,.F.);
#2412 = FACE_BOUND('',#2413,.F.);
#2413 = EDGE_LOOP('',(#2414,#2443,#2464,#2465));
#2414 = ORIENTED_EDGE('',*,*,#2415,.F.);
#2415 = EDGE_CURVE('',#2416,#2416,#2418,.T.);
#2416 = VERTEX_POINT('',#2417);
#2417 = CARTESIAN_POINT('',(1.525,4.,68.));
#2418 = SURFACE_CURVE('',#2419,(#2424,#2431),.PCURVE_S1.);
#2419 = CIRCLE('',#2420,1.525);
#2420 = AXIS2_PLACEMENT_3D('',#2421,#2422,#2423);
#2421 = CARTESIAN_POINT('',(0.,4.,68.));
#2422 = DIRECTION('',(0.,-1.,-2.2E-16));
#2423 = DIRECTION('',(1.,0.,0.));
#2424 = PCURVE('',#414,#2425);
#2425 = DEFINITIONAL_REPRESENTATION('',(#2426),#2430);
#2426 = LINE('',#2427,#2428);
#2427 = CARTESIAN_POINT('',(0.,-58.));
#2428 = VECTOR('',#2429,1.);
#2429 = DIRECTION('',(1.,0.));
#2430 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2431 = PCURVE('',#2432,#2437);
#2432 = PLANE('',#2433);
#2433 = AXIS2_PLACEMENT_3D('',#2434,#2435,#2436);
#2434 = CARTESIAN_POINT('',(-2.3E-16,4.,68.));
#2435 = DIRECTION('',(-0.,-1.,-2.2E-16));
#2436 = DIRECTION('',(0.,2.2E-16,-1.));
#2437 = DEFINITIONAL_REPRESENTATION('',(#2438),#2442);
#2438 = CIRCLE('',#2439,1.525);
#2439 = AXIS2_PLACEMENT_2D('',#2440,#2441);
#2440 = CARTESIAN_POINT('',(7.11E-15,2.3E-16));
#2441 = DIRECTION('',(0.,1.));
#2442 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2443 = ORIENTED_EDGE('',*,*,#2444,.T.);
#2444 = EDGE_CURVE('',#2416,#398,#2445,.T.);
#2445 = SEAM_CURVE('',#2446,(#2450,#2457),.PCURVE_S1.);
#2446 = LINE('',#2447,#2448);
#2447 = CARTESIAN_POINT('',(1.525,-54.,68.));
#2448 = VECTOR('',#2449,1.);
#2449 = DIRECTION('',(0.,1.,2.2E-16));
#2450 = PCURVE('',#414,#2451);
#2451 = DEFINITIONAL_REPRESENTATION('',(#2452),#2456);
#2452 = LINE('',#2453,#2454);
#2453 = CARTESIAN_POINT('',(6.28318530718,0.));
#2454 = VECTOR('',#2455,1.);
#2455 = DIRECTION('',(0.,-1.));
#2456 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2457 = PCURVE('',#414,#2458);
#2458 = DEFINITIONAL_REPRESENTATION('',(#2459),#2463);
#2459 = LINE('',#2460,#2461);
#2460 = CARTESIAN_POINT('',(0.,0.));
#2461 = VECTOR('',#2462,1.);
#2462 = DIRECTION('',(0.,-1.));
#2463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2464 = ORIENTED_EDGE('',*,*,#397,.T.);
#2465 = ORIENTED_EDGE('',*,*,#2444,.F.);
#2466 = ADVANCED_FACE('',(#2467),#445,.F.);
#2467 = FACE_BOUND('',#2468,.F.);
#2468 = EDGE_LOOP('',(#2469,#2498,#2519,#2520));
#2469 = ORIENTED_EDGE('',*,*,#2470,.F.);
#2470 = EDGE_CURVE('',#2471,#2471,#2473,.T.);
#2471 = VERTEX_POINT('',#2472);
#2472 = CARTESIAN_POINT('',(-15.79550807568,4.,38.));
#2473 = SURFACE_CURVE('',#2474,(#2479,#2486),.PCURVE_S1.);
#2474 = CIRCLE('',#2475,1.525);
#2475 = AXIS2_PLACEMENT_3D('',#2476,#2477,#2478);
#2476 = CARTESIAN_POINT('',(-17.32050807568,4.,38.));
#2477 = DIRECTION('',(0.,-1.,-2.2E-16));
#2478 = DIRECTION('',(1.,0.,0.));
#2479 = PCURVE('',#445,#2480);
#2480 = DEFINITIONAL_REPRESENTATION('',(#2481),#2485);
#2481 = LINE('',#2482,#2483);
#2482 = CARTESIAN_POINT('',(0.,-58.));
#2483 = VECTOR('',#2484,1.);
#2484 = DIRECTION('',(1.,0.));
#2485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2486 = PCURVE('',#2487,#2492);
#2487 = PLANE('',#2488);
#2488 = AXIS2_PLACEMENT_3D('',#2489,#2490,#2491);
#2489 = CARTESIAN_POINT('',(-17.32050807568,4.,38.));
#2490 = DIRECTION('',(-0.,-1.,-2.2E-16));
#2491 = DIRECTION('',(0.,2.2E-16,-1.));
#2492 = DEFINITIONAL_REPRESENTATION('',(#2493),#2497);
#2493 = CIRCLE('',#2494,1.525);
#2494 = AXIS2_PLACEMENT_2D('',#2495,#2496);
#2495 = CARTESIAN_POINT('',(-3.55E-15,0.));
#2496 = DIRECTION('',(0.,1.));
#2497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2498 = ORIENTED_EDGE('',*,*,#2499,.T.);
#2499 = EDGE_CURVE('',#2471,#429,#2500,.T.);
#2500 = SEAM_CURVE('',#2501,(#2505,#2512),.PCURVE_S1.);
#2501 = LINE('',#2502,#2503);
#2502 = CARTESIAN_POINT('',(-15.79550807568,-54.,38.));
#2503 = VECTOR('',#2504,1.);
#2504 = DIRECTION('',(0.,1.,2.2E-16));
#2505 = PCURVE('',#445,#2506);
#2506 = DEFINITIONAL_REPRESENTATION('',(#2507),#2511);
#2507 = LINE('',#2508,#2509);
#2508 = CARTESIAN_POINT('',(6.28318530718,0.));
#2509 = VECTOR('',#2510,1.);
#2510 = DIRECTION('',(0.,-1.));
#2511 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2512 = PCURVE('',#445,#2513);
#2513 = DEFINITIONAL_REPRESENTATION('',(#2514),#2518);
#2514 = LINE('',#2515,#2516);
#2515 = CARTESIAN_POINT('',(0.,0.));
#2516 = VECTOR('',#2517,1.);
#2517 = DIRECTION('',(0.,-1.));
#2518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2519 = ORIENTED_EDGE('',*,*,#428,.T.);
#2520 = ORIENTED_EDGE('',*,*,#2499,.F.);
#2521 = ADVANCED_FACE('',(#2522),#500,.F.);
#2522 = FACE_BOUND('',#2523,.F.);
#2523 = EDGE_LOOP('',(#2524,#2525,#2526));
#2524 = ORIENTED_EDGE('',*,*,#484,.T.);
#2525 = ORIENTED_EDGE('',*,*,#2280,.T.);
#2526 = ORIENTED_EDGE('',*,*,#2527,.T.);
#2527 = EDGE_CURVE('',#2253,#462,#2528,.T.);
#2528 = SURFACE_CURVE('',#2529,(#2534,#2545),.PCURVE_S1.);
#2529 = CIRCLE('',#2530,54.5);
#2530 = AXIS2_PLACEMENT_3D('',#2531,#2532,#2533);
#2531 = CARTESIAN_POINT('',(0.,-54.,7.));
#2532 = DIRECTION('',(0.,0.,-1.));
#2533 = DIRECTION('',(1.,0.,0.));
#2534 = PCURVE('',#500,#2535);
#2535 = DEFINITIONAL_REPRESENTATION('',(#2536),#2544);
#2536 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2537,#2538,#2539,#2540,
#2541,#2542,#2543),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2537 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#2538 = CARTESIAN_POINT('',(54.5,-150.7758293093));
#2539 = CARTESIAN_POINT('',(-27.25,-103.5774448031));
#2540 = CARTESIAN_POINT('',(-109.,-56.37906029688));
#2541 = CARTESIAN_POINT('',(-27.25,-9.18067579063));
#2542 = CARTESIAN_POINT('',(54.5,38.017708715621));
#2543 = CARTESIAN_POINT('',(54.5,-56.37906029688));
#2544 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2545 = PCURVE('',#562,#2546);
#2546 = DEFINITIONAL_REPRESENTATION('',(#2547),#2555);
#2547 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#2548,#2549,#2550,#2551,
#2552,#2553,#2554),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#2548 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#2549 = CARTESIAN_POINT('',(54.5,-94.3967690125));
#2550 = CARTESIAN_POINT('',(-27.25,-47.19838450625));
#2551 = CARTESIAN_POINT('',(-109.,-1.754865011071E-14));
#2552 = CARTESIAN_POINT('',(-27.25,47.198384506252));
#2553 = CARTESIAN_POINT('',(54.5,94.396769012504));
#2554 = CARTESIAN_POINT('',(54.5,-4.2E-15));
#2555 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2556 = ADVANCED_FACE('',(#2557,#2828,#3080,#3115,#3146,#3177,#3208,
    #3243,#3278,#3313),#562,.F.);
#2557 = FACE_BOUND('',#2558,.F.);
#2558 = EDGE_LOOP('',(#2559,#2589,#2618,#2639,#2640,#2641,#2642,#2666,
    #2694,#2723,#2746,#2774,#2802));
#2559 = ORIENTED_EDGE('',*,*,#2560,.T.);
#2560 = EDGE_CURVE('',#2561,#2563,#2565,.T.);
#2561 = VERTEX_POINT('',#2562);
#2562 = CARTESIAN_POINT('',(15.5,-104.,7.));
#2563 = VERTEX_POINT('',#2564);
#2564 = CARTESIAN_POINT('',(21.685248442201,-104.,7.));
#2565 = SURFACE_CURVE('',#2566,(#2570,#2577),.PCURVE_S1.);
#2566 = LINE('',#2567,#2568);
#2567 = CARTESIAN_POINT('',(-22.5,-104.,7.));
#2568 = VECTOR('',#2569,1.);
#2569 = DIRECTION('',(1.,0.,0.));
#2570 = PCURVE('',#562,#2571);
#2571 = DEFINITIONAL_REPRESENTATION('',(#2572),#2576);
#2572 = LINE('',#2573,#2574);
#2573 = CARTESIAN_POINT('',(-22.5,-50.));
#2574 = VECTOR('',#2575,1.);
#2575 = DIRECTION('',(1.,0.));
#2576 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2577 = PCURVE('',#2578,#2583);
#2578 = PLANE('',#2579);
#2579 = AXIS2_PLACEMENT_3D('',#2580,#2581,#2582);
#2580 = CARTESIAN_POINT('',(-45.,-104.,22.));
#2581 = DIRECTION('',(0.,-1.,0.));
#2582 = DIRECTION('',(1.,0.,0.));
#2583 = DEFINITIONAL_REPRESENTATION('',(#2584),#2588);
#2584 = LINE('',#2585,#2586);
#2585 = CARTESIAN_POINT('',(22.5,-15.));
#2586 = VECTOR('',#2587,1.);
#2587 = DIRECTION('',(1.,0.));
#2588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2589 = ORIENTED_EDGE('',*,*,#2590,.T.);
#2590 = EDGE_CURVE('',#2563,#2591,#2593,.T.);
#2591 = VERTEX_POINT('',#2592);
#2592 = CARTESIAN_POINT('',(51.,-73.21587885057,7.));
#2593 = SURFACE_CURVE('',#2594,(#2599,#2606),.PCURVE_S1.);
#2594 = CIRCLE('',#2595,54.5);
#2595 = AXIS2_PLACEMENT_3D('',#2596,#2597,#2598);
#2596 = CARTESIAN_POINT('',(0.,-54.,7.));
#2597 = DIRECTION('',(0.,0.,1.));
#2598 = DIRECTION('',(1.,0.,0.));
#2599 = PCURVE('',#562,#2600);
#2600 = DEFINITIONAL_REPRESENTATION('',(#2601),#2605);
#2601 = CIRCLE('',#2602,54.5);
#2602 = AXIS2_PLACEMENT_2D('',#2603,#2604);
#2603 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2604 = DIRECTION('',(1.,0.));
#2605 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2606 = PCURVE('',#2607,#2612);
#2607 = CYLINDRICAL_SURFACE('',#2608,54.5);
#2608 = AXIS2_PLACEMENT_3D('',#2609,#2610,#2611);
#2609 = CARTESIAN_POINT('',(0.,-54.,7.));
#2610 = DIRECTION('',(-0.,-0.,-1.));
#2611 = DIRECTION('',(1.,0.,0.));
#2612 = DEFINITIONAL_REPRESENTATION('',(#2613),#2617);
#2613 = LINE('',#2614,#2615);
#2614 = CARTESIAN_POINT('',(-0.,0.));
#2615 = VECTOR('',#2616,1.);
#2616 = DIRECTION('',(-1.,0.));
#2617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2618 = ORIENTED_EDGE('',*,*,#2619,.T.);
#2619 = EDGE_CURVE('',#2591,#583,#2620,.T.);
#2620 = SURFACE_CURVE('',#2621,(#2625,#2632),.PCURVE_S1.);
#2621 = LINE('',#2622,#2623);
#2622 = CARTESIAN_POINT('',(51.,-79.,7.));
#2623 = VECTOR('',#2624,1.);
#2624 = DIRECTION('',(0.,1.,0.));
#2625 = PCURVE('',#562,#2626);
#2626 = DEFINITIONAL_REPRESENTATION('',(#2627),#2631);
#2627 = LINE('',#2628,#2629);
#2628 = CARTESIAN_POINT('',(51.,-25.));
#2629 = VECTOR('',#2630,1.);
#2630 = DIRECTION('',(0.,1.));
#2631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2632 = PCURVE('',#622,#2633);
#2633 = DEFINITIONAL_REPRESENTATION('',(#2634),#2638);
#2634 = LINE('',#2635,#2636);
#2635 = CARTESIAN_POINT('',(25.,-15.));
#2636 = VECTOR('',#2637,1.);
#2637 = DIRECTION('',(1.,0.));
#2638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2639 = ORIENTED_EDGE('',*,*,#582,.T.);
#2640 = ORIENTED_EDGE('',*,*,#543,.F.);
#2641 = ORIENTED_EDGE('',*,*,#2527,.F.);
#2642 = ORIENTED_EDGE('',*,*,#2643,.T.);
#2643 = EDGE_CURVE('',#2253,#2644,#2646,.T.);
#2644 = VERTEX_POINT('',#2645);
#2645 = CARTESIAN_POINT('',(-51.,-34.78412114942,7.));
#2646 = SURFACE_CURVE('',#2647,(#2652,#2659),.PCURVE_S1.);
#2647 = CIRCLE('',#2648,54.5);
#2648 = AXIS2_PLACEMENT_3D('',#2649,#2650,#2651);
#2649 = CARTESIAN_POINT('',(0.,-54.,7.));
#2650 = DIRECTION('',(0.,0.,1.));
#2651 = DIRECTION('',(1.,0.,0.));
#2652 = PCURVE('',#562,#2653);
#2653 = DEFINITIONAL_REPRESENTATION('',(#2654),#2658);
#2654 = CIRCLE('',#2655,54.5);
#2655 = AXIS2_PLACEMENT_2D('',#2656,#2657);
#2656 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2657 = DIRECTION('',(1.,0.));
#2658 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2659 = PCURVE('',#2268,#2660);
#2660 = DEFINITIONAL_REPRESENTATION('',(#2661),#2665);
#2661 = LINE('',#2662,#2663);
#2662 = CARTESIAN_POINT('',(-0.,0.));
#2663 = VECTOR('',#2664,1.);
#2664 = DIRECTION('',(-1.,0.));
#2665 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2666 = ORIENTED_EDGE('',*,*,#2667,.T.);
#2667 = EDGE_CURVE('',#2644,#2668,#2670,.T.);
#2668 = VERTEX_POINT('',#2669);
#2669 = CARTESIAN_POINT('',(-51.,-73.21587885057,7.));
#2670 = SURFACE_CURVE('',#2671,(#2675,#2682),.PCURVE_S1.);
#2671 = LINE('',#2672,#2673);
#2672 = CARTESIAN_POINT('',(-51.,-29.,7.));
#2673 = VECTOR('',#2674,1.);
#2674 = DIRECTION('',(0.,-1.,0.));
#2675 = PCURVE('',#562,#2676);
#2676 = DEFINITIONAL_REPRESENTATION('',(#2677),#2681);
#2677 = LINE('',#2678,#2679);
#2678 = CARTESIAN_POINT('',(-51.,25.));
#2679 = VECTOR('',#2680,1.);
#2680 = DIRECTION('',(0.,-1.));
#2681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2682 = PCURVE('',#2683,#2688);
#2683 = PLANE('',#2684);
#2684 = AXIS2_PLACEMENT_3D('',#2685,#2686,#2687);
#2685 = CARTESIAN_POINT('',(-51.,-4.,22.));
#2686 = DIRECTION('',(-1.,0.,0.));
#2687 = DIRECTION('',(0.,-1.,0.));
#2688 = DEFINITIONAL_REPRESENTATION('',(#2689),#2693);
#2689 = LINE('',#2690,#2691);
#2690 = CARTESIAN_POINT('',(25.,-15.));
#2691 = VECTOR('',#2692,1.);
#2692 = DIRECTION('',(1.,0.));
#2693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2694 = ORIENTED_EDGE('',*,*,#2695,.T.);
#2695 = EDGE_CURVE('',#2668,#2696,#2698,.T.);
#2696 = VERTEX_POINT('',#2697);
#2697 = CARTESIAN_POINT('',(-21.6852484422,-104.,7.));
#2698 = SURFACE_CURVE('',#2699,(#2704,#2711),.PCURVE_S1.);
#2699 = CIRCLE('',#2700,54.5);
#2700 = AXIS2_PLACEMENT_3D('',#2701,#2702,#2703);
#2701 = CARTESIAN_POINT('',(0.,-54.,7.));
#2702 = DIRECTION('',(0.,0.,1.));
#2703 = DIRECTION('',(1.,0.,0.));
#2704 = PCURVE('',#562,#2705);
#2705 = DEFINITIONAL_REPRESENTATION('',(#2706),#2710);
#2706 = CIRCLE('',#2707,54.5);
#2707 = AXIS2_PLACEMENT_2D('',#2708,#2709);
#2708 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#2709 = DIRECTION('',(1.,0.));
#2710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2711 = PCURVE('',#2712,#2717);
#2712 = CYLINDRICAL_SURFACE('',#2713,54.5);
#2713 = AXIS2_PLACEMENT_3D('',#2714,#2715,#2716);
#2714 = CARTESIAN_POINT('',(0.,-54.,7.));
#2715 = DIRECTION('',(-0.,-0.,-1.));
#2716 = DIRECTION('',(1.,0.,0.));
#2717 = DEFINITIONAL_REPRESENTATION('',(#2718),#2722);
#2718 = LINE('',#2719,#2720);
#2719 = CARTESIAN_POINT('',(-0.,0.));
#2720 = VECTOR('',#2721,1.);
#2721 = DIRECTION('',(-1.,0.));
#2722 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2723 = ORIENTED_EDGE('',*,*,#2724,.T.);
#2724 = EDGE_CURVE('',#2696,#2725,#2727,.T.);
#2725 = VERTEX_POINT('',#2726);
#2726 = CARTESIAN_POINT('',(-15.5,-104.,7.));
#2727 = SURFACE_CURVE('',#2728,(#2732,#2739),.PCURVE_S1.);
#2728 = LINE('',#2729,#2730);
#2729 = CARTESIAN_POINT('',(-22.5,-104.,7.));
#2730 = VECTOR('',#2731,1.);
#2731 = DIRECTION('',(1.,0.,0.));
#2732 = PCURVE('',#562,#2733);
#2733 = DEFINITIONAL_REPRESENTATION('',(#2734),#2738);
#2734 = LINE('',#2735,#2736);
#2735 = CARTESIAN_POINT('',(-22.5,-50.));
#2736 = VECTOR('',#2737,1.);
#2737 = DIRECTION('',(1.,0.));
#2738 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2739 = PCURVE('',#2578,#2740);
#2740 = DEFINITIONAL_REPRESENTATION('',(#2741),#2745);
#2741 = LINE('',#2742,#2743);
#2742 = CARTESIAN_POINT('',(22.5,-15.));
#2743 = VECTOR('',#2744,1.);
#2744 = DIRECTION('',(1.,0.));
#2745 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2746 = ORIENTED_EDGE('',*,*,#2747,.F.);
#2747 = EDGE_CURVE('',#2748,#2725,#2750,.T.);
#2748 = VERTEX_POINT('',#2749);
#2749 = CARTESIAN_POINT('',(-15.5,-64.,7.));
#2750 = SURFACE_CURVE('',#2751,(#2755,#2762),.PCURVE_S1.);
#2751 = LINE('',#2752,#2753);
#2752 = CARTESIAN_POINT('',(-15.5,-64.,7.));
#2753 = VECTOR('',#2754,1.);
#2754 = DIRECTION('',(0.,-1.,0.));
#2755 = PCURVE('',#562,#2756);
#2756 = DEFINITIONAL_REPRESENTATION('',(#2757),#2761);
#2757 = LINE('',#2758,#2759);
#2758 = CARTESIAN_POINT('',(-15.5,-10.));
#2759 = VECTOR('',#2760,1.);
#2760 = DIRECTION('',(0.,-1.));
#2761 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2762 = PCURVE('',#2763,#2768);
#2763 = PLANE('',#2764);
#2764 = AXIS2_PLACEMENT_3D('',#2765,#2766,#2767);
#2765 = CARTESIAN_POINT('',(-15.5,-64.,7.));
#2766 = DIRECTION('',(1.,0.,0.));
#2767 = DIRECTION('',(0.,-1.,0.));
#2768 = DEFINITIONAL_REPRESENTATION('',(#2769),#2773);
#2769 = LINE('',#2770,#2771);
#2770 = CARTESIAN_POINT('',(0.,0.));
#2771 = VECTOR('',#2772,1.);
#2772 = DIRECTION('',(1.,0.));
#2773 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2774 = ORIENTED_EDGE('',*,*,#2775,.F.);
#2775 = EDGE_CURVE('',#2776,#2748,#2778,.T.);
#2776 = VERTEX_POINT('',#2777);
#2777 = CARTESIAN_POINT('',(15.5,-64.,7.));
#2778 = SURFACE_CURVE('',#2779,(#2783,#2790),.PCURVE_S1.);
#2779 = LINE('',#2780,#2781);
#2780 = CARTESIAN_POINT('',(15.5,-64.,7.));
#2781 = VECTOR('',#2782,1.);
#2782 = DIRECTION('',(-1.,0.,0.));
#2783 = PCURVE('',#562,#2784);
#2784 = DEFINITIONAL_REPRESENTATION('',(#2785),#2789);
#2785 = LINE('',#2786,#2787);
#2786 = CARTESIAN_POINT('',(15.5,-10.));
#2787 = VECTOR('',#2788,1.);
#2788 = DIRECTION('',(-1.,0.));
#2789 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2790 = PCURVE('',#2791,#2796);
#2791 = PLANE('',#2792);
#2792 = AXIS2_PLACEMENT_3D('',#2793,#2794,#2795);
#2793 = CARTESIAN_POINT('',(15.5,-64.,7.));
#2794 = DIRECTION('',(0.,-1.,0.));
#2795 = DIRECTION('',(-1.,0.,0.));
#2796 = DEFINITIONAL_REPRESENTATION('',(#2797),#2801);
#2797 = LINE('',#2798,#2799);
#2798 = CARTESIAN_POINT('',(0.,-0.));
#2799 = VECTOR('',#2800,1.);
#2800 = DIRECTION('',(1.,0.));
#2801 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2802 = ORIENTED_EDGE('',*,*,#2803,.F.);
#2803 = EDGE_CURVE('',#2561,#2776,#2804,.T.);
#2804 = SURFACE_CURVE('',#2805,(#2809,#2816),.PCURVE_S1.);
#2805 = LINE('',#2806,#2807);
#2806 = CARTESIAN_POINT('',(15.5,-114.,7.));
#2807 = VECTOR('',#2808,1.);
#2808 = DIRECTION('',(0.,1.,0.));
#2809 = PCURVE('',#562,#2810);
#2810 = DEFINITIONAL_REPRESENTATION('',(#2811),#2815);
#2811 = LINE('',#2812,#2813);
#2812 = CARTESIAN_POINT('',(15.5,-60.));
#2813 = VECTOR('',#2814,1.);
#2814 = DIRECTION('',(0.,1.));
#2815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2816 = PCURVE('',#2817,#2822);
#2817 = PLANE('',#2818);
#2818 = AXIS2_PLACEMENT_3D('',#2819,#2820,#2821);
#2819 = CARTESIAN_POINT('',(15.5,-114.,7.));
#2820 = DIRECTION('',(-1.,0.,0.));
#2821 = DIRECTION('',(0.,1.,0.));
#2822 = DEFINITIONAL_REPRESENTATION('',(#2823),#2827);
#2823 = LINE('',#2824,#2825);
#2824 = CARTESIAN_POINT('',(0.,0.));
#2825 = VECTOR('',#2826,1.);
#2826 = DIRECTION('',(1.,0.));
#2827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2828 = FACE_BOUND('',#2829,.F.);
#2829 = EDGE_LOOP('',(#2830,#2861,#2889,#2917,#2945,#2974,#2998,#3026,
    #3054));
#2830 = ORIENTED_EDGE('',*,*,#2831,.F.);
#2831 = EDGE_CURVE('',#2832,#2834,#2836,.T.);
#2832 = VERTEX_POINT('',#2833);
#2833 = CARTESIAN_POINT('',(-15.5,-24.,7.));
#2834 = VERTEX_POINT('',#2835);
#2835 = CARTESIAN_POINT('',(-15.5,-40.,7.));
#2836 = SURFACE_CURVE('',#2837,(#2842,#2849),.PCURVE_S1.);
#2837 = CIRCLE('',#2838,10.);
#2838 = AXIS2_PLACEMENT_3D('',#2839,#2840,#2841);
#2839 = CARTESIAN_POINT('',(-21.5,-32.,7.));
#2840 = DIRECTION('',(0.,0.,1.));
#2841 = DIRECTION('',(1.,0.,0.));
#2842 = PCURVE('',#562,#2843);
#2843 = DEFINITIONAL_REPRESENTATION('',(#2844),#2848);
#2844 = CIRCLE('',#2845,10.);
#2845 = AXIS2_PLACEMENT_2D('',#2846,#2847);
#2846 = CARTESIAN_POINT('',(-21.5,22.));
#2847 = DIRECTION('',(1.,0.));
#2848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2849 = PCURVE('',#2850,#2855);
#2850 = CYLINDRICAL_SURFACE('',#2851,10.);
#2851 = AXIS2_PLACEMENT_3D('',#2852,#2853,#2854);
#2852 = CARTESIAN_POINT('',(-21.5,-32.,22.));
#2853 = DIRECTION('',(0.,0.,1.));
#2854 = DIRECTION('',(1.,0.,0.));
#2855 = DEFINITIONAL_REPRESENTATION('',(#2856),#2860);
#2856 = LINE('',#2857,#2858);
#2857 = CARTESIAN_POINT('',(0.,-15.));
#2858 = VECTOR('',#2859,1.);
#2859 = DIRECTION('',(1.,0.));
#2860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2861 = ORIENTED_EDGE('',*,*,#2862,.F.);
#2862 = EDGE_CURVE('',#2863,#2832,#2865,.T.);
#2863 = VERTEX_POINT('',#2864);
#2864 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#2865 = SURFACE_CURVE('',#2866,(#2870,#2877),.PCURVE_S1.);
#2866 = LINE('',#2867,#2868);
#2867 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#2868 = VECTOR('',#2869,1.);
#2869 = DIRECTION('',(0.,-1.,0.));
#2870 = PCURVE('',#562,#2871);
#2871 = DEFINITIONAL_REPRESENTATION('',(#2872),#2876);
#2872 = LINE('',#2873,#2874);
#2873 = CARTESIAN_POINT('',(-15.5,46.));
#2874 = VECTOR('',#2875,1.);
#2875 = DIRECTION('',(0.,-1.));
#2876 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2877 = PCURVE('',#2878,#2883);
#2878 = PLANE('',#2879);
#2879 = AXIS2_PLACEMENT_3D('',#2880,#2881,#2882);
#2880 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#2881 = DIRECTION('',(1.,0.,0.));
#2882 = DIRECTION('',(0.,-1.,0.));
#2883 = DEFINITIONAL_REPRESENTATION('',(#2884),#2888);
#2884 = LINE('',#2885,#2886);
#2885 = CARTESIAN_POINT('',(0.,0.));
#2886 = VECTOR('',#2887,1.);
#2887 = DIRECTION('',(1.,0.));
#2888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2889 = ORIENTED_EDGE('',*,*,#2890,.F.);
#2890 = EDGE_CURVE('',#2891,#2863,#2893,.T.);
#2891 = VERTEX_POINT('',#2892);
#2892 = CARTESIAN_POINT('',(15.5,-8.,7.));
#2893 = SURFACE_CURVE('',#2894,(#2898,#2905),.PCURVE_S1.);
#2894 = LINE('',#2895,#2896);
#2895 = CARTESIAN_POINT('',(15.5,-8.,7.));
#2896 = VECTOR('',#2897,1.);
#2897 = DIRECTION('',(-1.,0.,0.));
#2898 = PCURVE('',#562,#2899);
#2899 = DEFINITIONAL_REPRESENTATION('',(#2900),#2904);
#2900 = LINE('',#2901,#2902);
#2901 = CARTESIAN_POINT('',(15.5,46.));
#2902 = VECTOR('',#2903,1.);
#2903 = DIRECTION('',(-1.,0.));
#2904 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2905 = PCURVE('',#2906,#2911);
#2906 = PLANE('',#2907);
#2907 = AXIS2_PLACEMENT_3D('',#2908,#2909,#2910);
#2908 = CARTESIAN_POINT('',(15.5,-8.,7.));
#2909 = DIRECTION('',(0.,-1.,0.));
#2910 = DIRECTION('',(-1.,0.,0.));
#2911 = DEFINITIONAL_REPRESENTATION('',(#2912),#2916);
#2912 = LINE('',#2913,#2914);
#2913 = CARTESIAN_POINT('',(0.,-0.));
#2914 = VECTOR('',#2915,1.);
#2915 = DIRECTION('',(1.,0.));
#2916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2917 = ORIENTED_EDGE('',*,*,#2918,.F.);
#2918 = EDGE_CURVE('',#2919,#2891,#2921,.T.);
#2919 = VERTEX_POINT('',#2920);
#2920 = CARTESIAN_POINT('',(15.5,-24.,7.));
#2921 = SURFACE_CURVE('',#2922,(#2926,#2933),.PCURVE_S1.);
#2922 = LINE('',#2923,#2924);
#2923 = CARTESIAN_POINT('',(15.5,-49.,7.));
#2924 = VECTOR('',#2925,1.);
#2925 = DIRECTION('',(0.,1.,0.));
#2926 = PCURVE('',#562,#2927);
#2927 = DEFINITIONAL_REPRESENTATION('',(#2928),#2932);
#2928 = LINE('',#2929,#2930);
#2929 = CARTESIAN_POINT('',(15.5,5.));
#2930 = VECTOR('',#2931,1.);
#2931 = DIRECTION('',(0.,1.));
#2932 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2933 = PCURVE('',#2934,#2939);
#2934 = PLANE('',#2935);
#2935 = AXIS2_PLACEMENT_3D('',#2936,#2937,#2938);
#2936 = CARTESIAN_POINT('',(15.5,-49.,7.));
#2937 = DIRECTION('',(-1.,0.,0.));
#2938 = DIRECTION('',(0.,1.,0.));
#2939 = DEFINITIONAL_REPRESENTATION('',(#2940),#2944);
#2940 = LINE('',#2941,#2942);
#2941 = CARTESIAN_POINT('',(0.,0.));
#2942 = VECTOR('',#2943,1.);
#2943 = DIRECTION('',(1.,0.));
#2944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2945 = ORIENTED_EDGE('',*,*,#2946,.F.);
#2946 = EDGE_CURVE('',#2947,#2919,#2949,.T.);
#2947 = VERTEX_POINT('',#2948);
#2948 = CARTESIAN_POINT('',(31.5,-32.,7.));
#2949 = SURFACE_CURVE('',#2950,(#2955,#2962),.PCURVE_S1.);
#2950 = CIRCLE('',#2951,10.);
#2951 = AXIS2_PLACEMENT_3D('',#2952,#2953,#2954);
#2952 = CARTESIAN_POINT('',(21.5,-32.,7.));
#2953 = DIRECTION('',(0.,0.,1.));
#2954 = DIRECTION('',(1.,0.,0.));
#2955 = PCURVE('',#562,#2956);
#2956 = DEFINITIONAL_REPRESENTATION('',(#2957),#2961);
#2957 = CIRCLE('',#2958,10.);
#2958 = AXIS2_PLACEMENT_2D('',#2959,#2960);
#2959 = CARTESIAN_POINT('',(21.5,22.));
#2960 = DIRECTION('',(1.,0.));
#2961 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2962 = PCURVE('',#2963,#2968);
#2963 = CYLINDRICAL_SURFACE('',#2964,10.);
#2964 = AXIS2_PLACEMENT_3D('',#2965,#2966,#2967);
#2965 = CARTESIAN_POINT('',(21.5,-32.,22.));
#2966 = DIRECTION('',(0.,0.,1.));
#2967 = DIRECTION('',(1.,0.,0.));
#2968 = DEFINITIONAL_REPRESENTATION('',(#2969),#2973);
#2969 = LINE('',#2970,#2971);
#2970 = CARTESIAN_POINT('',(0.,-15.));
#2971 = VECTOR('',#2972,1.);
#2972 = DIRECTION('',(1.,0.));
#2973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2974 = ORIENTED_EDGE('',*,*,#2975,.F.);
#2975 = EDGE_CURVE('',#2976,#2947,#2978,.T.);
#2976 = VERTEX_POINT('',#2977);
#2977 = CARTESIAN_POINT('',(15.5,-40.,7.));
#2978 = SURFACE_CURVE('',#2979,(#2984,#2991),.PCURVE_S1.);
#2979 = CIRCLE('',#2980,10.);
#2980 = AXIS2_PLACEMENT_3D('',#2981,#2982,#2983);
#2981 = CARTESIAN_POINT('',(21.5,-32.,7.));
#2982 = DIRECTION('',(0.,0.,1.));
#2983 = DIRECTION('',(1.,0.,0.));
#2984 = PCURVE('',#562,#2985);
#2985 = DEFINITIONAL_REPRESENTATION('',(#2986),#2990);
#2986 = CIRCLE('',#2987,10.);
#2987 = AXIS2_PLACEMENT_2D('',#2988,#2989);
#2988 = CARTESIAN_POINT('',(21.5,22.));
#2989 = DIRECTION('',(1.,0.));
#2990 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2991 = PCURVE('',#2963,#2992);
#2992 = DEFINITIONAL_REPRESENTATION('',(#2993),#2997);
#2993 = LINE('',#2994,#2995);
#2994 = CARTESIAN_POINT('',(0.,-15.));
#2995 = VECTOR('',#2996,1.);
#2996 = DIRECTION('',(1.,0.));
#2997 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2998 = ORIENTED_EDGE('',*,*,#2999,.F.);
#2999 = EDGE_CURVE('',#3000,#2976,#3002,.T.);
#3000 = VERTEX_POINT('',#3001);
#3001 = CARTESIAN_POINT('',(15.5,-49.,7.));
#3002 = SURFACE_CURVE('',#3003,(#3007,#3014),.PCURVE_S1.);
#3003 = LINE('',#3004,#3005);
#3004 = CARTESIAN_POINT('',(15.5,-49.,7.));
#3005 = VECTOR('',#3006,1.);
#3006 = DIRECTION('',(0.,1.,0.));
#3007 = PCURVE('',#562,#3008);
#3008 = DEFINITIONAL_REPRESENTATION('',(#3009),#3013);
#3009 = LINE('',#3010,#3011);
#3010 = CARTESIAN_POINT('',(15.5,5.));
#3011 = VECTOR('',#3012,1.);
#3012 = DIRECTION('',(0.,1.));
#3013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3014 = PCURVE('',#3015,#3020);
#3015 = PLANE('',#3016);
#3016 = AXIS2_PLACEMENT_3D('',#3017,#3018,#3019);
#3017 = CARTESIAN_POINT('',(15.5,-49.,7.));
#3018 = DIRECTION('',(-1.,0.,0.));
#3019 = DIRECTION('',(0.,1.,0.));
#3020 = DEFINITIONAL_REPRESENTATION('',(#3021),#3025);
#3021 = LINE('',#3022,#3023);
#3022 = CARTESIAN_POINT('',(0.,0.));
#3023 = VECTOR('',#3024,1.);
#3024 = DIRECTION('',(1.,0.));
#3025 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3026 = ORIENTED_EDGE('',*,*,#3027,.F.);
#3027 = EDGE_CURVE('',#3028,#3000,#3030,.T.);
#3028 = VERTEX_POINT('',#3029);
#3029 = CARTESIAN_POINT('',(-15.5,-49.,7.));
#3030 = SURFACE_CURVE('',#3031,(#3035,#3042),.PCURVE_S1.);
#3031 = LINE('',#3032,#3033);
#3032 = CARTESIAN_POINT('',(-15.5,-49.,7.));
#3033 = VECTOR('',#3034,1.);
#3034 = DIRECTION('',(1.,0.,0.));
#3035 = PCURVE('',#562,#3036);
#3036 = DEFINITIONAL_REPRESENTATION('',(#3037),#3041);
#3037 = LINE('',#3038,#3039);
#3038 = CARTESIAN_POINT('',(-15.5,5.));
#3039 = VECTOR('',#3040,1.);
#3040 = DIRECTION('',(1.,0.));
#3041 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3042 = PCURVE('',#3043,#3048);
#3043 = PLANE('',#3044);
#3044 = AXIS2_PLACEMENT_3D('',#3045,#3046,#3047);
#3045 = CARTESIAN_POINT('',(-15.5,-49.,7.));
#3046 = DIRECTION('',(0.,1.,0.));
#3047 = DIRECTION('',(1.,0.,0.));
#3048 = DEFINITIONAL_REPRESENTATION('',(#3049),#3053);
#3049 = LINE('',#3050,#3051);
#3050 = CARTESIAN_POINT('',(0.,0.));
#3051 = VECTOR('',#3052,1.);
#3052 = DIRECTION('',(1.,0.));
#3053 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3054 = ORIENTED_EDGE('',*,*,#3055,.F.);
#3055 = EDGE_CURVE('',#2834,#3028,#3056,.T.);
#3056 = SURFACE_CURVE('',#3057,(#3061,#3068),.PCURVE_S1.);
#3057 = LINE('',#3058,#3059);
#3058 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#3059 = VECTOR('',#3060,1.);
#3060 = DIRECTION('',(0.,-1.,0.));
#3061 = PCURVE('',#562,#3062);
#3062 = DEFINITIONAL_REPRESENTATION('',(#3063),#3067);
#3063 = LINE('',#3064,#3065);
#3064 = CARTESIAN_POINT('',(-15.5,46.));
#3065 = VECTOR('',#3066,1.);
#3066 = DIRECTION('',(0.,-1.));
#3067 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3068 = PCURVE('',#3069,#3074);
#3069 = PLANE('',#3070);
#3070 = AXIS2_PLACEMENT_3D('',#3071,#3072,#3073);
#3071 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#3072 = DIRECTION('',(1.,0.,0.));
#3073 = DIRECTION('',(0.,-1.,0.));
#3074 = DEFINITIONAL_REPRESENTATION('',(#3075),#3079);
#3075 = LINE('',#3076,#3077);
#3076 = CARTESIAN_POINT('',(0.,0.));
#3077 = VECTOR('',#3078,1.);
#3078 = DIRECTION('',(1.,0.));
#3079 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3080 = FACE_BOUND('',#3081,.F.);
#3081 = EDGE_LOOP('',(#3082));
#3082 = ORIENTED_EDGE('',*,*,#3083,.T.);
#3083 = EDGE_CURVE('',#3084,#3084,#3086,.T.);
#3084 = VERTEX_POINT('',#3085);
#3085 = CARTESIAN_POINT('',(1.475,-3.273497,7.));
#3086 = SURFACE_CURVE('',#3087,(#3092,#3103),.PCURVE_S1.);
#3087 = CIRCLE('',#3088,1.475);
#3088 = AXIS2_PLACEMENT_3D('',#3089,#3090,#3091);
#3089 = CARTESIAN_POINT('',(0.,-3.273497,7.));
#3090 = DIRECTION('',(0.,0.,-1.));
#3091 = DIRECTION('',(1.,0.,0.));
#3092 = PCURVE('',#562,#3093);
#3093 = DEFINITIONAL_REPRESENTATION('',(#3094),#3102);
#3094 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3095,#3096,#3097,#3098,
#3099,#3100,#3101),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3095 = CARTESIAN_POINT('',(1.475,50.726503));
#3096 = CARTESIAN_POINT('',(1.475,48.171728058836));
#3097 = CARTESIAN_POINT('',(-0.7375,49.449115529418));
#3098 = CARTESIAN_POINT('',(-2.95,50.726503));
#3099 = CARTESIAN_POINT('',(-0.7375,52.003890470582));
#3100 = CARTESIAN_POINT('',(1.475,53.281277941164));
#3101 = CARTESIAN_POINT('',(1.475,50.726503));
#3102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3103 = PCURVE('',#3104,#3109);
#3104 = CYLINDRICAL_SURFACE('',#3105,1.475);
#3105 = AXIS2_PLACEMENT_3D('',#3106,#3107,#3108);
#3106 = CARTESIAN_POINT('',(0.,-3.273497,7.));
#3107 = DIRECTION('',(0.,0.,-1.));
#3108 = DIRECTION('',(1.,0.,0.));
#3109 = DEFINITIONAL_REPRESENTATION('',(#3110),#3114);
#3110 = LINE('',#3111,#3112);
#3111 = CARTESIAN_POINT('',(0.,0.));
#3112 = VECTOR('',#3113,1.);
#3113 = DIRECTION('',(1.,0.));
#3114 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3115 = FACE_BOUND('',#3116,.F.);
#3116 = EDGE_LOOP('',(#3117));
#3117 = ORIENTED_EDGE('',*,*,#3118,.F.);
#3118 = EDGE_CURVE('',#3119,#3119,#3121,.T.);
#3119 = VERTEX_POINT('',#3120);
#3120 = CARTESIAN_POINT('',(43.5,-71.,7.));
#3121 = SURFACE_CURVE('',#3122,(#3127,#3134),.PCURVE_S1.);
#3122 = CIRCLE('',#3123,11.);
#3123 = AXIS2_PLACEMENT_3D('',#3124,#3125,#3126);
#3124 = CARTESIAN_POINT('',(32.5,-71.,7.));
#3125 = DIRECTION('',(0.,0.,1.));
#3126 = DIRECTION('',(1.,0.,0.));
#3127 = PCURVE('',#562,#3128);
#3128 = DEFINITIONAL_REPRESENTATION('',(#3129),#3133);
#3129 = CIRCLE('',#3130,11.);
#3130 = AXIS2_PLACEMENT_2D('',#3131,#3132);
#3131 = CARTESIAN_POINT('',(32.5,-17.));
#3132 = DIRECTION('',(1.,0.));
#3133 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3134 = PCURVE('',#3135,#3140);
#3135 = CYLINDRICAL_SURFACE('',#3136,11.);
#3136 = AXIS2_PLACEMENT_3D('',#3137,#3138,#3139);
#3137 = CARTESIAN_POINT('',(32.5,-71.,22.));
#3138 = DIRECTION('',(0.,0.,1.));
#3139 = DIRECTION('',(1.,0.,0.));
#3140 = DEFINITIONAL_REPRESENTATION('',(#3141),#3145);
#3141 = LINE('',#3142,#3143);
#3142 = CARTESIAN_POINT('',(0.,-15.));
#3143 = VECTOR('',#3144,1.);
#3144 = DIRECTION('',(1.,0.));
#3145 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3146 = FACE_BOUND('',#3147,.F.);
#3147 = EDGE_LOOP('',(#3148));
#3148 = ORIENTED_EDGE('',*,*,#3149,.F.);
#3149 = EDGE_CURVE('',#3150,#3150,#3152,.T.);
#3150 = VERTEX_POINT('',#3151);
#3151 = CARTESIAN_POINT('',(3.25,-54.,7.));
#3152 = SURFACE_CURVE('',#3153,(#3158,#3165),.PCURVE_S1.);
#3153 = CIRCLE('',#3154,3.25);
#3154 = AXIS2_PLACEMENT_3D('',#3155,#3156,#3157);
#3155 = CARTESIAN_POINT('',(0.,-54.,7.));
#3156 = DIRECTION('',(0.,0.,1.));
#3157 = DIRECTION('',(1.,0.,0.));
#3158 = PCURVE('',#562,#3159);
#3159 = DEFINITIONAL_REPRESENTATION('',(#3160),#3164);
#3160 = CIRCLE('',#3161,3.25);
#3161 = AXIS2_PLACEMENT_2D('',#3162,#3163);
#3162 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3163 = DIRECTION('',(1.,0.));
#3164 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3165 = PCURVE('',#3166,#3171);
#3166 = CYLINDRICAL_SURFACE('',#3167,3.25);
#3167 = AXIS2_PLACEMENT_3D('',#3168,#3169,#3170);
#3168 = CARTESIAN_POINT('',(0.,-54.,7.));
#3169 = DIRECTION('',(-0.,-0.,-1.));
#3170 = DIRECTION('',(1.,0.,0.));
#3171 = DEFINITIONAL_REPRESENTATION('',(#3172),#3176);
#3172 = LINE('',#3173,#3174);
#3173 = CARTESIAN_POINT('',(-0.,0.));
#3174 = VECTOR('',#3175,1.);
#3175 = DIRECTION('',(-1.,0.));
#3176 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3177 = FACE_BOUND('',#3178,.F.);
#3178 = EDGE_LOOP('',(#3179));
#3179 = ORIENTED_EDGE('',*,*,#3180,.F.);
#3180 = EDGE_CURVE('',#3181,#3181,#3183,.T.);
#3181 = VERTEX_POINT('',#3182);
#3182 = CARTESIAN_POINT('',(-21.5,-71.,7.));
#3183 = SURFACE_CURVE('',#3184,(#3189,#3196),.PCURVE_S1.);
#3184 = CIRCLE('',#3185,11.);
#3185 = AXIS2_PLACEMENT_3D('',#3186,#3187,#3188);
#3186 = CARTESIAN_POINT('',(-32.5,-71.,7.));
#3187 = DIRECTION('',(0.,0.,1.));
#3188 = DIRECTION('',(1.,0.,0.));
#3189 = PCURVE('',#562,#3190);
#3190 = DEFINITIONAL_REPRESENTATION('',(#3191),#3195);
#3191 = CIRCLE('',#3192,11.);
#3192 = AXIS2_PLACEMENT_2D('',#3193,#3194);
#3193 = CARTESIAN_POINT('',(-32.5,-17.));
#3194 = DIRECTION('',(1.,0.));
#3195 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3196 = PCURVE('',#3197,#3202);
#3197 = CYLINDRICAL_SURFACE('',#3198,11.);
#3198 = AXIS2_PLACEMENT_3D('',#3199,#3200,#3201);
#3199 = CARTESIAN_POINT('',(-32.5,-71.,22.));
#3200 = DIRECTION('',(0.,0.,1.));
#3201 = DIRECTION('',(1.,0.,0.));
#3202 = DEFINITIONAL_REPRESENTATION('',(#3203),#3207);
#3203 = LINE('',#3204,#3205);
#3204 = CARTESIAN_POINT('',(0.,-15.));
#3205 = VECTOR('',#3206,1.);
#3206 = DIRECTION('',(1.,0.));
#3207 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3208 = FACE_BOUND('',#3209,.F.);
#3209 = EDGE_LOOP('',(#3210));
#3210 = ORIENTED_EDGE('',*,*,#3211,.T.);
#3211 = EDGE_CURVE('',#3212,#3212,#3214,.T.);
#3212 = VERTEX_POINT('',#3213);
#3213 = CARTESIAN_POINT('',(-30.606276,-93.287605,7.));
#3214 = SURFACE_CURVE('',#3215,(#3220,#3231),.PCURVE_S1.);
#3215 = CIRCLE('',#3216,1.475);
#3216 = AXIS2_PLACEMENT_3D('',#3217,#3218,#3219);
#3217 = CARTESIAN_POINT('',(-32.081276,-93.287605,7.));
#3218 = DIRECTION('',(0.,0.,-1.));
#3219 = DIRECTION('',(1.,0.,0.));
#3220 = PCURVE('',#562,#3221);
#3221 = DEFINITIONAL_REPRESENTATION('',(#3222),#3230);
#3222 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3223,#3224,#3225,#3226,
#3227,#3228,#3229),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3223 = CARTESIAN_POINT('',(-30.606276,-39.287605));
#3224 = CARTESIAN_POINT('',(-30.606276,-41.84237994116));
#3225 = CARTESIAN_POINT('',(-32.818776,-40.56499247058));
#3226 = CARTESIAN_POINT('',(-35.031276,-39.287605));
#3227 = CARTESIAN_POINT('',(-32.818776,-38.01021752941));
#3228 = CARTESIAN_POINT('',(-30.606276,-36.73283005883));
#3229 = CARTESIAN_POINT('',(-30.606276,-39.287605));
#3230 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3231 = PCURVE('',#3232,#3237);
#3232 = CYLINDRICAL_SURFACE('',#3233,1.475);
#3233 = AXIS2_PLACEMENT_3D('',#3234,#3235,#3236);
#3234 = CARTESIAN_POINT('',(-32.081276,-93.287605,7.));
#3235 = DIRECTION('',(0.,0.,-1.));
#3236 = DIRECTION('',(1.,0.,0.));
#3237 = DEFINITIONAL_REPRESENTATION('',(#3238),#3242);
#3238 = LINE('',#3239,#3240);
#3239 = CARTESIAN_POINT('',(0.,0.));
#3240 = VECTOR('',#3241,1.);
#3241 = DIRECTION('',(1.,0.));
#3242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3243 = FACE_BOUND('',#3244,.F.);
#3244 = EDGE_LOOP('',(#3245));
#3245 = ORIENTED_EDGE('',*,*,#3246,.T.);
#3246 = EDGE_CURVE('',#3247,#3247,#3249,.T.);
#3247 = VERTEX_POINT('',#3248);
#3248 = CARTESIAN_POINT('',(33.556276,-93.287605,7.));
#3249 = SURFACE_CURVE('',#3250,(#3255,#3266),.PCURVE_S1.);
#3250 = CIRCLE('',#3251,1.475);
#3251 = AXIS2_PLACEMENT_3D('',#3252,#3253,#3254);
#3252 = CARTESIAN_POINT('',(32.081276,-93.287605,7.));
#3253 = DIRECTION('',(0.,0.,-1.));
#3254 = DIRECTION('',(1.,0.,0.));
#3255 = PCURVE('',#562,#3256);
#3256 = DEFINITIONAL_REPRESENTATION('',(#3257),#3265);
#3257 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3258,#3259,#3260,#3261,
#3262,#3263,#3264),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3258 = CARTESIAN_POINT('',(33.556276,-39.287605));
#3259 = CARTESIAN_POINT('',(33.556276,-41.84237994116));
#3260 = CARTESIAN_POINT('',(31.343776,-40.56499247058));
#3261 = CARTESIAN_POINT('',(29.131276,-39.287605));
#3262 = CARTESIAN_POINT('',(31.343776,-38.01021752941));
#3263 = CARTESIAN_POINT('',(33.556276,-36.73283005883));
#3264 = CARTESIAN_POINT('',(33.556276,-39.287605));
#3265 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3266 = PCURVE('',#3267,#3272);
#3267 = CYLINDRICAL_SURFACE('',#3268,1.475);
#3268 = AXIS2_PLACEMENT_3D('',#3269,#3270,#3271);
#3269 = CARTESIAN_POINT('',(32.081276,-93.287605,7.));
#3270 = DIRECTION('',(0.,0.,-1.));
#3271 = DIRECTION('',(1.,0.,0.));
#3272 = DEFINITIONAL_REPRESENTATION('',(#3273),#3277);
#3273 = LINE('',#3274,#3275);
#3274 = CARTESIAN_POINT('',(0.,0.));
#3275 = VECTOR('',#3276,1.);
#3276 = DIRECTION('',(1.,0.));
#3277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3278 = FACE_BOUND('',#3279,.F.);
#3279 = EDGE_LOOP('',(#3280));
#3280 = ORIENTED_EDGE('',*,*,#3281,.T.);
#3281 = EDGE_CURVE('',#3282,#3282,#3284,.T.);
#3282 = VERTEX_POINT('',#3283);
#3283 = CARTESIAN_POINT('',(-45.328799,-54.,7.));
#3284 = SURFACE_CURVE('',#3285,(#3290,#3301),.PCURVE_S1.);
#3285 = CIRCLE('',#3286,1.475);
#3286 = AXIS2_PLACEMENT_3D('',#3287,#3288,#3289);
#3287 = CARTESIAN_POINT('',(-46.803799,-54.,7.));
#3288 = DIRECTION('',(0.,0.,-1.));
#3289 = DIRECTION('',(1.,0.,0.));
#3290 = PCURVE('',#562,#3291);
#3291 = DEFINITIONAL_REPRESENTATION('',(#3292),#3300);
#3292 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3293,#3294,#3295,#3296,
#3297,#3298,#3299),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3293 = CARTESIAN_POINT('',(-45.328799,-4.2E-15));
#3294 = CARTESIAN_POINT('',(-45.328799,-2.554774941164));
#3295 = CARTESIAN_POINT('',(-47.541299,-1.277387470582));
#3296 = CARTESIAN_POINT('',(-49.753799,-4.561270805748E-15));
#3297 = CARTESIAN_POINT('',(-47.541299,1.277387470582));
#3298 = CARTESIAN_POINT('',(-45.328799,2.554774941164));
#3299 = CARTESIAN_POINT('',(-45.328799,-4.2E-15));
#3300 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3301 = PCURVE('',#3302,#3307);
#3302 = CYLINDRICAL_SURFACE('',#3303,1.475);
#3303 = AXIS2_PLACEMENT_3D('',#3304,#3305,#3306);
#3304 = CARTESIAN_POINT('',(-46.803799,-54.,7.));
#3305 = DIRECTION('',(0.,0.,-1.));
#3306 = DIRECTION('',(1.,0.,0.));
#3307 = DEFINITIONAL_REPRESENTATION('',(#3308),#3312);
#3308 = LINE('',#3309,#3310);
#3309 = CARTESIAN_POINT('',(0.,0.));
#3310 = VECTOR('',#3311,1.);
#3311 = DIRECTION('',(1.,0.));
#3312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3313 = FACE_BOUND('',#3314,.F.);
#3314 = EDGE_LOOP('',(#3315));
#3315 = ORIENTED_EDGE('',*,*,#3316,.T.);
#3316 = EDGE_CURVE('',#3317,#3317,#3319,.T.);
#3317 = VERTEX_POINT('',#3318);
#3318 = CARTESIAN_POINT('',(48.278799,-54.,7.));
#3319 = SURFACE_CURVE('',#3320,(#3325,#3336),.PCURVE_S1.);
#3320 = CIRCLE('',#3321,1.475);
#3321 = AXIS2_PLACEMENT_3D('',#3322,#3323,#3324);
#3322 = CARTESIAN_POINT('',(46.803799,-54.,7.));
#3323 = DIRECTION('',(0.,0.,-1.));
#3324 = DIRECTION('',(1.,0.,0.));
#3325 = PCURVE('',#562,#3326);
#3326 = DEFINITIONAL_REPRESENTATION('',(#3327),#3335);
#3327 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#3328,#3329,#3330,#3331,
#3332,#3333,#3334),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#3328 = CARTESIAN_POINT('',(48.278799,-4.2E-15));
#3329 = CARTESIAN_POINT('',(48.278799,-2.554774941164));
#3330 = CARTESIAN_POINT('',(46.066299,-1.277387470582));
#3331 = CARTESIAN_POINT('',(43.853799,-4.561270805748E-15));
#3332 = CARTESIAN_POINT('',(46.066299,1.277387470582));
#3333 = CARTESIAN_POINT('',(48.278799,2.554774941164));
#3334 = CARTESIAN_POINT('',(48.278799,-4.2E-15));
#3335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3336 = PCURVE('',#3337,#3342);
#3337 = CYLINDRICAL_SURFACE('',#3338,1.475);
#3338 = AXIS2_PLACEMENT_3D('',#3339,#3340,#3341);
#3339 = CARTESIAN_POINT('',(46.803799,-54.,7.));
#3340 = DIRECTION('',(0.,0.,-1.));
#3341 = DIRECTION('',(1.,0.,0.));
#3342 = DEFINITIONAL_REPRESENTATION('',(#3343),#3347);
#3343 = LINE('',#3344,#3345);
#3344 = CARTESIAN_POINT('',(0.,0.));
#3345 = VECTOR('',#3346,1.);
#3346 = DIRECTION('',(1.,0.));
#3347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3348 = ADVANCED_FACE('',(#3349,#3492,#3518,#3549,#3575,#3601,#3632,
    #3663,#3689),#649,.T.);
#3349 = FACE_BOUND('',#3350,.T.);
#3350 = EDGE_LOOP('',(#3351,#3374,#3375,#3376,#3400,#3423,#3447,#3470));
#3351 = ORIENTED_EDGE('',*,*,#3352,.T.);
#3352 = EDGE_CURVE('',#3353,#607,#3355,.T.);
#3353 = VERTEX_POINT('',#3354);
#3354 = CARTESIAN_POINT('',(51.,-73.21587885057,22.));
#3355 = SURFACE_CURVE('',#3356,(#3360,#3367),.PCURVE_S1.);
#3356 = LINE('',#3357,#3358);
#3357 = CARTESIAN_POINT('',(51.,-104.,22.));
#3358 = VECTOR('',#3359,1.);
#3359 = DIRECTION('',(0.,1.,0.));
#3360 = PCURVE('',#649,#3361);
#3361 = DEFINITIONAL_REPRESENTATION('',(#3362),#3366);
#3362 = LINE('',#3363,#3364);
#3363 = CARTESIAN_POINT('',(51.,-50.));
#3364 = VECTOR('',#3365,1.);
#3365 = DIRECTION('',(0.,1.));
#3366 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3367 = PCURVE('',#622,#3368);
#3368 = DEFINITIONAL_REPRESENTATION('',(#3369),#3373);
#3369 = LINE('',#3370,#3371);
#3370 = CARTESIAN_POINT('',(0.,0.));
#3371 = VECTOR('',#3372,1.);
#3372 = DIRECTION('',(1.,0.));
#3373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3374 = ORIENTED_EDGE('',*,*,#634,.T.);
#3375 = ORIENTED_EDGE('',*,*,#666,.T.);
#3376 = ORIENTED_EDGE('',*,*,#3377,.T.);
#3377 = EDGE_CURVE('',#667,#3378,#3380,.T.);
#3378 = VERTEX_POINT('',#3379);
#3379 = CARTESIAN_POINT('',(-51.,-34.78412114942,22.));
#3380 = SURFACE_CURVE('',#3381,(#3386,#3393),.PCURVE_S1.);
#3381 = CIRCLE('',#3382,54.5);
#3382 = AXIS2_PLACEMENT_3D('',#3383,#3384,#3385);
#3383 = CARTESIAN_POINT('',(0.,-54.,22.));
#3384 = DIRECTION('',(0.,0.,1.));
#3385 = DIRECTION('',(1.,0.,0.));
#3386 = PCURVE('',#649,#3387);
#3387 = DEFINITIONAL_REPRESENTATION('',(#3388),#3392);
#3388 = CIRCLE('',#3389,54.5);
#3389 = AXIS2_PLACEMENT_2D('',#3390,#3391);
#3390 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3391 = DIRECTION('',(1.,0.));
#3392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3393 = PCURVE('',#2268,#3394);
#3394 = DEFINITIONAL_REPRESENTATION('',(#3395),#3399);
#3395 = LINE('',#3396,#3397);
#3396 = CARTESIAN_POINT('',(-0.,-15.));
#3397 = VECTOR('',#3398,1.);
#3398 = DIRECTION('',(-1.,0.));
#3399 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3400 = ORIENTED_EDGE('',*,*,#3401,.T.);
#3401 = EDGE_CURVE('',#3378,#3402,#3404,.T.);
#3402 = VERTEX_POINT('',#3403);
#3403 = CARTESIAN_POINT('',(-51.,-73.21587885057,22.));
#3404 = SURFACE_CURVE('',#3405,(#3409,#3416),.PCURVE_S1.);
#3405 = LINE('',#3406,#3407);
#3406 = CARTESIAN_POINT('',(-51.,-4.,22.));
#3407 = VECTOR('',#3408,1.);
#3408 = DIRECTION('',(0.,-1.,0.));
#3409 = PCURVE('',#649,#3410);
#3410 = DEFINITIONAL_REPRESENTATION('',(#3411),#3415);
#3411 = LINE('',#3412,#3413);
#3412 = CARTESIAN_POINT('',(-51.,50.));
#3413 = VECTOR('',#3414,1.);
#3414 = DIRECTION('',(0.,-1.));
#3415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3416 = PCURVE('',#2683,#3417);
#3417 = DEFINITIONAL_REPRESENTATION('',(#3418),#3422);
#3418 = LINE('',#3419,#3420);
#3419 = CARTESIAN_POINT('',(0.,0.));
#3420 = VECTOR('',#3421,1.);
#3421 = DIRECTION('',(1.,0.));
#3422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3423 = ORIENTED_EDGE('',*,*,#3424,.T.);
#3424 = EDGE_CURVE('',#3402,#3425,#3427,.T.);
#3425 = VERTEX_POINT('',#3426);
#3426 = CARTESIAN_POINT('',(-21.68524844219,-104.,22.));
#3427 = SURFACE_CURVE('',#3428,(#3433,#3440),.PCURVE_S1.);
#3428 = CIRCLE('',#3429,54.5);
#3429 = AXIS2_PLACEMENT_3D('',#3430,#3431,#3432);
#3430 = CARTESIAN_POINT('',(0.,-54.,22.));
#3431 = DIRECTION('',(0.,0.,1.));
#3432 = DIRECTION('',(1.,0.,0.));
#3433 = PCURVE('',#649,#3434);
#3434 = DEFINITIONAL_REPRESENTATION('',(#3435),#3439);
#3435 = CIRCLE('',#3436,54.5);
#3436 = AXIS2_PLACEMENT_2D('',#3437,#3438);
#3437 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3438 = DIRECTION('',(1.,0.));
#3439 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3440 = PCURVE('',#2712,#3441);
#3441 = DEFINITIONAL_REPRESENTATION('',(#3442),#3446);
#3442 = LINE('',#3443,#3444);
#3443 = CARTESIAN_POINT('',(-0.,-15.));
#3444 = VECTOR('',#3445,1.);
#3445 = DIRECTION('',(-1.,0.));
#3446 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3447 = ORIENTED_EDGE('',*,*,#3448,.T.);
#3448 = EDGE_CURVE('',#3425,#3449,#3451,.T.);
#3449 = VERTEX_POINT('',#3450);
#3450 = CARTESIAN_POINT('',(21.685248442202,-104.,22.));
#3451 = SURFACE_CURVE('',#3452,(#3456,#3463),.PCURVE_S1.);
#3452 = LINE('',#3453,#3454);
#3453 = CARTESIAN_POINT('',(-45.,-104.,22.));
#3454 = VECTOR('',#3455,1.);
#3455 = DIRECTION('',(1.,0.,0.));
#3456 = PCURVE('',#649,#3457);
#3457 = DEFINITIONAL_REPRESENTATION('',(#3458),#3462);
#3458 = LINE('',#3459,#3460);
#3459 = CARTESIAN_POINT('',(-45.,-50.));
#3460 = VECTOR('',#3461,1.);
#3461 = DIRECTION('',(1.,0.));
#3462 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3463 = PCURVE('',#2578,#3464);
#3464 = DEFINITIONAL_REPRESENTATION('',(#3465),#3469);
#3465 = LINE('',#3466,#3467);
#3466 = CARTESIAN_POINT('',(0.,0.));
#3467 = VECTOR('',#3468,1.);
#3468 = DIRECTION('',(1.,0.));
#3469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3470 = ORIENTED_EDGE('',*,*,#3471,.T.);
#3471 = EDGE_CURVE('',#3449,#3353,#3472,.T.);
#3472 = SURFACE_CURVE('',#3473,(#3478,#3485),.PCURVE_S1.);
#3473 = CIRCLE('',#3474,54.5);
#3474 = AXIS2_PLACEMENT_3D('',#3475,#3476,#3477);
#3475 = CARTESIAN_POINT('',(0.,-54.,22.));
#3476 = DIRECTION('',(0.,0.,1.));
#3477 = DIRECTION('',(1.,0.,0.));
#3478 = PCURVE('',#649,#3479);
#3479 = DEFINITIONAL_REPRESENTATION('',(#3480),#3484);
#3480 = CIRCLE('',#3481,54.5);
#3481 = AXIS2_PLACEMENT_2D('',#3482,#3483);
#3482 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3483 = DIRECTION('',(1.,0.));
#3484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3485 = PCURVE('',#2607,#3486);
#3486 = DEFINITIONAL_REPRESENTATION('',(#3487),#3491);
#3487 = LINE('',#3488,#3489);
#3488 = CARTESIAN_POINT('',(-0.,-15.));
#3489 = VECTOR('',#3490,1.);
#3490 = DIRECTION('',(-1.,0.));
#3491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3492 = FACE_BOUND('',#3493,.T.);
#3493 = EDGE_LOOP('',(#3494));
#3494 = ORIENTED_EDGE('',*,*,#3495,.F.);
#3495 = EDGE_CURVE('',#3496,#3496,#3498,.T.);
#3496 = VERTEX_POINT('',#3497);
#3497 = CARTESIAN_POINT('',(43.5,-71.,22.));
#3498 = SURFACE_CURVE('',#3499,(#3504,#3511),.PCURVE_S1.);
#3499 = CIRCLE('',#3500,11.);
#3500 = AXIS2_PLACEMENT_3D('',#3501,#3502,#3503);
#3501 = CARTESIAN_POINT('',(32.5,-71.,22.));
#3502 = DIRECTION('',(0.,0.,1.));
#3503 = DIRECTION('',(1.,0.,0.));
#3504 = PCURVE('',#649,#3505);
#3505 = DEFINITIONAL_REPRESENTATION('',(#3506),#3510);
#3506 = CIRCLE('',#3507,11.);
#3507 = AXIS2_PLACEMENT_2D('',#3508,#3509);
#3508 = CARTESIAN_POINT('',(32.5,-17.));
#3509 = DIRECTION('',(1.,0.));
#3510 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3511 = PCURVE('',#3135,#3512);
#3512 = DEFINITIONAL_REPRESENTATION('',(#3513),#3517);
#3513 = LINE('',#3514,#3515);
#3514 = CARTESIAN_POINT('',(0.,0.));
#3515 = VECTOR('',#3516,1.);
#3516 = DIRECTION('',(1.,0.));
#3517 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3518 = FACE_BOUND('',#3519,.T.);
#3519 = EDGE_LOOP('',(#3520));
#3520 = ORIENTED_EDGE('',*,*,#3521,.F.);
#3521 = EDGE_CURVE('',#3522,#3522,#3524,.T.);
#3522 = VERTEX_POINT('',#3523);
#3523 = CARTESIAN_POINT('',(-46.175,-54.,22.));
#3524 = SURFACE_CURVE('',#3525,(#3530,#3537),.PCURVE_S1.);
#3525 = CIRCLE('',#3526,1.825);
#3526 = AXIS2_PLACEMENT_3D('',#3527,#3528,#3529);
#3527 = CARTESIAN_POINT('',(-48.,-54.,22.));
#3528 = DIRECTION('',(0.,0.,1.));
#3529 = DIRECTION('',(1.,0.,0.));
#3530 = PCURVE('',#649,#3531);
#3531 = DEFINITIONAL_REPRESENTATION('',(#3532),#3536);
#3532 = CIRCLE('',#3533,1.825);
#3533 = AXIS2_PLACEMENT_2D('',#3534,#3535);
#3534 = CARTESIAN_POINT('',(-48.,-4.2E-15));
#3535 = DIRECTION('',(1.,0.));
#3536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3537 = PCURVE('',#3538,#3543);
#3538 = CYLINDRICAL_SURFACE('',#3539,1.825);
#3539 = AXIS2_PLACEMENT_3D('',#3540,#3541,#3542);
#3540 = CARTESIAN_POINT('',(-48.,-54.,22.));
#3541 = DIRECTION('',(-0.,-0.,-1.));
#3542 = DIRECTION('',(1.,0.,0.));
#3543 = DEFINITIONAL_REPRESENTATION('',(#3544),#3548);
#3544 = LINE('',#3545,#3546);
#3545 = CARTESIAN_POINT('',(-0.,0.));
#3546 = VECTOR('',#3547,1.);
#3547 = DIRECTION('',(-1.,0.));
#3548 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3549 = FACE_BOUND('',#3550,.T.);
#3550 = EDGE_LOOP('',(#3551));
#3551 = ORIENTED_EDGE('',*,*,#3552,.F.);
#3552 = EDGE_CURVE('',#3553,#3553,#3555,.T.);
#3553 = VERTEX_POINT('',#3554);
#3554 = CARTESIAN_POINT('',(3.25,-54.,22.));
#3555 = SURFACE_CURVE('',#3556,(#3561,#3568),.PCURVE_S1.);
#3556 = CIRCLE('',#3557,3.25);
#3557 = AXIS2_PLACEMENT_3D('',#3558,#3559,#3560);
#3558 = CARTESIAN_POINT('',(0.,-54.,22.));
#3559 = DIRECTION('',(0.,0.,1.));
#3560 = DIRECTION('',(1.,0.,0.));
#3561 = PCURVE('',#649,#3562);
#3562 = DEFINITIONAL_REPRESENTATION('',(#3563),#3567);
#3563 = CIRCLE('',#3564,3.25);
#3564 = AXIS2_PLACEMENT_2D('',#3565,#3566);
#3565 = CARTESIAN_POINT('',(3.21E-15,-4.2E-15));
#3566 = DIRECTION('',(1.,0.));
#3567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3568 = PCURVE('',#3166,#3569);
#3569 = DEFINITIONAL_REPRESENTATION('',(#3570),#3574);
#3570 = LINE('',#3571,#3572);
#3571 = CARTESIAN_POINT('',(-0.,-15.));
#3572 = VECTOR('',#3573,1.);
#3573 = DIRECTION('',(-1.,0.));
#3574 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3575 = FACE_BOUND('',#3576,.T.);
#3576 = EDGE_LOOP('',(#3577));
#3577 = ORIENTED_EDGE('',*,*,#3578,.F.);
#3578 = EDGE_CURVE('',#3579,#3579,#3581,.T.);
#3579 = VERTEX_POINT('',#3580);
#3580 = CARTESIAN_POINT('',(-21.5,-71.,22.));
#3581 = SURFACE_CURVE('',#3582,(#3587,#3594),.PCURVE_S1.);
#3582 = CIRCLE('',#3583,11.);
#3583 = AXIS2_PLACEMENT_3D('',#3584,#3585,#3586);
#3584 = CARTESIAN_POINT('',(-32.5,-71.,22.));
#3585 = DIRECTION('',(0.,0.,1.));
#3586 = DIRECTION('',(1.,0.,0.));
#3587 = PCURVE('',#649,#3588);
#3588 = DEFINITIONAL_REPRESENTATION('',(#3589),#3593);
#3589 = CIRCLE('',#3590,11.);
#3590 = AXIS2_PLACEMENT_2D('',#3591,#3592);
#3591 = CARTESIAN_POINT('',(-32.5,-17.));
#3592 = DIRECTION('',(1.,0.));
#3593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3594 = PCURVE('',#3197,#3595);
#3595 = DEFINITIONAL_REPRESENTATION('',(#3596),#3600);
#3596 = LINE('',#3597,#3598);
#3597 = CARTESIAN_POINT('',(0.,0.));
#3598 = VECTOR('',#3599,1.);
#3599 = DIRECTION('',(1.,0.));
#3600 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3601 = FACE_BOUND('',#3602,.T.);
#3602 = EDGE_LOOP('',(#3603));
#3603 = ORIENTED_EDGE('',*,*,#3604,.F.);
#3604 = EDGE_CURVE('',#3605,#3605,#3607,.T.);
#3605 = VERTEX_POINT('',#3606);
#3606 = CARTESIAN_POINT('',(11.,-84.,22.));
#3607 = SURFACE_CURVE('',#3608,(#3613,#3620),.PCURVE_S1.);
#3608 = CIRCLE('',#3609,11.);
#3609 = AXIS2_PLACEMENT_3D('',#3610,#3611,#3612);
#3610 = CARTESIAN_POINT('',(0.,-84.,22.));
#3611 = DIRECTION('',(0.,0.,1.));
#3612 = DIRECTION('',(1.,0.,0.));
#3613 = PCURVE('',#649,#3614);
#3614 = DEFINITIONAL_REPRESENTATION('',(#3615),#3619);
#3615 = CIRCLE('',#3616,11.);
#3616 = AXIS2_PLACEMENT_2D('',#3617,#3618);
#3617 = CARTESIAN_POINT('',(3.21E-15,-30.));
#3618 = DIRECTION('',(1.,0.));
#3619 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3620 = PCURVE('',#3621,#3626);
#3621 = CYLINDRICAL_SURFACE('',#3622,11.);
#3622 = AXIS2_PLACEMENT_3D('',#3623,#3624,#3625);
#3623 = CARTESIAN_POINT('',(0.,-84.,22.));
#3624 = DIRECTION('',(0.,0.,1.));
#3625 = DIRECTION('',(1.,0.,0.));
#3626 = DEFINITIONAL_REPRESENTATION('',(#3627),#3631);
#3627 = LINE('',#3628,#3629);
#3628 = CARTESIAN_POINT('',(0.,0.));
#3629 = VECTOR('',#3630,1.);
#3630 = DIRECTION('',(1.,0.));
#3631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3632 = FACE_BOUND('',#3633,.T.);
#3633 = EDGE_LOOP('',(#3634));
#3634 = ORIENTED_EDGE('',*,*,#3635,.F.);
#3635 = EDGE_CURVE('',#3636,#3636,#3638,.T.);
#3636 = VERTEX_POINT('',#3637);
#3637 = CARTESIAN_POINT('',(49.825,-54.,22.));
#3638 = SURFACE_CURVE('',#3639,(#3644,#3651),.PCURVE_S1.);
#3639 = CIRCLE('',#3640,1.825);
#3640 = AXIS2_PLACEMENT_3D('',#3641,#3642,#3643);
#3641 = CARTESIAN_POINT('',(48.,-54.,22.));
#3642 = DIRECTION('',(0.,0.,1.));
#3643 = DIRECTION('',(1.,0.,0.));
#3644 = PCURVE('',#649,#3645);
#3645 = DEFINITIONAL_REPRESENTATION('',(#3646),#3650);
#3646 = CIRCLE('',#3647,1.825);
#3647 = AXIS2_PLACEMENT_2D('',#3648,#3649);
#3648 = CARTESIAN_POINT('',(48.,-4.2E-15));
#3649 = DIRECTION('',(1.,0.));
#3650 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3651 = PCURVE('',#3652,#3657);
#3652 = CYLINDRICAL_SURFACE('',#3653,1.825);
#3653 = AXIS2_PLACEMENT_3D('',#3654,#3655,#3656);
#3654 = CARTESIAN_POINT('',(48.,-54.,22.));
#3655 = DIRECTION('',(-0.,-0.,-1.));
#3656 = DIRECTION('',(1.,0.,0.));
#3657 = DEFINITIONAL_REPRESENTATION('',(#3658),#3662);
#3658 = LINE('',#3659,#3660);
#3659 = CARTESIAN_POINT('',(-0.,0.));
#3660 = VECTOR('',#3661,1.);
#3661 = DIRECTION('',(-1.,0.));
#3662 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3663 = FACE_BOUND('',#3664,.T.);
#3664 = EDGE_LOOP('',(#3665));
#3665 = ORIENTED_EDGE('',*,*,#3666,.F.);
#3666 = EDGE_CURVE('',#3667,#3667,#3669,.T.);
#3667 = VERTEX_POINT('',#3668);
#3668 = CARTESIAN_POINT('',(31.5,-32.,22.));
#3669 = SURFACE_CURVE('',#3670,(#3675,#3682),.PCURVE_S1.);
#3670 = CIRCLE('',#3671,10.);
#3671 = AXIS2_PLACEMENT_3D('',#3672,#3673,#3674);
#3672 = CARTESIAN_POINT('',(21.5,-32.,22.));
#3673 = DIRECTION('',(0.,0.,1.));
#3674 = DIRECTION('',(1.,0.,0.));
#3675 = PCURVE('',#649,#3676);
#3676 = DEFINITIONAL_REPRESENTATION('',(#3677),#3681);
#3677 = CIRCLE('',#3678,10.);
#3678 = AXIS2_PLACEMENT_2D('',#3679,#3680);
#3679 = CARTESIAN_POINT('',(21.5,22.));
#3680 = DIRECTION('',(1.,0.));
#3681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3682 = PCURVE('',#2963,#3683);
#3683 = DEFINITIONAL_REPRESENTATION('',(#3684),#3688);
#3684 = LINE('',#3685,#3686);
#3685 = CARTESIAN_POINT('',(0.,0.));
#3686 = VECTOR('',#3687,1.);
#3687 = DIRECTION('',(1.,0.));
#3688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3689 = FACE_BOUND('',#3690,.T.);
#3690 = EDGE_LOOP('',(#3691));
#3691 = ORIENTED_EDGE('',*,*,#3692,.F.);
#3692 = EDGE_CURVE('',#3693,#3693,#3695,.T.);
#3693 = VERTEX_POINT('',#3694);
#3694 = CARTESIAN_POINT('',(-11.5,-32.,22.));
#3695 = SURFACE_CURVE('',#3696,(#3701,#3708),.PCURVE_S1.);
#3696 = CIRCLE('',#3697,10.);
#3697 = AXIS2_PLACEMENT_3D('',#3698,#3699,#3700);
#3698 = CARTESIAN_POINT('',(-21.5,-32.,22.));
#3699 = DIRECTION('',(0.,0.,1.));
#3700 = DIRECTION('',(1.,0.,0.));
#3701 = PCURVE('',#649,#3702);
#3702 = DEFINITIONAL_REPRESENTATION('',(#3703),#3707);
#3703 = CIRCLE('',#3704,10.);
#3704 = AXIS2_PLACEMENT_2D('',#3705,#3706);
#3705 = CARTESIAN_POINT('',(-21.5,22.));
#3706 = DIRECTION('',(1.,0.));
#3707 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3708 = PCURVE('',#2850,#3709);
#3709 = DEFINITIONAL_REPRESENTATION('',(#3710),#3714);
#3710 = LINE('',#3711,#3712);
#3711 = CARTESIAN_POINT('',(0.,0.));
#3712 = VECTOR('',#3713,1.);
#3713 = DIRECTION('',(1.,0.));
#3714 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3715 = ADVANCED_FACE('',(#3716),#622,.T.);
#3716 = FACE_BOUND('',#3717,.T.);
#3717 = EDGE_LOOP('',(#3718,#3719,#3740,#3741));
#3718 = ORIENTED_EDGE('',*,*,#3352,.F.);
#3719 = ORIENTED_EDGE('',*,*,#3720,.T.);
#3720 = EDGE_CURVE('',#3353,#2591,#3721,.T.);
#3721 = SURFACE_CURVE('',#3722,(#3726,#3733),.PCURVE_S1.);
#3722 = LINE('',#3723,#3724);
#3723 = CARTESIAN_POINT('',(51.,-73.21587885057,7.));
#3724 = VECTOR('',#3725,1.);
#3725 = DIRECTION('',(-0.,-0.,-1.));
#3726 = PCURVE('',#622,#3727);
#3727 = DEFINITIONAL_REPRESENTATION('',(#3728),#3732);
#3728 = LINE('',#3729,#3730);
#3729 = CARTESIAN_POINT('',(30.784121149424,-15.));
#3730 = VECTOR('',#3731,1.);
#3731 = DIRECTION('',(0.,-1.));
#3732 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3733 = PCURVE('',#2607,#3734);
#3734 = DEFINITIONAL_REPRESENTATION('',(#3735),#3739);
#3735 = LINE('',#3736,#3737);
#3736 = CARTESIAN_POINT('',(-5.922853305643,0.));
#3737 = VECTOR('',#3738,1.);
#3738 = DIRECTION('',(-0.,1.));
#3739 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3740 = ORIENTED_EDGE('',*,*,#2619,.T.);
#3741 = ORIENTED_EDGE('',*,*,#606,.F.);
#3742 = ADVANCED_FACE('',(#3743),#1313,.F.);
#3743 = FACE_BOUND('',#3744,.F.);
#3744 = EDGE_LOOP('',(#3745,#3746,#3769,#3796));
#3745 = ORIENTED_EDGE('',*,*,#1075,.F.);
#3746 = ORIENTED_EDGE('',*,*,#3747,.T.);
#3747 = EDGE_CURVE('',#1076,#3748,#3750,.T.);
#3748 = VERTEX_POINT('',#3749);
#3749 = CARTESIAN_POINT('',(12.5,4.,48.));
#3750 = SEAM_CURVE('',#3751,(#3755,#3762),.PCURVE_S1.);
#3751 = LINE('',#3752,#3753);
#3752 = CARTESIAN_POINT('',(12.5,-54.,48.));
#3753 = VECTOR('',#3754,1.);
#3754 = DIRECTION('',(0.,1.,2.2E-16));
#3755 = PCURVE('',#1313,#3756);
#3756 = DEFINITIONAL_REPRESENTATION('',(#3757),#3761);
#3757 = LINE('',#3758,#3759);
#3758 = CARTESIAN_POINT('',(6.28318530718,0.));
#3759 = VECTOR('',#3760,1.);
#3760 = DIRECTION('',(0.,-1.));
#3761 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3762 = PCURVE('',#1313,#3763);
#3763 = DEFINITIONAL_REPRESENTATION('',(#3764),#3768);
#3764 = LINE('',#3765,#3766);
#3765 = CARTESIAN_POINT('',(0.,0.));
#3766 = VECTOR('',#3767,1.);
#3767 = DIRECTION('',(0.,-1.));
#3768 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3769 = ORIENTED_EDGE('',*,*,#3770,.T.);
#3770 = EDGE_CURVE('',#3748,#3748,#3771,.T.);
#3771 = SURFACE_CURVE('',#3772,(#3777,#3784),.PCURVE_S1.);
#3772 = CIRCLE('',#3773,12.5);
#3773 = AXIS2_PLACEMENT_3D('',#3774,#3775,#3776);
#3774 = CARTESIAN_POINT('',(0.,4.,48.));
#3775 = DIRECTION('',(0.,-1.,0.));
#3776 = DIRECTION('',(1.,0.,0.));
#3777 = PCURVE('',#1313,#3778);
#3778 = DEFINITIONAL_REPRESENTATION('',(#3779),#3783);
#3779 = LINE('',#3780,#3781);
#3780 = CARTESIAN_POINT('',(0.,-58.));
#3781 = VECTOR('',#3782,1.);
#3782 = DIRECTION('',(1.,0.));
#3783 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3784 = PCURVE('',#3785,#3790);
#3785 = PLANE('',#3786);
#3786 = AXIS2_PLACEMENT_3D('',#3787,#3788,#3789);
#3787 = CARTESIAN_POINT('',(-1.31E-15,4.,48.));
#3788 = DIRECTION('',(0.,-1.,-2.2E-16));
#3789 = DIRECTION('',(0.,2.2E-16,-1.));
#3790 = DEFINITIONAL_REPRESENTATION('',(#3791),#3795);
#3791 = CIRCLE('',#3792,12.5);
#3792 = AXIS2_PLACEMENT_2D('',#3793,#3794);
#3793 = CARTESIAN_POINT('',(7.11E-15,1.31E-15));
#3794 = DIRECTION('',(0.,1.));
#3795 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3796 = ORIENTED_EDGE('',*,*,#3747,.F.);
#3797 = ADVANCED_FACE('',(#3798),#1596,.F.);
#3798 = FACE_BOUND('',#3799,.F.);
#3799 = EDGE_LOOP('',(#3800,#3801,#3824,#3846));
#3800 = ORIENTED_EDGE('',*,*,#1438,.F.);
#3801 = ORIENTED_EDGE('',*,*,#3802,.T.);
#3802 = EDGE_CURVE('',#1439,#3803,#3805,.T.);
#3803 = VERTEX_POINT('',#3804);
#3804 = CARTESIAN_POINT('',(20.320508075689,4.,38.));
#3805 = SEAM_CURVE('',#3806,(#3810,#3817),.PCURVE_S1.);
#3806 = LINE('',#3807,#3808);
#3807 = CARTESIAN_POINT('',(20.320508075689,-54.,38.));
#3808 = VECTOR('',#3809,1.);
#3809 = DIRECTION('',(0.,1.,2.2E-16));
#3810 = PCURVE('',#1596,#3811);
#3811 = DEFINITIONAL_REPRESENTATION('',(#3812),#3816);
#3812 = LINE('',#3813,#3814);
#3813 = CARTESIAN_POINT('',(6.28318530718,0.));
#3814 = VECTOR('',#3815,1.);
#3815 = DIRECTION('',(0.,-1.));
#3816 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3817 = PCURVE('',#1596,#3818);
#3818 = DEFINITIONAL_REPRESENTATION('',(#3819),#3823);
#3819 = LINE('',#3820,#3821);
#3820 = CARTESIAN_POINT('',(0.,0.));
#3821 = VECTOR('',#3822,1.);
#3822 = DIRECTION('',(0.,-1.));
#3823 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3824 = ORIENTED_EDGE('',*,*,#3825,.T.);
#3825 = EDGE_CURVE('',#3803,#3803,#3826,.T.);
#3826 = SURFACE_CURVE('',#3827,(#3832,#3839),.PCURVE_S1.);
#3827 = CIRCLE('',#3828,3.);
#3828 = AXIS2_PLACEMENT_3D('',#3829,#3830,#3831);
#3829 = CARTESIAN_POINT('',(17.320508075689,4.,38.));
#3830 = DIRECTION('',(0.,-1.,0.));
#3831 = DIRECTION('',(1.,0.,0.));
#3832 = PCURVE('',#1596,#3833);
#3833 = DEFINITIONAL_REPRESENTATION('',(#3834),#3838);
#3834 = LINE('',#3835,#3836);
#3835 = CARTESIAN_POINT('',(0.,-58.));
#3836 = VECTOR('',#3837,1.);
#3837 = DIRECTION('',(1.,0.));
#3838 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3839 = PCURVE('',#2322,#3840);
#3840 = DEFINITIONAL_REPRESENTATION('',(#3841),#3845);
#3841 = CIRCLE('',#3842,3.);
#3842 = AXIS2_PLACEMENT_2D('',#3843,#3844);
#3843 = CARTESIAN_POINT('',(3.55E-15,0.));
#3844 = DIRECTION('',(0.,1.));
#3845 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3846 = ORIENTED_EDGE('',*,*,#3802,.F.);
#3847 = ADVANCED_FACE('',(#3848),#1839,.F.);
#3848 = FACE_BOUND('',#3849,.F.);
#3849 = EDGE_LOOP('',(#3850,#3851,#3874,#3896));
#3850 = ORIENTED_EDGE('',*,*,#1681,.F.);
#3851 = ORIENTED_EDGE('',*,*,#3852,.T.);
#3852 = EDGE_CURVE('',#1682,#3853,#3855,.T.);
#3853 = VERTEX_POINT('',#3854);
#3854 = CARTESIAN_POINT('',(-14.32050807568,4.,38.));
#3855 = SEAM_CURVE('',#3856,(#3860,#3867),.PCURVE_S1.);
#3856 = LINE('',#3857,#3858);
#3857 = CARTESIAN_POINT('',(-14.32050807568,-54.,38.));
#3858 = VECTOR('',#3859,1.);
#3859 = DIRECTION('',(0.,1.,2.2E-16));
#3860 = PCURVE('',#1839,#3861);
#3861 = DEFINITIONAL_REPRESENTATION('',(#3862),#3866);
#3862 = LINE('',#3863,#3864);
#3863 = CARTESIAN_POINT('',(6.28318530718,0.));
#3864 = VECTOR('',#3865,1.);
#3865 = DIRECTION('',(0.,-1.));
#3866 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3867 = PCURVE('',#1839,#3868);
#3868 = DEFINITIONAL_REPRESENTATION('',(#3869),#3873);
#3869 = LINE('',#3870,#3871);
#3870 = CARTESIAN_POINT('',(0.,0.));
#3871 = VECTOR('',#3872,1.);
#3872 = DIRECTION('',(0.,-1.));
#3873 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3874 = ORIENTED_EDGE('',*,*,#3875,.T.);
#3875 = EDGE_CURVE('',#3853,#3853,#3876,.T.);
#3876 = SURFACE_CURVE('',#3877,(#3882,#3889),.PCURVE_S1.);
#3877 = CIRCLE('',#3878,3.);
#3878 = AXIS2_PLACEMENT_3D('',#3879,#3880,#3881);
#3879 = CARTESIAN_POINT('',(-17.32050807568,4.,38.));
#3880 = DIRECTION('',(0.,-1.,0.));
#3881 = DIRECTION('',(1.,0.,0.));
#3882 = PCURVE('',#1839,#3883);
#3883 = DEFINITIONAL_REPRESENTATION('',(#3884),#3888);
#3884 = LINE('',#3885,#3886);
#3885 = CARTESIAN_POINT('',(0.,-58.));
#3886 = VECTOR('',#3887,1.);
#3887 = DIRECTION('',(1.,0.));
#3888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3889 = PCURVE('',#2487,#3890);
#3890 = DEFINITIONAL_REPRESENTATION('',(#3891),#3895);
#3891 = CIRCLE('',#3892,3.);
#3892 = AXIS2_PLACEMENT_2D('',#3893,#3894);
#3893 = CARTESIAN_POINT('',(-3.55E-15,0.));
#3894 = DIRECTION('',(0.,1.));
#3895 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3896 = ORIENTED_EDGE('',*,*,#3852,.F.);
#3897 = ADVANCED_FACE('',(#3898),#2082,.F.);
#3898 = FACE_BOUND('',#3899,.F.);
#3899 = EDGE_LOOP('',(#3900,#3901,#3924,#3946));
#3900 = ORIENTED_EDGE('',*,*,#1924,.F.);
#3901 = ORIENTED_EDGE('',*,*,#3902,.T.);
#3902 = EDGE_CURVE('',#1925,#3903,#3905,.T.);
#3903 = VERTEX_POINT('',#3904);
#3904 = CARTESIAN_POINT('',(3.,4.,68.));
#3905 = SEAM_CURVE('',#3906,(#3910,#3917),.PCURVE_S1.);
#3906 = LINE('',#3907,#3908);
#3907 = CARTESIAN_POINT('',(3.,-54.,68.));
#3908 = VECTOR('',#3909,1.);
#3909 = DIRECTION('',(0.,1.,2.2E-16));
#3910 = PCURVE('',#2082,#3911);
#3911 = DEFINITIONAL_REPRESENTATION('',(#3912),#3916);
#3912 = LINE('',#3913,#3914);
#3913 = CARTESIAN_POINT('',(6.28318530718,0.));
#3914 = VECTOR('',#3915,1.);
#3915 = DIRECTION('',(0.,-1.));
#3916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3917 = PCURVE('',#2082,#3918);
#3918 = DEFINITIONAL_REPRESENTATION('',(#3919),#3923);
#3919 = LINE('',#3920,#3921);
#3920 = CARTESIAN_POINT('',(0.,0.));
#3921 = VECTOR('',#3922,1.);
#3922 = DIRECTION('',(0.,-1.));
#3923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3924 = ORIENTED_EDGE('',*,*,#3925,.T.);
#3925 = EDGE_CURVE('',#3903,#3903,#3926,.T.);
#3926 = SURFACE_CURVE('',#3927,(#3932,#3939),.PCURVE_S1.);
#3927 = CIRCLE('',#3928,3.);
#3928 = AXIS2_PLACEMENT_3D('',#3929,#3930,#3931);
#3929 = CARTESIAN_POINT('',(0.,4.,68.));
#3930 = DIRECTION('',(0.,-1.,0.));
#3931 = DIRECTION('',(1.,0.,0.));
#3932 = PCURVE('',#2082,#3933);
#3933 = DEFINITIONAL_REPRESENTATION('',(#3934),#3938);
#3934 = LINE('',#3935,#3936);
#3935 = CARTESIAN_POINT('',(0.,-58.));
#3936 = VECTOR('',#3937,1.);
#3937 = DIRECTION('',(1.,0.));
#3938 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3939 = PCURVE('',#2432,#3940);
#3940 = DEFINITIONAL_REPRESENTATION('',(#3941),#3945);
#3941 = CIRCLE('',#3942,3.);
#3942 = AXIS2_PLACEMENT_2D('',#3943,#3944);
#3943 = CARTESIAN_POINT('',(7.11E-15,2.3E-16));
#3944 = DIRECTION('',(0.,1.));
#3945 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3946 = ORIENTED_EDGE('',*,*,#3902,.F.);
#3947 = ADVANCED_FACE('',(#3948),#2268,.T.);
#3948 = FACE_BOUND('',#3949,.F.);
#3949 = EDGE_LOOP('',(#3950,#3951,#3972,#3973));
#3950 = ORIENTED_EDGE('',*,*,#3377,.T.);
#3951 = ORIENTED_EDGE('',*,*,#3952,.T.);
#3952 = EDGE_CURVE('',#3378,#2644,#3953,.T.);
#3953 = SURFACE_CURVE('',#3954,(#3958,#3965),.PCURVE_S1.);
#3954 = LINE('',#3955,#3956);
#3955 = CARTESIAN_POINT('',(-51.,-34.78412114942,7.));
#3956 = VECTOR('',#3957,1.);
#3957 = DIRECTION('',(-0.,-0.,-1.));
#3958 = PCURVE('',#2268,#3959);
#3959 = DEFINITIONAL_REPRESENTATION('',(#3960),#3964);
#3960 = LINE('',#3961,#3962);
#3961 = CARTESIAN_POINT('',(-2.781260652053,0.));
#3962 = VECTOR('',#3963,1.);
#3963 = DIRECTION('',(-0.,1.));
#3964 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3965 = PCURVE('',#2683,#3966);
#3966 = DEFINITIONAL_REPRESENTATION('',(#3967),#3971);
#3967 = LINE('',#3968,#3969);
#3968 = CARTESIAN_POINT('',(30.784121149424,-15.));
#3969 = VECTOR('',#3970,1.);
#3970 = DIRECTION('',(0.,-1.));
#3971 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3972 = ORIENTED_EDGE('',*,*,#2643,.F.);
#3973 = ORIENTED_EDGE('',*,*,#2252,.T.);
#3974 = ADVANCED_FACE('',(#3975,#3978),#2322,.T.);
#3975 = FACE_BOUND('',#3976,.T.);
#3976 = EDGE_LOOP('',(#3977));
#3977 = ORIENTED_EDGE('',*,*,#3825,.T.);
#3978 = FACE_BOUND('',#3979,.T.);
#3979 = EDGE_LOOP('',(#3980));
#3980 = ORIENTED_EDGE('',*,*,#2305,.F.);
#3981 = ADVANCED_FACE('',(#3982,#3985),#2398,.T.);
#3982 = FACE_BOUND('',#3983,.T.);
#3983 = EDGE_LOOP('',(#3984));
#3984 = ORIENTED_EDGE('',*,*,#2383,.T.);
#3985 = FACE_BOUND('',#3986,.T.);
#3986 = EDGE_LOOP('',(#3987));
#3987 = ORIENTED_EDGE('',*,*,#3988,.T.);
#3988 = EDGE_CURVE('',#3989,#3989,#3991,.T.);
#3989 = VERTEX_POINT('',#3990);
#3990 = CARTESIAN_POINT('',(6.55,8.7,48.));
#3991 = SURFACE_CURVE('',#3992,(#3997,#4008),.PCURVE_S1.);
#3992 = CIRCLE('',#3993,6.55);
#3993 = AXIS2_PLACEMENT_3D('',#3994,#3995,#3996);
#3994 = CARTESIAN_POINT('',(0.,8.7,48.));
#3995 = DIRECTION('',(0.,-1.,-2.2E-16));
#3996 = DIRECTION('',(1.,0.,0.));
#3997 = PCURVE('',#2398,#3998);
#3998 = DEFINITIONAL_REPRESENTATION('',(#3999),#4007);
#3999 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#4000,#4001,#4002,#4003,
#4004,#4005,#4006),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#4000 = CARTESIAN_POINT('',(-7.11E-15,6.55));
#4001 = CARTESIAN_POINT('',(11.344932789576,6.55));
#4002 = CARTESIAN_POINT('',(5.672466394788,-3.275));
#4003 = CARTESIAN_POINT('',(-5.505712693117E-15,-13.1));
#4004 = CARTESIAN_POINT('',(-5.672466394788,-3.275));
#4005 = CARTESIAN_POINT('',(-11.34493278957,6.55));
#4006 = CARTESIAN_POINT('',(-7.11E-15,6.55));
#4007 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4008 = PCURVE('',#4009,#4014);
#4009 = CYLINDRICAL_SURFACE('',#4010,6.55);
#4010 = AXIS2_PLACEMENT_3D('',#4011,#4012,#4013);
#4011 = CARTESIAN_POINT('',(0.,-54.,48.));
#4012 = DIRECTION('',(0.,-1.,-2.2E-16));
#4013 = DIRECTION('',(1.,0.,0.));
#4014 = DEFINITIONAL_REPRESENTATION('',(#4015),#4019);
#4015 = LINE('',#4016,#4017);
#4016 = CARTESIAN_POINT('',(0.,-62.7));
#4017 = VECTOR('',#4018,1.);
#4018 = DIRECTION('',(1.,0.));
#4019 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4020 = ADVANCED_FACE('',(#4021,#4024),#2432,.T.);
#4021 = FACE_BOUND('',#4022,.T.);
#4022 = EDGE_LOOP('',(#4023));
#4023 = ORIENTED_EDGE('',*,*,#3925,.T.);
#4024 = FACE_BOUND('',#4025,.T.);
#4025 = EDGE_LOOP('',(#4026));
#4026 = ORIENTED_EDGE('',*,*,#2415,.F.);
#4027 = ADVANCED_FACE('',(#4028,#4031),#2487,.T.);
#4028 = FACE_BOUND('',#4029,.T.);
#4029 = EDGE_LOOP('',(#4030));
#4030 = ORIENTED_EDGE('',*,*,#3875,.T.);
#4031 = FACE_BOUND('',#4032,.T.);
#4032 = EDGE_LOOP('',(#4033));
#4033 = ORIENTED_EDGE('',*,*,#2470,.F.);
#4034 = ADVANCED_FACE('',(#4035),#2578,.T.);
#4035 = FACE_BOUND('',#4036,.T.);
#4036 = EDGE_LOOP('',(#4037,#4038,#4059,#4060,#4083,#4111,#4132,#4133));
#4037 = ORIENTED_EDGE('',*,*,#3448,.F.);
#4038 = ORIENTED_EDGE('',*,*,#4039,.T.);
#4039 = EDGE_CURVE('',#3425,#2696,#4040,.T.);
#4040 = SURFACE_CURVE('',#4041,(#4045,#4052),.PCURVE_S1.);
#4041 = LINE('',#4042,#4043);
#4042 = CARTESIAN_POINT('',(-21.6852484422,-104.,7.));
#4043 = VECTOR('',#4044,1.);
#4044 = DIRECTION('',(-0.,-0.,-1.));
#4045 = PCURVE('',#2578,#4046);
#4046 = DEFINITIONAL_REPRESENTATION('',(#4047),#4051);
#4047 = LINE('',#4048,#4049);
#4048 = CARTESIAN_POINT('',(23.314751557799,-15.));
#4049 = VECTOR('',#4050,1.);
#4050 = DIRECTION('',(0.,-1.));
#4051 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4052 = PCURVE('',#2712,#4053);
#4053 = DEFINITIONAL_REPRESENTATION('',(#4054),#4058);
#4054 = LINE('',#4055,#4056);
#4055 = CARTESIAN_POINT('',(-4.303168310829,0.));
#4056 = VECTOR('',#4057,1.);
#4057 = DIRECTION('',(-0.,1.));
#4058 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4059 = ORIENTED_EDGE('',*,*,#2724,.T.);
#4060 = ORIENTED_EDGE('',*,*,#4061,.F.);
#4061 = EDGE_CURVE('',#4062,#2725,#4064,.T.);
#4062 = VERTEX_POINT('',#4063);
#4063 = CARTESIAN_POINT('',(-15.5,-104.,17.));
#4064 = SURFACE_CURVE('',#4065,(#4069,#4076),.PCURVE_S1.);
#4065 = LINE('',#4066,#4067);
#4066 = CARTESIAN_POINT('',(-15.5,-104.,14.5));
#4067 = VECTOR('',#4068,1.);
#4068 = DIRECTION('',(0.,0.,-1.));
#4069 = PCURVE('',#2578,#4070);
#4070 = DEFINITIONAL_REPRESENTATION('',(#4071),#4075);
#4071 = LINE('',#4072,#4073);
#4072 = CARTESIAN_POINT('',(29.5,-7.5));
#4073 = VECTOR('',#4074,1.);
#4074 = DIRECTION('',(0.,-1.));
#4075 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4076 = PCURVE('',#2763,#4077);
#4077 = DEFINITIONAL_REPRESENTATION('',(#4078),#4082);
#4078 = LINE('',#4079,#4080);
#4079 = CARTESIAN_POINT('',(40.,-7.5));
#4080 = VECTOR('',#4081,1.);
#4081 = DIRECTION('',(0.,1.));
#4082 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4083 = ORIENTED_EDGE('',*,*,#4084,.T.);
#4084 = EDGE_CURVE('',#4062,#4085,#4087,.T.);
#4085 = VERTEX_POINT('',#4086);
#4086 = CARTESIAN_POINT('',(15.5,-104.,17.));
#4087 = SURFACE_CURVE('',#4088,(#4092,#4099),.PCURVE_S1.);
#4088 = LINE('',#4089,#4090);
#4089 = CARTESIAN_POINT('',(-22.5,-104.,17.));
#4090 = VECTOR('',#4091,1.);
#4091 = DIRECTION('',(1.,0.,0.));
#4092 = PCURVE('',#2578,#4093);
#4093 = DEFINITIONAL_REPRESENTATION('',(#4094),#4098);
#4094 = LINE('',#4095,#4096);
#4095 = CARTESIAN_POINT('',(22.5,-5.));
#4096 = VECTOR('',#4097,1.);
#4097 = DIRECTION('',(1.,0.));
#4098 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4099 = PCURVE('',#4100,#4105);
#4100 = PLANE('',#4101);
#4101 = AXIS2_PLACEMENT_3D('',#4102,#4103,#4104);
#4102 = CARTESIAN_POINT('',(0.,-89.,17.));
#4103 = DIRECTION('',(0.,0.,1.));
#4104 = DIRECTION('',(1.,0.,0.));
#4105 = DEFINITIONAL_REPRESENTATION('',(#4106),#4110);
#4106 = LINE('',#4107,#4108);
#4107 = CARTESIAN_POINT('',(-22.5,-15.));
#4108 = VECTOR('',#4109,1.);
#4109 = DIRECTION('',(1.,0.));
#4110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4111 = ORIENTED_EDGE('',*,*,#4112,.F.);
#4112 = EDGE_CURVE('',#2561,#4085,#4113,.T.);
#4113 = SURFACE_CURVE('',#4114,(#4118,#4125),.PCURVE_S1.);
#4114 = LINE('',#4115,#4116);
#4115 = CARTESIAN_POINT('',(15.5,-104.,14.5));
#4116 = VECTOR('',#4117,1.);
#4117 = DIRECTION('',(0.,0.,1.));
#4118 = PCURVE('',#2578,#4119);
#4119 = DEFINITIONAL_REPRESENTATION('',(#4120),#4124);
#4120 = LINE('',#4121,#4122);
#4121 = CARTESIAN_POINT('',(60.5,-7.5));
#4122 = VECTOR('',#4123,1.);
#4123 = DIRECTION('',(0.,1.));
#4124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4125 = PCURVE('',#2817,#4126);
#4126 = DEFINITIONAL_REPRESENTATION('',(#4127),#4131);
#4127 = LINE('',#4128,#4129);
#4128 = CARTESIAN_POINT('',(10.,-7.5));
#4129 = VECTOR('',#4130,1.);
#4130 = DIRECTION('',(0.,-1.));
#4131 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4132 = ORIENTED_EDGE('',*,*,#2560,.T.);
#4133 = ORIENTED_EDGE('',*,*,#4134,.F.);
#4134 = EDGE_CURVE('',#3449,#2563,#4135,.T.);
#4135 = SURFACE_CURVE('',#4136,(#4140,#4147),.PCURVE_S1.);
#4136 = LINE('',#4137,#4138);
#4137 = CARTESIAN_POINT('',(21.685248442201,-104.,7.));
#4138 = VECTOR('',#4139,1.);
#4139 = DIRECTION('',(-0.,-0.,-1.));
#4140 = PCURVE('',#2578,#4141);
#4141 = DEFINITIONAL_REPRESENTATION('',(#4142),#4146);
#4142 = LINE('',#4143,#4144);
#4143 = CARTESIAN_POINT('',(66.685248442201,-15.));
#4144 = VECTOR('',#4145,1.);
#4145 = DIRECTION('',(0.,-1.));
#4146 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4147 = PCURVE('',#2607,#4148);
#4148 = DEFINITIONAL_REPRESENTATION('',(#4149),#4153);
#4149 = LINE('',#4150,#4151);
#4150 = CARTESIAN_POINT('',(-5.12160964994,0.));
#4151 = VECTOR('',#4152,1.);
#4152 = DIRECTION('',(-0.,1.));
#4153 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4154 = ADVANCED_FACE('',(#4155),#2817,.T.);
#4155 = FACE_BOUND('',#4156,.T.);
#4156 = EDGE_LOOP('',(#4157,#4158,#4159,#4182));
#4157 = ORIENTED_EDGE('',*,*,#2803,.F.);
#4158 = ORIENTED_EDGE('',*,*,#4112,.T.);
#4159 = ORIENTED_EDGE('',*,*,#4160,.T.);
#4160 = EDGE_CURVE('',#4085,#4161,#4163,.T.);
#4161 = VERTEX_POINT('',#4162);
#4162 = CARTESIAN_POINT('',(15.5,-64.,17.));
#4163 = SURFACE_CURVE('',#4164,(#4168,#4175),.PCURVE_S1.);
#4164 = LINE('',#4165,#4166);
#4165 = CARTESIAN_POINT('',(15.5,-114.,17.));
#4166 = VECTOR('',#4167,1.);
#4167 = DIRECTION('',(0.,1.,0.));
#4168 = PCURVE('',#2817,#4169);
#4169 = DEFINITIONAL_REPRESENTATION('',(#4170),#4174);
#4170 = LINE('',#4171,#4172);
#4171 = CARTESIAN_POINT('',(0.,-10.));
#4172 = VECTOR('',#4173,1.);
#4173 = DIRECTION('',(1.,0.));
#4174 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4175 = PCURVE('',#4100,#4176);
#4176 = DEFINITIONAL_REPRESENTATION('',(#4177),#4181);
#4177 = LINE('',#4178,#4179);
#4178 = CARTESIAN_POINT('',(15.5,-25.));
#4179 = VECTOR('',#4180,1.);
#4180 = DIRECTION('',(0.,1.));
#4181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4182 = ORIENTED_EDGE('',*,*,#4183,.F.);
#4183 = EDGE_CURVE('',#2776,#4161,#4184,.T.);
#4184 = SURFACE_CURVE('',#4185,(#4189,#4196),.PCURVE_S1.);
#4185 = LINE('',#4186,#4187);
#4186 = CARTESIAN_POINT('',(15.5,-64.,7.));
#4187 = VECTOR('',#4188,1.);
#4188 = DIRECTION('',(0.,0.,1.));
#4189 = PCURVE('',#2817,#4190);
#4190 = DEFINITIONAL_REPRESENTATION('',(#4191),#4195);
#4191 = LINE('',#4192,#4193);
#4192 = CARTESIAN_POINT('',(50.,0.));
#4193 = VECTOR('',#4194,1.);
#4194 = DIRECTION('',(0.,-1.));
#4195 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4196 = PCURVE('',#2791,#4197);
#4197 = DEFINITIONAL_REPRESENTATION('',(#4198),#4202);
#4198 = LINE('',#4199,#4200);
#4199 = CARTESIAN_POINT('',(0.,-0.));
#4200 = VECTOR('',#4201,1.);
#4201 = DIRECTION('',(0.,-1.));
#4202 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4203 = ADVANCED_FACE('',(#4204),#2607,.T.);
#4204 = FACE_BOUND('',#4205,.F.);
#4205 = EDGE_LOOP('',(#4206,#4207,#4208,#4209));
#4206 = ORIENTED_EDGE('',*,*,#3720,.T.);
#4207 = ORIENTED_EDGE('',*,*,#2590,.F.);
#4208 = ORIENTED_EDGE('',*,*,#4134,.F.);
#4209 = ORIENTED_EDGE('',*,*,#3471,.T.);
#4210 = ADVANCED_FACE('',(#4211),#2791,.T.);
#4211 = FACE_BOUND('',#4212,.T.);
#4212 = EDGE_LOOP('',(#4213,#4214,#4237,#4258));
#4213 = ORIENTED_EDGE('',*,*,#4183,.T.);
#4214 = ORIENTED_EDGE('',*,*,#4215,.T.);
#4215 = EDGE_CURVE('',#4161,#4216,#4218,.T.);
#4216 = VERTEX_POINT('',#4217);
#4217 = CARTESIAN_POINT('',(-15.5,-64.,17.));
#4218 = SURFACE_CURVE('',#4219,(#4223,#4230),.PCURVE_S1.);
#4219 = LINE('',#4220,#4221);
#4220 = CARTESIAN_POINT('',(15.5,-64.,17.));
#4221 = VECTOR('',#4222,1.);
#4222 = DIRECTION('',(-1.,0.,0.));
#4223 = PCURVE('',#2791,#4224);
#4224 = DEFINITIONAL_REPRESENTATION('',(#4225),#4229);
#4225 = LINE('',#4226,#4227);
#4226 = CARTESIAN_POINT('',(0.,-10.));
#4227 = VECTOR('',#4228,1.);
#4228 = DIRECTION('',(1.,0.));
#4229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4230 = PCURVE('',#4100,#4231);
#4231 = DEFINITIONAL_REPRESENTATION('',(#4232),#4236);
#4232 = LINE('',#4233,#4234);
#4233 = CARTESIAN_POINT('',(15.5,25.));
#4234 = VECTOR('',#4235,1.);
#4235 = DIRECTION('',(-1.,0.));
#4236 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4237 = ORIENTED_EDGE('',*,*,#4238,.F.);
#4238 = EDGE_CURVE('',#2748,#4216,#4239,.T.);
#4239 = SURFACE_CURVE('',#4240,(#4244,#4251),.PCURVE_S1.);
#4240 = LINE('',#4241,#4242);
#4241 = CARTESIAN_POINT('',(-15.5,-64.,7.));
#4242 = VECTOR('',#4243,1.);
#4243 = DIRECTION('',(0.,0.,1.));
#4244 = PCURVE('',#2791,#4245);
#4245 = DEFINITIONAL_REPRESENTATION('',(#4246),#4250);
#4246 = LINE('',#4247,#4248);
#4247 = CARTESIAN_POINT('',(31.,0.));
#4248 = VECTOR('',#4249,1.);
#4249 = DIRECTION('',(0.,-1.));
#4250 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4251 = PCURVE('',#2763,#4252);
#4252 = DEFINITIONAL_REPRESENTATION('',(#4253),#4257);
#4253 = LINE('',#4254,#4255);
#4254 = CARTESIAN_POINT('',(0.,0.));
#4255 = VECTOR('',#4256,1.);
#4256 = DIRECTION('',(0.,-1.));
#4257 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4258 = ORIENTED_EDGE('',*,*,#2775,.F.);
#4259 = ADVANCED_FACE('',(#4260),#2763,.T.);
#4260 = FACE_BOUND('',#4261,.T.);
#4261 = EDGE_LOOP('',(#4262,#4263,#4264,#4285));
#4262 = ORIENTED_EDGE('',*,*,#2747,.F.);
#4263 = ORIENTED_EDGE('',*,*,#4238,.T.);
#4264 = ORIENTED_EDGE('',*,*,#4265,.T.);
#4265 = EDGE_CURVE('',#4216,#4062,#4266,.T.);
#4266 = SURFACE_CURVE('',#4267,(#4271,#4278),.PCURVE_S1.);
#4267 = LINE('',#4268,#4269);
#4268 = CARTESIAN_POINT('',(-15.5,-64.,17.));
#4269 = VECTOR('',#4270,1.);
#4270 = DIRECTION('',(0.,-1.,0.));
#4271 = PCURVE('',#2763,#4272);
#4272 = DEFINITIONAL_REPRESENTATION('',(#4273),#4277);
#4273 = LINE('',#4274,#4275);
#4274 = CARTESIAN_POINT('',(0.,-10.));
#4275 = VECTOR('',#4276,1.);
#4276 = DIRECTION('',(1.,0.));
#4277 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4278 = PCURVE('',#4100,#4279);
#4279 = DEFINITIONAL_REPRESENTATION('',(#4280),#4284);
#4280 = LINE('',#4281,#4282);
#4281 = CARTESIAN_POINT('',(-15.5,25.));
#4282 = VECTOR('',#4283,1.);
#4283 = DIRECTION('',(0.,-1.));
#4284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4285 = ORIENTED_EDGE('',*,*,#4061,.T.);
#4286 = ADVANCED_FACE('',(#4287),#2712,.T.);
#4287 = FACE_BOUND('',#4288,.F.);
#4288 = EDGE_LOOP('',(#4289,#4290,#4291,#4292));
#4289 = ORIENTED_EDGE('',*,*,#3424,.T.);
#4290 = ORIENTED_EDGE('',*,*,#4039,.T.);
#4291 = ORIENTED_EDGE('',*,*,#2695,.F.);
#4292 = ORIENTED_EDGE('',*,*,#4293,.F.);
#4293 = EDGE_CURVE('',#3402,#2668,#4294,.T.);
#4294 = SURFACE_CURVE('',#4295,(#4299,#4306),.PCURVE_S1.);
#4295 = LINE('',#4296,#4297);
#4296 = CARTESIAN_POINT('',(-51.,-73.21587885057,7.));
#4297 = VECTOR('',#4298,1.);
#4298 = DIRECTION('',(-0.,-0.,-1.));
#4299 = PCURVE('',#2712,#4300);
#4300 = DEFINITIONAL_REPRESENTATION('',(#4301),#4305);
#4301 = LINE('',#4302,#4303);
#4302 = CARTESIAN_POINT('',(-3.501924655126,0.));
#4303 = VECTOR('',#4304,1.);
#4304 = DIRECTION('',(-0.,1.));
#4305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4306 = PCURVE('',#2683,#4307);
#4307 = DEFINITIONAL_REPRESENTATION('',(#4308),#4312);
#4308 = LINE('',#4309,#4310);
#4309 = CARTESIAN_POINT('',(69.215878850576,-15.));
#4310 = VECTOR('',#4311,1.);
#4311 = DIRECTION('',(0.,-1.));
#4312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4313 = ADVANCED_FACE('',(#4314),#2683,.T.);
#4314 = FACE_BOUND('',#4315,.T.);
#4315 = EDGE_LOOP('',(#4316,#4317,#4318,#4319));
#4316 = ORIENTED_EDGE('',*,*,#3401,.F.);
#4317 = ORIENTED_EDGE('',*,*,#3952,.T.);
#4318 = ORIENTED_EDGE('',*,*,#2667,.T.);
#4319 = ORIENTED_EDGE('',*,*,#4293,.F.);
#4320 = ADVANCED_FACE('',(#4321),#2850,.F.);
#4321 = FACE_BOUND('',#4322,.F.);
#4322 = EDGE_LOOP('',(#4323,#4346,#4347,#4348,#4377,#4398,#4399,#4422));
#4323 = ORIENTED_EDGE('',*,*,#4324,.F.);
#4324 = EDGE_CURVE('',#3693,#4325,#4327,.T.);
#4325 = VERTEX_POINT('',#4326);
#4326 = CARTESIAN_POINT('',(-11.5,-32.,12.));
#4327 = SEAM_CURVE('',#4328,(#4332,#4339),.PCURVE_S1.);
#4328 = LINE('',#4329,#4330);
#4329 = CARTESIAN_POINT('',(-11.5,-32.,22.));
#4330 = VECTOR('',#4331,1.);
#4331 = DIRECTION('',(-0.,-0.,-1.));
#4332 = PCURVE('',#2850,#4333);
#4333 = DEFINITIONAL_REPRESENTATION('',(#4334),#4338);
#4334 = LINE('',#4335,#4336);
#4335 = CARTESIAN_POINT('',(6.28318530718,0.));
#4336 = VECTOR('',#4337,1.);
#4337 = DIRECTION('',(0.,-1.));
#4338 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4339 = PCURVE('',#2850,#4340);
#4340 = DEFINITIONAL_REPRESENTATION('',(#4341),#4345);
#4341 = LINE('',#4342,#4343);
#4342 = CARTESIAN_POINT('',(0.,0.));
#4343 = VECTOR('',#4344,1.);
#4344 = DIRECTION('',(0.,-1.));
#4345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4346 = ORIENTED_EDGE('',*,*,#3692,.F.);
#4347 = ORIENTED_EDGE('',*,*,#4324,.T.);
#4348 = ORIENTED_EDGE('',*,*,#4349,.T.);
#4349 = EDGE_CURVE('',#4325,#4350,#4352,.T.);
#4350 = VERTEX_POINT('',#4351);
#4351 = CARTESIAN_POINT('',(-15.5,-24.,12.));
#4352 = SURFACE_CURVE('',#4353,(#4358,#4365),.PCURVE_S1.);
#4353 = CIRCLE('',#4354,10.);
#4354 = AXIS2_PLACEMENT_3D('',#4355,#4356,#4357);
#4355 = CARTESIAN_POINT('',(-21.5,-32.,12.));
#4356 = DIRECTION('',(0.,0.,1.));
#4357 = DIRECTION('',(1.,0.,0.));
#4358 = PCURVE('',#2850,#4359);
#4359 = DEFINITIONAL_REPRESENTATION('',(#4360),#4364);
#4360 = LINE('',#4361,#4362);
#4361 = CARTESIAN_POINT('',(0.,-10.));
#4362 = VECTOR('',#4363,1.);
#4363 = DIRECTION('',(1.,0.));
#4364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4365 = PCURVE('',#4366,#4371);
#4366 = PLANE('',#4367);
#4367 = AXIS2_PLACEMENT_3D('',#4368,#4369,#4370);
#4368 = CARTESIAN_POINT('',(0.,-28.5,12.));
#4369 = DIRECTION('',(0.,0.,1.));
#4370 = DIRECTION('',(1.,0.,0.));
#4371 = DEFINITIONAL_REPRESENTATION('',(#4372),#4376);
#4372 = CIRCLE('',#4373,10.);
#4373 = AXIS2_PLACEMENT_2D('',#4374,#4375);
#4374 = CARTESIAN_POINT('',(-21.5,-3.5));
#4375 = DIRECTION('',(1.,0.));
#4376 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4377 = ORIENTED_EDGE('',*,*,#4378,.F.);
#4378 = EDGE_CURVE('',#2832,#4350,#4379,.T.);
#4379 = SURFACE_CURVE('',#4380,(#4384,#4391),.PCURVE_S1.);
#4380 = LINE('',#4381,#4382);
#4381 = CARTESIAN_POINT('',(-15.5,-24.,22.));
#4382 = VECTOR('',#4383,1.);
#4383 = DIRECTION('',(0.,0.,1.));
#4384 = PCURVE('',#2850,#4385);
#4385 = DEFINITIONAL_REPRESENTATION('',(#4386),#4390);
#4386 = LINE('',#4387,#4388);
#4387 = CARTESIAN_POINT('',(0.927295218002,0.));
#4388 = VECTOR('',#4389,1.);
#4389 = DIRECTION('',(0.,1.));
#4390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4391 = PCURVE('',#2878,#4392);
#4392 = DEFINITIONAL_REPRESENTATION('',(#4393),#4397);
#4393 = LINE('',#4394,#4395);
#4394 = CARTESIAN_POINT('',(16.,-15.));
#4395 = VECTOR('',#4396,1.);
#4396 = DIRECTION('',(0.,-1.));
#4397 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4398 = ORIENTED_EDGE('',*,*,#2831,.T.);
#4399 = ORIENTED_EDGE('',*,*,#4400,.T.);
#4400 = EDGE_CURVE('',#2834,#4401,#4403,.T.);
#4401 = VERTEX_POINT('',#4402);
#4402 = CARTESIAN_POINT('',(-15.5,-40.,12.));
#4403 = SURFACE_CURVE('',#4404,(#4408,#4415),.PCURVE_S1.);
#4404 = LINE('',#4405,#4406);
#4405 = CARTESIAN_POINT('',(-15.5,-40.,22.));
#4406 = VECTOR('',#4407,1.);
#4407 = DIRECTION('',(0.,0.,1.));
#4408 = PCURVE('',#2850,#4409);
#4409 = DEFINITIONAL_REPRESENTATION('',(#4410),#4414);
#4410 = LINE('',#4411,#4412);
#4411 = CARTESIAN_POINT('',(5.355890089178,0.));
#4412 = VECTOR('',#4413,1.);
#4413 = DIRECTION('',(0.,1.));
#4414 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4415 = PCURVE('',#3069,#4416);
#4416 = DEFINITIONAL_REPRESENTATION('',(#4417),#4421);
#4417 = LINE('',#4418,#4419);
#4418 = CARTESIAN_POINT('',(32.,-15.));
#4419 = VECTOR('',#4420,1.);
#4420 = DIRECTION('',(0.,-1.));
#4421 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4422 = ORIENTED_EDGE('',*,*,#4423,.T.);
#4423 = EDGE_CURVE('',#4401,#4325,#4424,.T.);
#4424 = SURFACE_CURVE('',#4425,(#4430,#4437),.PCURVE_S1.);
#4425 = CIRCLE('',#4426,10.);
#4426 = AXIS2_PLACEMENT_3D('',#4427,#4428,#4429);
#4427 = CARTESIAN_POINT('',(-21.5,-32.,12.));
#4428 = DIRECTION('',(0.,0.,1.));
#4429 = DIRECTION('',(1.,0.,0.));
#4430 = PCURVE('',#2850,#4431);
#4431 = DEFINITIONAL_REPRESENTATION('',(#4432),#4436);
#4432 = LINE('',#4433,#4434);
#4433 = CARTESIAN_POINT('',(0.,-10.));
#4434 = VECTOR('',#4435,1.);
#4435 = DIRECTION('',(1.,0.));
#4436 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4437 = PCURVE('',#4366,#4438);
#4438 = DEFINITIONAL_REPRESENTATION('',(#4439),#4443);
#4439 = CIRCLE('',#4440,10.);
#4440 = AXIS2_PLACEMENT_2D('',#4441,#4442);
#4441 = CARTESIAN_POINT('',(-21.5,-3.5));
#4442 = DIRECTION('',(1.,0.));
#4443 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4444 = ADVANCED_FACE('',(#4445),#2878,.T.);
#4445 = FACE_BOUND('',#4446,.T.);
#4446 = EDGE_LOOP('',(#4447,#4448,#4471,#4492));
#4447 = ORIENTED_EDGE('',*,*,#2862,.F.);
#4448 = ORIENTED_EDGE('',*,*,#4449,.T.);
#4449 = EDGE_CURVE('',#2863,#4450,#4452,.T.);
#4450 = VERTEX_POINT('',#4451);
#4451 = CARTESIAN_POINT('',(-15.5,-8.,12.));
#4452 = SURFACE_CURVE('',#4453,(#4457,#4464),.PCURVE_S1.);
#4453 = LINE('',#4454,#4455);
#4454 = CARTESIAN_POINT('',(-15.5,-8.,7.));
#4455 = VECTOR('',#4456,1.);
#4456 = DIRECTION('',(0.,0.,1.));
#4457 = PCURVE('',#2878,#4458);
#4458 = DEFINITIONAL_REPRESENTATION('',(#4459),#4463);
#4459 = LINE('',#4460,#4461);
#4460 = CARTESIAN_POINT('',(0.,0.));
#4461 = VECTOR('',#4462,1.);
#4462 = DIRECTION('',(0.,-1.));
#4463 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4464 = PCURVE('',#2906,#4465);
#4465 = DEFINITIONAL_REPRESENTATION('',(#4466),#4470);
#4466 = LINE('',#4467,#4468);
#4467 = CARTESIAN_POINT('',(31.,0.));
#4468 = VECTOR('',#4469,1.);
#4469 = DIRECTION('',(0.,-1.));
#4470 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4471 = ORIENTED_EDGE('',*,*,#4472,.T.);
#4472 = EDGE_CURVE('',#4450,#4350,#4473,.T.);
#4473 = SURFACE_CURVE('',#4474,(#4478,#4485),.PCURVE_S1.);
#4474 = LINE('',#4475,#4476);
#4475 = CARTESIAN_POINT('',(-15.5,-8.,12.));
#4476 = VECTOR('',#4477,1.);
#4477 = DIRECTION('',(0.,-1.,0.));
#4478 = PCURVE('',#2878,#4479);
#4479 = DEFINITIONAL_REPRESENTATION('',(#4480),#4484);
#4480 = LINE('',#4481,#4482);
#4481 = CARTESIAN_POINT('',(0.,-5.));
#4482 = VECTOR('',#4483,1.);
#4483 = DIRECTION('',(1.,0.));
#4484 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4485 = PCURVE('',#4366,#4486);
#4486 = DEFINITIONAL_REPRESENTATION('',(#4487),#4491);
#4487 = LINE('',#4488,#4489);
#4488 = CARTESIAN_POINT('',(-15.5,20.5));
#4489 = VECTOR('',#4490,1.);
#4490 = DIRECTION('',(0.,-1.));
#4491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4492 = ORIENTED_EDGE('',*,*,#4378,.F.);
#4493 = ADVANCED_FACE('',(#4494),#3069,.T.);
#4494 = FACE_BOUND('',#4495,.T.);
#4495 = EDGE_LOOP('',(#4496,#4497,#4498,#4521));
#4496 = ORIENTED_EDGE('',*,*,#3055,.F.);
#4497 = ORIENTED_EDGE('',*,*,#4400,.T.);
#4498 = ORIENTED_EDGE('',*,*,#4499,.T.);
#4499 = EDGE_CURVE('',#4401,#4500,#4502,.T.);
#4500 = VERTEX_POINT('',#4501);
#4501 = CARTESIAN_POINT('',(-15.5,-49.,12.));
#4502 = SURFACE_CURVE('',#4503,(#4507,#4514),.PCURVE_S1.);
#4503 = LINE('',#4504,#4505);
#4504 = CARTESIAN_POINT('',(-15.5,-8.,12.));
#4505 = VECTOR('',#4506,1.);
#4506 = DIRECTION('',(0.,-1.,0.));
#4507 = PCURVE('',#3069,#4508);
#4508 = DEFINITIONAL_REPRESENTATION('',(#4509),#4513);
#4509 = LINE('',#4510,#4511);
#4510 = CARTESIAN_POINT('',(0.,-5.));
#4511 = VECTOR('',#4512,1.);
#4512 = DIRECTION('',(1.,0.));
#4513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4514 = PCURVE('',#4366,#4515);
#4515 = DEFINITIONAL_REPRESENTATION('',(#4516),#4520);
#4516 = LINE('',#4517,#4518);
#4517 = CARTESIAN_POINT('',(-15.5,20.5));
#4518 = VECTOR('',#4519,1.);
#4519 = DIRECTION('',(0.,-1.));
#4520 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4521 = ORIENTED_EDGE('',*,*,#4522,.F.);
#4522 = EDGE_CURVE('',#3028,#4500,#4523,.T.);
#4523 = SURFACE_CURVE('',#4524,(#4528,#4535),.PCURVE_S1.);
#4524 = LINE('',#4525,#4526);
#4525 = CARTESIAN_POINT('',(-15.5,-49.,7.));
#4526 = VECTOR('',#4527,1.);
#4527 = DIRECTION('',(0.,0.,1.));
#4528 = PCURVE('',#3069,#4529);
#4529 = DEFINITIONAL_REPRESENTATION('',(#4530),#4534);
#4530 = LINE('',#4531,#4532);
#4531 = CARTESIAN_POINT('',(41.,0.));
#4532 = VECTOR('',#4533,1.);
#4533 = DIRECTION('',(0.,-1.));
#4534 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4535 = PCURVE('',#3043,#4536);
#4536 = DEFINITIONAL_REPRESENTATION('',(#4537),#4541);
#4537 = LINE('',#4538,#4539);
#4538 = CARTESIAN_POINT('',(0.,0.));
#4539 = VECTOR('',#4540,1.);
#4540 = DIRECTION('',(0.,-1.));
#4541 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4542 = ADVANCED_FACE('',(#4543),#2906,.T.);
#4543 = FACE_BOUND('',#4544,.T.);
#4544 = EDGE_LOOP('',(#4545,#4568,#4589,#4590));
#4545 = ORIENTED_EDGE('',*,*,#4546,.T.);
#4546 = EDGE_CURVE('',#2891,#4547,#4549,.T.);
#4547 = VERTEX_POINT('',#4548);
#4548 = CARTESIAN_POINT('',(15.5,-8.,12.));
#4549 = SURFACE_CURVE('',#4550,(#4554,#4561),.PCURVE_S1.);
#4550 = LINE('',#4551,#4552);
#4551 = CARTESIAN_POINT('',(15.5,-8.,7.));
#4552 = VECTOR('',#4553,1.);
#4553 = DIRECTION('',(0.,0.,1.));
#4554 = PCURVE('',#2906,#4555);
#4555 = DEFINITIONAL_REPRESENTATION('',(#4556),#4560);
#4556 = LINE('',#4557,#4558);
#4557 = CARTESIAN_POINT('',(0.,-0.));
#4558 = VECTOR('',#4559,1.);
#4559 = DIRECTION('',(0.,-1.));
#4560 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4561 = PCURVE('',#2934,#4562);
#4562 = DEFINITIONAL_REPRESENTATION('',(#4563),#4567);
#4563 = LINE('',#4564,#4565);
#4564 = CARTESIAN_POINT('',(41.,0.));
#4565 = VECTOR('',#4566,1.);
#4566 = DIRECTION('',(0.,-1.));
#4567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4568 = ORIENTED_EDGE('',*,*,#4569,.T.);
#4569 = EDGE_CURVE('',#4547,#4450,#4570,.T.);
#4570 = SURFACE_CURVE('',#4571,(#4575,#4582),.PCURVE_S1.);
#4571 = LINE('',#4572,#4573);
#4572 = CARTESIAN_POINT('',(15.5,-8.,12.));
#4573 = VECTOR('',#4574,1.);
#4574 = DIRECTION('',(-1.,0.,0.));
#4575 = PCURVE('',#2906,#4576);
#4576 = DEFINITIONAL_REPRESENTATION('',(#4577),#4581);
#4577 = LINE('',#4578,#4579);
#4578 = CARTESIAN_POINT('',(0.,-5.));
#4579 = VECTOR('',#4580,1.);
#4580 = DIRECTION('',(1.,0.));
#4581 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4582 = PCURVE('',#4366,#4583);
#4583 = DEFINITIONAL_REPRESENTATION('',(#4584),#4588);
#4584 = LINE('',#4585,#4586);
#4585 = CARTESIAN_POINT('',(15.5,20.5));
#4586 = VECTOR('',#4587,1.);
#4587 = DIRECTION('',(-1.,0.));
#4588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4589 = ORIENTED_EDGE('',*,*,#4449,.F.);
#4590 = ORIENTED_EDGE('',*,*,#2890,.F.);
#4591 = ADVANCED_FACE('',(#4592),#3043,.T.);
#4592 = FACE_BOUND('',#4593,.T.);
#4593 = EDGE_LOOP('',(#4594,#4595,#4618,#4639));
#4594 = ORIENTED_EDGE('',*,*,#4522,.T.);
#4595 = ORIENTED_EDGE('',*,*,#4596,.T.);
#4596 = EDGE_CURVE('',#4500,#4597,#4599,.T.);
#4597 = VERTEX_POINT('',#4598);
#4598 = CARTESIAN_POINT('',(15.5,-49.,12.));
#4599 = SURFACE_CURVE('',#4600,(#4604,#4611),.PCURVE_S1.);
#4600 = LINE('',#4601,#4602);
#4601 = CARTESIAN_POINT('',(-15.5,-49.,12.));
#4602 = VECTOR('',#4603,1.);
#4603 = DIRECTION('',(1.,0.,0.));
#4604 = PCURVE('',#3043,#4605);
#4605 = DEFINITIONAL_REPRESENTATION('',(#4606),#4610);
#4606 = LINE('',#4607,#4608);
#4607 = CARTESIAN_POINT('',(0.,-5.));
#4608 = VECTOR('',#4609,1.);
#4609 = DIRECTION('',(1.,0.));
#4610 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4611 = PCURVE('',#4366,#4612);
#4612 = DEFINITIONAL_REPRESENTATION('',(#4613),#4617);
#4613 = LINE('',#4614,#4615);
#4614 = CARTESIAN_POINT('',(-15.5,-20.5));
#4615 = VECTOR('',#4616,1.);
#4616 = DIRECTION('',(1.,0.));
#4617 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4618 = ORIENTED_EDGE('',*,*,#4619,.F.);
#4619 = EDGE_CURVE('',#3000,#4597,#4620,.T.);
#4620 = SURFACE_CURVE('',#4621,(#4625,#4632),.PCURVE_S1.);
#4621 = LINE('',#4622,#4623);
#4622 = CARTESIAN_POINT('',(15.5,-49.,7.));
#4623 = VECTOR('',#4624,1.);
#4624 = DIRECTION('',(0.,0.,1.));
#4625 = PCURVE('',#3043,#4626);
#4626 = DEFINITIONAL_REPRESENTATION('',(#4627),#4631);
#4627 = LINE('',#4628,#4629);
#4628 = CARTESIAN_POINT('',(31.,0.));
#4629 = VECTOR('',#4630,1.);
#4630 = DIRECTION('',(0.,-1.));
#4631 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4632 = PCURVE('',#3015,#4633);
#4633 = DEFINITIONAL_REPRESENTATION('',(#4634),#4638);
#4634 = LINE('',#4635,#4636);
#4635 = CARTESIAN_POINT('',(0.,0.));
#4636 = VECTOR('',#4637,1.);
#4637 = DIRECTION('',(0.,-1.));
#4638 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4639 = ORIENTED_EDGE('',*,*,#3027,.F.);
#4640 = ADVANCED_FACE('',(#4641),#2934,.T.);
#4641 = FACE_BOUND('',#4642,.T.);
#4642 = EDGE_LOOP('',(#4643,#4644,#4667,#4688));
#4643 = ORIENTED_EDGE('',*,*,#2918,.F.);
#4644 = ORIENTED_EDGE('',*,*,#4645,.T.);
#4645 = EDGE_CURVE('',#2919,#4646,#4648,.T.);
#4646 = VERTEX_POINT('',#4647);
#4647 = CARTESIAN_POINT('',(15.5,-24.,12.));
#4648 = SURFACE_CURVE('',#4649,(#4653,#4660),.PCURVE_S1.);
#4649 = LINE('',#4650,#4651);
#4650 = CARTESIAN_POINT('',(15.5,-24.,22.));
#4651 = VECTOR('',#4652,1.);
#4652 = DIRECTION('',(0.,0.,1.));
#4653 = PCURVE('',#2934,#4654);
#4654 = DEFINITIONAL_REPRESENTATION('',(#4655),#4659);
#4655 = LINE('',#4656,#4657);
#4656 = CARTESIAN_POINT('',(25.,-15.));
#4657 = VECTOR('',#4658,1.);
#4658 = DIRECTION('',(0.,-1.));
#4659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4660 = PCURVE('',#2963,#4661);
#4661 = DEFINITIONAL_REPRESENTATION('',(#4662),#4666);
#4662 = LINE('',#4663,#4664);
#4663 = CARTESIAN_POINT('',(2.214297435588,0.));
#4664 = VECTOR('',#4665,1.);
#4665 = DIRECTION('',(0.,1.));
#4666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4667 = ORIENTED_EDGE('',*,*,#4668,.T.);
#4668 = EDGE_CURVE('',#4646,#4547,#4669,.T.);
#4669 = SURFACE_CURVE('',#4670,(#4674,#4681),.PCURVE_S1.);
#4670 = LINE('',#4671,#4672);
#4671 = CARTESIAN_POINT('',(15.5,-49.,12.));
#4672 = VECTOR('',#4673,1.);
#4673 = DIRECTION('',(0.,1.,0.));
#4674 = PCURVE('',#2934,#4675);
#4675 = DEFINITIONAL_REPRESENTATION('',(#4676),#4680);
#4676 = LINE('',#4677,#4678);
#4677 = CARTESIAN_POINT('',(0.,-5.));
#4678 = VECTOR('',#4679,1.);
#4679 = DIRECTION('',(1.,0.));
#4680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4681 = PCURVE('',#4366,#4682);
#4682 = DEFINITIONAL_REPRESENTATION('',(#4683),#4687);
#4683 = LINE('',#4684,#4685);
#4684 = CARTESIAN_POINT('',(15.5,-20.5));
#4685 = VECTOR('',#4686,1.);
#4686 = DIRECTION('',(0.,1.));
#4687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4688 = ORIENTED_EDGE('',*,*,#4546,.F.);
#4689 = ADVANCED_FACE('',(#4690),#3015,.T.);
#4690 = FACE_BOUND('',#4691,.T.);
#4691 = EDGE_LOOP('',(#4692,#4693,#4694,#4717));
#4692 = ORIENTED_EDGE('',*,*,#2999,.F.);
#4693 = ORIENTED_EDGE('',*,*,#4619,.T.);
#4694 = ORIENTED_EDGE('',*,*,#4695,.T.);
#4695 = EDGE_CURVE('',#4597,#4696,#4698,.T.);
#4696 = VERTEX_POINT('',#4697);
#4697 = CARTESIAN_POINT('',(15.5,-40.,12.));
#4698 = SURFACE_CURVE('',#4699,(#4703,#4710),.PCURVE_S1.);
#4699 = LINE('',#4700,#4701);
#4700 = CARTESIAN_POINT('',(15.5,-49.,12.));
#4701 = VECTOR('',#4702,1.);
#4702 = DIRECTION('',(0.,1.,0.));
#4703 = PCURVE('',#3015,#4704);
#4704 = DEFINITIONAL_REPRESENTATION('',(#4705),#4709);
#4705 = LINE('',#4706,#4707);
#4706 = CARTESIAN_POINT('',(0.,-5.));
#4707 = VECTOR('',#4708,1.);
#4708 = DIRECTION('',(1.,0.));
#4709 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4710 = PCURVE('',#4366,#4711);
#4711 = DEFINITIONAL_REPRESENTATION('',(#4712),#4716);
#4712 = LINE('',#4713,#4714);
#4713 = CARTESIAN_POINT('',(15.5,-20.5));
#4714 = VECTOR('',#4715,1.);
#4715 = DIRECTION('',(0.,1.));
#4716 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4717 = ORIENTED_EDGE('',*,*,#4718,.F.);
#4718 = EDGE_CURVE('',#2976,#4696,#4719,.T.);
#4719 = SURFACE_CURVE('',#4720,(#4724,#4731),.PCURVE_S1.);
#4720 = LINE('',#4721,#4722);
#4721 = CARTESIAN_POINT('',(15.5,-40.,22.));
#4722 = VECTOR('',#4723,1.);
#4723 = DIRECTION('',(0.,0.,1.));
#4724 = PCURVE('',#3015,#4725);
#4725 = DEFINITIONAL_REPRESENTATION('',(#4726),#4730);
#4726 = LINE('',#4727,#4728);
#4727 = CARTESIAN_POINT('',(9.,-15.));
#4728 = VECTOR('',#4729,1.);
#4729 = DIRECTION('',(0.,-1.));
#4730 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4731 = PCURVE('',#2963,#4732);
#4732 = DEFINITIONAL_REPRESENTATION('',(#4733),#4737);
#4733 = LINE('',#4734,#4735);
#4734 = CARTESIAN_POINT('',(4.068887871591,0.));
#4735 = VECTOR('',#4736,1.);
#4736 = DIRECTION('',(0.,1.));
#4737 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4738 = ADVANCED_FACE('',(#4739),#2963,.F.);
#4739 = FACE_BOUND('',#4740,.F.);
#4740 = EDGE_LOOP('',(#4741,#4762,#4763,#4764,#4765,#4766,#4788,#4789));
#4741 = ORIENTED_EDGE('',*,*,#4742,.F.);
#4742 = EDGE_CURVE('',#3667,#2947,#4743,.T.);
#4743 = SEAM_CURVE('',#4744,(#4748,#4755),.PCURVE_S1.);
#4744 = LINE('',#4745,#4746);
#4745 = CARTESIAN_POINT('',(31.5,-32.,22.));
#4746 = VECTOR('',#4747,1.);
#4747 = DIRECTION('',(-0.,-0.,-1.));
#4748 = PCURVE('',#2963,#4749);
#4749 = DEFINITIONAL_REPRESENTATION('',(#4750),#4754);
#4750 = LINE('',#4751,#4752);
#4751 = CARTESIAN_POINT('',(6.28318530718,0.));
#4752 = VECTOR('',#4753,1.);
#4753 = DIRECTION('',(0.,-1.));
#4754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4755 = PCURVE('',#2963,#4756);
#4756 = DEFINITIONAL_REPRESENTATION('',(#4757),#4761);
#4757 = LINE('',#4758,#4759);
#4758 = CARTESIAN_POINT('',(0.,0.));
#4759 = VECTOR('',#4760,1.);
#4760 = DIRECTION('',(0.,-1.));
#4761 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4762 = ORIENTED_EDGE('',*,*,#3666,.F.);
#4763 = ORIENTED_EDGE('',*,*,#4742,.T.);
#4764 = ORIENTED_EDGE('',*,*,#2946,.T.);
#4765 = ORIENTED_EDGE('',*,*,#4645,.T.);
#4766 = ORIENTED_EDGE('',*,*,#4767,.T.);
#4767 = EDGE_CURVE('',#4646,#4696,#4768,.T.);
#4768 = SURFACE_CURVE('',#4769,(#4774,#4781),.PCURVE_S1.);
#4769 = CIRCLE('',#4770,10.);
#4770 = AXIS2_PLACEMENT_3D('',#4771,#4772,#4773);
#4771 = CARTESIAN_POINT('',(21.5,-32.,12.));
#4772 = DIRECTION('',(0.,0.,1.));
#4773 = DIRECTION('',(1.,0.,0.));
#4774 = PCURVE('',#2963,#4775);
#4775 = DEFINITIONAL_REPRESENTATION('',(#4776),#4780);
#4776 = LINE('',#4777,#4778);
#4777 = CARTESIAN_POINT('',(0.,-10.));
#4778 = VECTOR('',#4779,1.);
#4779 = DIRECTION('',(1.,0.));
#4780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4781 = PCURVE('',#4366,#4782);
#4782 = DEFINITIONAL_REPRESENTATION('',(#4783),#4787);
#4783 = CIRCLE('',#4784,10.);
#4784 = AXIS2_PLACEMENT_2D('',#4785,#4786);
#4785 = CARTESIAN_POINT('',(21.5,-3.5));
#4786 = DIRECTION('',(1.,0.));
#4787 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4788 = ORIENTED_EDGE('',*,*,#4718,.F.);
#4789 = ORIENTED_EDGE('',*,*,#2975,.T.);
#4790 = ADVANCED_FACE('',(#4791),#3104,.F.);
#4791 = FACE_BOUND('',#4792,.F.);
#4792 = EDGE_LOOP('',(#4793,#4816,#4843,#4844));
#4793 = ORIENTED_EDGE('',*,*,#4794,.T.);
#4794 = EDGE_CURVE('',#3084,#4795,#4797,.T.);
#4795 = VERTEX_POINT('',#4796);
#4796 = CARTESIAN_POINT('',(1.475,-3.273497,11.));
#4797 = SEAM_CURVE('',#4798,(#4802,#4809),.PCURVE_S1.);
#4798 = LINE('',#4799,#4800);
#4799 = CARTESIAN_POINT('',(1.475,-3.273497,7.));
#4800 = VECTOR('',#4801,1.);
#4801 = DIRECTION('',(0.,0.,1.));
#4802 = PCURVE('',#3104,#4803);
#4803 = DEFINITIONAL_REPRESENTATION('',(#4804),#4808);
#4804 = LINE('',#4805,#4806);
#4805 = CARTESIAN_POINT('',(0.,0.));
#4806 = VECTOR('',#4807,1.);
#4807 = DIRECTION('',(0.,-1.));
#4808 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4809 = PCURVE('',#3104,#4810);
#4810 = DEFINITIONAL_REPRESENTATION('',(#4811),#4815);
#4811 = LINE('',#4812,#4813);
#4812 = CARTESIAN_POINT('',(6.28318530718,0.));
#4813 = VECTOR('',#4814,1.);
#4814 = DIRECTION('',(0.,-1.));
#4815 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4816 = ORIENTED_EDGE('',*,*,#4817,.T.);
#4817 = EDGE_CURVE('',#4795,#4795,#4818,.T.);
#4818 = SURFACE_CURVE('',#4819,(#4824,#4831),.PCURVE_S1.);
#4819 = CIRCLE('',#4820,1.475);
#4820 = AXIS2_PLACEMENT_3D('',#4821,#4822,#4823);
#4821 = CARTESIAN_POINT('',(0.,-3.273497,11.));
#4822 = DIRECTION('',(0.,0.,-1.));
#4823 = DIRECTION('',(1.,0.,0.));
#4824 = PCURVE('',#3104,#4825);
#4825 = DEFINITIONAL_REPRESENTATION('',(#4826),#4830);
#4826 = LINE('',#4827,#4828);
#4827 = CARTESIAN_POINT('',(0.,-4.));
#4828 = VECTOR('',#4829,1.);
#4829 = DIRECTION('',(1.,0.));
#4830 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4831 = PCURVE('',#4832,#4837);
#4832 = PLANE('',#4833);
#4833 = AXIS2_PLACEMENT_3D('',#4834,#4835,#4836);
#4834 = CARTESIAN_POINT('',(-3.5E-16,-3.273497,11.));
#4835 = DIRECTION('',(-0.,-0.,-1.));
#4836 = DIRECTION('',(-1.,0.,0.));
#4837 = DEFINITIONAL_REPRESENTATION('',(#4838),#4842);
#4838 = CIRCLE('',#4839,1.475);
#4839 = AXIS2_PLACEMENT_2D('',#4840,#4841);
#4840 = CARTESIAN_POINT('',(-3.5E-16,-7.11E-15));
#4841 = DIRECTION('',(-1.,0.));
#4842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4843 = ORIENTED_EDGE('',*,*,#4794,.F.);
#4844 = ORIENTED_EDGE('',*,*,#3083,.F.);
#4845 = ADVANCED_FACE('',(#4846),#3135,.F.);
#4846 = FACE_BOUND('',#4847,.F.);
#4847 = EDGE_LOOP('',(#4848,#4849,#4870,#4871));
#4848 = ORIENTED_EDGE('',*,*,#3495,.F.);
#4849 = ORIENTED_EDGE('',*,*,#4850,.T.);
#4850 = EDGE_CURVE('',#3496,#3119,#4851,.T.);
#4851 = SEAM_CURVE('',#4852,(#4856,#4863),.PCURVE_S1.);
#4852 = LINE('',#4853,#4854);
#4853 = CARTESIAN_POINT('',(43.5,-71.,22.));
#4854 = VECTOR('',#4855,1.);
#4855 = DIRECTION('',(-0.,-0.,-1.));
#4856 = PCURVE('',#3135,#4857);
#4857 = DEFINITIONAL_REPRESENTATION('',(#4858),#4862);
#4858 = LINE('',#4859,#4860);
#4859 = CARTESIAN_POINT('',(6.28318530718,0.));
#4860 = VECTOR('',#4861,1.);
#4861 = DIRECTION('',(0.,-1.));
#4862 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4863 = PCURVE('',#3135,#4864);
#4864 = DEFINITIONAL_REPRESENTATION('',(#4865),#4869);
#4865 = LINE('',#4866,#4867);
#4866 = CARTESIAN_POINT('',(0.,0.));
#4867 = VECTOR('',#4868,1.);
#4868 = DIRECTION('',(0.,-1.));
#4869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4870 = ORIENTED_EDGE('',*,*,#3118,.T.);
#4871 = ORIENTED_EDGE('',*,*,#4850,.F.);
#4872 = ADVANCED_FACE('',(#4873),#3166,.F.);
#4873 = FACE_BOUND('',#4874,.T.);
#4874 = EDGE_LOOP('',(#4875,#4896,#4897,#4898));
#4875 = ORIENTED_EDGE('',*,*,#4876,.T.);
#4876 = EDGE_CURVE('',#3150,#3553,#4877,.T.);
#4877 = SEAM_CURVE('',#4878,(#4882,#4889),.PCURVE_S1.);
#4878 = LINE('',#4879,#4880);
#4879 = CARTESIAN_POINT('',(3.25,-54.,7.));
#4880 = VECTOR('',#4881,1.);
#4881 = DIRECTION('',(0.,0.,1.));
#4882 = PCURVE('',#3166,#4883);
#4883 = DEFINITIONAL_REPRESENTATION('',(#4884),#4888);
#4884 = LINE('',#4885,#4886);
#4885 = CARTESIAN_POINT('',(-0.,0.));
#4886 = VECTOR('',#4887,1.);
#4887 = DIRECTION('',(-0.,-1.));
#4888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4889 = PCURVE('',#3166,#4890);
#4890 = DEFINITIONAL_REPRESENTATION('',(#4891),#4895);
#4891 = LINE('',#4892,#4893);
#4892 = CARTESIAN_POINT('',(-6.28318530718,0.));
#4893 = VECTOR('',#4894,1.);
#4894 = DIRECTION('',(-0.,-1.));
#4895 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4896 = ORIENTED_EDGE('',*,*,#3552,.T.);
#4897 = ORIENTED_EDGE('',*,*,#4876,.F.);
#4898 = ORIENTED_EDGE('',*,*,#3149,.F.);
#4899 = ADVANCED_FACE('',(#4900),#3197,.F.);
#4900 = FACE_BOUND('',#4901,.F.);
#4901 = EDGE_LOOP('',(#4902,#4903,#4924,#4925));
#4902 = ORIENTED_EDGE('',*,*,#3578,.F.);
#4903 = ORIENTED_EDGE('',*,*,#4904,.T.);
#4904 = EDGE_CURVE('',#3579,#3181,#4905,.T.);
#4905 = SEAM_CURVE('',#4906,(#4910,#4917),.PCURVE_S1.);
#4906 = LINE('',#4907,#4908);
#4907 = CARTESIAN_POINT('',(-21.5,-71.,22.));
#4908 = VECTOR('',#4909,1.);
#4909 = DIRECTION('',(-0.,-0.,-1.));
#4910 = PCURVE('',#3197,#4911);
#4911 = DEFINITIONAL_REPRESENTATION('',(#4912),#4916);
#4912 = LINE('',#4913,#4914);
#4913 = CARTESIAN_POINT('',(6.28318530718,0.));
#4914 = VECTOR('',#4915,1.);
#4915 = DIRECTION('',(0.,-1.));
#4916 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4917 = PCURVE('',#3197,#4918);
#4918 = DEFINITIONAL_REPRESENTATION('',(#4919),#4923);
#4919 = LINE('',#4920,#4921);
#4920 = CARTESIAN_POINT('',(0.,0.));
#4921 = VECTOR('',#4922,1.);
#4922 = DIRECTION('',(0.,-1.));
#4923 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4924 = ORIENTED_EDGE('',*,*,#3180,.T.);
#4925 = ORIENTED_EDGE('',*,*,#4904,.F.);
#4926 = ADVANCED_FACE('',(#4927),#3232,.F.);
#4927 = FACE_BOUND('',#4928,.F.);
#4928 = EDGE_LOOP('',(#4929,#4952,#4979,#4980));
#4929 = ORIENTED_EDGE('',*,*,#4930,.T.);
#4930 = EDGE_CURVE('',#3212,#4931,#4933,.T.);
#4931 = VERTEX_POINT('',#4932);
#4932 = CARTESIAN_POINT('',(-30.606276,-93.287605,11.));
#4933 = SEAM_CURVE('',#4934,(#4938,#4945),.PCURVE_S1.);
#4934 = LINE('',#4935,#4936);
#4935 = CARTESIAN_POINT('',(-30.606276,-93.287605,7.));
#4936 = VECTOR('',#4937,1.);
#4937 = DIRECTION('',(0.,0.,1.));
#4938 = PCURVE('',#3232,#4939);
#4939 = DEFINITIONAL_REPRESENTATION('',(#4940),#4944);
#4940 = LINE('',#4941,#4942);
#4941 = CARTESIAN_POINT('',(0.,0.));
#4942 = VECTOR('',#4943,1.);
#4943 = DIRECTION('',(0.,-1.));
#4944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4945 = PCURVE('',#3232,#4946);
#4946 = DEFINITIONAL_REPRESENTATION('',(#4947),#4951);
#4947 = LINE('',#4948,#4949);
#4948 = CARTESIAN_POINT('',(6.28318530718,0.));
#4949 = VECTOR('',#4950,1.);
#4950 = DIRECTION('',(0.,-1.));
#4951 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4952 = ORIENTED_EDGE('',*,*,#4953,.T.);
#4953 = EDGE_CURVE('',#4931,#4931,#4954,.T.);
#4954 = SURFACE_CURVE('',#4955,(#4960,#4967),.PCURVE_S1.);
#4955 = CIRCLE('',#4956,1.475);
#4956 = AXIS2_PLACEMENT_3D('',#4957,#4958,#4959);
#4957 = CARTESIAN_POINT('',(-32.081276,-93.287605,11.));
#4958 = DIRECTION('',(0.,0.,-1.));
#4959 = DIRECTION('',(1.,0.,0.));
#4960 = PCURVE('',#3232,#4961);
#4961 = DEFINITIONAL_REPRESENTATION('',(#4962),#4966);
#4962 = LINE('',#4963,#4964);
#4963 = CARTESIAN_POINT('',(0.,-4.));
#4964 = VECTOR('',#4965,1.);
#4965 = DIRECTION('',(1.,0.));
#4966 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4967 = PCURVE('',#4968,#4973);
#4968 = PLANE('',#4969);
#4969 = AXIS2_PLACEMENT_3D('',#4970,#4971,#4972);
#4970 = CARTESIAN_POINT('',(-32.081276,-93.287605,11.));
#4971 = DIRECTION('',(-0.,-0.,-1.));
#4972 = DIRECTION('',(-1.,0.,0.));
#4973 = DEFINITIONAL_REPRESENTATION('',(#4974),#4978);
#4974 = CIRCLE('',#4975,1.475);
#4975 = AXIS2_PLACEMENT_2D('',#4976,#4977);
#4976 = CARTESIAN_POINT('',(0.,-7.11E-15));
#4977 = DIRECTION('',(-1.,0.));
#4978 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4979 = ORIENTED_EDGE('',*,*,#4930,.F.);
#4980 = ORIENTED_EDGE('',*,*,#3211,.F.);
#4981 = ADVANCED_FACE('',(#4982),#3267,.F.);
#4982 = FACE_BOUND('',#4983,.F.);
#4983 = EDGE_LOOP('',(#4984,#5007,#5034,#5035));
#4984 = ORIENTED_EDGE('',*,*,#4985,.T.);
#4985 = EDGE_CURVE('',#3247,#4986,#4988,.T.);
#4986 = VERTEX_POINT('',#4987);
#4987 = CARTESIAN_POINT('',(33.556276,-93.287605,11.));
#4988 = SEAM_CURVE('',#4989,(#4993,#5000),.PCURVE_S1.);
#4989 = LINE('',#4990,#4991);
#4990 = CARTESIAN_POINT('',(33.556276,-93.287605,7.));
#4991 = VECTOR('',#4992,1.);
#4992 = DIRECTION('',(0.,0.,1.));
#4993 = PCURVE('',#3267,#4994);
#4994 = DEFINITIONAL_REPRESENTATION('',(#4995),#4999);
#4995 = LINE('',#4996,#4997);
#4996 = CARTESIAN_POINT('',(0.,0.));
#4997 = VECTOR('',#4998,1.);
#4998 = DIRECTION('',(0.,-1.));
#4999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5000 = PCURVE('',#3267,#5001);
#5001 = DEFINITIONAL_REPRESENTATION('',(#5002),#5006);
#5002 = LINE('',#5003,#5004);
#5003 = CARTESIAN_POINT('',(6.28318530718,0.));
#5004 = VECTOR('',#5005,1.);
#5005 = DIRECTION('',(0.,-1.));
#5006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5007 = ORIENTED_EDGE('',*,*,#5008,.T.);
#5008 = EDGE_CURVE('',#4986,#4986,#5009,.T.);
#5009 = SURFACE_CURVE('',#5010,(#5015,#5022),.PCURVE_S1.);
#5010 = CIRCLE('',#5011,1.475);
#5011 = AXIS2_PLACEMENT_3D('',#5012,#5013,#5014);
#5012 = CARTESIAN_POINT('',(32.081276,-93.287605,11.));
#5013 = DIRECTION('',(0.,0.,-1.));
#5014 = DIRECTION('',(1.,0.,0.));
#5015 = PCURVE('',#3267,#5016);
#5016 = DEFINITIONAL_REPRESENTATION('',(#5017),#5021);
#5017 = LINE('',#5018,#5019);
#5018 = CARTESIAN_POINT('',(0.,-4.));
#5019 = VECTOR('',#5020,1.);
#5020 = DIRECTION('',(1.,0.));
#5021 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5022 = PCURVE('',#5023,#5028);
#5023 = PLANE('',#5024);
#5024 = AXIS2_PLACEMENT_3D('',#5025,#5026,#5027);
#5025 = CARTESIAN_POINT('',(32.081276,-93.287605,11.));
#5026 = DIRECTION('',(-0.,-0.,-1.));
#5027 = DIRECTION('',(-1.,0.,0.));
#5028 = DEFINITIONAL_REPRESENTATION('',(#5029),#5033);
#5029 = CIRCLE('',#5030,1.475);
#5030 = AXIS2_PLACEMENT_2D('',#5031,#5032);
#5031 = CARTESIAN_POINT('',(0.,0.));
#5032 = DIRECTION('',(-1.,0.));
#5033 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5034 = ORIENTED_EDGE('',*,*,#4985,.F.);
#5035 = ORIENTED_EDGE('',*,*,#3246,.F.);
#5036 = ADVANCED_FACE('',(#5037),#3302,.F.);
#5037 = FACE_BOUND('',#5038,.F.);
#5038 = EDGE_LOOP('',(#5039,#5062,#5089,#5090));
#5039 = ORIENTED_EDGE('',*,*,#5040,.T.);
#5040 = EDGE_CURVE('',#3282,#5041,#5043,.T.);
#5041 = VERTEX_POINT('',#5042);
#5042 = CARTESIAN_POINT('',(-45.328799,-54.,11.));
#5043 = SEAM_CURVE('',#5044,(#5048,#5055),.PCURVE_S1.);
#5044 = LINE('',#5045,#5046);
#5045 = CARTESIAN_POINT('',(-45.328799,-54.,7.));
#5046 = VECTOR('',#5047,1.);
#5047 = DIRECTION('',(0.,0.,1.));
#5048 = PCURVE('',#3302,#5049);
#5049 = DEFINITIONAL_REPRESENTATION('',(#5050),#5054);
#5050 = LINE('',#5051,#5052);
#5051 = CARTESIAN_POINT('',(0.,0.));
#5052 = VECTOR('',#5053,1.);
#5053 = DIRECTION('',(0.,-1.));
#5054 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5055 = PCURVE('',#3302,#5056);
#5056 = DEFINITIONAL_REPRESENTATION('',(#5057),#5061);
#5057 = LINE('',#5058,#5059);
#5058 = CARTESIAN_POINT('',(6.28318530718,0.));
#5059 = VECTOR('',#5060,1.);
#5060 = DIRECTION('',(0.,-1.));
#5061 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5062 = ORIENTED_EDGE('',*,*,#5063,.T.);
#5063 = EDGE_CURVE('',#5041,#5041,#5064,.T.);
#5064 = SURFACE_CURVE('',#5065,(#5070,#5077),.PCURVE_S1.);
#5065 = CIRCLE('',#5066,1.475);
#5066 = AXIS2_PLACEMENT_3D('',#5067,#5068,#5069);
#5067 = CARTESIAN_POINT('',(-46.803799,-54.,11.));
#5068 = DIRECTION('',(0.,0.,-1.));
#5069 = DIRECTION('',(1.,0.,0.));
#5070 = PCURVE('',#3302,#5071);
#5071 = DEFINITIONAL_REPRESENTATION('',(#5072),#5076);
#5072 = LINE('',#5073,#5074);
#5073 = CARTESIAN_POINT('',(0.,-4.));
#5074 = VECTOR('',#5075,1.);
#5075 = DIRECTION('',(1.,0.));
#5076 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5077 = PCURVE('',#5078,#5083);
#5078 = PLANE('',#5079);
#5079 = AXIS2_PLACEMENT_3D('',#5080,#5081,#5082);
#5080 = CARTESIAN_POINT('',(-46.803799,-54.,11.));
#5081 = DIRECTION('',(-0.,-0.,-1.));
#5082 = DIRECTION('',(-1.,0.,0.));
#5083 = DEFINITIONAL_REPRESENTATION('',(#5084),#5088);
#5084 = CIRCLE('',#5085,1.475);
#5085 = AXIS2_PLACEMENT_2D('',#5086,#5087);
#5086 = CARTESIAN_POINT('',(0.,1.1E-16));
#5087 = DIRECTION('',(-1.,0.));
#5088 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5089 = ORIENTED_EDGE('',*,*,#5040,.F.);
#5090 = ORIENTED_EDGE('',*,*,#3281,.F.);
#5091 = ADVANCED_FACE('',(#5092),#3337,.F.);
#5092 = FACE_BOUND('',#5093,.F.);
#5093 = EDGE_LOOP('',(#5094,#5117,#5144,#5145));
#5094 = ORIENTED_EDGE('',*,*,#5095,.T.);
#5095 = EDGE_CURVE('',#3317,#5096,#5098,.T.);
#5096 = VERTEX_POINT('',#5097);
#5097 = CARTESIAN_POINT('',(48.278799,-54.,11.));
#5098 = SEAM_CURVE('',#5099,(#5103,#5110),.PCURVE_S1.);
#5099 = LINE('',#5100,#5101);
#5100 = CARTESIAN_POINT('',(48.278799,-54.,7.));
#5101 = VECTOR('',#5102,1.);
#5102 = DIRECTION('',(0.,0.,1.));
#5103 = PCURVE('',#3337,#5104);
#5104 = DEFINITIONAL_REPRESENTATION('',(#5105),#5109);
#5105 = LINE('',#5106,#5107);
#5106 = CARTESIAN_POINT('',(0.,0.));
#5107 = VECTOR('',#5108,1.);
#5108 = DIRECTION('',(0.,-1.));
#5109 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5110 = PCURVE('',#3337,#5111);
#5111 = DEFINITIONAL_REPRESENTATION('',(#5112),#5116);
#5112 = LINE('',#5113,#5114);
#5113 = CARTESIAN_POINT('',(6.28318530718,0.));
#5114 = VECTOR('',#5115,1.);
#5115 = DIRECTION('',(0.,-1.));
#5116 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5117 = ORIENTED_EDGE('',*,*,#5118,.T.);
#5118 = EDGE_CURVE('',#5096,#5096,#5119,.T.);
#5119 = SURFACE_CURVE('',#5120,(#5125,#5132),.PCURVE_S1.);
#5120 = CIRCLE('',#5121,1.475);
#5121 = AXIS2_PLACEMENT_3D('',#5122,#5123,#5124);
#5122 = CARTESIAN_POINT('',(46.803799,-54.,11.));
#5123 = DIRECTION('',(0.,0.,-1.));
#5124 = DIRECTION('',(1.,0.,0.));
#5125 = PCURVE('',#3337,#5126);
#5126 = DEFINITIONAL_REPRESENTATION('',(#5127),#5131);
#5127 = LINE('',#5128,#5129);
#5128 = CARTESIAN_POINT('',(0.,-4.));
#5129 = VECTOR('',#5130,1.);
#5130 = DIRECTION('',(1.,0.));
#5131 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5132 = PCURVE('',#5133,#5138);
#5133 = PLANE('',#5134);
#5134 = AXIS2_PLACEMENT_3D('',#5135,#5136,#5137);
#5135 = CARTESIAN_POINT('',(46.803799,-54.,11.));
#5136 = DIRECTION('',(-0.,-0.,-1.));
#5137 = DIRECTION('',(-1.,0.,0.));
#5138 = DEFINITIONAL_REPRESENTATION('',(#5139),#5143);
#5139 = CIRCLE('',#5140,1.475);
#5140 = AXIS2_PLACEMENT_2D('',#5141,#5142);
#5141 = CARTESIAN_POINT('',(7.11E-15,1.1E-16));
#5142 = DIRECTION('',(-1.,0.));
#5143 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5144 = ORIENTED_EDGE('',*,*,#5095,.F.);
#5145 = ORIENTED_EDGE('',*,*,#3316,.F.);
#5146 = ADVANCED_FACE('',(#5147),#3538,.T.);
#5147 = FACE_BOUND('',#5148,.F.);
#5148 = EDGE_LOOP('',(#5149,#5172,#5199,#5200));
#5149 = ORIENTED_EDGE('',*,*,#5150,.T.);
#5150 = EDGE_CURVE('',#3522,#5151,#5153,.T.);
#5151 = VERTEX_POINT('',#5152);
#5152 = CARTESIAN_POINT('',(-46.175,-54.,25.6));
#5153 = SEAM_CURVE('',#5154,(#5158,#5165),.PCURVE_S1.);
#5154 = LINE('',#5155,#5156);
#5155 = CARTESIAN_POINT('',(-46.175,-54.,22.));
#5156 = VECTOR('',#5157,1.);
#5157 = DIRECTION('',(0.,0.,1.));
#5158 = PCURVE('',#3538,#5159);
#5159 = DEFINITIONAL_REPRESENTATION('',(#5160),#5164);
#5160 = LINE('',#5161,#5162);
#5161 = CARTESIAN_POINT('',(-0.,0.));
#5162 = VECTOR('',#5163,1.);
#5163 = DIRECTION('',(-0.,-1.));
#5164 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5165 = PCURVE('',#3538,#5166);
#5166 = DEFINITIONAL_REPRESENTATION('',(#5167),#5171);
#5167 = LINE('',#5168,#5169);
#5168 = CARTESIAN_POINT('',(-6.28318530718,0.));
#5169 = VECTOR('',#5170,1.);
#5170 = DIRECTION('',(-0.,-1.));
#5171 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5172 = ORIENTED_EDGE('',*,*,#5173,.T.);
#5173 = EDGE_CURVE('',#5151,#5151,#5174,.T.);
#5174 = SURFACE_CURVE('',#5175,(#5180,#5187),.PCURVE_S1.);
#5175 = CIRCLE('',#5176,1.825);
#5176 = AXIS2_PLACEMENT_3D('',#5177,#5178,#5179);
#5177 = CARTESIAN_POINT('',(-48.,-54.,25.6));
#5178 = DIRECTION('',(0.,0.,1.));
#5179 = DIRECTION('',(1.,0.,0.));
#5180 = PCURVE('',#3538,#5181);
#5181 = DEFINITIONAL_REPRESENTATION('',(#5182),#5186);
#5182 = LINE('',#5183,#5184);
#5183 = CARTESIAN_POINT('',(-0.,-3.6));
#5184 = VECTOR('',#5185,1.);
#5185 = DIRECTION('',(-1.,0.));
#5186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5187 = PCURVE('',#5188,#5193);
#5188 = PLANE('',#5189);
#5189 = AXIS2_PLACEMENT_3D('',#5190,#5191,#5192);
#5190 = CARTESIAN_POINT('',(-48.,-54.,25.6));
#5191 = DIRECTION('',(0.,0.,1.));
#5192 = DIRECTION('',(1.,0.,0.));
#5193 = DEFINITIONAL_REPRESENTATION('',(#5194),#5198);
#5194 = CIRCLE('',#5195,1.825);
#5195 = AXIS2_PLACEMENT_2D('',#5196,#5197);
#5196 = CARTESIAN_POINT('',(0.,-2.1E-16));
#5197 = DIRECTION('',(1.,0.));
#5198 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5199 = ORIENTED_EDGE('',*,*,#5150,.F.);
#5200 = ORIENTED_EDGE('',*,*,#3521,.F.);
#5201 = ADVANCED_FACE('',(#5202),#3621,.F.);
#5202 = FACE_BOUND('',#5203,.F.);
#5203 = EDGE_LOOP('',(#5204,#5205,#5228,#5250));
#5204 = ORIENTED_EDGE('',*,*,#3604,.F.);
#5205 = ORIENTED_EDGE('',*,*,#5206,.T.);
#5206 = EDGE_CURVE('',#3605,#5207,#5209,.T.);
#5207 = VERTEX_POINT('',#5208);
#5208 = CARTESIAN_POINT('',(11.,-84.,17.));
#5209 = SEAM_CURVE('',#5210,(#5214,#5221),.PCURVE_S1.);
#5210 = LINE('',#5211,#5212);
#5211 = CARTESIAN_POINT('',(11.,-84.,22.));
#5212 = VECTOR('',#5213,1.);
#5213 = DIRECTION('',(-0.,-0.,-1.));
#5214 = PCURVE('',#3621,#5215);
#5215 = DEFINITIONAL_REPRESENTATION('',(#5216),#5220);
#5216 = LINE('',#5217,#5218);
#5217 = CARTESIAN_POINT('',(6.28318530718,0.));
#5218 = VECTOR('',#5219,1.);
#5219 = DIRECTION('',(0.,-1.));
#5220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5221 = PCURVE('',#3621,#5222);
#5222 = DEFINITIONAL_REPRESENTATION('',(#5223),#5227);
#5223 = LINE('',#5224,#5225);
#5224 = CARTESIAN_POINT('',(0.,0.));
#5225 = VECTOR('',#5226,1.);
#5226 = DIRECTION('',(0.,-1.));
#5227 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5228 = ORIENTED_EDGE('',*,*,#5229,.T.);
#5229 = EDGE_CURVE('',#5207,#5207,#5230,.T.);
#5230 = SURFACE_CURVE('',#5231,(#5236,#5243),.PCURVE_S1.);
#5231 = CIRCLE('',#5232,11.);
#5232 = AXIS2_PLACEMENT_3D('',#5233,#5234,#5235);
#5233 = CARTESIAN_POINT('',(0.,-84.,17.));
#5234 = DIRECTION('',(0.,0.,1.));
#5235 = DIRECTION('',(1.,0.,0.));
#5236 = PCURVE('',#3621,#5237);
#5237 = DEFINITIONAL_REPRESENTATION('',(#5238),#5242);
#5238 = LINE('',#5239,#5240);
#5239 = CARTESIAN_POINT('',(0.,-5.));
#5240 = VECTOR('',#5241,1.);
#5241 = DIRECTION('',(1.,0.));
#5242 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5243 = PCURVE('',#4100,#5244);
#5244 = DEFINITIONAL_REPRESENTATION('',(#5245),#5249);
#5245 = CIRCLE('',#5246,11.);
#5246 = AXIS2_PLACEMENT_2D('',#5247,#5248);
#5247 = CARTESIAN_POINT('',(0.,5.));
#5248 = DIRECTION('',(1.,0.));
#5249 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5250 = ORIENTED_EDGE('',*,*,#5206,.F.);
#5251 = ADVANCED_FACE('',(#5252),#3652,.T.);
#5252 = FACE_BOUND('',#5253,.F.);
#5253 = EDGE_LOOP('',(#5254,#5277,#5304,#5305));
#5254 = ORIENTED_EDGE('',*,*,#5255,.T.);
#5255 = EDGE_CURVE('',#3636,#5256,#5258,.T.);
#5256 = VERTEX_POINT('',#5257);
#5257 = CARTESIAN_POINT('',(49.825,-54.,25.6));
#5258 = SEAM_CURVE('',#5259,(#5263,#5270),.PCURVE_S1.);
#5259 = LINE('',#5260,#5261);
#5260 = CARTESIAN_POINT('',(49.825,-54.,22.));
#5261 = VECTOR('',#5262,1.);
#5262 = DIRECTION('',(0.,0.,1.));
#5263 = PCURVE('',#3652,#5264);
#5264 = DEFINITIONAL_REPRESENTATION('',(#5265),#5269);
#5265 = LINE('',#5266,#5267);
#5266 = CARTESIAN_POINT('',(-0.,0.));
#5267 = VECTOR('',#5268,1.);
#5268 = DIRECTION('',(-0.,-1.));
#5269 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5270 = PCURVE('',#3652,#5271);
#5271 = DEFINITIONAL_REPRESENTATION('',(#5272),#5276);
#5272 = LINE('',#5273,#5274);
#5273 = CARTESIAN_POINT('',(-6.28318530718,0.));
#5274 = VECTOR('',#5275,1.);
#5275 = DIRECTION('',(-0.,-1.));
#5276 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5277 = ORIENTED_EDGE('',*,*,#5278,.T.);
#5278 = EDGE_CURVE('',#5256,#5256,#5279,.T.);
#5279 = SURFACE_CURVE('',#5280,(#5285,#5292),.PCURVE_S1.);
#5280 = CIRCLE('',#5281,1.825);
#5281 = AXIS2_PLACEMENT_3D('',#5282,#5283,#5284);
#5282 = CARTESIAN_POINT('',(48.,-54.,25.6));
#5283 = DIRECTION('',(0.,0.,1.));
#5284 = DIRECTION('',(1.,0.,0.));
#5285 = PCURVE('',#3652,#5286);
#5286 = DEFINITIONAL_REPRESENTATION('',(#5287),#5291);
#5287 = LINE('',#5288,#5289);
#5288 = CARTESIAN_POINT('',(-0.,-3.6));
#5289 = VECTOR('',#5290,1.);
#5290 = DIRECTION('',(-1.,0.));
#5291 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5292 = PCURVE('',#5293,#5298);
#5293 = PLANE('',#5294);
#5294 = AXIS2_PLACEMENT_3D('',#5295,#5296,#5297);
#5295 = CARTESIAN_POINT('',(48.,-54.,25.6));
#5296 = DIRECTION('',(0.,0.,1.));
#5297 = DIRECTION('',(1.,0.,0.));
#5298 = DEFINITIONAL_REPRESENTATION('',(#5299),#5303);
#5299 = CIRCLE('',#5300,1.825);
#5300 = AXIS2_PLACEMENT_2D('',#5301,#5302);
#5301 = CARTESIAN_POINT('',(0.,-2.1E-16));
#5302 = DIRECTION('',(1.,0.));
#5303 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5304 = ORIENTED_EDGE('',*,*,#5255,.F.);
#5305 = ORIENTED_EDGE('',*,*,#3635,.F.);
#5306 = ADVANCED_FACE('',(#5307,#5310),#3785,.T.);
#5307 = FACE_BOUND('',#5308,.T.);
#5308 = EDGE_LOOP('',(#5309));
#5309 = ORIENTED_EDGE('',*,*,#3770,.T.);
#5310 = FACE_BOUND('',#5311,.T.);
#5311 = EDGE_LOOP('',(#5312));
#5312 = ORIENTED_EDGE('',*,*,#5313,.F.);
#5313 = EDGE_CURVE('',#5314,#5314,#5316,.T.);
#5314 = VERTEX_POINT('',#5315);
#5315 = CARTESIAN_POINT('',(6.55,4.,48.));
#5316 = SURFACE_CURVE('',#5317,(#5322,#5329),.PCURVE_S1.);
#5317 = CIRCLE('',#5318,6.55);
#5318 = AXIS2_PLACEMENT_3D('',#5319,#5320,#5321);
#5319 = CARTESIAN_POINT('',(0.,4.,48.));
#5320 = DIRECTION('',(0.,-1.,-2.2E-16));
#5321 = DIRECTION('',(1.,0.,0.));
#5322 = PCURVE('',#3785,#5323);
#5323 = DEFINITIONAL_REPRESENTATION('',(#5324),#5328);
#5324 = CIRCLE('',#5325,6.55);
#5325 = AXIS2_PLACEMENT_2D('',#5326,#5327);
#5326 = CARTESIAN_POINT('',(7.11E-15,1.31E-15));
#5327 = DIRECTION('',(0.,1.));
#5328 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5329 = PCURVE('',#4009,#5330);
#5330 = DEFINITIONAL_REPRESENTATION('',(#5331),#5335);
#5331 = LINE('',#5332,#5333);
#5332 = CARTESIAN_POINT('',(0.,-58.));
#5333 = VECTOR('',#5334,1.);
#5334 = DIRECTION('',(1.,0.));
#5335 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5336 = ADVANCED_FACE('',(#5337),#4009,.F.);
#5337 = FACE_BOUND('',#5338,.F.);
#5338 = EDGE_LOOP('',(#5339,#5360,#5361,#5362));
#5339 = ORIENTED_EDGE('',*,*,#5340,.F.);
#5340 = EDGE_CURVE('',#5314,#3989,#5341,.T.);
#5341 = SEAM_CURVE('',#5342,(#5346,#5353),.PCURVE_S1.);
#5342 = LINE('',#5343,#5344);
#5343 = CARTESIAN_POINT('',(6.55,-54.,48.));
#5344 = VECTOR('',#5345,1.);
#5345 = DIRECTION('',(0.,1.,2.2E-16));
#5346 = PCURVE('',#4009,#5347);
#5347 = DEFINITIONAL_REPRESENTATION('',(#5348),#5352);
#5348 = LINE('',#5349,#5350);
#5349 = CARTESIAN_POINT('',(6.28318530718,0.));
#5350 = VECTOR('',#5351,1.);
#5351 = DIRECTION('',(0.,-1.));
#5352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5353 = PCURVE('',#4009,#5354);
#5354 = DEFINITIONAL_REPRESENTATION('',(#5355),#5359);
#5355 = LINE('',#5356,#5357);
#5356 = CARTESIAN_POINT('',(0.,0.));
#5357 = VECTOR('',#5358,1.);
#5358 = DIRECTION('',(0.,-1.));
#5359 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5360 = ORIENTED_EDGE('',*,*,#5313,.F.);
#5361 = ORIENTED_EDGE('',*,*,#5340,.T.);
#5362 = ORIENTED_EDGE('',*,*,#3988,.T.);
#5363 = ADVANCED_FACE('',(#5364,#5370),#4100,.F.);
#5364 = FACE_BOUND('',#5365,.F.);
#5365 = EDGE_LOOP('',(#5366,#5367,#5368,#5369));
#5366 = ORIENTED_EDGE('',*,*,#4215,.T.);
#5367 = ORIENTED_EDGE('',*,*,#4265,.T.);
#5368 = ORIENTED_EDGE('',*,*,#4084,.T.);
#5369 = ORIENTED_EDGE('',*,*,#4160,.T.);
#5370 = FACE_BOUND('',#5371,.F.);
#5371 = EDGE_LOOP('',(#5372));
#5372 = ORIENTED_EDGE('',*,*,#5229,.F.);
#5373 = ADVANCED_FACE('',(#5374),#4366,.F.);
#5374 = FACE_BOUND('',#5375,.F.);
#5375 = EDGE_LOOP('',(#5376,#5377,#5378,#5379,#5380,#5381,#5382,#5383,
    #5384));
#5376 = ORIENTED_EDGE('',*,*,#4499,.T.);
#5377 = ORIENTED_EDGE('',*,*,#4596,.T.);
#5378 = ORIENTED_EDGE('',*,*,#4695,.T.);
#5379 = ORIENTED_EDGE('',*,*,#4767,.F.);
#5380 = ORIENTED_EDGE('',*,*,#4668,.T.);
#5381 = ORIENTED_EDGE('',*,*,#4569,.T.);
#5382 = ORIENTED_EDGE('',*,*,#4472,.T.);
#5383 = ORIENTED_EDGE('',*,*,#4349,.F.);
#5384 = ORIENTED_EDGE('',*,*,#4423,.F.);
#5385 = ADVANCED_FACE('',(#5386),#4832,.T.);
#5386 = FACE_BOUND('',#5387,.T.);
#5387 = EDGE_LOOP('',(#5388));
#5388 = ORIENTED_EDGE('',*,*,#4817,.T.);
#5389 = ADVANCED_FACE('',(#5390),#4968,.T.);
#5390 = FACE_BOUND('',#5391,.T.);
#5391 = EDGE_LOOP('',(#5392));
#5392 = ORIENTED_EDGE('',*,*,#4953,.T.);
#5393 = ADVANCED_FACE('',(#5394),#5023,.T.);
#5394 = FACE_BOUND('',#5395,.T.);
#5395 = EDGE_LOOP('',(#5396));
#5396 = ORIENTED_EDGE('',*,*,#5008,.T.);
#5397 = ADVANCED_FACE('',(#5398),#5078,.T.);
#5398 = FACE_BOUND('',#5399,.T.);
#5399 = EDGE_LOOP('',(#5400));
#5400 = ORIENTED_EDGE('',*,*,#5063,.T.);
#5401 = ADVANCED_FACE('',(#5402),#5133,.T.);
#5402 = FACE_BOUND('',#5403,.T.);
#5403 = EDGE_LOOP('',(#5404));
#5404 = ORIENTED_EDGE('',*,*,#5118,.T.);
#5405 = ADVANCED_FACE('',(#5406),#5188,.T.);
#5406 = FACE_BOUND('',#5407,.T.);
#5407 = EDGE_LOOP('',(#5408));
#5408 = ORIENTED_EDGE('',*,*,#5173,.T.);
#5409 = ADVANCED_FACE('',(#5410),#5293,.T.);
#5410 = FACE_BOUND('',#5411,.T.);
#5411 = EDGE_LOOP('',(#5412));
#5412 = ORIENTED_EDGE('',*,*,#5278,.T.);
#5413 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#5417)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#5414,#5415,#5416)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#5414 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#5415 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#5416 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#5417 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#5414,
  'distance_accuracy_value','confusion accuracy');
#5418 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#5419 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #5420),#5413);
#5420 = STYLED_ITEM('color',(#5421),#15);
#5421 = PRESENTATION_STYLE_ASSIGNMENT((#5422,#5428));
#5422 = SURFACE_STYLE_USAGE(.BOTH.,#5423);
#5423 = SURFACE_SIDE_STYLE('',(#5424));
#5424 = SURFACE_STYLE_FILL_AREA(#5425);
#5425 = FILL_AREA_STYLE('',(#5426));
#5426 = FILL_AREA_STYLE_COLOUR('',#5427);
#5427 = COLOUR_RGB('',0.776470601559,0.776470601559,0.776470601559);
#5428 = CURVE_STYLE('',#5429,POSITIVE_LENGTH_MEASURE(0.1),#5427);
#5429 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
