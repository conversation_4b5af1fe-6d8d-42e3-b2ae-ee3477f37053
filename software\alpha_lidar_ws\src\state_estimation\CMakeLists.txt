cmake_minimum_required(VERSION 3.0.2)
project(state_estimation)

#SET(CMAKE_BUILD_TYPE "Debug")
#set(CMAKE_CXX_FLAGS "-std=c++11")
set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -g -pthread")
#set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall -g -pthread")

ADD_COMPILE_OPTIONS(-std=c++14 )
ADD_COMPILE_OPTIONS(-std=c++14 )
set( CMAKE_CXX_FLAGS "-std=c++14 -O3" )

add_definitions(-DROOT_DIR=\"${CMAKE_CURRENT_SOURCE_DIR}/\")

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fexceptions" )
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -pthread -std=c++0x -std=c++14 -fexceptions")

message("Current CPU archtecture: ${CMAKE_SYSTEM_PROCESSOR}")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "(x86)|(X86)|(amd64)|(AMD64)|(aarch64)" )
  include(ProcessorCount)
  ProcessorCount(N)
  message("Processer number:  ${N}")
  if(N GREATER 14)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=12)
    message("core for MP: 14")
  elseif(N GREATER 10)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=8)
    message("core for MP: 8")
  elseif(N GREATER 7)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=6)
    message("core for MP: 6")
  elseif(N GREATER 3)
    add_definitions(-DMP_EN)
    add_definitions(-DMP_PROC_NUM=3)
    message("core for MP: 3")
  else()
    add_definitions(-DMP_PROC_NUM=1)
  endif()
else()
  add_definitions(-DMP_PROC_NUM=1)
endif()

find_package(Boost REQUIRED COMPONENTS timer)

find_package(OpenMP REQUIRED)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}   ${OpenMP_C_FLAGS}")

find_package(PythonLibs REQUIRED)
find_path(MATPLOTLIB_CPP_INCLUDE_DIRS "matplotlibcpp.h")

find_package(catkin REQUIRED COMPONENTS
  geometry_msgs
  nav_msgs
  sensor_msgs
  roscpp
  rospy
  std_msgs
  pcl_ros
  tf
  # livox_ros_driver  # 注释掉如果不使用Livox传感器
  message_generation
  eigen_conversions
)

find_package(Eigen3 REQUIRED)
find_package(PCL 1.8 REQUIRED)
#find_package(GTSAM REQUIRED QUIET)

# GPS support - try to find GeographicLib
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
  pkg_check_modules(GeographicLib QUIET geographic)
endif()

if(NOT GeographicLib_FOUND)
  # Fallback: try to find GeographicLib manually
  find_path(GeographicLib_INCLUDE_DIR GeographicLib/LocalCartesian.hpp)
  find_library(GeographicLib_LIBRARY NAMES geographic Geographic)
  if(GeographicLib_INCLUDE_DIR AND GeographicLib_LIBRARY)
    set(GeographicLib_FOUND TRUE)
    set(GeographicLib_INCLUDE_DIRS ${GeographicLib_INCLUDE_DIR})
    set(GeographicLib_LIBRARIES ${GeographicLib_LIBRARY})
  endif()
endif()

if(GeographicLib_FOUND)
  message(STATUS "Found GeographicLib: ${GeographicLib_LIBRARIES}")
  add_definitions(-DUSE_GPS_INTEGRATION)
else()
  message(WARNING "GeographicLib not found. GPS integration will be disabled.")
endif()

message(Eigen: ${EIGEN3_INCLUDE_DIR})

include_directories(
  ${catkin_INCLUDE_DIRS}
  ${EIGEN3_INCLUDE_DIR}
  ${PCL_INCLUDE_DIRS}
  ${PYTHON_INCLUDE_DIRS}
  include)

# Add GeographicLib include directories if found
if(GeographicLib_FOUND)
  include_directories(${GeographicLib_INCLUDE_DIRS})
endif()

#add_message_files(
#  FILES
#  Pose6D.msg
#)

generate_messages(
 DEPENDENCIES
 geometry_msgs
)

catkin_package(
  CATKIN_DEPENDS geometry_msgs nav_msgs roscpp rospy std_msgs message_runtime
  DEPENDS EIGEN3 PCL
  INCLUDE_DIRS
)

add_executable(${PROJECT_NAME}_node src/voxelMapping.cpp src/preprocess.cpp)
target_link_libraries(${PROJECT_NAME}_node ${catkin_LIBRARIES} ${PCL_LIBRARIES} ${PYTHON_LIBRARIES})
target_include_directories(${PROJECT_NAME}_node PRIVATE ${PYTHON_INCLUDE_DIRS})

# Link GeographicLib if found
if(GeographicLib_FOUND)
  target_link_libraries(${PROJECT_NAME}_node ${GeographicLib_LIBRARIES})
endif()

add_executable(pandar_to_velodyne src/converter/pandar_to_velodyne.cpp)
target_link_libraries(pandar_to_velodyne ${catkin_LIBRARIES} ${PCL_LIBRARIES} ${PYTHON_LIBRARIES})
target_include_directories(pandar_to_velodyne PRIVATE ${PYTHON_INCLUDE_DIRS})

add_executable(rs_to_velodyne src/converter/rs_to_velodyne.cpp)
target_link_libraries(rs_to_velodyne ${catkin_LIBRARIES} ${PCL_LIBRARIES} ${PYTHON_LIBRARIES})
target_include_directories(rs_to_velodyne PRIVATE ${PYTHON_INCLUDE_DIRS})

# 增强版SLAM回环检测集成模块
add_executable(enhanced_slam_loop_closure_integration src/enhanced_slam_loop_closure_integration.cpp)
target_link_libraries(enhanced_slam_loop_closure_integration ${catkin_LIBRARIES} ${PCL_LIBRARIES})

# 强度值保持的PCD保存模块
add_executable(intensity_preserving_pcd_saver src/intensity_preserving_pcd_saver.cpp)
target_link_libraries(intensity_preserving_pcd_saver ${catkin_LIBRARIES} ${PCL_LIBRARIES})

# 自适应参数优化器
add_executable(adaptive_parameter_optimizer src/adaptive_parameter_optimizer.cpp)
target_link_libraries(adaptive_parameter_optimizer ${catkin_LIBRARIES} ${PCL_LIBRARIES})

add_executable(force_start_end_loop_matcher src/force_start_end_loop_matcher.cpp)
target_link_libraries(force_start_end_loop_matcher ${catkin_LIBRARIES} ${PCL_LIBRARIES})

# 安装Python脚本
catkin_install_python(PROGRAMS
  scripts/enhanced_gps_loop_closure_optimizer.py
  scripts/intensity_quality_monitor.py
  scripts/advanced_intensity_analyzer.py
  scripts/performance_dashboard.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
