#!/bin/bash

# 基础SLAM系统启动脚本 - 避免复杂功能

echo "=========================================="
echo "🚀 基础SLAM系统启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/basic_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "检查系统组件..."

# 检查核心节点
if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
    echo "✅ SLAM核心节点可用"
    SLAM_AVAILABLE=true
else
    echo "❌ SLAM核心节点不可用"
    SLAM_AVAILABLE=false
fi

# 检查强度保持模块
if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
    echo "✅ 强度保持模块可用"
    INTENSITY_AVAILABLE=true
else
    echo "⚠️  强度保持模块不可用"
    INTENSITY_AVAILABLE=false
fi

if [ "$SLAM_AVAILABLE" = false ]; then
    echo ""
    echo "❌ 核心SLAM节点不可用，无法启动系统"
    echo "请先运行编译修复脚本: ./quick_fix_compile.sh"
    exit 1
fi

echo ""
echo "启动基础SLAM系统..."

# 方法1: 尝试使用launch文件
if [ -f "src/state_estimation/launch/mapping_robosense.launch" ]; then
    echo "使用launch文件启动..."
    roslaunch state_estimation mapping_robosense.launch &
    LAUNCH_PID=$!
    sleep 5
    
    # 检查launch是否成功
    if ps -p $LAUNCH_PID > /dev/null; then
        echo "✅ Launch文件启动成功"
        LAUNCH_SUCCESS=true
    else
        echo "⚠️  Launch文件启动失败，尝试手动启动"
        LAUNCH_SUCCESS=false
    fi
else
    echo "⚠️  Launch文件不存在，尝试手动启动"
    LAUNCH_SUCCESS=false
fi

# 方法2: 手动启动核心组件
if [ "$LAUNCH_SUCCESS" = false ]; then
    echo "手动启动核心组件..."
    
    # 启动核心SLAM节点
    rosrun state_estimation state_estimation_node &
    sleep 3
    
    echo "✅ 核心SLAM节点已启动"
fi

# 启动强度保持模块
if [ "$INTENSITY_AVAILABLE" = true ]; then
    echo "启动强度保持模块..."
    rosrun state_estimation intensity_preserving_pcd_saver \
        _save_directory:="$OUTPUT_DIR" \
        _save_interval:=10.0 &
    sleep 2
    echo "✅ 强度保持模块已启动"
fi

# 启动简单的智能检测器（如果可用）
if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
    echo "启动智能检测器..."
    rosrun state_estimation intelligent_start_end_detector.py \
        _gps_topic:=/rtk/gnss \
        _departure_threshold:=30.0 \
        _return_threshold:=50.0 \
        _min_trajectory_points:=100 \
        _gps_quality_threshold:=-1 &
    sleep 2
    echo "✅ 智能检测器已启动"
fi

echo ""
echo "=========================================="
echo "🚀 基础SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统状态:"
echo "  输出目录: $OUTPUT_DIR"
echo "  核心SLAM: ✅ 运行中"
if [ "$INTENSITY_AVAILABLE" = true ]; then
    echo "  强度保持: ✅ 运行中"
else
    echo "  强度保持: ❌ 不可用"
fi
echo ""
echo "功能说明:"
echo "✅ 基础SLAM定位和建图"
echo "✅ 点云数据处理"
echo "✅ 轨迹生成"
if [ "$INTENSITY_AVAILABLE" = true ]; then
    echo "✅ 强度值保持"
fi
echo "✅ 智能首尾检测"
echo ""
echo "监控命令:"
echo "  SLAM位姿:    rostopic echo /aft_mapped_to_init"
echo "  点云数据:    rostopic echo /cloud_registered"
echo "  轨迹路径:    rostopic echo /path"
echo "  智能检测:    rostopic echo /intelligent_detector_status"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "查看点云数据:"
echo "  rviz"
echo "  然后添加以下topic:"
echo "    - /cloud_registered (PointCloud2)"
echo "    - /path (Path)"
echo "    - /aft_mapped_to_init (PoseStamped)"
echo ""
echo "输出文件将保存到: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
