#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Viewer
Extract GPS data from ROS bag files and create 3D trajectory visualization
with floating analysis panel on the right side
"""

try:
    import rosbag
    USE_ROSBAG = True
except ImportError:
    try:
        import bagpy
        USE_ROSBAG = False
        print("⚠️  Using bagpy instead of rosbag (Windows compatibility mode)")
    except ImportError:
        print("❌ Error: Need to install rosbag or bagpy")
        print("Please run: pip install bagpy")
        exit(1)

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
import argparse
import sys
import os
from collections import defaultdict
import math
import pandas as pd

class GPS3DTrajectoryViewer:
    def __init__(self):
        self.gps_data = []
        self.trajectory_points = []
        # GPS quality color mapping
        self.quality_colors = {
            -1: 'red',      # NO_FIX: Red
            0:  'green',    # RTK_FIXED: Green (Fixed solution)
            1:  'blue',     # SBAS_FIX: Blue
            2:  'yellow',   # GBAS_FIX: Yellow
            3:  'purple',   # OTHER: Purple
        }
        self.quality_names = {
            -1: "NO_FIX",
            0:  "RTK_FIXED",
            1:  "SBAS_FIX", 
            2:  "GBAS_FIX",
            3:  "OTHER"
        }
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
    def extract_gps_from_bag(self, bag_file, topic_name="/rtk/gnss"):
        """Extract GPS data from bag file"""
        print(f"🔍 Extracting GPS data from {bag_file}...")
        
        if USE_ROSBAG:
            return self._extract_with_rosbag(bag_file, topic_name)
        else:
            return self._extract_with_bagpy(bag_file, topic_name)
    
    def _extract_with_rosbag(self, bag_file, topic_name):
        """Extract GPS data using rosbag"""
        try:
            bag = rosbag.Bag(bag_file, 'r')
            gps_count = 0
            
            for topic, msg, t in bag.read_messages(topics=[topic_name]):
                gps_info = {
                    'timestamp': t.to_sec(),
                    'latitude': msg.latitude,
                    'longitude': msg.longitude,
                    'altitude': msg.altitude,
                    'status': msg.status.status,
                    'service': msg.status.service,
                }
                self.gps_data.append(gps_info)
                gps_count += 1
                
                if gps_count % 500 == 0:
                    print(f"  Extracted {gps_count} GPS points...")
                    
            bag.close()
            print(f"✅ GPS data extraction completed, total {len(self.gps_data)} points")
            
        except Exception as e:
            print(f"❌ Failed to read bag file: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def _extract_with_bagpy(self, bag_file, topic_name):
        """Extract GPS data using bagpy"""
        try:
            bag = bagpy.bagreader(bag_file)
            print(f"📡 Reading topic: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            df = pd.read_csv(gps_csv)
            
            gps_count = 0
            for index, row in df.iterrows():
                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1
                    
                    if gps_count % 500 == 0:
                        print(f"  Extracted {gps_count} GPS points...")
                        
                except (ValueError, KeyError) as e:
                    continue
                    
            print(f"✅ GPS data extraction completed, total {len(self.gps_data)} points")
            
        except Exception as e:
            print(f"❌ Failed to read bag file: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def convert_to_local_coordinates(self):
        """Convert GPS coordinates to local coordinate system"""
        if not self.gps_data:
            return
            
        # Use first valid GPS point as origin
        for gps in self.gps_data:
            if gps['status'] >= 0:
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude'] 
                self.origin_alt = gps['altitude']
                break
                
        if self.origin_lat is None:
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return
                
        print(f"📍 GPS origin: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}, alt={self.origin_alt:.3f}")
        
        # Convert to local coordinates
        for gps in self.gps_data:
            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt
            
            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff
            
            point_info = {
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)
    
    def analyze_gps_quality(self):
        """Analyze GPS quality statistics"""
        if not self.gps_data:
            return {}
            
        status_count = defaultdict(int)
        total_points = len(self.gps_data)
        
        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3
            status_count[status] += 1
            
        # Calculate trajectory statistics
        trajectory_length = 0.0
        start_end_distance = 0.0
        closure_error = 0.0
        
        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])
            
            # Calculate trajectory length
            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                trajectory_length += np.linalg.norm(p2 - p1)
            
            # Calculate start-end distance
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0
        
        return {
            'status_count': status_count,
            'total_points': total_points,
            'trajectory_length': trajectory_length,
            'start_end_distance': start_end_distance,
            'closure_error': closure_error,
            'positions': np.array([p['position'] for p in self.trajectory_points]) if self.trajectory_points else None
        }

    def create_3d_visualization_with_analysis(self):
        """Create 3D visualization with floating analysis panel"""
        if not self.trajectory_points:
            print("❌ No trajectory points to visualize")
            return False

        print("🎨 Creating 3D trajectory visualization with analysis panel...")

        # Analyze GPS quality
        analysis = self.analyze_gps_quality()

        # Create figure with custom layout
        fig = plt.figure(figsize=(18, 10))

        # Create 3D subplot (left side, larger)
        ax = fig.add_subplot(121, projection='3d')
        ax.set_position([0.05, 0.1, 0.6, 0.8])  # [left, bottom, width, height]

        # Group by GPS status
        status_groups = defaultdict(list)
        for i, point in enumerate(self.trajectory_points):
            status = point['status']
            if status not in self.quality_names:
                status = 3
            status_groups[status].append(i)

        # Plot 3D trajectory with different colors for different GPS quality
        for status, indices in status_groups.items():
            if len(indices) < 2:
                continue

            points = [self.trajectory_points[i]['position'] for i in indices]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            z_coords = [p[2] for p in points]

            color = self.quality_colors[status]
            quality_name = self.quality_names[status]

            # RTK fixed solution: thick solid line, others: dashed line
            if status == 0:  # RTK_FIXED
                ax.plot(x_coords, y_coords, z_coords,
                       color=color, linewidth=4, linestyle='-',
                       alpha=0.9, label=f'{quality_name}')
            else:
                ax.plot(x_coords, y_coords, z_coords,
                       color=color, linewidth=2.5, linestyle='--',
                       alpha=0.8, label=f'{quality_name}')

        # Mark start and end points
        if self.trajectory_points:
            start_pos = self.trajectory_points[0]['position']
            end_pos = self.trajectory_points[-1]['position']

            ax.scatter(start_pos[0], start_pos[1], start_pos[2],
                      c='lime', s=300, marker='o', edgecolors='black', linewidth=3,
                      label='Start', alpha=1.0, zorder=10)
            ax.scatter(end_pos[0], end_pos[1], end_pos[2],
                      c='red', s=300, marker='s', edgecolors='black', linewidth=3,
                      label='End', alpha=1.0, zorder=10)

        # Set labels and title
        ax.set_xlabel('X (meters)', fontsize=14, fontweight='bold')
        ax.set_ylabel('Y (meters)', fontsize=14, fontweight='bold')
        ax.set_zlabel('Z (meters)', fontsize=14, fontweight='bold')
        ax.set_title('GPS 3D Trajectory Visualization', fontsize=16, fontweight='bold', pad=20)

        # Add legend
        ax.legend(loc='upper left', fontsize=11, framealpha=0.9)

        # Add grid
        ax.grid(True, alpha=0.3)

        # Set equal aspect ratio
        if analysis['positions'] is not None:
            positions = analysis['positions']
            max_range = np.array([positions[:, 0].max() - positions[:, 0].min(),
                                positions[:, 1].max() - positions[:, 1].min(),
                                positions[:, 2].max() - positions[:, 2].min()]).max() / 2.0

            mid_x = (positions[:, 0].max() + positions[:, 0].min()) * 0.5
            mid_y = (positions[:, 1].max() + positions[:, 1].min()) * 0.5
            mid_z = (positions[:, 2].max() + positions[:, 2].min()) * 0.5

            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)

        # Create analysis panel (right side)
        self._create_analysis_panel(fig, analysis)

        return True

    def _create_analysis_panel(self, fig, analysis):
        """Create floating analysis panel on the right side"""
        # Create text area for analysis results
        ax_text = fig.add_subplot(122)
        ax_text.set_position([0.68, 0.1, 0.3, 0.8])  # [left, bottom, width, height]
        ax_text.axis('off')

        # Prepare analysis text
        analysis_text = self._format_analysis_text(analysis)

        # Add background
        bbox_props = dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8, edgecolor="black")

        # Display analysis text
        ax_text.text(0.05, 0.95, analysis_text, transform=ax_text.transAxes,
                    fontsize=11, verticalalignment='top', horizontalalignment='left',
                    bbox=bbox_props, fontfamily='monospace')

        ax_text.set_title('GPS Trajectory Analysis', fontsize=14, fontweight='bold', pad=20)

    def _format_analysis_text(self, analysis):
        """Format analysis results as text"""
        text_lines = []

        # Header
        text_lines.append("=" * 35)
        text_lines.append("📊 GPS QUALITY ANALYSIS")
        text_lines.append("=" * 35)
        text_lines.append("")

        # GPS Quality Statistics
        text_lines.append("🛰️  GPS Quality Distribution:")
        text_lines.append("-" * 30)
        total_points = analysis['total_points']

        for status, count in sorted(analysis['status_count'].items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "UNKNOWN")
            color = self.quality_colors.get(status, 'gray')
            line_style = "Solid" if status == 0 else "Dashed"
            text_lines.append(f"{quality_name:12}: {count:6,d} pts")
            text_lines.append(f"{'':14} ({percentage:5.1f}%)")
            text_lines.append(f"{'':14} {color.title()} {line_style}")
            text_lines.append("")

        text_lines.append("=" * 35)
        text_lines.append("📏 TRAJECTORY STATISTICS")
        text_lines.append("=" * 35)
        text_lines.append("")

        # Trajectory Statistics
        text_lines.append(f"Total Points:     {total_points:,}")
        text_lines.append(f"Trajectory Length: {analysis['trajectory_length']:.1f} m")
        text_lines.append(f"Start-End Distance: {analysis['start_end_distance']:.1f} m")
        text_lines.append(f"Closure Error:     {analysis['closure_error']:.3f}%")
        text_lines.append("")

        # Coordinate ranges
        if analysis['positions'] is not None:
            positions = analysis['positions']
            text_lines.append("📐 COORDINATE RANGES:")
            text_lines.append("-" * 25)
            text_lines.append(f"X Range: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} m")
            text_lines.append(f"Y Range: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} m")
            text_lines.append(f"Z Range: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} m")
            text_lines.append("")

        # GPS Origin
        text_lines.append("🌍 GPS ORIGIN:")
        text_lines.append("-" * 15)
        text_lines.append(f"Latitude:  {self.origin_lat:.8f}°")
        text_lines.append(f"Longitude: {self.origin_lon:.8f}°")
        text_lines.append(f"Altitude:  {self.origin_alt:.3f} m")
        text_lines.append("")

        # Quality Assessment
        text_lines.append("✅ QUALITY ASSESSMENT:")
        text_lines.append("-" * 22)
        if analysis['closure_error'] < 0.1:
            text_lines.append("Closure Error: EXCELLENT")
        elif analysis['closure_error'] < 0.5:
            text_lines.append("Closure Error: GOOD")
        elif analysis['closure_error'] < 1.0:
            text_lines.append("Closure Error: FAIR")
        else:
            text_lines.append("Closure Error: POOR")

        rtk_percentage = analysis['status_count'].get(0, 0) / total_points * 100
        if rtk_percentage > 95:
            text_lines.append("GPS Quality: EXCELLENT")
        elif rtk_percentage > 80:
            text_lines.append("GPS Quality: GOOD")
        elif rtk_percentage > 50:
            text_lines.append("GPS Quality: FAIR")
        else:
            text_lines.append("GPS Quality: POOR")

        return "\n".join(text_lines)

    def run_analysis(self, bag_file, topic_name="/rtk/gnss"):
        """Run complete GPS 3D trajectory analysis"""
        print("🚀 GPS 3D Trajectory Viewer")
        print("="*50)

        # Check if file exists
        if not os.path.exists(bag_file):
            print(f"❌ Error: bag file does not exist: {bag_file}")
            return False

        # Extract GPS data
        if not self.extract_gps_from_bag(bag_file, topic_name):
            print("❌ GPS data extraction failed")
            return False

        # Convert coordinates
        self.convert_to_local_coordinates()

        # Create 3D visualization with analysis panel
        if self.create_3d_visualization_with_analysis():
            print("\n🖥️  3D trajectory visualization window opened")
            print("💡 Operation tips:")
            print("   - Drag to rotate the 3D view")
            print("   - Scroll to zoom in/out")
            print("   - Right panel shows detailed analysis")
            print("   - Close window to exit")

            plt.show()
        else:
            print("❌ Visualization creation failed")
            return False

        print("\n✅ GPS 3D trajectory analysis completed")
        return True

def main():
    parser = argparse.ArgumentParser(
        description='GPS 3D Trajectory Viewer - Visualize GPS trajectory with quality analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Usage examples:
  python3 gps_3d_trajectory_viewer.py data.bag
  python3 gps_3d_trajectory_viewer.py data.bag --topic /gps/fix

Features:
  - 3D trajectory visualization with quality color coding
  - Floating analysis panel with detailed statistics
  - RTK fixed solution: Green solid line
  - Other GPS qualities: Colored dashed lines
  - Interactive 3D rotation and zoom
        """
    )
    parser.add_argument('bag_file', help='ROS bag file path')
    parser.add_argument('--topic', default='/rtk/gnss',
                       help='GPS topic name (default: /rtk/gnss)')

    args = parser.parse_args()

    # Run analysis
    viewer = GPS3DTrajectoryViewer()
    success = viewer.run_analysis(args.bag_file, args.topic)

    if not success:
        print("❌ GPS 3D trajectory analysis failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
