<?xml version="1.0"?>
<package>
  <name>state_estimation</name>
  <version>0.0.0</version>

  <description>
    This is a modified version of LOAM which is original algorithm
    is described in the following paper:
    <PERSON><PERSON> and <PERSON><PERSON> <PERSON>. LOAM: Lidar Odometry and Mapping in Real-time.
      Robotics: Science and Systems Conference (RSS). Berkeley, CA, July 2014.
  </description>

  <maintainer email="<EMAIL>">claydergc</maintainer>

  <license>BSD</license>

  <author email="<EMAIL>"><PERSON></author>
  
  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>pcl_ros</build_depend>
  <!-- <build_depend>livox_ros_driver</build_depend> -->
  <build_depend>message_generation</build_depend>
  <!-- GPS integration dependencies -->
  <build_depend>libgeographic-dev</build_depend>

  <run_depend>geometry_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>pcl_ros</run_depend>
  <!-- <run_depend>livox_ros_driver</run_depend> -->
  <run_depend>message_runtime</run_depend>
  <!-- GPS integration dependencies -->
  <run_depend>libgeographic-dev</run_depend>

  <test_depend>rostest</test_depend>
  <test_depend>rosbag</test_depend>

  <export>
  </export>
</package>
