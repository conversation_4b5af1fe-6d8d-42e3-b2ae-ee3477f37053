@echo off
echo ========================================
echo GPS 3D 轨迹分析器 - Bagpy修复版构建
echo ========================================
echo.

echo 📋 检查文件...
if not exist "gps_gui_analyzer_bagpy_fixed.py" (
    echo ❌ 修复版主程序文件不存在
    pause
    exit /b 1
)

echo ✅ 修复版主程序文件存在
echo.

echo 🔧 清理之前的构建...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "GPS_3D_Analyzer_Bagpy_Fixed_Release" rmdir /s /q "GPS_3D_Analyzer_Bagpy_Fixed_Release"

echo 🚀 开始构建修复版exe...
echo 使用简化的PyInstaller命令避免复杂依赖问题...
echo.

pyinstaller --onefile --windowed --name=GPS_3D_Analyzer_Bagpy_Fixed ^
    --add-data "gps_gui_analyzer_bagpy_fixed.py;." ^
    gps_gui_analyzer_bagpy_fixed.py

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 构建失败
    echo 尝试使用更详细的参数...
    echo.
    
    pyinstaller --onefile --windowed --name=GPS_3D_Analyzer_Bagpy_Fixed ^
        --hidden-import=bagpy ^
        --hidden-import=matplotlib.backends.backend_tkagg ^
        --hidden-import=mpl_toolkits.mplot3d ^
        --hidden-import=pandas ^
        --hidden-import=numpy ^
        --collect-submodules=bagpy ^
        gps_gui_analyzer_bagpy_fixed.py
        
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ 详细构建也失败
        pause
        exit /b 1
    )
)

echo ✅ 构建完成
echo.

echo 📁 创建发布包...
mkdir "GPS_3D_Analyzer_Bagpy_Fixed_Release"

if exist "dist\GPS_3D_Analyzer_Bagpy_Fixed.exe" (
    copy "dist\GPS_3D_Analyzer_Bagpy_Fixed.exe" "GPS_3D_Analyzer_Bagpy_Fixed_Release\"
    echo ✅ 可执行文件复制完成
) else (
    echo ❌ 可执行文件不存在
    pause
    exit /b 1
)

echo.
echo 📝 创建说明文件...
echo # GPS 3D 轨迹分析器 - Bagpy修复版 > "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo. >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo ## 修复内容 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 本版本修复了bagpy库在PyInstaller打包时的兼容性问题。 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo. >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo ## 使用方法 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 1. 双击 GPS_3D_Analyzer_Bagpy_Fixed.exe 启动程序 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 2. 选择ROS bag文件 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 3. 设置GPS话题（默认: /rtk/gnss） >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 4. 选择输出文件位置 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 5. 点击"开始分析" >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo 6. 查看3D轨迹和分析结果 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo. >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo ## 界面特色 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo - 深灰色专业主题 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo - 深绿色文字显示 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo - 深色选择背景 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo - 修复的鼠标3D控制 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"
echo - 完整中文支持 >> "GPS_3D_Analyzer_Bagpy_Fixed_Release\README.txt"

echo.
echo ========================================
echo ✅ Bagpy修复版构建完成！
echo ========================================
echo.
echo 📁 发布文件位置: GPS_3D_Analyzer_Bagpy_Fixed_Release\
echo 🚀 可执行文件: GPS_3D_Analyzer_Bagpy_Fixed.exe
echo.

for %%f in ("GPS_3D_Analyzer_Bagpy_Fixed_Release\*") do (
    echo   📄 %%~nxf
)

echo.
echo 💡 现在可以测试修复版exe文件
echo 💡 如果仍有问题，请检查终端输出
echo.
pause
