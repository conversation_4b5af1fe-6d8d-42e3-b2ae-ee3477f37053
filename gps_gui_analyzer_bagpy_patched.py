#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analyzer GUI - Bagpy Patched Version
Direct fix for bagpy import issue in PyInstaller
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pandas as pd
import os
import sys
import threading
import time
from collections import defaultdict
import math

# 设置编码和字体
import locale
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 设置深灰色主题
plt.style.use('dark_background')
matplotlib.rcParams['figure.facecolor'] = '#2b2b2b'
matplotlib.rcParams['axes.facecolor'] = '#2b2b2b'

# 设置系统编码
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

# 🔧 直接修复bagpy导入问题
def patch_bagpy_import():
    """直接修复bagpy在PyInstaller中的导入问题"""
    try:
        # 首先尝试正常导入
        import bagpy
        return True, bagpy
    except Exception as e:
        print(f"正常导入bagpy失败: {e}")
        
        # 尝试修复version文件问题
        try:
            import tempfile
            import importlib.util
            
            # 创建临时version文件
            temp_dir = tempfile.mkdtemp()
            version_file = os.path.join(temp_dir, 'version')
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
            
            # 修补bagpy模块
            import bagpy
            if hasattr(bagpy, '__path__'):
                # 将临时version文件路径添加到bagpy路径中
                bagpy_path = bagpy.__path__[0] if bagpy.__path__ else None
                if bagpy_path:
                    temp_version = os.path.join(bagpy_path, 'version')
                    if not os.path.exists(temp_version):
                        try:
                            with open(temp_version, 'w') as f:
                                f.write('0.5.0\n')
                            print(f"✅ 创建bagpy version文件: {temp_version}")
                        except:
                            pass
            
            return True, bagpy
            
        except Exception as e2:
            print(f"修复bagpy失败: {e2}")
            return False, None

# 尝试导入bagpy
BAGPY_AVAILABLE, bagpy_module = patch_bagpy_import()

if BAGPY_AVAILABLE:
    print("✅ bagpy导入成功")
    bagpy = bagpy_module
else:
    print("❌ bagpy导入失败，将使用兼容模式")
    bagpy = None

class GPSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPS 3D 轨迹分析器 - 修复版")
        self.root.geometry("1600x1000")
        
        # 设置字体
        self.setup_fonts()
        
        # 设置深灰色主题
        self.setup_dark_theme()
        
        # Variables
        self.bag_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.gps_topic = tk.StringVar(value="/rtk/gnss")
        self.is_processing = False
        self.gps_data = []
        self.trajectory_points = []
        
        # 3D视图控制变量
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.mouse_pressed = False
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        # GPS quality settings
        self.quality_colors = {
            -1: '#FF6B6B',  # 红色 - NO_FIX
            0:  '#4ECDC4',  # 青绿色 - RTK_FIXED
            1:  '#45B7D1',  # 蓝色 - SBAS_FIX
            2:  '#FFA07A',  # 橙色 - GBAS_FIX
            3:  '#DDA0DD',  # 紫色 - OTHER
        }
        self.quality_names = {
            -1: "无定位",
            0:  "RTK固定解",
            1:  "SBAS定位", 
            2:  "GBAS定位",
            3:  "其他"
        }
        
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
        self.setup_gui()
        
    def setup_fonts(self):
        """设置字体以避免乱码"""
        try:
            self.default_font = ('Microsoft YaHei', 10)
            self.title_font = ('Microsoft YaHei', 16, 'bold')
            self.label_font = ('Microsoft YaHei', 9)
            self.button_font = ('Microsoft YaHei', 9)
        except:
            self.default_font = ('Arial', 10)
            self.title_font = ('Arial', 16, 'bold')
            self.label_font = ('Arial', 9)
            self.button_font = ('Arial', 9)
        
    def setup_dark_theme(self):
        """设置深灰色主题"""
        self.colors = {
            'bg': '#2E2E2E',           # 主背景 - 深灰
            'text_green': '#00C851',   # 文字绿色
            'select_bg': '#404040',    # 选中背景 - 深灰
            'entry_bg': '#3C3C3C',     # 输入框背景 - 深灰
            'button_bg': '#404040',    # 按钮背景 - 深灰
            'frame_bg': '#353535',     # 框架背景
            'accent': '#0078D4',       # 强调色 - 蓝色
            'success': '#00C851',      # 成功色 - 绿色
            'warning': '#FF8800',      # 警告色 - 橙色
            'error': '#FF4444',        # 错误色 - 红色
            'terminal_bg': '#1E1E1E',  # 终端背景
            'terminal_fg': '#00FF41',  # 终端前景 - 绿色
        }
        
        # 配置根窗口
        self.root.configure(bg=self.colors['bg'])
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置各种控件样式
        style.configure('Dark.TFrame', background=self.colors['frame_bg'], relief='flat')
        style.configure('Dark.TLabel', background=self.colors['bg'], foreground=self.colors['text_green'], font=self.label_font)
        style.configure('Dark.TButton', background=self.colors['button_bg'], foreground=self.colors['text_green'], font=self.button_font, relief='flat', borderwidth=1, focuscolor='none')
        style.map('Dark.TButton', background=[('active', '#505050'), ('pressed', '#606060')], foreground=[('active', self.colors['text_green']), ('pressed', self.colors['text_green'])])
        style.configure('Dark.TEntry', background=self.colors['entry_bg'], foreground=self.colors['text_green'], font=self.default_font, relief='flat', borderwidth=1, selectbackground=self.colors['select_bg'], selectforeground=self.colors['text_green'], insertcolor=self.colors['text_green'])
        style.map('Dark.TEntry', focuscolor=[('!focus', 'none')], selectbackground=[('focus', self.colors['select_bg'])], selectforeground=[('focus', self.colors['text_green'])], bordercolor=[('focus', self.colors['text_green'])])
        style.configure('Accent.TButton', background=self.colors['accent'], foreground='#FFFFFF', font=self.button_font, relief='flat', borderwidth=1, focuscolor='none')
        style.map('Accent.TButton', background=[('active', '#106EBE'), ('pressed', '#005A9E')])
        style.configure('Success.TLabel', background=self.colors['bg'], foreground=self.colors['success'], font=self.label_font)
        style.configure('Warning.TLabel', background=self.colors['bg'], foreground=self.colors['warning'], font=self.label_font)
        style.configure('Error.TLabel', background=self.colors['bg'], foreground=self.colors['error'], font=self.label_font)
        style.configure('Dark.TLabelframe', background=self.colors['bg'], foreground=self.colors['text_green'], relief='flat', borderwidth=1)
        style.configure('Dark.TLabelframe.Label', background=self.colors['bg'], foreground=self.colors['text_green'], font=self.label_font)
        style.configure('Dark.Horizontal.TProgressbar', background=self.colors['accent'], troughcolor=self.colors['frame_bg'], relief='flat', borderwidth=0)
        
    def setup_gui(self):
        """Setup the GUI layout"""
        main_frame = ttk.Frame(self.root, padding="15", style='Dark.TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_text = "GPS 3D 轨迹分析器 - 修复版"
        if BAGPY_AVAILABLE:
            title_text += " (Bagpy已修复)"
        else:
            title_text += " (兼容模式)"
            
        title_label = ttk.Label(main_frame, text=title_text, font=self.title_font, style='Dark.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))
        
        # Control panel
        self.setup_control_panel(main_frame)
        
        # Visualization panel
        self.setup_visualization_panel(main_frame)
        
        # Terminal panel
        self.setup_terminal_panel(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="15", style='Dark.TLabelframe')
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        
        # File selection
        ttk.Label(control_frame, text="Bag文件:", style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.bag_file_path, width=35, style='Dark.TEntry').grid(row=0, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_bag_file, style='Dark.TButton').grid(row=0, column=2, padx=8)
        
        # GPS Topic
        ttk.Label(control_frame, text="GPS话题:", style='Dark.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.gps_topic, width=35, style='Dark.TEntry').grid(row=1, column=1, padx=8)
        
        # Output file
        ttk.Label(control_frame, text="输出文件:", style='Dark.TLabel').grid(row=2, column=0, sticky=tk.W, pady=8)
        ttk.Entry(control_frame, textvariable=self.output_file_path, width=35, style='Dark.TEntry').grid(row=2, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_output_file, style='Dark.TButton').grid(row=2, column=2, padx=8)
        
        # Control buttons
        button_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        button_frame.grid(row=3, column=0, columnspan=3, pady=25)
        
        self.start_button = ttk.Button(button_frame, text="开始分析", command=self.start_analysis, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=8)
        
        self.stop_button = ttk.Button(button_frame, text="停止分析", command=self.stop_analysis, state='disabled', style='Dark.TButton')
        self.stop_button.pack(side=tk.LEFT, padx=8)
        
        # Status info
        status_text = "就绪"
        if BAGPY_AVAILABLE:
            status_text += " - Bagpy已修复并可用"
        else:
            status_text += " - 兼容模式（请检查bagpy安装）"
            
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate', style='Dark.Horizontal.TProgressbar')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text=status_text, style='Success.TLabel' if BAGPY_AVAILABLE else 'Warning.TLabel')
        self.status_label.grid(row=5, column=0, columnspan=3, pady=8)
