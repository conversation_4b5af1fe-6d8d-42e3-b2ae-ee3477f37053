# GPS回环检测系统 - YAML参数配置指南

## 🎯 YAML配置系统概述

现在您可以通过YAML文件方便地调整所有GPS回环检测参数，支持预设配置和动态参数调整。

## 📁 配置文件结构

```
src/state_estimation/
├── config/
│   └── gps_loop_closure_params.yaml    # 主配置文件
├── launch/
│   ├── mapping_robosense_with_enhanced_gps_loop.launch  # 主启动文件
│   ├── gps_loop_poor_gps.launch        # GPS质量差专用
│   └── gps_loop_good_gps.launch        # GPS质量好专用
└── scripts/
    └── adjust_gps_loop_params.py       # 参数动态调整工具
```

## 🚀 使用方法

### 方法1: 使用预设配置启动

#### 适合您的GPS status=-1场景（推荐）
```bash
# 使用GPS质量差预设
roslaunch state_estimation gps_loop_poor_gps.launch

# 或者指定预设
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch use_preset:=poor_gps_preset
```

#### 其他预设选项
```bash
# GPS质量好场景
roslaunch state_estimation gps_loop_good_gps.launch

# 高性能计算场景
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch use_preset:=high_performance_preset

# 低性能计算场景
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch use_preset:=low_performance_preset

# 不使用预设，使用默认配置
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch use_preset:=none
```

### 方法2: 命令行参数覆盖

```bash
# 覆盖特定参数
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \
    use_preset:=poor_gps_preset \
    loop_closure_distance_threshold:=12.0 \
    intermediate_loop_threshold:=15.0 \
    gps_quality_threshold:=-1
```

### 方法3: 自定义配置文件

```bash
# 使用自定义配置文件
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \
    config_file:=/path/to/your/custom_config.yaml \
    use_preset:=none
```

## ⚙️ 参数详细说明

### GPS回环检测优化器参数

| 参数名 | 默认值 | 说明 | 推荐范围 |
|--------|--------|------|----------|
| `loop_closure_distance_threshold` | 8.0 | 起点-终点回环距离阈值(米) | 5.0-15.0 |
| `intermediate_loop_threshold` | 10.0 | 中间回环距离阈值(米) | 6.0-20.0 |
| `min_trajectory_length` | 30.0 | 最小轨迹长度(米) | 20.0-100.0 |
| `gps_quality_threshold` | -1 | GPS质量阈值 | -1(接受所有) 到 2 |
| `check_interval` | 1.0 | 检查间隔(秒) | 0.5-5.0 |
| `revisit_threshold` | 12.0 | 重访距离阈值(米) | 8.0-25.0 |

### SLAM回环检测集成参数

| 参数名 | 默认值 | 说明 | 推荐范围 |
|--------|--------|------|----------|
| `force_search_radius` | 15.0 | 基础搜索半径(米) | 10.0-30.0 |
| `intermediate_search_radius` | 20.0 | 中间回环搜索半径(米) | 12.0-40.0 |
| `start_end_score_threshold` | 0.30 | 起点-终点匹配阈值 | 0.2-0.6 |
| `intermediate_score_threshold` | 0.40 | 中间回环匹配阈值 | 0.25-0.7 |
| `voxel_leaf_size` | 0.15 | 体素滤波大小(米) | 0.05-0.5 |
| `max_search_candidates` | 10 | 最大搜索候选数 | 5-30 |

## 🔧 动态参数调整

### 交互式调整工具

```bash
# 启动交互式参数调整工具
rosrun state_estimation adjust_gps_loop_params.py

# 或者
python3 src/state_estimation/scripts/adjust_gps_loop_params.py
```

### 命令行快速调整

```bash
# 显示当前参数
python3 src/state_estimation/scripts/adjust_gps_loop_params.py show

# 加载预设
python3 src/state_estimation/scripts/adjust_gps_loop_params.py preset poor_gps_preset

# 设置GPS参数
python3 src/state_estimation/scripts/adjust_gps_loop_params.py set gps loop_closure_distance_threshold 12.0

# 设置SLAM参数
python3 src/state_estimation/scripts/adjust_gps_loop_params.py set slam force_search_radius 20.0
```

### 使用rosparam直接调整

```bash
# 查看当前参数
rosparam list | grep loop_closure

# 设置参数
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 12.0
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 20.0

# 获取参数值
rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold
```

## 📊 针对不同场景的参数建议

### 您的GPS status=-1场景（推荐配置）

```yaml
enhanced_gps_loop_closure_optimizer:
  gps_quality_threshold: -1              # 接受NO_FIX状态
  loop_closure_distance_threshold: 10.0  # 放宽距离要求
  intermediate_loop_threshold: 12.0      # 放宽中间回环要求
  min_trajectory_length: 25.0            # 降低最小轨迹长度
  
enhanced_slam_loop_closure_integration:
  force_search_radius: 20.0              # 增大搜索范围
  intermediate_search_radius: 25.0       # 增大中间回环搜索
  start_end_score_threshold: 0.40        # 放宽匹配要求
  intermediate_score_threshold: 0.50     # 放宽中间回环匹配
  voxel_leaf_size: 0.2                   # 增大体素大小减少计算
```

### 室内或GPS信号差场景

```yaml
enhanced_gps_loop_closure_optimizer:
  gps_quality_threshold: -1
  loop_closure_distance_threshold: 15.0  # 进一步放宽
  intermediate_loop_threshold: 18.0
  min_trajectory_length: 20.0
  check_interval: 2.0                    # 降低检查频率
  
enhanced_slam_loop_closure_integration:
  force_search_radius: 30.0
  start_end_score_threshold: 0.60        # 大幅放宽匹配要求
  intermediate_score_threshold: 0.70
  voxel_leaf_size: 0.3                   # 进一步减少计算量
```

### 高精度GPS场景

```yaml
enhanced_gps_loop_closure_optimizer:
  gps_quality_threshold: 0               # 只接受RTK_FIXED
  loop_closure_distance_threshold: 3.0   # 严格距离要求
  intermediate_loop_threshold: 4.0
  min_trajectory_length: 50.0
  
enhanced_slam_loop_closure_integration:
  force_search_radius: 8.0
  start_end_score_threshold: 0.15        # 严格匹配要求
  intermediate_score_threshold: 0.20
  voxel_leaf_size: 0.05                  # 高精度处理
```

## 🔍 参数调优指南

### 回环检测不够敏感时

```bash
# 增大搜索范围
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 15.0
rosparam set /enhanced_gps_loop_closure_optimizer/intermediate_loop_threshold 18.0

# 放宽匹配要求
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.50
rosparam set /enhanced_slam_loop_closure_integration/intermediate_score_threshold 0.60
```

### 误检测太多时

```bash
# 缩小搜索范围
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 6.0
rosparam set /enhanced_gps_loop_closure_optimizer/intermediate_loop_threshold 8.0

# 严格匹配要求
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.25
rosparam set /enhanced_slam_loop_closure_integration/intermediate_score_threshold 0.30
```

### 计算性能不足时

```bash
# 减少计算量
rosparam set /enhanced_slam_loop_closure_integration/voxel_leaf_size 0.3
rosparam set /enhanced_slam_loop_closure_integration/max_search_candidates 5
rosparam set /enhanced_gps_loop_closure_optimizer/check_interval 2.0
```

## 📈 监控参数效果

### 监控回环检测效果

```bash
# 监控回环检测频率
rostopic hz /force_loop_closure
rostopic hz /intermediate_loop_detected

# 监控匹配分数
rostopic echo /matching_score

# 监控回环距离
rostopic echo /loop_closure_distance
```

### 查看参数生效情况

```bash
# 查看节点参数
rosnode info /enhanced_gps_loop_closure_optimizer
rosnode info /enhanced_slam_loop_closure_integration

# 查看所有回环相关参数
rosparam list | grep -E "(loop_closure|enhanced_)"
```

## 🎯 快速启动命令总结

### 适合您的场景（GPS status=-1）

```bash
# 方法1: 使用专用launch文件（最简单）
roslaunch state_estimation gps_loop_poor_gps.launch

# 方法2: 使用预设配置
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch use_preset:=poor_gps_preset

# 方法3: 命令行微调
roslaunch state_estimation mapping_robosense_with_enhanced_gps_loop.launch \
    use_preset:=poor_gps_preset \
    loop_closure_distance_threshold:=12.0 \
    intermediate_loop_threshold:=15.0
```

**🎉 现在您可以通过YAML文件和多种方式灵活调整GPS回环检测系统的所有参数！**
