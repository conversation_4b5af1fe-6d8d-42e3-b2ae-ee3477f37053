# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'GPS Analyzer Team'),
        StringStruct(u'FileDescription', u'GPS 3D Trajectory Analyzer'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'GPS_3D_Analyzer'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'GPS_3D_Analyzer.exe'),
        StringStruct(u'ProductName', u'GPS 3D Trajectory Analyzer'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
