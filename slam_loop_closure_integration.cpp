/**
 * SLAM回环检测集成模块
 * 集成GPS强制回环检测到SLAM系统中
 * 当接收到GPS强制回环信号时，强制搜索回环匹配点云
 */

#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64.h>
#include <sensor_msgs/PointCloud2.h>
#include <geometry_msgs/PoseStamped.h>
#include <nav_msgs/Path.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>
#include <pcl/registration/ndt.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl_conversions/pcl_conversions.h>

#include <vector>
#include <deque>
#include <mutex>
#include <thread>
#include <chrono>

class SLAMLoopClosureIntegration {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 订阅器
    ros::Subscriber force_loop_closure_sub_;
    ros::Subscriber loop_distance_sub_;
    ros::Subscriber pointcloud_sub_;
    ros::Subscriber pose_sub_;
    
    // 发布器
    ros::Publisher loop_closure_result_pub_;
    ros::Publisher corrected_pose_pub_;
    ros::Publisher loop_closure_info_pub_;
    
    // TF
    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;
    
    // 参数
    double force_search_radius_;           // 强制搜索半径
    double loop_closure_score_threshold_;  // 回环匹配分数阈值
    int max_search_candidates_;            // 最大搜索候选数
    double voxel_leaf_size_;              // 体素滤波叶子大小
    int min_keyframes_for_loop_;          // 最小关键帧数
    
    // 数据存储
    std::deque<sensor_msgs::PointCloud2> keyframe_clouds_;
    std::deque<geometry_msgs::PoseStamped> keyframe_poses_;
    std::mutex data_mutex_;
    
    // 状态
    bool force_loop_closure_requested_;
    double current_loop_distance_;
    bool loop_closure_in_progress_;
    
public:
    SLAMLoopClosureIntegration() : 
        private_nh_("~"), 
        tf_listener_(tf_buffer_),
        force_loop_closure_requested_(false),
        current_loop_distance_(0.0),
        loop_closure_in_progress_(false) {
        
        // 读取参数
        private_nh_.param("force_search_radius", force_search_radius_, 10.0);
        private_nh_.param("loop_closure_score_threshold", loop_closure_score_threshold_, 0.3);
        private_nh_.param("max_search_candidates", max_search_candidates_, 10);
        private_nh_.param("voxel_leaf_size", voxel_leaf_size_, 0.1);
        private_nh_.param("min_keyframes_for_loop", min_keyframes_for_loop_, 50);
        
        // 初始化订阅器
        force_loop_closure_sub_ = nh_.subscribe("/force_loop_closure", 1, 
            &SLAMLoopClosureIntegration::forceLoopClosureCallback, this);
        loop_distance_sub_ = nh_.subscribe("/loop_closure_distance", 1,
            &SLAMLoopClosureIntegration::loopDistanceCallback, this);
        pointcloud_sub_ = nh_.subscribe("/cloud_registered", 1,
            &SLAMLoopClosureIntegration::pointCloudCallback, this);
        pose_sub_ = nh_.subscribe("/aft_mapped_to_init", 1,
            &SLAMLoopClosureIntegration::poseCallback, this);
        
        // 初始化发布器
        loop_closure_result_pub_ = nh_.advertise<std_msgs::Bool>("/loop_closure_result", 1);
        corrected_pose_pub_ = nh_.advertise<geometry_msgs::PoseStamped>("/corrected_pose", 1);
        loop_closure_info_pub_ = nh_.advertise<std_msgs::Float64>("/loop_closure_score", 1);
        
        ROS_INFO("🚀 SLAM回环检测集成模块启动");
        ROS_INFO("📏 强制搜索半径: %.1fm", force_search_radius_);
        ROS_INFO("🎯 匹配分数阈值: %.3f", loop_closure_score_threshold_);
        ROS_INFO("🔍 最大搜索候选数: %d", max_search_candidates_);
    }
    
    void forceLoopClosureCallback(const std_msgs::Bool::ConstPtr& msg) {
        if (msg->data && !loop_closure_in_progress_) {
            ROS_WARN("🔄 接收到GPS强制回环检测请求!");
            force_loop_closure_requested_ = true;
            
            // 在单独线程中执行回环检测
            std::thread loop_thread(&SLAMLoopClosureIntegration::performForceLoopClosure, this);
            loop_thread.detach();
        }
    }
    
    void loopDistanceCallback(const std_msgs::Float64::ConstPtr& msg) {
        current_loop_distance_ = msg->data;
    }
    
    void pointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // 存储关键帧点云
        keyframe_clouds_.push_back(*msg);
        
        // 限制存储数量
        if (keyframe_clouds_.size() > 1000) {
            keyframe_clouds_.pop_front();
        }
    }
    
    void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // 存储关键帧位姿
        keyframe_poses_.push_back(*msg);
        
        // 限制存储数量
        if (keyframe_poses_.size() > 1000) {
            keyframe_poses_.pop_front();
        }
    }
    
    void performForceLoopClosure() {
        loop_closure_in_progress_ = true;
        ROS_INFO("🔍 开始强制回环检测搜索...");
        
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        if (keyframe_clouds_.size() < min_keyframes_for_loop_ || 
            keyframe_poses_.size() < min_keyframes_for_loop_) {
            ROS_WARN("⚠️  关键帧数量不足，跳过回环检测");
            loop_closure_in_progress_ = false;
            return;
        }
        
        // 获取当前点云和位姿
        sensor_msgs::PointCloud2 current_cloud = keyframe_clouds_.back();
        geometry_msgs::PoseStamped current_pose = keyframe_poses_.back();
        
        // 搜索候选回环帧
        std::vector<int> candidates = findLoopClosureCandidates(current_pose);
        
        ROS_INFO("🎯 找到 %lu 个回环候选帧", candidates.size());
        
        // 执行点云匹配
        double best_score = std::numeric_limits<double>::max();
        int best_candidate = -1;
        geometry_msgs::PoseStamped corrected_pose;
        
        for (int candidate_idx : candidates) {
            if (candidate_idx >= 0 && candidate_idx < keyframe_clouds_.size()) {
                double score = performPointCloudMatching(
                    current_cloud, 
                    keyframe_clouds_[candidate_idx],
                    current_pose,
                    keyframe_poses_[candidate_idx],
                    corrected_pose
                );
                
                if (score < best_score && score < loop_closure_score_threshold_) {
                    best_score = score;
                    best_candidate = candidate_idx;
                }
            }
        }
        
        // 发布结果
        std_msgs::Bool result_msg;
        if (best_candidate >= 0) {
            result_msg.data = true;
            corrected_pose_pub_.publish(corrected_pose);
            
            std_msgs::Float64 score_msg;
            score_msg.data = best_score;
            loop_closure_info_pub_.publish(score_msg);
            
            ROS_INFO("✅ 强制回环检测成功!");
            ROS_INFO("🎯 最佳匹配分数: %.4f", best_score);
            ROS_INFO("📍 匹配关键帧索引: %d", best_candidate);
        } else {
            result_msg.data = false;
            ROS_WARN("❌ 强制回环检测失败，未找到合适的匹配");
        }
        
        loop_closure_result_pub_.publish(result_msg);
        
        // 重置状态
        force_loop_closure_requested_ = false;
        loop_closure_in_progress_ = false;
        
        ROS_INFO("🏁 强制回环检测完成");
    }
    
    std::vector<int> findLoopClosureCandidates(const geometry_msgs::PoseStamped& current_pose) {
        std::vector<int> candidates;
        
        // 基于GPS距离的强制搜索策略
        // 搜索轨迹前半部分的关键帧作为候选
        int search_end = std::min((int)(keyframe_poses_.size() * 0.6), 
                                 (int)keyframe_poses_.size() - 50);
        
        for (int i = 0; i < search_end; i++) {
            double distance = calculatePoseDistance(current_pose, keyframe_poses_[i]);
            
            if (distance <= force_search_radius_) {
                candidates.push_back(i);
                
                if (candidates.size() >= max_search_candidates_) {
                    break;
                }
            }
        }
        
        // 如果基于距离的搜索候选不足，添加轨迹开始部分的帧
        if (candidates.size() < 3) {
            int early_frames = std::min(20, (int)keyframe_poses_.size() / 4);
            for (int i = 0; i < early_frames; i++) {
                if (std::find(candidates.begin(), candidates.end(), i) == candidates.end()) {
                    candidates.push_back(i);
                }
            }
        }
        
        return candidates;
    }
    
    double performPointCloudMatching(
        const sensor_msgs::PointCloud2& cloud1,
        const sensor_msgs::PointCloud2& cloud2,
        const geometry_msgs::PoseStamped& pose1,
        const geometry_msgs::PoseStamped& pose2,
        geometry_msgs::PoseStamped& corrected_pose) {
        
        // 转换为PCL点云
        pcl::PointCloud<pcl::PointXYZ>::Ptr source(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::PointCloud<pcl::PointXYZ>::Ptr target(new pcl::PointCloud<pcl::PointXYZ>);
        
        pcl::fromROSMsg(cloud1, *source);
        pcl::fromROSMsg(cloud2, *target);
        
        // 体素滤波降采样
        pcl::VoxelGrid<pcl::PointXYZ> voxel_filter;
        voxel_filter.setLeafSize(voxel_leaf_size_, voxel_leaf_size_, voxel_leaf_size_);
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr source_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::PointCloud<pcl::PointXYZ>::Ptr target_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        
        voxel_filter.setInputCloud(source);
        voxel_filter.filter(*source_filtered);
        
        voxel_filter.setInputCloud(target);
        voxel_filter.filter(*target_filtered);
        
        // 使用NDT进行配准
        pcl::NormalDistributionsTransform<pcl::PointXYZ, pcl::PointXYZ> ndt;
        ndt.setTransformationEpsilon(0.01);
        ndt.setStepSize(0.1);
        ndt.setResolution(1.0);
        ndt.setMaximumIterations(35);
        
        ndt.setInputSource(source_filtered);
        ndt.setInputTarget(target_filtered);
        
        // 执行配准
        pcl::PointCloud<pcl::PointXYZ>::Ptr output_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        ndt.align(*output_cloud);
        
        // 获取配准结果
        double fitness_score = ndt.getFitnessScore();
        Eigen::Matrix4f transformation = ndt.getFinalTransformation();
        
        // 计算修正后的位姿
        corrected_pose = pose1;  // 这里需要根据transformation矩阵计算实际的修正位姿
        
        return fitness_score;
    }
    
    double calculatePoseDistance(const geometry_msgs::PoseStamped& pose1, 
                                const geometry_msgs::PoseStamped& pose2) {
        double dx = pose1.pose.position.x - pose2.pose.position.x;
        double dy = pose1.pose.position.y - pose2.pose.position.y;
        double dz = pose1.pose.position.z - pose2.pose.position.z;
        
        return sqrt(dx*dx + dy*dy + dz*dz);
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "slam_loop_closure_integration");
    
    try {
        SLAMLoopClosureIntegration integration;
        
        ROS_INFO("✅ SLAM回环检测集成模块运行中...");
        ROS_INFO("💡 等待GPS强制回环检测信号...");
        
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("❌ SLAM回环检测集成模块异常: %s", e.what());
        return -1;
    }
    
    ROS_INFO("🛑 SLAM回环检测集成模块已停止");
    return 0;
}
