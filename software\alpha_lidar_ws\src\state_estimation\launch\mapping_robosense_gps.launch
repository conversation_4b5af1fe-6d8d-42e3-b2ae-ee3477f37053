<launch>
    <!-- Launch file for RoboSense LiDAR with GPS integration -->

    <arg name="rviz" default="true"/>
    <arg name="debug" default="false"/>
    <arg name="from_packets" default="false"/>
    <arg name="bag_path" default=""/>
    
    <!-- GPS parameters -->
    <arg name="gps_topic" default="/rtk/gnss"/>
    <arg name="enable_gps_correction" default="true"/>
    <arg name="enable_gps_loop_closure" default="true"/>

    <rosparam command="load" file="$(find state_estimation)/config/rs16_rotation_v2.yaml"/>
    
    <!-- Override GPS parameters -->
    <param name="common/gps_topic" value="$(arg gps_topic)"/>
    <param name="gps/enable_correction" value="$(arg enable_gps_correction)"/>
    <param name="gps/enable_loop_closure" value="$(arg enable_gps_loop_closure)"/>
    <param name="gps/height_correction_threshold" value="0.3"/>
    <param name="gps/correction_rate" value="0.1"/>
    <param name="gps/loop_closure_distance" value="3.0"/>

    <group if="$(eval arg('debug') == False)">
        <node pkg="state_estimation" type="state_estimation_node" name="state_estimation_node" output="screen">
            <!-- GPS topic remapping -->
            <remap from="/gps/fix" to="$(arg gps_topic)"/>
        </node>
    </group>

    <!--- RS to Velodyne -->
    <node pkg="state_estimation" type="rs_to_velodyne" name="rs_to_velodyne" output="screen" args="XYZIRT XYZIRT">
        <remap from="/rslidar_points" to="/rslidar_points"/>
    </node>

    <group if="$(arg rviz)">
        <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz"
              args="-d $(find state_estimation)/config/rviz_cfg/rs16.rviz"/>
    </group>

    <!--- result logging -->
    <arg name="logging" default="true"/>
    <group if="$(arg logging)">
        <node pkg="state_estimation" type="logger.py" name="intermediate_result_logger" output="screen"/>
    </group>

    <group if="$(eval arg('bag_path') != '')">
        <node pkg="rosbag" type="play" name="player" output="screen" args="$(arg bag_path) --pause"/>
    </group>

</launch>
