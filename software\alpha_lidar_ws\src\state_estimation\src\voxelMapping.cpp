// This is an advanced implementation of the algorithm described in the
// following paper:
//   <PERSON><PERSON> and <PERSON><PERSON>. LOAM: Lidar Odometry and Mapping in Real-time.
//     Robotics: Science and Systems Conference (RSS). Berkeley, CA, July 2014.

// Modifier: Livox               <EMAIL>

// Copyright 2013, <PERSON>, Carnegie Mellon University
// Further contributions copyright (c) 2016, Southwest Research Institute
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// 1. Redistributions of source code must retain the above copyright notice,
//    this list of conditions and the following disclaimer.
// 2. Redistributions in binary form must reproduce the above copyright notice,
//    this list of conditions and the following disclaimer in the documentation
//    and/or other materials provided with the distribution.
// 3. Neither the name of the copyright holder nor the names of its
//    contributors may be used to endorse or promote products derived from this
//    software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
#include <omp.h>
#include <mutex>
#include <math.h>
#include <thread>
#include <fstream>
#include <csignal>
#include <unistd.h>
#include <Python.h>
#include <so3_math.h>
#include <ros/ros.h>
#include <Eigen/Core>
#include "IMU_Processing.hpp"
#include <nav_msgs/Odometry.h>
#include <nav_msgs/Path.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/random_sample.h>
#include <sensor_msgs/PointCloud2.h>
#include <std_msgs/String.h>
#include <std_msgs/Float64.h>
#include <std_msgs/Bool.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <geometry_msgs/Vector3.h>
// #include <livox_ros_driver/CustomMsg.h>  // 注释掉livox依赖
#include "preprocess.h"
#include "voxel_map_util.hpp"

#include <rosbag/bag.h>
#include <rosbag/view.h>
#include <boost/foreach.hpp>
#include <boost/filesystem.hpp>
#include <pcl/io/pcd_io.h>
#include <iomanip>
#include <pcl/registration/icp.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/registration/icp.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#define foreach BOOST_FOREACH

// GPS integration headers - 强制启用
#define USE_GPS_INTEGRATION  // 强制启用GPS集成
#include <sensor_msgs/NavSatFix.h>
#include <deque>
#include <limits>
// 简化版本，不依赖GeographicLib
#define HAS_GEOGRAPHIC_LIB_SIMPLE

#define INIT_TIME           (0.1)
#define LASER_POINT_COV     (0.001)
#define MAXN                (720000)
#define PUBFRAME_PERIOD     (20)

/*** Time Log Variables ***/
double kdtree_incremental_time = 0.0, kdtree_search_time = 0.0, kdtree_delete_time = 0.0;
double T1[MAXN], s_plot[MAXN], s_plot2[MAXN], s_plot3[MAXN], s_plot4[MAXN], s_plot5[MAXN], s_plot6[MAXN], s_plot7[MAXN], s_plot8[MAXN], s_plot9[MAXN], s_plot10[MAXN], s_plot11[MAXN];
double match_time = 0, solve_time = 0, solve_const_H_time = 0;
int    kdtree_size_st = 0, kdtree_size_end = 0, add_point_size = 0, kdtree_delete_counter = 0;
bool   time_sync_en = false, extrinsic_est_en = true, path_en = true, encoder_fusion_en=true;
double lidar_time_offset = 0.0;
double encoder_zeropoint_offset_deg = 0.0;
/**************************/

float res_last[100000] = {0.0};
float DET_RANGE = 300.0f;
const float MOV_THRESHOLD = 1.5f;
double intensity_th = 1.0f;

mutex mtx_buffer;
condition_variable sig_buffer;

string root_dir = ROOT_DIR;
string  lid_topic, imu_topic, encoder_topic;

double res_mean_last = 0.05, total_residual = 0.0;
double last_timestamp_lidar = 0, last_timestamp_imu = -1.0;
double gyr_cov = 0.1, acc_cov = 0.1, b_gyr_cov = 0.0001, b_acc_cov = 0.0001;
double filter_size_surf_min = 0;
double total_distance = 0, lidar_end_time = 0, first_lidar_time = 0.0, lidar_end_time_prev=0;
int    effct_feat_num = 0, time_log_counter = 0, scan_count = 0, publish_count = 0;
int    iterCount = 0, feats_down_size = 0, NUM_MAX_ITERATIONS = 0, laserCloudValidNum = 0, pcd_index = 0;
int scan_index = 0;
bool   point_selected_surf[100000] = {0};
bool   lidar_pushed, flg_first_scan = true, flg_exit = false, flg_EKF_inited;
bool   scan_pub_en = false, dense_pub_en = false, scan_body_pub_en = false;
double publish_limit_z = 1000000.0;
int publish_dense_skip = 1;
int publish_downsample_points = 1000000;
int publish_path_skip = 1;

// GPS integration global variables
#ifdef USE_GPS_INTEGRATION
std::deque<sensor_msgs::NavSatFix> gps_buffer;
std::mutex gps_mutex;
// 简化的GPS坐标转换（不依赖GeographicLib）
struct SimpleGPSOrigin {
    double lat0 = 0.0;
    double lon0 = 0.0;
    double alt0 = 0.0;
    bool is_set = false;
} gps_origin;
bool gps_origin_set = false;
bool enable_gps_correction = false;
bool enable_gps_loop_closure = false;
double gps_height_correction_threshold = 0.3;  // 30cm threshold
double gps_correction_rate = 0.1;  // 10% correction rate
double gps_loop_closure_distance = 10.0;  // 10m loop closure distance
double gps_loop_closure_min_distance = 50.0;  // Minimum trajectory length 50m
double gps_icp_trigger_distance = 20.0;  // GPS triggered ICP loop closure distance 20m (降低触发距离)
bool enable_gps_icp_loop_closure = true;  // Enable GPS triggered ICP loop closure (强制启用)
string gps_topic = "/rtk/gnss";  // Actual GPS topic

// ICP loop closure related variables
std::deque<std::pair<double, PointCloudXYZI::Ptr>> keyframe_buffer;  // Keyframe buffer
std::deque<std::pair<double, Eigen::Vector3d>> pose_buffer;  // Pose buffer
int keyframe_skip = 10;  // Save one keyframe every 10 frames
double icp_fitness_threshold = 0.3;  // ICP matching quality threshold
double icp_max_correspondence_distance = 1.0;  // ICP maximum correspondence distance

// 增强的GPS约束参数
bool enable_gps_plane_constraint = true;    // 启用GPS平面约束
double gps_plane_constraint_weight = 0.2;   // 平面约束权重
double gps_xy_correction_threshold = 0.5;   // XY方向校正阈值
double gps_xy_correction_rate = 0.05;       // XY方向校正率
int gps_constraint_window_size = 50;        // GPS约束窗口大小
std::deque<std::pair<double, Eigen::Vector3d>> gps_trajectory_buffer;  // GPS轨迹缓冲区

// 动态约束控制
bool dynamic_constraint_control = true;     // 启用动态约束控制
double constraint_quality_threshold = 0.7;  // 约束质量阈值
int constraint_disable_count = 0;           // 约束禁用计数器
int constraint_enable_count = 0;            // 约束启用计数器

// GPS起始位置记录
Eigen::Vector3d start_gps_position = Eigen::Vector3d::Zero();
Eigen::Vector3d start_slam_position = Eigen::Vector3d::Zero();
bool start_positions_set = false;

// GPS约束控制回调函数
void gps_constraint_control_callback(const std_msgs::Bool::ConstPtr& msg) {
    if (dynamic_constraint_control) {
        bool previous_state = enable_gps_plane_constraint;
        enable_gps_plane_constraint = msg->data;

        if (previous_state != enable_gps_plane_constraint) {
            if (enable_gps_plane_constraint) {
                constraint_enable_count++;
                ROS_INFO("[Dynamic Control] GPS Constraint ENABLED (Count: %d)", constraint_enable_count);
            } else {
                constraint_disable_count++;
                ROS_WARN("[Dynamic Control] GPS Constraint DISABLED (Count: %d)", constraint_disable_count);
            }
        }
    }
}

// GPS状态监控
struct {
    bool is_rtk_fixed = false;
    double last_update_time = 0.0;
    int consecutive_good_fixes = 0;
    double position_std = 1.0;
    double total_correction_applied = 0.0;  // 累积校正量监控
    int large_error_count = 0;  // 大误差计数
} gps_status;
#endif
bool adaptive_voxelization = false;
std::vector<double> adaptive_threshold;
std::vector<double> adaptive_multiple_factor;
bool init_gravity_with_pose;

vector<vector<int>>  pointSearchInd_surf;
vector<PointVector>  Nearest_Points;
vector<double>       extrinT(3, 0.0);
vector<double>       extrinR(9, 0.0);
vector<double>       extrinT_encoder(3, 0.0);
vector<double>       extrinR_encoder(9, 0.0);
deque<double>                     time_buffer;
deque<PointCloudXYZI::Ptr>        lidar_buffer;
deque<sensor_msgs::Imu::ConstPtr> imu_buffer;
deque<nav_msgs::Odometry::ConstPtr> encoder_buffer;

PointCloudXYZI::Ptr featsFromMap(new PointCloudXYZI());
PointCloudXYZI::Ptr feats_undistort(new PointCloudXYZI());
PointCloudXYZI::Ptr feats_down_body(new PointCloudXYZI());
PointCloudXYZI::Ptr feats_down_world(new PointCloudXYZI());
PointCloudXYZI::Ptr normvec(new PointCloudXYZI(100000, 1));
//PointCloudXYZI::Ptr laserCloudOri(new PointCloudXYZI(100000, 1));
//PointCloudXYZI::Ptr corr_normvect(new PointCloudXYZI(100000, 1));
PointCloudXYZI::Ptr _featsArray;
std::vector<M3D> var_down_body;

pcl::VoxelGrid<PointType> downSizeFilterSurf;
pcl::VoxelGrid<PointType> downSizeFilterAdaptive;
pcl::RandomSample<PointType> downSizeFilterVis;

std::vector<float> nn_dist_in_feats;
std::vector<float> nn_plane_std;
PointCloudXYZI::Ptr feats_with_correspondence(new PointCloudXYZI());

V3F XAxisPoint_body(LIDAR_SP_LEN, 0.0, 0.0);
V3F XAxisPoint_world(LIDAR_SP_LEN, 0.0, 0.0);
V3D euler_cur;
V3D position_last(Zero3d);
V3D Lidar_T_wrt_IMU(Zero3d);
M3D Lidar_R_wrt_IMU(Eye3d);

SO3 R_lidar_o_encoder(Eye3d);
V3D t_lidar_o_encoder(Zero3d);
SO3 R_encoder_o_zero_point(Eye3d);

// params for voxel mapping algorithm
double min_eigen_value = 0.003;
int max_layer = 0;

int max_cov_points_size = 50;
int max_points_size = 50;
double sigma_num = 2.0;
double max_voxel_size = 1.0;
std::vector<int> layer_size;

double ranging_cov = 0.0;
double angle_cov = 0.0;
std::vector<double> layer_point_size;

bool publish_voxel_map = false;
int publish_max_voxel_layer = 0;

std::unordered_map<VOXEL_LOC, OctoTree *> voxel_map;

/*** EKF inputs and output ***/
MeasureGroup Measures;
esekfom::esekf<state_ikfom, 12, input_ikfom> kf;
state_ikfom state_point, state_point_prev;
vect3 pos_lid;

nav_msgs::Path path;
nav_msgs::Odometry odomAftMapped;
geometry_msgs::Quaternion geoQuat;
geometry_msgs::PoseStamped msg_body_pose;

shared_ptr<Preprocess> p_pre(new Preprocess());
shared_ptr<ImuProcess> p_imu(new ImuProcess());

void SigHandle(int sig)
{
    flg_exit = true;
    ROS_WARN("catch sig %d", sig);
    sig_buffer.notify_all();
}

#ifdef USE_GPS_INTEGRATION
// GPS回调函数
void gps_callback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
    std::lock_guard<std::mutex> lock(gps_mutex);

    static int gps_count = 0;
    gps_count++;

    if (gps_count % 10 == 1) {  // Print every 10th GPS message
        ROS_INFO("\033[1;33m[GPS] Callback #%d: lat=%.6f, lon=%.6f, alt=%.2f, status=%d\033[0m",
                gps_count, gps_msg->latitude, gps_msg->longitude, gps_msg->altitude, gps_msg->status.status);
    }

    // Check GPS validity - 放宽GPS状态要求，允许STATUS_NO_FIX也参与处理
    if (gps_msg->status.status < sensor_msgs::NavSatStatus::STATUS_NO_FIX) {
        if (gps_count % 10 == 1) {
            ROS_WARN("[GPS] Invalid status: %d", gps_msg->status.status);
        }
        return;
    }

    // 记录GPS状态信息
    if (gps_count % 10 == 1) {
        const char* status_str = "UNKNOWN";
        switch(gps_msg->status.status) {
            case -1: status_str = "NO_FIX"; break;
            case 0: status_str = "FIX"; break;
            case 1: status_str = "SBAS_FIX"; break;
            case 2: status_str = "GBAS_FIX"; break;
        }
        ROS_INFO("[GPS] Status: %s (%d), Quality: %s",
                status_str, gps_msg->status.status,
                gps_msg->status.status >= 0 ? "GOOD" : "POOR");
    }

    // 更新GPS状态 - 修复状态判断
    gps_status.last_update_time = gps_msg->header.stamp.toSec();

    // status=0是RTK固定解，最高精度
    gps_status.is_rtk_fixed = (gps_msg->status.status == 0);

    // 接受status>=0的所有GPS定位
    if (gps_msg->status.status >= 0) {
        gps_status.consecutive_good_fixes++;
        if (gps_status.consecutive_good_fixes % 10 == 1) {
            ROS_INFO("[GPS Status] Good fixes: %d, Status: %d (RTK_FIXED)",
                    gps_status.consecutive_good_fixes, gps_msg->status.status);
        }
    } else {
        gps_status.consecutive_good_fixes = 0;
        ROS_WARN("[GPS Status] Poor GPS quality, status: %d", gps_msg->status.status);
    }

    // 设置坐标原点（简化版本） - 降低阈值
    if (!gps_origin_set && gps_status.consecutive_good_fixes > 2) {
        gps_origin.lat0 = gps_msg->latitude;
        gps_origin.lon0 = gps_msg->longitude;
        gps_origin.alt0 = gps_msg->altitude;
        gps_origin.is_set = true;
        gps_origin_set = true;
        ROS_INFO("\033[1;33m[GPS] Origin Set: lat=%.8f, lon=%.8f, alt=%.3f\033[0m",
                gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
    }

    // 添加到缓冲区
    gps_buffer.push_back(*gps_msg);
    while (gps_buffer.size() > 500) {
        gps_buffer.pop_front();
    }
}

// 简化的GPS坐标转换函数
Eigen::Vector3d convertGPSToLocal(const sensor_msgs::NavSatFix& gps_msg) {
    if (!gps_origin_set || !gps_origin.is_set) return Eigen::Vector3d::Zero();

    // 简化的坐标转换（适用于小范围区域）
    const double R = 6371000.0; // 地球半径（米）
    const double deg_to_rad = M_PI / 180.0;

    double lat_rad = gps_msg.latitude * deg_to_rad;
    double lon_rad = gps_msg.longitude * deg_to_rad;
    double lat0_rad = gps_origin.lat0 * deg_to_rad;
    double lon0_rad = gps_origin.lon0 * deg_to_rad;

    double x = R * (lon_rad - lon0_rad) * cos(lat0_rad);
    double y = R * (lat_rad - lat0_rad);
    double z = gps_msg.altitude - gps_origin.alt0;

    return Eigen::Vector3d(x, y, z);
}

// 获取GPS位置函数
bool getGPSPosition(double timestamp, Eigen::Vector3d& gps_position, double& gps_std) {
    std::lock_guard<std::mutex> lock(gps_mutex);

    if (gps_buffer.empty() || !gps_origin_set) return false;

    // 查找时间戳最近的GPS数据
    double min_time_diff = std::numeric_limits<double>::max();
    sensor_msgs::NavSatFix closest_gps;
    bool found = false;

    for (const auto& gps_msg : gps_buffer) {
        double time_diff = std::abs(gps_msg.header.stamp.toSec() - timestamp);
        if (time_diff < min_time_diff && time_diff < 0.5) {  // 0.5秒内的数据
            min_time_diff = time_diff;
            closest_gps = gps_msg;
            found = true;
        }
    }

    if (!found) return false;

    gps_position = convertGPSToLocal(closest_gps);
    gps_std = gps_status.is_rtk_fixed ? 0.02 : 1.0;  // RTK: 2cm, 普通GPS: 1m

    return true;
}

// 高度校正函数
Eigen::Vector3d correctHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp) {
    if (!enable_gps_correction) return slam_position;

    Eigen::Vector3d gps_position;
    double gps_std;

    if (!getGPSPosition(timestamp, gps_position, gps_std)) {
        return slam_position;
    }

    // 计算高度差
    double height_diff = gps_position.z() - slam_position.z();

    // 如果高度差超过阈值，进行校正
    if (std::abs(height_diff) > gps_height_correction_threshold) {
        Eigen::Vector3d corrected_position = slam_position;

        // 渐进式校正，避免突跳
        corrected_position.z() += height_diff * gps_correction_rate;

        ROS_INFO("\033[1;33m[GPS] Height Correction: SLAM=%.3f, GPS=%.3f, Corrected=%.3f\033[0m",
                slam_position.z(), gps_position.z(), corrected_position.z());

        return corrected_position;
    }

    return slam_position;
}

// 回环检测函数
bool detectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp,
                         Eigen::Vector3d& loop_constraint) {
    if (!enable_gps_loop_closure || !start_positions_set) return false;

    Eigen::Vector3d current_gps_pos;
    double gps_std;

    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }

    // 检查SLAM轨迹长度是否足够，使用SLAM累积距离而不是GPS直线距离
    double slam_trajectory_length = (current_slam_pos - start_slam_position).norm();
    if (slam_trajectory_length < gps_loop_closure_min_distance) {
        static int skip_count = 0;
        if (++skip_count % 100 == 1) {  // 每100次打印一次
            ROS_DEBUG("Loop closure skipped: SLAM trajectory %.1fm < required %.1fm",
                     slam_trajectory_length, gps_loop_closure_min_distance);
        }
        return false;  // SLAM轨迹长度不足50米，不进行回环检测
    }

    // 检查是否接近起始位置
    double distance_to_start = (current_gps_pos - start_gps_position).norm();

    // 添加调试信息
    static int debug_count = 0;
    if (++debug_count % 20 == 1) {  // 每20次打印一次状态
        ROS_ERROR("[GPS Loop Debug] SLAM_traj=%.1fm, GPS_dist_to_start=%.1fm, Required_traj=%.1fm",
                slam_trajectory_length, distance_to_start, gps_loop_closure_min_distance);
    }

    // 安全的回环检测，只有在10米以内才开始考虑回环约束
    if (distance_to_start < 10.0) {  // 10米安全距离，避免过早触发
        // 计算回环约束
        Eigen::Vector3d slam_drift = current_slam_pos - start_slam_position;
        Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position;

        loop_constraint = gps_drift - slam_drift;

        // 分层的约束强度调整，更加保守
        double distance_factor = 0.0;  // 默认不应用约束

        if (distance_to_start < 3.0) {
            // 3米以内：轻微约束
            distance_factor = 0.3;
        }
        if (distance_to_start < 2.0) {
            // 2米以内：中等约束
            distance_factor = 0.5;
        }
        if (distance_to_start < 1.0) {
            // 1米以内：较强约束
            distance_factor = 0.7;
        }

        // 应用约束强度因子
        loop_constraint *= distance_factor;

        ROS_INFO("\033[1;33m[GPS] Safe Loop Closure: Distance=%.2fm, Constraint[%.3f,%.3f,%.3f], Factor=%.2f\033[0m",
                distance_to_start, loop_constraint.x(), loop_constraint.y(), loop_constraint.z(), distance_factor);

        // 只有约束足够大且距离足够近才应用
        return (loop_constraint.norm() > 0.2 && distance_to_start < 3.0);
    }

    return false;
}

// GPS触发的ICP回环检测函数
bool performGPSTriggeredICPLoopClosure(const Eigen::Vector3d& current_slam_pos,
                                       const PointCloudXYZI::Ptr& current_cloud,
                                       double timestamp,
                                       Eigen::Matrix4f& loop_transform) {
    if (!enable_gps_icp_loop_closure || !start_positions_set) return false;

    Eigen::Vector3d current_gps_pos;
    double gps_std;
    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }

    // 检查GPS距离起点是否在触发范围内
    double gps_distance_to_start = (current_gps_pos - start_gps_position).norm();
    double trajectory_length = (current_slam_pos - start_slam_position).norm();

    // 添加ICP调试信息 - 强制输出
    static int icp_debug_count = 0;
    if (++icp_debug_count % 10 == 1) {  // 每10次打印一次状态
        ROS_ERROR("[ICP Loop Debug] GPS_dist=%.1fm, SLAM_traj=%.1fm, Keyframes=%zu, Trigger_dist=%.1fm, Enable=%s",
                gps_distance_to_start, trajectory_length, keyframe_buffer.size(), gps_icp_trigger_distance,
                enable_gps_icp_loop_closure ? "YES" : "NO");
    }

    if (gps_distance_to_start > gps_icp_trigger_distance) {
        return false;  // 距离太远，不触发ICP回环检测
    }

    // 检查轨迹长度是否足够
    if (trajectory_length < gps_loop_closure_min_distance) {
        return false;  // 轨迹长度不足
    }

    // 寻找起始区域的关键帧进行ICP匹配
    PointCloudXYZI::Ptr start_area_cloud(new PointCloudXYZI());
    bool found_start_keyframes = false;

    // 收集起始区域的关键帧点云
    for (const auto& keyframe : keyframe_buffer) {
        if (keyframe_buffer.size() > 0) {
            // 找到前10%的关键帧作为起始区域
            size_t start_region_size = std::max(1, (int)(keyframe_buffer.size() * 0.1));
            if (&keyframe - &keyframe_buffer[0] < start_region_size) {
                *start_area_cloud += *(keyframe.second);
                found_start_keyframes = true;
            }
        }
    }

    if (!found_start_keyframes || start_area_cloud->empty()) {
        return false;
    }

    // 对点云进行下采样以提高ICP效率
    pcl::VoxelGrid<PointType> voxel_filter;
    voxel_filter.setLeafSize(0.2f, 0.2f, 0.2f);  // 20cm体素

    PointCloudXYZI::Ptr source_filtered(new PointCloudXYZI());
    PointCloudXYZI::Ptr target_filtered(new PointCloudXYZI());

    voxel_filter.setInputCloud(current_cloud);
    voxel_filter.filter(*source_filtered);

    voxel_filter.setInputCloud(start_area_cloud);
    voxel_filter.filter(*target_filtered);

    if (source_filtered->empty() || target_filtered->empty()) {
        return false;
    }

    // 执行ICP配准
    pcl::IterativeClosestPoint<PointType, PointType> icp;
    icp.setInputSource(source_filtered);
    icp.setInputTarget(target_filtered);
    icp.setMaxCorrespondenceDistance(icp_max_correspondence_distance);
    icp.setMaximumIterations(50);
    icp.setTransformationEpsilon(1e-6);
    icp.setEuclideanFitnessEpsilon(1e-6);

    PointCloudXYZI::Ptr aligned_cloud(new PointCloudXYZI());
    icp.align(*aligned_cloud);

    // 发布ICP适应度分数供约束控制器使用
    // 注意：这里暂时注释掉，避免编译错误
    // std_msgs::Float64 fitness_msg;
    // fitness_msg.data = icp.getFitnessScore();
    // pub_icp_fitness.publish(fitness_msg);

    // 检查ICP收敛性和匹配质量
    if (icp.hasConverged() && icp.getFitnessScore() < icp_fitness_threshold) {
        loop_transform = icp.getFinalTransformation();

        ROS_INFO("\033[1;33m[GPS-ICP] Loop Closure Success: GPS_dist=%.2fm, SLAM_dist=%.2fm, Fitness=%.4f\033[0m",
                gps_distance_to_start, trajectory_length, icp.getFitnessScore());

        return true;
    } else {
        ROS_DEBUG("\033[1;33m[GPS-ICP] Loop Closure Failed: GPS_dist=%.2fm, Converged=%s, Fitness=%.4f\033[0m",
                 gps_distance_to_start, icp.hasConverged() ? "Yes" : "No", icp.getFitnessScore());
        return false;
    }
}

// 关键帧管理函数
void manageKeyframes(const PointCloudXYZI::Ptr& current_cloud,
                    const Eigen::Vector3d& current_pose,
                    double timestamp) {
    static int frame_count = 0;
    frame_count++;

    // 每隔keyframe_skip帧保存一个关键帧
    if (frame_count % keyframe_skip == 0) {
        // 创建关键帧点云的副本
        PointCloudXYZI::Ptr keyframe_cloud(new PointCloudXYZI(*current_cloud));

        // 添加到关键帧缓冲区
        keyframe_buffer.push_back(std::make_pair(timestamp, keyframe_cloud));
        pose_buffer.push_back(std::make_pair(timestamp, current_pose));

        // 限制缓冲区大小，保持最近的200个关键帧
        while (keyframe_buffer.size() > 200) {
            keyframe_buffer.pop_front();
        }
        while (pose_buffer.size() > 200) {
            pose_buffer.pop_front();
        }

        ROS_DEBUG("Keyframe saved: Total keyframes=%zu, Timestamp=%.3f",
                 keyframe_buffer.size(), timestamp);
    }
}

// 增强的GPS平面约束函数
Eigen::Vector3d applyGPSPlaneConstraint(const Eigen::Vector3d& slam_position, double timestamp) {
    if (!enable_gps_plane_constraint || !start_positions_set) return slam_position;

    Eigen::Vector3d current_gps_pos;
    double gps_std;

    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return slam_position;
    }

    // 计算SLAM和GPS的相对位移
    Eigen::Vector3d slam_drift = slam_position - start_slam_position;
    Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position;

    // 计算平面位置误差（XY方向）
    Eigen::Vector2d slam_xy(slam_drift.x(), slam_drift.y());
    Eigen::Vector2d gps_xy(gps_drift.x(), gps_drift.y());
    Eigen::Vector2d xy_error = gps_xy - slam_xy;

    double xy_error_norm = xy_error.norm();

    // 检查是否在回环区域，采用10米安全距离
    double distance_to_start = (current_gps_pos - start_gps_position).norm();
    bool in_loop_area = distance_to_start < 10.0;  // 10米安全距离

    // 保守的动态调整校正阈值和校正率
    double effective_threshold = gps_xy_correction_threshold;
    double effective_rate = gps_xy_correction_rate;

    if (in_loop_area && distance_to_start < 5.0) {
        // 只有在5米以内才开始调整参数
        effective_threshold *= 0.9;  // 阈值轻微降低到90%
        effective_rate *= 1.2;       // 校正率轻微提高到1.2倍

        // 根据距离起始点的远近进行温和调整
        if (distance_to_start < 3.0) {
            double distance_factor = 1.0 - (distance_to_start / 3.0);
            effective_rate *= (1.0 + distance_factor * 0.2);  // 最多增加20%的校正率
        }
    }

    // 如果XY误差超过阈值，应用平面约束
    if (xy_error_norm > effective_threshold) {
        Eigen::Vector3d corrected_position = slam_position;

        // 应用XY方向的渐进式校正
        corrected_position.x() += xy_error.x() * effective_rate;
        corrected_position.y() += xy_error.y() * effective_rate;

        // 记录校正信息
        static int correction_count = 0;
        if (++correction_count % 10 == 1) {  // 更频繁的日志输出
            ROS_INFO("\033[1;33m[GPS] Plane Constraint: XY_error=%.3f, Correction=[%.3f,%.3f], InLoop=%s, Rate=%.3f\033[0m",
                    xy_error_norm, xy_error.x() * effective_rate, xy_error.y() * effective_rate,
                    in_loop_area ? "YES" : "NO", effective_rate);
        }

        return corrected_position;
    }

    return slam_position;
}

// 全局轨迹一致性约束
void applyGlobalTrajectoryConstraint() {
    if (!enable_gps_plane_constraint || gps_trajectory_buffer.size() < 10) return;

    // 计算GPS轨迹的总体漂移趋势
    Eigen::Vector3d total_gps_drift = gps_trajectory_buffer.back().second - gps_trajectory_buffer.front().second;
    double trajectory_length = (gps_trajectory_buffer.back().first - gps_trajectory_buffer.front().first);

    if (trajectory_length > 60.0) {  // 超过1分钟的轨迹
        // 计算平均漂移率
        Eigen::Vector2d avg_drift_rate(total_gps_drift.x() / trajectory_length,
                                      total_gps_drift.y() / trajectory_length);

        // 如果漂移率过大，记录警告
        if (avg_drift_rate.norm() > 0.1) {  // 10cm/s的漂移率
            ROS_WARN("High trajectory drift detected: %.3f m/s", avg_drift_rate.norm());
        }
    }
}

// 全局轨迹对齐函数
void performGlobalTrajectoryAlignment() {
    if (!enable_gps_plane_constraint || gps_trajectory_buffer.size() < 20) return;

    // 计算最近一段时间的平均位置误差
    int recent_samples = std::min(20, (int)gps_trajectory_buffer.size());
    Eigen::Vector3d avg_error = Eigen::Vector3d::Zero();

    for (int i = gps_trajectory_buffer.size() - recent_samples; i < gps_trajectory_buffer.size(); i++) {
        double timestamp = gps_trajectory_buffer[i].first;
        Eigen::Vector3d gps_pos = gps_trajectory_buffer[i].second;

        // 这里需要获取对应时间的SLAM位置，简化处理
        Eigen::Vector3d slam_drift = state_point.pos - start_slam_position;
        Eigen::Vector3d gps_drift = gps_pos - start_gps_position;

        avg_error += (gps_drift - slam_drift);
    }

    avg_error /= recent_samples;

    // 如果平均误差较大，记录警告并建议校正
    if (avg_error.head<2>().norm() > 1.0) {  // XY方向误差超过1米
        ROS_WARN("Large trajectory alignment error detected: XY=%.3f m, Z=%.3f m",
                avg_error.head<2>().norm(), std::abs(avg_error.z()));
        ROS_WARN("Consider increasing GPS correction rates or checking GPS quality");
    }
}




#endif

const bool var_contrast(pointWithCov &x, pointWithCov &y) {
    return (x.cov.diagonal().norm() < y.cov.diagonal().norm());
};

void pointBodyToWorld_ikfom(PointType const * const pi, PointType * const po, state_ikfom &s)
{
    V3D p_body(pi->x, pi->y, pi->z);
    V3D p_global(s.rot * (s.offset_R_L_I*p_body + s.offset_T_L_I) + s.pos);

    po->x = p_global(0);
    po->y = p_global(1);
    po->z = p_global(2);
    po->intensity = pi->intensity;
}


void pointBodyToWorld(PointType const * const pi, PointType * const po)
{
    V3D p_body(pi->x, pi->y, pi->z);
    V3D p_global(state_point.rot * (state_point.offset_R_L_I*p_body + state_point.offset_T_L_I) + state_point.pos);

    po->x = p_global(0);
    po->y = p_global(1);
    po->z = p_global(2);
//    po->intensity = pi->intensity;
}

template<typename T>
void pointBodyToWorld(const Matrix<T, 3, 1> &pi, Matrix<T, 3, 1> &po)
{
    V3D p_body(pi[0], pi[1], pi[2]);
    V3D p_global(state_point.rot * (state_point.offset_R_L_I*p_body + state_point.offset_T_L_I) + state_point.pos);

    po[0] = p_global(0);
    po[1] = p_global(1);
    po[2] = p_global(2);
}

void RGBpointBodyToWorld(PointType const * const pi, PointType * const po)
{
    V3D p_body(pi->x, pi->y, pi->z);
    V3D p_global(state_point.rot * (state_point.offset_R_L_I*p_body + state_point.offset_T_L_I) + state_point.pos);

    po->x = p_global(0);
    po->y = p_global(1);
    po->z = p_global(2);
    po->intensity = pi->intensity;
}

void RGBpointBodyLidarToIMU(PointType const * const pi, PointType * const po)
{
    V3D p_body_lidar(pi->x, pi->y, pi->z);
    V3D p_body_imu(state_point.offset_R_L_I*p_body_lidar + state_point.offset_T_L_I);

    po->x = p_body_imu(0);
    po->y = p_body_imu(1);
    po->z = p_body_imu(2);
    po->intensity = pi->intensity;

    po->curvature = pi->curvature;
    po->normal_x = pi->normal_x;
}

void standard_pcl_cbk(const sensor_msgs::PointCloud2::ConstPtr &msg)
{
    auto time_offset = lidar_time_offset;
//    std::printf("lidar offset:%f\n", lidar_time_offset);
    mtx_buffer.lock();
    scan_count ++;
    double preprocess_start_time = omp_get_wtime();
    if (msg->header.stamp.toSec() + time_offset < last_timestamp_lidar)
    {
//        ROS_ERROR("lidar loop back, clear buffer");
        ROS_ERROR("lidar loop back, skip this scan!!!");
//        lidar_buffer.clear();
        mtx_buffer.unlock();
        sig_buffer.notify_all();
        return;
    }

    PointCloudXYZI::Ptr  ptr(new PointCloudXYZI());
    p_pre->process(msg, ptr);
    // 删除过少的点
    // std::printf("points: %ld\n", ptr->size());
    if (ptr->size() < 120)
    {
//        ROS_ERROR("lidar loop back, clear buffer");
        ROS_ERROR("Too few points, skip this scan!!!");
//        lidar_buffer.clear();
        mtx_buffer.unlock();
        sig_buffer.notify_all();
        return;
    }

    lidar_buffer.push_back(ptr);
    time_buffer.push_back(msg->header.stamp.toSec() + time_offset);
    last_timestamp_lidar = msg->header.stamp.toSec() + time_offset;
    s_plot11[scan_count] = omp_get_wtime() - preprocess_start_time;
    mtx_buffer.unlock();
    sig_buffer.notify_all();
}

double timediff_lidar_wrt_imu = 0.0;
bool   timediff_set_flg = false;
// 注释掉livox相关的回调函数
/*
void livox_pcl_cbk(const livox_ros_driver::CustomMsg::ConstPtr &msg)
{
    mtx_buffer.lock();
    double preprocess_start_time = omp_get_wtime();
    scan_count ++;
    if (msg->header.stamp.toSec() < last_timestamp_lidar)
    {
//        ROS_ERROR("lidar loop back, clear buffer");
        ROS_ERROR("lidar loop back, skip this scan!!!");
//        lidar_buffer.clear();
        mtx_buffer.unlock();
        sig_buffer.notify_all();
        return;
    }
    last_timestamp_lidar = msg->header.stamp.toSec();

    if (!time_sync_en && abs(last_timestamp_imu - last_timestamp_lidar) > 10.0 && !imu_buffer.empty() && !lidar_buffer.empty() )
    {
        printf("IMU and LiDAR not Synced, IMU time: %lf, lidar header time: %lf \n",last_timestamp_imu, last_timestamp_lidar);
    }

    if (time_sync_en && !timediff_set_flg && abs(last_timestamp_lidar - last_timestamp_imu) > 1 && !imu_buffer.empty())
    {
        timediff_set_flg = true;
        timediff_lidar_wrt_imu = last_timestamp_lidar + 0.1 - last_timestamp_imu;
        printf("Self sync IMU and LiDAR, time diff is %.10lf \n", timediff_lidar_wrt_imu);
    }

    PointCloudXYZI::Ptr  ptr(new PointCloudXYZI());
    p_pre->process(msg, ptr);
    lidar_buffer.push_back(ptr);
    time_buffer.push_back(last_timestamp_lidar);

    s_plot11[scan_count] = omp_get_wtime() - preprocess_start_time;
    mtx_buffer.unlock();
    sig_buffer.notify_all();
}
*/

void imu_cbk(const sensor_msgs::Imu::ConstPtr &msg_in)
{
    publish_count ++;
    // cout<<"IMU got at: "<<msg_in->header.stamp.toSec()<<endl;
    sensor_msgs::Imu::Ptr msg(new sensor_msgs::Imu(*msg_in));

    if (abs(timediff_lidar_wrt_imu) > 0.1 && time_sync_en)
    {
        msg->header.stamp = \
        ros::Time().fromSec(timediff_lidar_wrt_imu + msg_in->header.stamp.toSec());
    }

    double timestamp = msg->header.stamp.toSec();

    if (timestamp < last_timestamp_imu)
    {
//        ROS_WARN("imu loop back, clear buffer");
//        imu_buffer.clear();
        ROS_WARN("imu loop back, ignoring!!!");
        ROS_WARN("current T: %f, last T: %f", timestamp, last_timestamp_imu);
        return;
    }
    // 剔除异常数据
    if (std::abs(msg->angular_velocity.x) > 10
        || std::abs(msg->angular_velocity.y) > 10
        || std::abs(msg->angular_velocity.z) > 10) {
        ROS_WARN("Large IMU measurement!!! Drop Data!!! %.3f  %.3f  %.3f",
                 msg->angular_velocity.x,
                 msg->angular_velocity.y,
                 msg->angular_velocity.z
        );
        return;
    }

//    // 如果是第一帧 拿过来做重力对齐
//    // TODO 用多帧平均的重力
//    if (is_first_imu) {
//        double acc_vec[3] = {msg_in->linear_acceleration.x, msg_in->linear_acceleration.y, msg_in->linear_acceleration.z};
//
//        R__world__o__initial = SO3(g2R(Eigen::Vector3d(acc_vec)));
//
//        is_first_imu = false;
//    }

    last_timestamp_imu = timestamp;

    mtx_buffer.lock();

    imu_buffer.push_back(msg);
    mtx_buffer.unlock();
    sig_buffer.notify_all();
}

void encoder_cbk(const nav_msgs::Odometry::ConstPtr &msg_in)
{
    encoder_buffer.push_back(msg_in);
}

double lidar_mean_scantime = 0.0;
int    scan_num = 0;
bool sync_packages(MeasureGroup &meas)
{
    if (lidar_buffer.empty() || imu_buffer.empty()) {
        return false;
    }

    /*** push a lidar scan ***/
    if(!lidar_pushed)
    {
        meas.lidar = lidar_buffer.front();
        meas.lidar_beg_time = time_buffer.front();
        if (meas.lidar->points.size() <= 1) // time too little
        {
            lidar_end_time = meas.lidar_beg_time + lidar_mean_scantime;
            ROS_WARN("Too few input point cloud!\n");
        }
        else if (meas.lidar->points.back().curvature / double(1000) < 0.5 * lidar_mean_scantime)
        {
            lidar_end_time = meas.lidar_beg_time + lidar_mean_scantime;
        }
        else
        {

//            std::printf("\nFirst 100 points: \n");
//            for(int i=0; i < 100; ++i){
//                std::printf("%f ", meas.lidar->points[i].curvature  / double(1000));
//            }
//
//            std::printf("\n Last 100 points: \n");
//            for(int i=100; i >0; --i){
//                std::printf("%f ", meas.lidar->points[meas.lidar->size() - i - 1].curvature / double(1000));
//            }
//            std::printf("last point offset time: %f\n", meas.lidar->points.back().curvature / double(1000));
            scan_num ++;
            lidar_end_time = meas.lidar_beg_time + meas.lidar->points.back().curvature / double(1000);
//            lidar_end_time = meas.lidar_beg_time + (meas.lidar->points[meas.lidar->points.size() - 20]).curvature / double(1000);
            lidar_mean_scantime += (meas.lidar->points.back().curvature / double(1000) - lidar_mean_scantime) / scan_num;
//            std::printf("pcl_bag_time: %f\n", meas.lidar_beg_time);
//            std::printf("lidar_end_time: %f\n", lidar_end_time);
        }

        meas.lidar_end_time = lidar_end_time;
//        std::printf("Scan start timestamp: %f, Scan end time: %f\n", meas.lidar_beg_time, meas.lidar_end_time);

        lidar_pushed = true;
    }

    if (last_timestamp_imu < lidar_end_time)
    {
        return false;
    }

    /*** push imu data, and pop from imu buffer ***/
    double imu_time = imu_buffer.front()->header.stamp.toSec();
    meas.imu.clear();
    while ((!imu_buffer.empty()) && (imu_time < lidar_end_time))
    {
        imu_time = imu_buffer.front()->header.stamp.toSec();
        if(imu_time > lidar_end_time) break;
        meas.imu.push_back(imu_buffer.front());
        imu_buffer.pop_front();
    }

    lidar_buffer.pop_front();
    time_buffer.pop_front();
    lidar_pushed = false;
    return true;
}

PointCloudXYZI::Ptr pcl_wait_pub(new PointCloudXYZI(500000, 1));
PointCloudXYZI::Ptr pcl_wait_save(new PointCloudXYZI());

// 全局点云保存相关变量
PointCloudXYZI::Ptr global_map_points(new PointCloudXYZI());
bool pcd_save_en = false;
int pcd_save_interval = -1;
string pcd_save_directory = "/home/<USER>/slam_share/aLidar/temp";
int saved_frame_count = 0;


// Save global point cloud to PCD file
void save_global_map_pcd() {
    if (!pcd_save_en || global_map_points->empty()) return;

    // Create save directory
    if (!boost::filesystem::exists(pcd_save_directory)) {
        boost::filesystem::create_directories(pcd_save_directory);
    }

    // Generate filename
    std::stringstream ss;
    ss << pcd_save_directory << "/global_map_"
       << std::setfill('0') << std::setw(6) << saved_frame_count << ".pcd";

    // Save PCD file
    try {
        pcl::io::savePCDFileBinary(ss.str(), *global_map_points);
        ROS_INFO("Global map saved to: %s (Points: %zu)", ss.str().c_str(), global_map_points->size());
        saved_frame_count++;

        // 如果设置了间隔保存，清空累积的点云
        if (pcd_save_interval > 0) {
            global_map_points->clear();
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to save PCD file: %s", e.what());
    }
}

void publish_frame_world(const ros::Publisher & pubLaserCloudFull)
{
    if(scan_pub_en)
    {
        if (scan_index <= 1){
            return;
        }
        PointCloudXYZI::Ptr laserCloudFullRes(dense_pub_en ? feats_undistort : feats_down_body);
        // 随机下采样 只用于可视化
        if(!dense_pub_en){
            downSizeFilterVis.setSample(publish_downsample_points);
            downSizeFilterVis.setSeed (std::rand());
            downSizeFilterVis.setInputCloud(feats_down_body);
            downSizeFilterVis.filter(*laserCloudFullRes);
        }
        int size = laserCloudFullRes->points.size();
        PointCloudXYZI laserCloudWorld;
        for (int i = 0; i < size; i++)
        {
            if (i % publish_dense_skip !=0) {
                continue;
            }
            PointType const * const p = &laserCloudFullRes->points[i];
            if(p->intensity < intensity_th){
                continue;
            }
            // 转换到gravity aligned系 删除过高的点, 保证平面的可视化效果
            V3D p_body;
            p_body << p->x, p->y, p->z;
            V3D p_gravaity = p_imu->Initial_R_wrt_G * state_point.rot * state_point.offset_R_L_I * p_body;
            if (p_gravaity.z() > publish_limit_z) {
                continue;
            }

//            if (p->x < 0 and p->x > -4
//                    and p->y < 1.5 and p->y > -1.5
//                            and p->z < 2 and p->z > -1) {
//                continue;
//            }
            PointType p_world;

            RGBpointBodyToWorld(p, &p_world);
//            if (p_world.z > 1) {
//                continue;
//            }
            laserCloudWorld.push_back(p_world);
//            RGBpointBodyToWorld(&laserCloudFullRes->points[i], \
//                                &laserCloudWorld->points[i]);
        }

        sensor_msgs::PointCloud2 laserCloudmsg;
        pcl::toROSMsg(laserCloudWorld, laserCloudmsg);
        laserCloudmsg.header.stamp = ros::Time().fromSec(lidar_end_time);
        laserCloudmsg.header.frame_id = "camera_init";
        pubLaserCloudFull.publish(laserCloudmsg);
        publish_count -= PUBFRAME_PERIOD;

        // 累积全局点云用于保存
        if (pcd_save_en) {
            *global_map_points += laserCloudWorld;

            // 根据间隔设置决定何时保存
            if (pcd_save_interval > 0 && scan_index % pcd_save_interval == 0) {
                save_global_map_pcd();
            }
        }
    }

}

void publish_frame_body(const ros::Publisher & pubLaserCloudFull_body)
{
//    int size = feats_undistort->points.size();
    PointCloudXYZI::Ptr laserCloudFullRes(dense_pub_en ? feats_undistort : feats_down_body);
    int size = laserCloudFullRes->points.size();
    PointCloudXYZI::Ptr laserCloudIMUBody(new PointCloudXYZI(size, 1));
    for (int i = 0; i < size; i++)
    {
        RGBpointBodyLidarToIMU(&laserCloudFullRes->points[i], \
                            &laserCloudIMUBody->points[i]);
    }

    sensor_msgs::PointCloud2 laserCloudmsg;
    pcl::toROSMsg(*laserCloudIMUBody, laserCloudmsg);
    laserCloudmsg.header.stamp = ros::Time().fromSec(lidar_end_time);
    laserCloudmsg.header.frame_id = "body";
    pubLaserCloudFull_body.publish(laserCloudmsg);
    publish_count -= PUBFRAME_PERIOD;
}

void publish_map(const ros::Publisher & pubLaserCloudMap)
{
    sensor_msgs::PointCloud2 laserCloudMap;
    pcl::toROSMsg(*featsFromMap, laserCloudMap);
    laserCloudMap.header.stamp = ros::Time().fromSec(lidar_end_time);
    laserCloudMap.header.frame_id = "camera_init";
    pubLaserCloudMap.publish(laserCloudMap);
}

template<typename T>
void set_posestamp(T & out)
{
    out.pose.position.x = state_point.pos(0);
    out.pose.position.y = state_point.pos(1);
    out.pose.position.z = state_point.pos(2);
    out.pose.orientation.x = geoQuat.x;
    out.pose.orientation.y = geoQuat.y;
    out.pose.orientation.z = geoQuat.z;
    out.pose.orientation.w = geoQuat.w;

}

void publish_odometry(const ros::Publisher & pubOdomAftMapped)
{
    odomAftMapped.header.frame_id = "camera_init";
    odomAftMapped.child_frame_id = "body";
    odomAftMapped.header.stamp = ros::Time().fromSec(lidar_end_time);// ros::Time().fromSec(lidar_end_time);
    set_posestamp(odomAftMapped.pose);
    pubOdomAftMapped.publish(odomAftMapped);
    auto P = kf.get_P();
    for (int i = 0; i < 6; i ++)
    {
        int k = i < 3 ? i + 3 : i - 3;
        odomAftMapped.pose.covariance[i*6 + 0] = P(k, 3);
        odomAftMapped.pose.covariance[i*6 + 1] = P(k, 4);
        odomAftMapped.pose.covariance[i*6 + 2] = P(k, 5);
        odomAftMapped.pose.covariance[i*6 + 3] = P(k, 0);
        odomAftMapped.pose.covariance[i*6 + 4] = P(k, 1);
        odomAftMapped.pose.covariance[i*6 + 5] = P(k, 2);
    }

    static tf::TransformBroadcaster br;
    tf::Transform                   transform;
    tf::Quaternion                  q;
    transform.setOrigin(tf::Vector3(odomAftMapped.pose.pose.position.x, \
                                    odomAftMapped.pose.pose.position.y, \
                                    odomAftMapped.pose.pose.position.z));
    q.setW(odomAftMapped.pose.pose.orientation.w);
    q.setX(odomAftMapped.pose.pose.orientation.x);
    q.setY(odomAftMapped.pose.pose.orientation.y);
    q.setZ(odomAftMapped.pose.pose.orientation.z);
    transform.setRotation( q );
    br.sendTransform( tf::StampedTransform( transform, odomAftMapped.header.stamp, "camera_init", "body" ) );

    static tf::TransformBroadcaster br_world;
    transform.setOrigin(tf::Vector3(0, 0, 0));
    q.setValue(p_imu->Initial_R_wrt_G.x(), p_imu->Initial_R_wrt_G.y(), p_imu->Initial_R_wrt_G.z(), p_imu->Initial_R_wrt_G.w());
    transform.setRotation(q);
    br_world.sendTransform(tf::StampedTransform(transform, odomAftMapped.header.stamp, "world", "camera_init"));
}

void publish_path(const ros::Publisher pubPath)
{
    set_posestamp(msg_body_pose);
    msg_body_pose.header.stamp = ros::Time().fromSec(lidar_end_time);
    msg_body_pose.header.frame_id = "camera_init";

    /*** if path is too large, the rvis will crash ***/
    static int jjj = 0;
    jjj++;
    if (jjj % publish_path_skip == 0)
    {
        path.header.stamp = msg_body_pose.header.stamp;
        path.poses.push_back(msg_body_pose);
        pubPath.publish(path);
    }
}

void transformLidar(const state_ikfom &state_point, const PointCloudXYZI::Ptr &input_cloud, PointCloudXYZI::Ptr &trans_cloud)
{
    trans_cloud->clear();
    for (size_t i = 0; i < input_cloud->size(); i++) {
        pcl::PointXYZINormal p_c = input_cloud->points[i];
        Eigen::Vector3d p_lidar(p_c.x, p_c.y, p_c.z);
        // HACK we need to specify p_body as a V3D type!!!
        V3D p_body = state_point.rot * (state_point.offset_R_L_I * p_lidar + state_point.offset_T_L_I) + state_point.pos;
        PointType pi;
        pi.x = p_body(0);
        pi.y = p_body(1);
        pi.z = p_body(2);
        pi.intensity = p_c.intensity;
        trans_cloud->points.push_back(pi);
    }
}

//M3D transformLiDARCovToWorld(Eigen::Vector3d &p_lidar, const esekfom::esekf<state_ikfom, 12, input_ikfom>& kf, const Eigen::Matrix3d& COV_lidar)
//{
//    double match_start = omp_get_wtime();
//    // FIXME 这里首先假定LiDAR系和body是重叠的 没有外参
//    M3D point_crossmat;
//    point_crossmat << SKEW_SYM_MATRX(p_lidar);
//    // 注意这里Rt的cov顺序
//    M3D rot_var = kf.get_P().block<3, 3>(3, 3);
//    M3D t_var = kf.get_P().block<3, 3>(0, 0);
//    auto state = kf.get_x();
//
//    // Eq. (3)
//    M3D COV_world =
//            state.rot * COV_lidar * state.rot.conjugate()
//            + state.rot * (-point_crossmat) * rot_var * (-point_crossmat).transpose()  * state.rot.conjugate()
//            + t_var;
//    return COV_world;
//    // Voxel map 真实实现
////    M3D cov_world = R_body * COV_lidar * R_body.conjugate() +
////          (-point_crossmat) * rot_var * (-point_crossmat).transpose() + t_var;
//
//}

M3D transformLiDARCovToWorld(Eigen::Vector3d &p_lidar, const esekfom::esekf<state_ikfom, 12, input_ikfom>& kf, const Eigen::Matrix3d& COV_lidar)
{
    M3D point_crossmat;
    point_crossmat << SKEW_SYM_MATRX(p_lidar);
    auto state = kf.get_x();

    // lidar到body的方差传播
    // 注意外参的var是先rot 后pos
    M3D il_rot_var = kf.get_P().block<3, 3>(6, 6);
    M3D il_t_var = kf.get_P().block<3, 3>(9, 9);

    M3D COV_body =
            state.offset_R_L_I * COV_lidar * state.offset_R_L_I.conjugate()
            + state.offset_R_L_I * (-point_crossmat) * il_rot_var * (-point_crossmat).transpose() * state.offset_R_L_I.conjugate()
            + il_t_var;

    // body的坐标
    V3D p_body = state.offset_R_L_I * p_lidar + state.offset_T_L_I;

    // body到world的方差传播
    // 注意pose的var是先pos 后rot
    point_crossmat << SKEW_SYM_MATRX(p_body);
    M3D rot_var = kf.get_P().block<3, 3>(3, 3);
    M3D t_var = kf.get_P().block<3, 3>(0, 0);

    // Eq. (3)
    M3D COV_world =
        state.rot * COV_body * state.rot.conjugate()
        + state.rot * (-point_crossmat) * rot_var * (-point_crossmat).transpose()  * state.rot.conjugate()
        + t_var;

    return COV_world;
    // Voxel map 真实实现
//    M3D cov_world = R_body * COV_lidar * R_body.conjugate() +
//          (-point_crossmat) * rot_var * (-point_crossmat).transpose() + t_var;

}

void observation_model_share(state_ikfom &s, esekfom::dyn_share_datastruct<double> &ekfom_data)
{
//    laserCloudOri->clear();
//    corr_normvect->clear();
    feats_with_correspondence->clear();
    total_residual = 0.0;

    // =================================================================================================================
    // 用当前迭代轮最新的位姿估计值 将点云转换到world地图系
    vector<pointWithCov> pv_list;
    PointCloudXYZI::Ptr world_lidar(new PointCloudXYZI);
    // FIXME stupid mistake 这里应该用迭代的最新线性化点
    // FIXME stupid mistake 这里应该用迭代的最新线性化点
//    transformLidar(state_point, feats_down_body, world_lidar);
    transformLidar(s, feats_down_body, world_lidar);
    pv_list.resize(feats_down_body->size());
    for (size_t i = 0; i < feats_down_body->size(); i++) {
        // 保存body系和world系坐标
        pointWithCov pv;
        pv.point << feats_down_body->points[i].x, feats_down_body->points[i].y, feats_down_body->points[i].z;
        pv.point_world << world_lidar->points[i].x, world_lidar->points[i].y, world_lidar->points[i].z;
        // 计算lidar点的cov
        // 注意这个在每次迭代时是存在重复计算的 因为lidar系的点云covariance是不变的
        // M3D cov_lidar = calcBodyCov(pv.point, ranging_cov, angle_cov);
        M3D cov_lidar = var_down_body[i];
        // 将body系的var转换到world系
        M3D cov_world = transformLiDARCovToWorld(pv.point, kf, cov_lidar);
        pv.cov = cov_world;
        pv.cov_lidar = cov_lidar;
        pv_list[i] = pv;
    }

    // ===============================================================================================================
    // 查找最近点 并构建residual
    double match_start = omp_get_wtime();
    std::vector<ptpl, Eigen::aligned_allocator<ptpl>> ptpl_list;
    std::vector<V3D, Eigen::aligned_allocator<V3D>> non_match_list;

    current_state_point = s;
    BuildResidualListOMP(voxel_map, max_voxel_size, 3.0, max_layer, pv_list,
                         ptpl_list, non_match_list);
    double match_end = omp_get_wtime();
    // std::printf("Match Time: %f\n", match_end - match_start);

    /*** Computation of Measuremnt Jacobian matrix H and measurents vector ***/
    // 根据匹配结果 设置H和R的维度
    // h_x是观测值对状态量的导数 TODO 为什么不加上状态量对状态量误差的导数？？？？像quaternion那本书？
    effct_feat_num = ptpl_list.size();
    if (effct_feat_num < 1){
        ekfom_data.valid = false;
        ROS_WARN("No Effective Points! \n");
        return;
    }
    ekfom_data.h_x = MatrixXd::Zero(effct_feat_num, 12); //23 因为点面距离只和位姿 外参有关 对其他状态量的导数都是0
    ekfom_data.h.resize(effct_feat_num);
    ekfom_data.R.resize(effct_feat_num, 1); // 把R作为向量 用的时候转换成diag
//    ekfom_data.R.setZero();
//    printf("isDiag: %d  R norm: %f\n", ekfom_data.R.isDiagonal(1e-10), ekfom_data.R.norm());

//    // 求每个匹配点到平面的距离
//    for (int i = 0; i < ptpl_list.size(); i++) {
//        // 取出匹配到的world系norm
//        PointType pl;
//        pl.x = ptpl_list[i].normal(0);
//        pl.y = ptpl_list[i].normal(1);
//        pl.z = ptpl_list[i].normal(2);
//
//        // 将原始点云转换到world系
//        V3D pi_world(s.rot * (s.offset_R_L_I * ptpl_list[i].point + s.offset_T_L_I) + s.pos);
//
//        // 求点面距离
//        float dis = pi_world.x() * pl.x + pi_world.y() * pl.y + pi_world.z() * pl.z + ptpl_list[i].d;
//        pl.intensity = dis;
////        std::printf("%.5f   %.5f\n", dis, ptpl_list[i].pd2);
////        std::printf("%.5f  %.5f\n", pi_world.x(), ptpl_list[i].point_world.x());
////        std::printf("%.5f  %.5f\n", pi_world.y(), ptpl_list[i].point_world.y());
//
//        PointType pi_body;
//        pi_body.x = ptpl_list[i].point(0);
//        pi_body.y = ptpl_list[i].point(1);
//        pi_body.z = ptpl_list[i].point(2);
//        laserCloudOri->push_back(pi_body);
//        corr_normvect->push_back(pl);
//        // for visualization
//        feats_with_correspondence->push_back(pi_body);
//
//        total_residual += fabs(dis);
//    }
//    assert(laserCloudOri->size() == effct_feat_num && corr_normvect->size() == effct_feat_num);
#ifdef MP_EN
    omp_set_num_threads(MP_PROC_NUM);
#pragma omp parallel for
#endif
    for (int i = 0; i < effct_feat_num; i++)
    {

//        const PointType &laser_p  = laserCloudOri->points[i];
        V3D point_this_be(ptpl_list[i].point);
        M3D point_be_crossmat;
        point_be_crossmat << SKEW_SYM_MATRX(point_this_be);
        V3D point_this = s.offset_R_L_I * point_this_be + s.offset_T_L_I;
        M3D point_crossmat;
        point_crossmat<<SKEW_SYM_MATRX(point_this);

        /*** get the normal vector of closest surface/corner ***/
//        const PointType &norm_p = corr_normvect->points[i];
//        V3D norm_vec(norm_p.x, norm_p.y, norm_p.z);
        V3D norm_vec(ptpl_list[i].normal);

        /*** calculate the Measuremnt Jacobian matrix H ***/
        V3D C(s.rot.conjugate() *norm_vec);
        V3D A(point_crossmat * C);
        if (extrinsic_est_en)
        {
            V3D B(point_be_crossmat * s.offset_R_L_I.conjugate() * C); //s.rot.conjugate()*norm_vec);
            // ekfom_data.h_x.block<1, 12>(i,0) << norm_p.x, norm_p.y, norm_p.z, VEC_FROM_ARRAY(A), VEC_FROM_ARRAY(B), VEC_FROM_ARRAY(C);
            ekfom_data.h_x.block<1, 12>(i,0) << norm_vec.x(), norm_vec.y(), norm_vec.z(), VEC_FROM_ARRAY(A), VEC_FROM_ARRAY(B), VEC_FROM_ARRAY(C);
        }
        else
        {
            // ekfom_data.h_x.block<1, 12>(i,0) << norm_p.x, norm_p.y, norm_p.z, VEC_FROM_ARRAY(A), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
            ekfom_data.h_x.block<1, 12>(i,0) << norm_vec.x(), norm_vec.y(), norm_vec.z(), VEC_FROM_ARRAY(A), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0;
        }

        /*** Measuremnt: distance to the closest surface/corner ***/
//        ekfom_data.h(i) = -norm_p.intensity;
        float pd2 = norm_vec.x() * ptpl_list[i].point_world.x()
                + norm_vec.y() * ptpl_list[i].point_world.y()
                + norm_vec.z() * ptpl_list[i].point_world.z()
                + ptpl_list[i].d;
        ekfom_data.h(i) = -pd2;

        /*** Covariance ***/
//        // norm_p中存了匹配的平面法向 还有点面距离
//        V3D point_world = s.rot * (s.offset_R_L_I * ptpl_list[i].point + s.offset_T_L_I) + s.pos;
//        // /*** get the normal vector of closest surface/corner ***/
//        Eigen::Matrix<double, 1, 6> J_nq;
//        J_nq.block<1, 3>(0, 0) = point_world - ptpl_list[i].center;
//        J_nq.block<1, 3>(0, 3) = -ptpl_list[i].normal;
//        double sigma_l = J_nq * ptpl_list[i].plane_cov * J_nq.transpose();
//
//        M3D cov_lidar = calcBodyCov(ptpl_list[i].point, ranging_cov, angle_cov);
//        M3D R_cov_Rt = s.rot * cov_lidar * s.rot.conjugate();
//        // HACK 1. 因为是标量 所以求逆直接用1除
//        // HACK 2. 不同分量的方差用加法来合成 因为公式(12)中的Sigma是对角阵，逐元素运算之后就是对角线上的项目相加
//        double R_inv = 1.0 / (sigma_l + norm_vec.transpose() * R_cov_Rt * norm_vec);

        // norm_p中存了匹配的平面法向 还有点面距离
        // V3D point_world = s.rot * (s.offset_R_L_I * ptpl_list[i].point + s.offset_T_L_I) + s.pos;
        V3D point_world = ptpl_list[i].point_world;
        // /*** get the normal vector of closest surface/corner ***/
        Eigen::Matrix<double, 1, 6> J_nq;
        J_nq.block<1, 3>(0, 0) = point_world - ptpl_list[i].center;
        J_nq.block<1, 3>(0, 3) = -ptpl_list[i].normal;
        double sigma_l = J_nq * ptpl_list[i].plane_cov * J_nq.transpose();

        // M3D cov_lidar = calcBodyCov(ptpl_list[i].point, ranging_cov, angle_cov);
        M3D cov_lidar = ptpl_list[i].cov_lidar;
        M3D R_cov_Rt = s.rot * s.offset_R_L_I * cov_lidar * s.offset_R_L_I.conjugate() * s.rot.conjugate();
        // HACK 1. 因为是标量 所以求逆直接用1除
        // HACK 2. 不同分量的方差用加法来合成 因为公式(12)中的Sigma是对角阵，逐元素运算之后就是对角线上的项目相加
        double R_inv = 1.0 / (sigma_l + norm_vec.transpose() * R_cov_Rt * norm_vec);

        // 计算测量方差R并赋值 目前暂时使用固定值
        // ekfom_data.R(i) = 1.0 / LASER_POINT_COV;
        ekfom_data.R(i) = R_inv;
    }

    // std::printf("Effective Points: %d\n", effct_feat_num);
    res_mean_last = total_residual / effct_feat_num;
    // std::printf("res_mean: %f\n", res_mean_last);
    // std::printf("ef_num: %d\n", effct_feat_num);
}

nav_msgs::Odometry uncoupled_encoder_fusion(double time_odom, const state_ikfom &state)
{
    nav_msgs::Odometry result_odom;
    result_odom.header.frame_id = "camera_init";
    result_odom.child_frame_id = "encoder_body";
    result_odom.header.stamp = ros::Time().fromSec(time_odom);

    if (time_odom <= 0) {
        ROS_WARN("First frame, return.");
        result_odom.child_frame_id = "-1";
        return result_odom;
    }
    // 如果没有encoder数据或者odom数据太早 直接返回
    if (encoder_buffer.empty() || time_odom < encoder_buffer.front()->header.stamp.toSec()) {
        // ROS_WARN("No encoder data or odometry data too early!");
        result_odom.child_frame_id = "-1";
        return result_odom;
    }
    // 如果encoder数据太晚，需要等待或者直接drop
    if (encoder_buffer.back()->header.stamp.toSec() < time_odom) {
        ROS_WARN("Odometry data too late, need wait for encoder! %f, %f",
                 encoder_buffer.back()->header.stamp.toSec(), time_odom);
        result_odom.child_frame_id = "-2";
        return result_odom;
    }
    bool found = false;
    // 找到距离time最近的前后两帧encoder数据
    for (auto it = encoder_buffer.begin(); it != encoder_buffer.end() - 1; it++) {
        double time_encoder_head = (*it)->header.stamp.toSec();
        double time_encoder_tail = (*(it + 1))->header.stamp.toSec();
        if (time_odom <= time_encoder_tail) {
            // find encoder poses previous and next to current odometry
            found = true;
            SO3 encoder_head = SO3(
                    (*it)->pose.pose.orientation.w,
                    (*it)->pose.pose.orientation.x,
                    (*it)->pose.pose.orientation.y,
                    (*it)->pose.pose.orientation.z);
            SO3 encoder_tail = SO3(
                    (*(it + 1))->pose.pose.orientation.w,
                    (*(it + 1))->pose.pose.orientation.x,
                    (*(it + 1))->pose.pose.orientation.y,
                    (*(it + 1))->pose.pose.orientation.z);

            // slerp to align the encoder measurements with odometry
            SO3 R_base_o_encoder = encoder_head.slerp(
                    (time_odom - time_encoder_head) / (time_encoder_tail - time_encoder_head), encoder_tail);
            SO3 R_encoder_o_base = (R_base_o_encoder * R_encoder_o_zero_point).inverse();

            // lidar w.r.t odom(camera_init) pose
            SO3 R_odom_o_lidar = state.rot * state.offset_R_L_I;
            V3D t_odom_o_lidar = state.rot * state.offset_T_L_I + state.pos;

            // R_odom_o_lidar * (R_lidar_o_encoder * R_encoder_o_base + t_lidar_o_encoder) + t_odom_o_lidar
            SO3 R_odom_o_base = R_odom_o_lidar * R_lidar_o_encoder * R_encoder_o_base;
            V3D t_odom_o_base = R_odom_o_lidar * t_lidar_o_encoder + t_odom_o_lidar;

            /*
            // debug information
            auto euler_head = encoder_head.toRotationMatrix().eulerAngles(0, 1, 2);
            auto euler_tail = encoder_tail.toRotationMatrix().eulerAngles(0, 1, 2);
            auto euler_slerp = R_base_o_encoder.toRotationMatrix().eulerAngles(0, 1, 2);

            std::printf("\n\n\n");
            std::printf("head: %.7f -> %.3f %.3f %.3f\n",
                        time_encoder_head, euler_head[0], euler_head[1], euler_head[2]);
            std::printf("tail: %.7f -> %.3f %.3f %.3f\n",
                        time_encoder_tail, euler_tail[0], euler_tail[1], euler_tail[2]);
            std::printf("odom: %.7f -> %.3f %.3f %.3f\n",
                        time_odom, euler_slerp[0], euler_slerp[1], euler_slerp[2]);
            std::printf("Q front: %.7f, Q back: %.3f, Q size: %ld\n",
                        encoder_buffer.front()->header.stamp.toSec(),
                        encoder_buffer.back()->header.stamp.toSec(),
                        encoder_buffer.size());
            */

            result_odom.pose.pose.orientation.w = R_odom_o_base.w();
            result_odom.pose.pose.orientation.x = R_odom_o_base.x();
            result_odom.pose.pose.orientation.y = R_odom_o_base.y();
            result_odom.pose.pose.orientation.z = R_odom_o_base.z();
            result_odom.pose.pose.position.x = t_odom_o_base.x();
            result_odom.pose.pose.position.y = t_odom_o_base.y();
            result_odom.pose.pose.position.z = t_odom_o_base.z();
            break;
        }
    }
    // clear up encoder queue
    // XXX may need lock？
    double time_encoder = encoder_buffer.front()->header.stamp.toSec();
    while (time_encoder < time_odom - 0.1 && !encoder_buffer.empty()) {
        encoder_buffer.pop_front();
        time_encoder = encoder_buffer.front()->header.stamp.toSec();
    }
    // std::printf("After Q clean up, size: %ld\n", encoder_buffer.size());

    return result_odom;
}


/*** ROS subscribe initialization ***/
ros::Subscriber sub_pcl;
ros::Subscriber sub_imu;
ros::Subscriber sub_encoder;
ros::Publisher pubLaserCloudFull;
ros::Publisher pubLaserCloudFull_body;
ros::Publisher pubLaserCloudEffect;
ros::Publisher pubLaserCloudMap;
ros::Publisher pubOdomAftMapped;
ros::Publisher pubOdomEncoderBody;
ros::Publisher pubExtrinsic;
ros::Publisher pubPath;
ros::Publisher voxel_map_pub;
ros::Publisher stats_pub;
ros::Publisher pub_icp_fitness;  // ICP适应度分数发布器

// for Plane Map
bool init_map = false;


// statistic
double sum_optimize_time = 0, sum_update_time = 0;
std::fstream stat_latency("/tmp/latency.csv", std::ios::out);

void execute(){
    // execute one step of state estimation and mapping
    if (flg_first_scan)
    {
        first_lidar_time = Measures.lidar_beg_time;
        p_imu->first_lidar_time = first_lidar_time;
        flg_first_scan = false;
        // continue;
        return;
    }

    double t_optimize_start = omp_get_wtime();
    p_imu->Process(Measures, kf, feats_undistort);
    state_point = kf.get_x();
    pos_lid = state_point.pos + state_point.rot * state_point.offset_T_L_I;

  if (feats_undistort->empty() || (feats_undistort == NULL)) {
    ROS_WARN("No point, skip this scan!\n");
    // continue;
    return;
  }

    flg_EKF_inited = (Measures.lidar_beg_time - first_lidar_time) < INIT_TIME ? \
                            false : true;
    // ===============================================================================================================
    // 第一帧 如果ekf初始化了 就初始化voxel地图
    if (flg_EKF_inited && !init_map) {
        PointCloudXYZI::Ptr world_lidar(new PointCloudXYZI);
        transformLidar(state_point, feats_undistort, world_lidar);
        std::vector<pointWithCov> pv_list;

        // std::cout << kf.get_P() << std::endl;
        // 计算第一帧所有点的covariance 并用于构建初始地图
        for (size_t i = 0; i < world_lidar->size(); i++) {
            pointWithCov pv;
            pv.point << world_lidar->points[i].x, world_lidar->points[i].y,
                    world_lidar->points[i].z;
            V3D point_this(feats_undistort->points[i].x,
                           feats_undistort->points[i].y,
                           feats_undistort->points[i].z);
            // if z=0, error will occur in calcBodyCov. To be solved
            if (point_this[2] == 0) {
                point_this[2] = 0.001;
            }
            M3D cov_lidar = calcBodyCov(point_this, ranging_cov, angle_cov);
            // 转换到world系
            M3D cov_world = transformLiDARCovToWorld(point_this, kf, cov_lidar);

            pv.cov = cov_world;
            pv_list.push_back(pv);
            Eigen::Vector3d sigma_pv = pv.cov.diagonal();
            sigma_pv[0] = sqrt(sigma_pv[0]);
            sigma_pv[1] = sqrt(sigma_pv[1]);
            sigma_pv[2] = sqrt(sigma_pv[2]);
        }

        // 当前state point 赋值
        current_state_point = kf.get_x();
        buildVoxelMap(pv_list, max_voxel_size, max_layer, layer_size,
                      max_points_size, max_points_size, min_eigen_value,
                      voxel_map);
        std::cout << "build voxel map" << std::endl;

        if (publish_voxel_map) {
            current_state_point = kf.get_x();
            pubVoxelMap(voxel_map, publish_max_voxel_layer, voxel_map_pub);
            publish_frame_world(pubLaserCloudFull);
            publish_frame_body(pubLaserCloudFull_body);
        }
        init_map = true;
        // continue;
        return;
    }

    /*** downsample the feature points in a scan ***/
    downSizeFilterSurf.setInputCloud(feats_undistort);
    downSizeFilterSurf.filter(*feats_down_body);

    // 如果首次下采样点数量还是太多(一般是大场景,不需要这么多点) 那么就adaptive 再次下采样
    if (adaptive_voxelization) {
        size_t feats_down_size_first = feats_down_body->points.size();
        // 倒序查找LUT确定下采样的粒度
        // XXX 需要保证LUT是升序的!
        int search_idx;
        bool is_found = false;
        if (feats_down_size_first < adaptive_threshold[0]) {
            // LUT的第一个值是特殊保护值,用于退化环境升采样
            // 如果点数太少了,少于保护值, 那么反倒进行升采样, 防止退化
            search_idx = 0;
            is_found = true;
        } else{
            for (search_idx = adaptive_threshold.size() - 1; search_idx > 0; search_idx--) {
                // std::printf("\n %ld %ld %f %f\n",
                //            search_idx, adaptive_threshold.size(), adaptive_threshold[search_idx], adaptive_multiple_factor[search_idx]);
                // 首次大于阈值, 就用对应的下采样粒度
                if (feats_down_size_first > adaptive_threshold[search_idx]) {
                    is_found = true;
                    break;
                }
            }
        }
        // 如果查找到LUT中的某个阈值, 就用对应的粒度下采样
        if (is_found) {
            float leaf_size_scaled = filter_size_surf_min * adaptive_multiple_factor[search_idx];
            downSizeFilterAdaptive.setLeafSize(leaf_size_scaled, leaf_size_scaled, leaf_size_scaled);
            if (leaf_size_scaled < filter_size_surf_min) {
                // 升采样 用原始点云
                downSizeFilterAdaptive.setInputCloud(feats_undistort);
            } else {
                downSizeFilterAdaptive.setInputCloud(feats_down_body);
            }
            downSizeFilterAdaptive.filter(*feats_down_body);
            std::printf("ADV: RAW: %10ld | First:  %10ld | Adap: %10ld, %5fpts, %5fm\n",
                        feats_undistort->size(),
                        feats_down_size_first,
                        feats_down_body->size(),
                        adaptive_threshold[search_idx],
                        leaf_size_scaled);
        }
    }

    sort(feats_down_body->points.begin(), feats_down_body->points.end(), time_list);

    feats_down_size = feats_down_body->points.size();
    // 由于点云的body var是一直不变的 因此提前计算 在迭代时可以复用
    var_down_body.clear();
    for (auto & pt:feats_down_body->points) {
        V3D point_this(pt.x, pt.y, pt.z);
        var_down_body.push_back(calcBodyCov(point_this, ranging_cov, angle_cov));
    }

    /*** ICP and iterated Kalman filter update ***/
    if (feats_down_size < 5)
    {
        ROS_WARN("Too few points (<5 points), skip this scan!\n");
        // continue;
        return;
    }
    // ===============================================================================================================
    // 开始迭代滤波
    /*** iterated state estimation ***/
    double solve_H_time = 0;
    kf.update_iterated_dyn_share_diagonal();
//            kf.update_iterated_dyn_share_modified(LASER_POINT_COV, solve_H_time);
    double t_optimize_end = omp_get_wtime();
    sum_optimize_time += t_optimize_end - t_optimize_start;

    state_point = kf.get_x();

//    // HACK 强行重置ba bg
//    state_point.ba.setZero();
//    state_point.bg.setZero();
//    kf.change_x(state_point);

#ifdef USE_GPS_INTEGRATION
    // ========== GPS集成点1: 记录起始位置 ==========
    static int start_debug_count = 0;
    if (!start_positions_set) {
        if (++start_debug_count % 50 == 1) {  // 每50次打印一次状态
            ROS_WARN("[GPS Start Debug] Origin_set=%s, Good_fixes=%d, Required=2",
                    gps_origin_set ? "YES" : "NO", gps_status.consecutive_good_fixes);
        }

        if (gps_origin_set && gps_status.consecutive_good_fixes > 2) {  // 进一步降低阈值到2
            Eigen::Vector3d gps_pos;
            double gps_std;
            if (getGPSPosition(lidar_end_time, gps_pos, gps_std)) {
                start_gps_position = gps_pos;
                start_slam_position = state_point.pos;
                start_positions_set = true;
                ROS_ERROR("\033[1;32m[GPS] *** START POSITION RECORDED *** - SLAM: [%.3f,%.3f,%.3f], GPS: [%.3f,%.3f,%.3f]\033[0m",
                        start_slam_position.x(), start_slam_position.y(), start_slam_position.z(),
                        start_gps_position.x(), start_gps_position.y(), start_gps_position.z());
            } else {
                ROS_WARN("[GPS Start Debug] Failed to get GPS position for start recording");
            }
        }
    }

    // ========== GPS集成点2: 高度校正 ==========
    Eigen::Vector3d corrected_position = correctHeightWithGPS(state_point.pos, lidar_end_time);
    if ((corrected_position - state_point.pos).norm() > 0.01) {
        state_point.pos = corrected_position;
        kf.change_x(state_point);  // 更新滤波器状态
        ROS_DEBUG("Applied GPS Height Correction");
    }

    // ========== 关键帧管理 ==========
    manageKeyframes(feats_undistort, state_point.pos, lidar_end_time);

    // ========== GPS集成点2.5: GPS触发的ICP回环检测 ==========
    Eigen::Matrix4f icp_loop_transform = Eigen::Matrix4f::Identity();
    if (performGPSTriggeredICPLoopClosure(state_point.pos, feats_undistort, lidar_end_time, icp_loop_transform)) {
        // 从变换矩阵中提取位置校正
        Eigen::Vector3f translation = icp_loop_transform.block<3, 1>(0, 3);
        Eigen::Vector3d position_correction(translation.x(), translation.y(), translation.z());

        // 应用ICP回环校正
        if (position_correction.norm() < 3.0) {  // 限制最大校正距离为3米
            state_point.pos += position_correction.cast<double>() * 0.3;  // 30%的校正强度
            kf.change_x(state_point);

            ROS_WARN("\033[1;33m[GPS-ICP] Applied Loop Closure: Correction=[%.3f,%.3f,%.3f], Norm=%.3f\033[0m",
                    position_correction.x(), position_correction.y(), position_correction.z(),
                    position_correction.norm());
        }
    }

    // ========== GPS集成点2.6: 增强的平面约束校正 ==========
    Eigen::Vector3d plane_corrected_position = applyGPSPlaneConstraint(state_point.pos, lidar_end_time);
    if ((plane_corrected_position - state_point.pos).norm() > 0.01) {
        state_point.pos = plane_corrected_position;
        kf.change_x(state_point);  // 更新滤波器状态
        ROS_DEBUG("Applied Enhanced GPS Plane Constraint");
    }

    // ========== GPS集成点3: 温和的回环约束检测 ==========
    static int loop_call_count = 0;
    if (++loop_call_count % 100 == 1) {
        ROS_ERROR("[GPS Loop Call] Calling detectGPSLoopClosure #%d, start_set=%s, enable=%s",
                 loop_call_count, start_positions_set ? "YES" : "NO",
                 enable_gps_loop_closure ? "YES" : "NO");
    }

    Eigen::Vector3d loop_constraint;
    if (detectGPSLoopClosure(state_point.pos, lidar_end_time, loop_constraint)) {
        // 智能的位置校正方法 - 处理大误差情况
        double constraint_norm = loop_constraint.norm();
        double correction_factor = 0.0;

        if (constraint_norm > 10.0) {
            // 大误差情况（>10米）：强力校正
            correction_factor = 0.8;  // 80%校正，快速收敛
            ROS_ERROR("\033[1;31m[GPS] LARGE ERROR DETECTED: %.1fm, applying strong correction\033[0m", constraint_norm);
        } else if (constraint_norm > 5.0) {
            // 中等误差（5-10米）：中等校正
            correction_factor = 0.5;  // 50%校正
        } else if (constraint_norm > 2.0) {
            // 小误差（2-5米）：温和校正
            correction_factor = 0.3;  // 30%校正
        } else if (constraint_norm > 0.5) {
            // 微小误差（0.5-2米）：轻微校正
            correction_factor = 0.15;  // 15%校正
        } else {
            // 极小误差（<0.5米）：精细校正
            correction_factor = 0.05;  // 5%校正
        }

        // 应用校正，但限制单次最大校正距离
        Eigen::Vector3d correction = loop_constraint * correction_factor;
        double max_single_correction = 3.0;  // 单次最大校正3米
        if (correction.norm() > max_single_correction) {
            correction = correction.normalized() * max_single_correction;
        }

        state_point.pos += correction;
        kf.change_x(state_point);

        // 监控累积校正量
        gps_status.total_correction_applied += correction.norm();
        if (constraint_norm > 10.0) {
            gps_status.large_error_count++;
        }

        ROS_WARN("\033[1;33m[GPS] Applied Smart Loop Closure: Error=%.1fm, Correction=%.1fm, Factor=%.1f%%, Total_correction=%.1fm\033[0m",
                constraint_norm, correction.norm(), correction_factor * 100, gps_status.total_correction_applied);
    }

    // ========== GPS集成点4: 轨迹一致性检查 ==========
    if (scan_index % 100 == 0) {  // 每100帧检查一次
        performGlobalTrajectoryAlignment();
    }
#endif

    euler_cur = SO3ToEuler(state_point.rot);
    pos_lid = state_point.pos + state_point.rot * state_point.offset_T_L_I;
    geoQuat.x = state_point.rot.coeffs()[0];
    geoQuat.y = state_point.rot.coeffs()[1];
    geoQuat.z = state_point.rot.coeffs()[2];
    geoQuat.w = state_point.rot.coeffs()[3];

    // ===============================================================================================================
    // 更新地图
    /*** add the points to the voxel map ***/
    double t_update_start = omp_get_wtime();
    // 用最新的状态估计将点及点的covariance转换到world系
    std::vector<pointWithCov> pv_list;
    PointCloudXYZI::Ptr world_lidar(new PointCloudXYZI);
    transformLidar(state_point, feats_down_body, world_lidar);
    for (size_t i = 0; i < feats_down_body->size(); i++) {
        // 保存body系和world系坐标
        pointWithCov pv;
        pv.point << feats_down_body->points[i].x, feats_down_body->points[i].y, feats_down_body->points[i].z;
        // 计算lidar点的cov
        // FIXME 这里错误的使用世界系的点来calcBodyCov时 反倒在某些seq（比如hilti2022的03 15）上效果更好 需要考虑是不是init_plane时使用更大的cov更好
        // 注意这个在每次迭代时是存在重复计算的 因为lidar系的点云covariance是不变的
        // M3D cov_lidar = calcBodyCov(pv.point, ranging_cov, angle_cov);
        M3D cov_lidar = var_down_body[i];
        // 将body系的var转换到world系
        M3D cov_world = transformLiDARCovToWorld(pv.point, kf, cov_lidar);

        // 最终updateVoxelMap需要用的是world系的point
        pv.cov = cov_world;
        pv.point << world_lidar->points[i].x, world_lidar->points[i].y, world_lidar->points[i].z;
        pv_list.push_back(pv);
    }

    // 当前state point 赋值
    current_state_point = kf.get_x();
    std::sort(pv_list.begin(), pv_list.end(), var_contrast);
    updateVoxelMapOMP(pv_list, max_voxel_size, max_layer, layer_size,
                      max_points_size, max_points_size, min_eigen_value,
                      voxel_map);
    double t_update_end = omp_get_wtime();
    sum_update_time += t_update_end - t_update_start;
    // ===============================================================================================================
    // fuse encoder angles with odometry, transform odomtry to a fixed frame (w.r.t carrier)
    if (encoder_fusion_en){
        nav_msgs::Odometry odometry_encoder_body =
                uncoupled_encoder_fusion(lidar_end_time_prev, state_point_prev);
        // XXX: use previous frame to avoid the high probability of odometry fall outside of encoder queue
        // TODO: perform encoder fusion in seperated thread with fixed frequency
        state_point_prev = kf.get_x();
        lidar_end_time_prev = lidar_end_time;
        if (!(odometry_encoder_body.child_frame_id == "-1" || odometry_encoder_body.child_frame_id == "-2"))
        {
            pubOdomEncoderBody.publish(odometry_encoder_body);
        }
    }
    scan_index++;
    // ===============================================================================================================
    // 可视化相关
    /******* Publish odometry *******/
    double t_vis_start = omp_get_wtime();
    publish_odometry(pubOdomAftMapped);
//
//            /*** add the feature points to map kdtree ***/
//            map_incremental();
//
    // TODO skip first few frames
    // TODO downsample dense point clouds
    /******* Publish points *******/
    if (path_en)                         publish_path(pubPath);
    if (scan_pub_en)      publish_frame_world(pubLaserCloudFull);
    if (scan_pub_en && scan_body_pub_en) publish_frame_body(pubLaserCloudFull_body);
    if (publish_voxel_map && pubLaserCloudMap.getNumSubscribers() > 0) {
//        current_state_point = kf.get_x();
        pubColoredVoxels(voxel_map, publish_max_voxel_layer, pubLaserCloudMap, lidar_end_time);
    }
    if (publish_voxel_map && voxel_map_pub.getNumSubscribers() > 0) {
//        current_state_point = kf.get_x();
        pubVoxelMap(voxel_map, publish_max_voxel_layer, voxel_map_pub);
    }
    double t_vis_end = omp_get_wtime();
    nav_msgs::Odometry stat_msg;
    stat_msg.header = odomAftMapped.header;
    stat_msg.pose.pose.position.x = t_optimize_end - t_optimize_start;
    stat_msg.pose.pose.position.y = t_update_end - t_update_start;
    stats_pub.publish(stat_msg);
    // publish_effect_world(pubLaserCloudEffect);
    // publish_map(pubLaserCloudMap);
//
    std::printf("v: %.2f %.2f %.2f BA: %.4f %.4f %.4f   BG: %.4f %.4f %.4f   g: %.4f %.4f %.4f\n",
                kf.get_x().vel.x(),kf.get_x().vel.y(),kf.get_x().vel.z(),
                kf.get_x().ba.x(),kf.get_x().ba.y(),kf.get_x().ba.z(),
                kf.get_x().bg.x(),kf.get_x().bg.y(),kf.get_x().bg.z(),
                kf.get_x().grav.get_vect().x(), kf.get_x().grav.get_vect().y(), kf.get_x().grav.get_vect().z()
    );

    std::printf("Mean Latency: %.3fs |  Mean Topt: %.5fs   Tu: %.5fs   | Cur Topt: %.5fs   Tu: %.5fs   Tvis: %.5fs\n",
                (sum_optimize_time + sum_update_time) / scan_index + (t_vis_end - t_vis_start),
                sum_optimize_time / scan_index, sum_update_time / scan_index,
                t_optimize_end - t_optimize_start,
                t_update_end - t_update_start,
                t_vis_end - t_vis_start);
    stat_latency
            << lidar_end_time << ", "
            << t_optimize_end - t_optimize_start << ", "
            << t_update_end - t_update_start << ", "
            << t_vis_end - t_vis_start << ", "
            << std::endl;
}

int main(int argc, char** argv)
{
    ros::init(argc, argv, "laserMapping");
    ros::NodeHandle nh;

    nh.param<double>("time_offset", lidar_time_offset, 0.0);

    nh.param<bool>("publish/path_en",path_en, true);
    nh.param<bool>("publish/scan_publish_en",scan_pub_en, true);
    nh.param<bool>("publish/dense_publish_en",dense_pub_en, true);
    nh.param<bool>("publish/scan_bodyframe_pub_en",scan_body_pub_en, true);
    nh.param<double>("publish/intensity_th", intensity_th, 1.0);

    nh.param<string>("common/lid_topic",lid_topic,"/velodyne_points");  // 改为标准velodyne topic
    nh.param<string>("common/imu_topic", imu_topic,"/imu/data");        // 改为标准imu topic
    nh.param<string>("common/encoder_topic", encoder_topic,"/encoder");
    nh.param<bool>("common/time_sync_en", time_sync_en, false);

#ifdef USE_GPS_INTEGRATION
    // GPS相关参数 - 强制启用
    nh.param<string>("common/gps_topic", gps_topic, "/rtk/gnss");  // 修改默认topic
    nh.param<bool>("gps/enable_correction", enable_gps_correction, true);  // 默认启用
    nh.param<bool>("gps/enable_loop_closure", enable_gps_loop_closure, true);  // 强制启用
    enable_gps_loop_closure = true;  // 强制启用回环检测
    nh.param<bool>("gps/enable_plane_constraint", enable_gps_plane_constraint, true);  // 默认启用平面约束
    nh.param<double>("gps/height_correction_threshold", gps_height_correction_threshold, 0.3);
    nh.param<double>("gps/correction_rate", gps_correction_rate, 0.1);
    nh.param<double>("gps/loop_closure_distance", gps_loop_closure_distance, 10.0);
    nh.param<double>("gps/loop_closure_min_distance", gps_loop_closure_min_distance, 20.0);  // 降低到20米
    nh.param<double>("gps/icp_trigger_distance", gps_icp_trigger_distance, 20.0);  // 降低默认值到20米
    nh.param<bool>("gps/enable_icp_loop_closure", enable_gps_icp_loop_closure, true);
    enable_gps_icp_loop_closure = true;  // 强制启用ICP回环检测
    nh.param<int>("gps/keyframe_skip", keyframe_skip, 10);
    nh.param<double>("gps/icp_fitness_threshold", icp_fitness_threshold, 0.3);
    nh.param<double>("gps/icp_max_correspondence_distance", icp_max_correspondence_distance, 1.0);

    // 温和GPS约束参数 - 平衡精度和稳定性
    nh.param<double>("gps/plane_constraint_weight", gps_plane_constraint_weight, 0.1);
    nh.param<double>("gps/xy_correction_threshold", gps_xy_correction_threshold, 0.8);
    nh.param<double>("gps/xy_correction_rate", gps_xy_correction_rate, 0.03);
    nh.param<int>("gps/constraint_window_size", gps_constraint_window_size, 50);

    ROS_WARN("\033[1;33m=== GPS Conservative Constraint System Enabled ===\033[0m");
    ROS_INFO("\033[1;33mGPS Integration Parameters (Conservative Mode):\033[0m");
    ROS_INFO("\033[1;33m  GPS topic: %s\033[0m", gps_topic.c_str());
    ROS_INFO("\033[1;33m  Height correction: %s\033[0m", enable_gps_correction ? "Enabled" : "Disabled");
    ROS_INFO("\033[1;33m  Loop closure: %s (Range: %.1fm, Min trajectory: %.1fm)\033[0m",
             enable_gps_loop_closure ? "Enabled" : "Disabled",
             gps_loop_closure_distance, gps_loop_closure_min_distance);
    ROS_INFO("\033[1;33m  ICP Loop closure: %s (Trigger distance: %.1fm, Fitness threshold: %.2f)\033[0m",
             enable_gps_icp_loop_closure ? "Enabled" : "Disabled",
             gps_icp_trigger_distance, icp_fitness_threshold);
    ROS_INFO("\033[1;33m  Plane constraint: %s (Threshold: %.2fm, Rate: %.1f%%)\033[0m",
             enable_gps_plane_constraint ? "Enabled" : "Disabled",
             gps_xy_correction_threshold, gps_xy_correction_rate * 100);
    ROS_INFO("\033[1;33m  Height threshold: %.3f m\033[0m", gps_height_correction_threshold);
    ROS_INFO("\033[1;33m  Height correction rate: %.1f%%\033[0m", gps_correction_rate * 100);
    ROS_INFO("\033[1;33m  XY correction threshold: %.3f m\033[0m", gps_xy_correction_threshold);
    ROS_INFO("\033[1;33m  XY correction rate: %.1f%%\033[0m", gps_xy_correction_rate * 100);
    ROS_INFO("\033[1;33m  Plane constraint weight: %.2f\033[0m", gps_plane_constraint_weight);
#else
    ROS_ERROR("\033[1;31m[GPS] GPS Integration NOT Compiled! Please check compilation configuration.\033[0m");
#endif

    // PCD保存参数
    nh.param<bool>("pcd_save/pcd_save_en", pcd_save_en, false);
    nh.param<int>("pcd_save/interval", pcd_save_interval, -1);
    nh.param<string>("pcd_save/directory", pcd_save_directory, "/home/<USER>/slam_share/aLidar/temp");

    ROS_INFO("PCD Save Parameters:");
    ROS_INFO("  Save enabled: %s", pcd_save_en ? "Yes" : "No");
    if (pcd_save_en) {
        ROS_INFO("  Save interval: %s", pcd_save_interval == -1 ? "All frames to one file" : (std::to_string(pcd_save_interval) + " frames").c_str());
        ROS_INFO("  Save directory: %s", pcd_save_directory.c_str());
    }

    // mapping algorithm params
    nh.param<float>("mapping/det_range",DET_RANGE,300.f);
    nh.param<int>("mapping/max_iteration", NUM_MAX_ITERATIONS, 4);
    nh.param<int>("mapping/max_points_size", max_points_size, 100);
    nh.param<int>("mapping/max_cov_points_size", max_cov_points_size, 100);
    nh.param<vector<double>>("mapping/layer_point_size", layer_point_size,vector<double>());
    nh.param<int>("mapping/max_layer", max_layer, 2);
    nh.param<double>("mapping/voxel_size", max_voxel_size, 1.0);
    nh.param<double>("mapping/down_sample_size", filter_size_surf_min, 0.5);
    std::cout << "filter_size_surf_min:" << filter_size_surf_min << std::endl;
    nh.param<double>("mapping/plannar_threshold", min_eigen_value, 0.01);
    nh.param<bool>("mapping/extrinsic_est_en", extrinsic_est_en, true);
    nh.param<vector<double>>("mapping/extrinsic_T", extrinT, vector<double>());
    nh.param<vector<double>>("mapping/extrinsic_R", extrinR, vector<double>());
    nh.param<bool>("mapping/encoder_fusion_en", encoder_fusion_en, false);
    nh.param<vector<double>>("mapping/extrinsic_T_encoder_lidar", extrinT_encoder, vector<double>());
    nh.param<vector<double>>("mapping/extrinsic_R_encoder_lidar", extrinR_encoder, vector<double>());
    nh.param<double>("mapping/encoder_offset_deg", encoder_zeropoint_offset_deg, 0);
    nh.param<bool>("mapping/adaptive_voxelization", adaptive_voxelization, false);
    nh.param<vector<double>>("mapping/adaptive_threshold", adaptive_threshold, vector<double>({1000}));
    nh.param<vector<double>>("mapping/adaptive_multiple_factor", adaptive_multiple_factor, vector<double>({2.0}));
    nh.param<bool>("mapping/init_gravity_with_pose", init_gravity_with_pose, false);

    // noise model params
    nh.param<double>("noise_model/ranging_cov", ranging_cov, 0.02);
    nh.param<double>("noise_model/angle_cov", angle_cov, 0.05);
    nh.param<double>("noise_model/gyr_cov",gyr_cov,0.1);
    nh.param<double>("noise_model/acc_cov",acc_cov,0.1);
    nh.param<double>("noise_model/b_gyr_cov",b_gyr_cov,0.0001);
    nh.param<double>("noise_model/b_acc_cov",b_acc_cov,0.0001);

    // visualization params
    nh.param<bool>("publish/pub_voxel_map", publish_voxel_map, false);
    nh.param<int>("publish/publish_max_voxel_layer", publish_max_voxel_layer, 0);
    nh.param<int>("publish/publish_downsample_points", publish_downsample_points, 1000000);
    nh.param<int>("publish/publish_dense_skip", publish_dense_skip, 1);
    nh.param<int>("publish/publish_path_skip", publish_path_skip, 1);
    nh.param<double>("publish/publish_limit_z", publish_limit_z, 1000000.0);

    nh.param<double>("preprocess/blind", p_pre->blind, 0.01);
    nh.param<int>("preprocess/lidar_type", p_pre->lidar_type, VELO16);  // 改为VELO16，避免使用AVIA
    nh.param<int>("preprocess/scan_line", p_pre->N_SCANS, 16);
    nh.param<int>("preprocess/scan_rate", p_pre->SCAN_RATE, 10);
    nh.param<int>("preprocess/point_filter_num", p_pre->point_filter_num, 1);
    nh.param<bool>("preprocess/feature_extract_enable", p_pre->feature_enabled, false);
    cout<<"p_pre->lidar_type "<<p_pre->lidar_type<<endl;
    for (int i = 0; i < layer_point_size.size(); i++) {
        layer_size.push_back(layer_point_size[i]);
    }

    path.header.stamp    = ros::Time::now();
    path.header.frame_id ="camera_init";

    /*** variables definition ***/
    int effect_feat_num = 0, frame_num = 0;
    bool flg_EKF_converged, EKF_stop_flg = 0;
    scan_index = 0;

    _featsArray.reset(new PointCloudXYZI());

    memset(point_selected_surf, true, sizeof(point_selected_surf));
    memset(res_last, -1000.0f, sizeof(res_last));
    downSizeFilterSurf.setLeafSize(filter_size_surf_min, filter_size_surf_min, filter_size_surf_min);
    memset(point_selected_surf, true, sizeof(point_selected_surf));
    memset(res_last, -1000.0f, sizeof(res_last));

    Lidar_T_wrt_IMU<<VEC_FROM_ARRAY(extrinT);
    Lidar_R_wrt_IMU<<MAT_FROM_ARRAY(extrinR);
    p_imu->set_extrinsic(Lidar_T_wrt_IMU, Lidar_R_wrt_IMU);
    p_imu->set_gyr_cov(V3D(gyr_cov, gyr_cov, gyr_cov));
    p_imu->set_acc_cov(V3D(acc_cov, acc_cov, acc_cov));
    p_imu->set_gyr_bias_cov(V3D(b_gyr_cov, b_gyr_cov, b_gyr_cov));
    p_imu->set_acc_bias_cov(V3D(b_acc_cov, b_acc_cov, b_acc_cov));
    p_imu->set_init_gravity_with_pose(init_gravity_with_pose);

    // calc encoder w.r.t lidar extrinsic
    M3D MAT_R_encoder_o_lidar;
    V3D t_encoder_o_lidar;
    t_encoder_o_lidar<<VEC_FROM_ARRAY(extrinT_encoder);
    MAT_R_encoder_o_lidar<<MAT_FROM_ARRAY(extrinR_encoder);
    SO3 R_encoder_o_lidar = MAT_R_encoder_o_lidar;
    // inverse for convenient
    R_lidar_o_encoder = R_encoder_o_lidar.inverse();
    t_lidar_o_encoder = -(R_lidar_o_encoder * t_encoder_o_lidar);
    // encoder zero-point offset
    R_encoder_o_zero_point = Eigen::AngleAxisd(
            encoder_zeropoint_offset_deg / 180.0 * M_PI, V3D::UnitZ());

    double epsi[23] = {0.001};
    fill(epsi, epsi+23, 0.001);
    kf.init_dyn_share(get_f, df_dx, df_dw, observation_model_share, NUM_MAX_ITERATIONS, epsi);

    /*** ROS subscribe initialization ***/
    // 强制使用standard_pcl_cbk，避免livox依赖
    sub_pcl = nh.subscribe(lid_topic, 200000, standard_pcl_cbk);
    sub_imu = nh.subscribe(imu_topic, 200000, imu_cbk);
    sub_encoder = nh.subscribe(encoder_topic, 200000, encoder_cbk);

#ifdef USE_GPS_INTEGRATION
    // GPS订阅
    ros::Subscriber sub_gps = nh.subscribe(gps_topic, 200000, gps_callback);

    // 动态约束控制订阅器
    ros::Subscriber sub_constraint_control = nh.subscribe("/gps_constraint_control", 10, gps_constraint_control_callback);

    ROS_WARN("\033[1;33m[GPS] Subscription Set: %s\033[0m", gps_topic.c_str());
    ROS_WARN("\033[1;33m[GPS] Integration Enabled - Height threshold: %.2fm, Correction rate: %.0f%%\033[0m",
             gps_height_correction_threshold, gps_correction_rate * 100);
    ROS_INFO("\033[1;36m[GPS] Dynamic Constraint Control: %s\033[0m",
             dynamic_constraint_control ? "ENABLED" : "DISABLED");
#else
    ROS_ERROR("\033[1;31m[GPS] GPS Integration Code NOT Compiled!\033[0m");
#endif
    pubLaserCloudFull = nh.advertise<sensor_msgs::PointCloud2>("/cloud_registered", 100000);

    // 添加ICP适应度分数发布器
    pub_icp_fitness = nh.advertise<std_msgs::Float64>("/icp_fitness_score", 10);
    pubLaserCloudFull_body = nh.advertise<sensor_msgs::PointCloud2>("/cloud_registered_body", 100000);
    pubLaserCloudEffect = nh.advertise<sensor_msgs::PointCloud2>("/cloud_effected", 100000);
    pubLaserCloudMap = nh.advertise<sensor_msgs::PointCloud2>("/Laser_map", 100000);
    pubOdomAftMapped = nh.advertise<nav_msgs::Odometry>("/Odometry", 100000);
    pubOdomEncoderBody = nh.advertise<nav_msgs::Odometry>("/Encoder", 100000);
    pubExtrinsic = nh.advertise<nav_msgs::Odometry>("/Extrinsic", 100000);
    pubPath = nh.advertise<nav_msgs::Path>("/path", 100000);
    voxel_map_pub = nh.advertise<visualization_msgs::MarkerArray>("/planes", 10000);
    stats_pub = nh.advertise<nav_msgs::Odometry>("/stats", 10000);

//------------------------------------------------------------------------------------------------------
    // statistic
    stat_latency.setf(ios::fixed);
    stat_latency.precision(10);//精度为输出小数点后5位

////------------------------------------------------------------------------------------------------------
//    // 用rosbag读取
//    signal(SIGINT, SigHandle);
//
//    std::string bag_file = "/tmp/2024-03-05-17-21-56.bag";
//    rosbag::Bag bag;
//    try {
//        bag.open(bag_file, rosbag::bagmode::Read);
//    } catch (const rosbag::BagException& e) {
//        ROS_ERROR("Could not open bag file: %s", e.what());
//        return -1;
//    }
//
//    std::vector<std::string> topics;
//    topics.push_back(std::string("/velodyne_points"));
//    topics.push_back(std::string("/imu/data"));
//
//    rosbag::View view(bag, rosbag::TopicQuery(topics));
//        foreach(rosbag::MessageInstance const m, view) {
//            // 中途退出
//            if (flg_exit) break;
//
//            sensor_msgs::PointCloud2ConstPtr lidar_msg = m.instantiate<sensor_msgs::PointCloud2>();
//            if (lidar_msg != NULL) {
//                standard_pcl_cbk(lidar_msg);
//            }
//
//            sensor_msgs::ImuConstPtr imu_msg = m.instantiate<sensor_msgs::Imu>();
//            if (imu_msg != NULL) {
//                imu_cbk(imu_msg);
//            }
//
//            if (sync_packages(Measures)) {
//                // execute one step
//                execute();
//            }
//        }
//    bag.close();

    signal(SIGINT, SigHandle);
    ros::Rate rate(5000);
    bool status = ros::ok();
    while (status)
    {
        if (flg_exit) break;
        ros::spinOnce();
        if(sync_packages(Measures))
        {
            // execute one step
            execute();
        }

        status = ros::ok();
        rate.sleep();
    }
    stat_latency.close();

    // 程序结束时保存最终的全局点云
    if (pcd_save_en && !global_map_points->empty()) {
        if (pcd_save_interval == -1) {
            // 保存所有累积的点云到一个文件
            save_global_map_pcd();
        }
        ROS_INFO("Final global map saved. Total points: %zu", global_map_points->size());
    }

    return 0;
}
