#!/bin/bash

# 编译检查脚本

echo "=========================================="
echo "αLiDAR 编译检查脚本"
echo "=========================================="

WORKSPACE_PATH="$HOME/alpha_lidar_GPS/software/alpha_lidar_ws"

if [ ! -d "$WORKSPACE_PATH" ]; then
    echo "❌ 工作空间不存在: $WORKSPACE_PATH"
    exit 1
fi

cd "$WORKSPACE_PATH"

echo "1. 检查源代码语法..."

# 检查重复函数定义
echo "检查重复函数定义..."
DUPLICATE_FUNCTIONS=$(grep -n "Eigen::Vector3d applyGPSPlaneConstraint" src/state_estimation/src/voxelMapping.cpp | wc -l)
if [ "$DUPLICATE_FUNCTIONS" -gt 1 ]; then
    echo "❌ 发现重复的applyGPSPlaneConstraint函数定义:"
    grep -n "Eigen::Vector3d applyGPSPlaneConstraint" src/state_estimation/src/voxelMapping.cpp
    exit 1
else
    echo "✅ applyGPSPlaneConstraint函数定义正常"
fi

DUPLICATE_GLOBAL=$(grep -n "void performGlobalTrajectoryAlignment" src/state_estimation/src/voxelMapping.cpp | wc -l)
if [ "$DUPLICATE_GLOBAL" -gt 1 ]; then
    echo "❌ 发现重复的performGlobalTrajectoryAlignment函数定义:"
    grep -n "void performGlobalTrajectoryAlignment" src/state_estimation/src/voxelMapping.cpp
    exit 1
else
    echo "✅ performGlobalTrajectoryAlignment函数定义正常"
fi

echo ""
echo "2. 检查GPS集成代码..."

# 检查GPS集成是否启用
if grep -q "#define USE_GPS_INTEGRATION" src/state_estimation/src/voxelMapping.cpp; then
    echo "✅ GPS集成已启用"
else
    echo "❌ GPS集成未启用"
fi

# 检查关键函数是否存在
FUNCTIONS=("gps_callback" "convertGPSToLocal" "getGPSPosition" "correctHeightWithGPS" "detectGPSLoopClosure" "applyGPSPlaneConstraint")

for func in "${FUNCTIONS[@]}"; do
    if grep -q "$func" src/state_estimation/src/voxelMapping.cpp; then
        echo "✅ 函数 $func 存在"
    else
        echo "❌ 函数 $func 缺失"
    fi
done

echo ""
echo "3. 检查配置文件..."

CONFIG_FILE="src/state_estimation/config/rs16_rotation_v2.yaml"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置文件存在"
    
    if grep -q "gps_topic.*rtk/gnss" "$CONFIG_FILE"; then
        echo "✅ GPS topic配置正确"
    else
        echo "❌ GPS topic配置错误"
    fi
    
    if grep -q "pcd_save_en.*true" "$CONFIG_FILE"; then
        echo "✅ PCD保存已启用"
    else
        echo "❌ PCD保存未启用"
    fi
else
    echo "❌ 配置文件不存在"
fi

echo ""
echo "4. 检查头文件包含..."

HEADERS=("boost/filesystem.hpp" "pcl/io/pcd_io.h" "sensor_msgs/NavSatFix.h")

for header in "${HEADERS[@]}"; do
    if grep -q "#include <$header>" src/state_estimation/src/voxelMapping.cpp; then
        echo "✅ 头文件 $header 已包含"
    else
        echo "❌ 头文件 $header 缺失"
    fi
done

echo ""
echo "5. 语法检查建议..."
echo "在Ubuntu环境下运行以下命令进行编译:"
echo "cd $WORKSPACE_PATH"
echo "rm -rf build/ devel/"
echo "source /opt/ros/noetic/setup.bash"
echo "catkin_make -DCATKIN_WHITELIST_PACKAGES=\"state_estimation\""

echo ""
echo "=========================================="
echo "检查完成"
echo "=========================================="
