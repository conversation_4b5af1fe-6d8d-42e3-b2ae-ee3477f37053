/**
 * 强度值保持的PCD保存模块
 * 确保输出的PCD文件包含真实的点云强度值
 * 支持回环检测优化后的精确匹配点云保存
 */

#include <ros/ros.h>
#include <sensor_msgs/PointCloud2.h>
#include <std_msgs/Bool.h>
#include <std_msgs/String.h>
#include <geometry_msgs/PoseStamped.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/registration/icp.h>
#include <pcl/registration/ndt.h>
#include <pcl_conversions/pcl_conversions.h>

#include <boost/filesystem.hpp>
#include <deque>
#include <mutex>
#include <thread>
#include <iomanip>
#include <sstream>

// 定义带强度的点类型
struct PointXYZIWithOriginalIntensity {
    PCL_ADD_POINT4D;
    float intensity;
    float original_intensity;  // 保存原始强度值
    uint32_t timestamp;        // 时间戳
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
} EIGEN_ALIGN16;

POINT_CLOUD_REGISTER_POINT_STRUCT(PointXYZIWithOriginalIntensity,
    (float, x, x)
    (float, y, y)
    (float, z, z)
    (float, intensity, intensity)
    (float, original_intensity, original_intensity)
    (uint32_t, timestamp, timestamp)
)

class IntensityPreservingPCDSaver {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 订阅器
    ros::Subscriber raw_pointcloud_sub_;
    ros::Subscriber processed_pointcloud_sub_;
    ros::Subscriber loop_closure_result_sub_;
    ros::Subscriber pose_sub_;
    
    // 发布器
    ros::Publisher enhanced_pointcloud_pub_;
    ros::Publisher intensity_stats_pub_;
    
    // 参数
    std::string save_directory_;
    bool save_enabled_;
    bool save_raw_intensity_;
    bool save_loop_corrected_;
    double intensity_scale_factor_;
    double voxel_leaf_size_;
    int max_points_per_file_;
    
    // 数据存储
    std::deque<sensor_msgs::PointCloud2> raw_pointcloud_buffer_;
    std::deque<sensor_msgs::PointCloud2> processed_pointcloud_buffer_;
    std::deque<geometry_msgs::PoseStamped> pose_buffer_;
    
    // 强度值统计
    struct IntensityStats {
        double min_intensity = std::numeric_limits<double>::max();
        double max_intensity = std::numeric_limits<double>::lowest();
        double mean_intensity = 0.0;
        size_t point_count = 0;
        std::vector<double> intensity_histogram;
    };
    
    IntensityStats raw_intensity_stats_;
    IntensityStats processed_intensity_stats_;
    
    // 状态管理
    std::mutex data_mutex_;
    bool loop_closure_detected_;
    int saved_file_count_;
    
    // 全局点云累积
    pcl::PointCloud<PointXYZIWithOriginalIntensity>::Ptr global_pointcloud_;
    
public:
    IntensityPreservingPCDSaver() : 
        private_nh_("~"),
        loop_closure_detected_(false),
        saved_file_count_(0) {
        
        // 读取参数
        loadParameters();
        
        // 初始化数据结构
        global_pointcloud_.reset(new pcl::PointCloud<PointXYZIWithOriginalIntensity>());
        raw_intensity_stats_.intensity_histogram.resize(256, 0.0);
        processed_intensity_stats_.intensity_histogram.resize(256, 0.0);
        
        // 初始化订阅器
        raw_pointcloud_sub_ = nh_.subscribe("/velodyne_points", 10,
            &IntensityPreservingPCDSaver::rawPointCloudCallback, this);
        processed_pointcloud_sub_ = nh_.subscribe("/cloud_registered", 10,
            &IntensityPreservingPCDSaver::processedPointCloudCallback, this);
        loop_closure_result_sub_ = nh_.subscribe("/loop_closure_result", 1,
            &IntensityPreservingPCDSaver::loopClosureResultCallback, this);
        pose_sub_ = nh_.subscribe("/aft_mapped_to_init", 10,
            &IntensityPreservingPCDSaver::poseCallback, this);
        
        // 初始化发布器
        enhanced_pointcloud_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("/enhanced_pointcloud_with_intensity", 1);
        intensity_stats_pub_ = nh_.advertise<std_msgs::String>("/intensity_statistics", 1);
        
        // 创建保存目录
        if (save_enabled_) {
            createSaveDirectory();
        }
        
        ROS_INFO("Intensity Preserving PCD Saver Started");
        printParameters();
    }
    
    void loadParameters() {
        private_nh_.param("save_directory", save_directory_, std::string("/home/<USER>/slam_share/aLidar/intensity_preserved"));
        private_nh_.param("save_enabled", save_enabled_, true);
        private_nh_.param("save_raw_intensity", save_raw_intensity_, true);
        private_nh_.param("save_loop_corrected", save_loop_corrected_, true);
        private_nh_.param("intensity_scale_factor", intensity_scale_factor_, 1.0);
        private_nh_.param("voxel_leaf_size", voxel_leaf_size_, 0.1);
        private_nh_.param("max_points_per_file", max_points_per_file_, 1000000);
    }
    
    void printParameters() {
        ROS_INFO("Intensity Preserving PCD Saver Parameters:");
        ROS_INFO("  Save directory: %s", save_directory_.c_str());
        ROS_INFO("  Save enabled: %s", save_enabled_ ? "Yes" : "No");
        ROS_INFO("  Save raw intensity: %s", save_raw_intensity_ ? "Yes" : "No");
        ROS_INFO("  Save loop corrected: %s", save_loop_corrected_ ? "Yes" : "No");
        ROS_INFO("  Intensity scale factor: %.2f", intensity_scale_factor_);
        ROS_INFO("  Voxel leaf size: %.3f", voxel_leaf_size_);
        ROS_INFO("  Max points per file: %d", max_points_per_file_);
    }
    
    void createSaveDirectory() {
        try {
            if (!boost::filesystem::exists(save_directory_)) {
                boost::filesystem::create_directories(save_directory_);
                ROS_INFO("Created save directory: %s", save_directory_.c_str());
            }
            
            // 创建子目录
            std::string raw_dir = save_directory_ + "/raw_intensity";
            std::string processed_dir = save_directory_ + "/processed_intensity";
            std::string loop_corrected_dir = save_directory_ + "/loop_corrected";
            
            boost::filesystem::create_directories(raw_dir);
            boost::filesystem::create_directories(processed_dir);
            boost::filesystem::create_directories(loop_corrected_dir);
            
        } catch (const std::exception& e) {
            ROS_ERROR("Failed to create save directory: %s", e.what());
            save_enabled_ = false;
        }
    }
    
    void rawPointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // 保存原始点云
        raw_pointcloud_buffer_.push_back(*msg);
        if (raw_pointcloud_buffer_.size() > 100) {
            raw_pointcloud_buffer_.pop_front();
        }
        
        // 分析原始强度值
        analyzeIntensityValues(*msg, raw_intensity_stats_);
        
        // 处理并保存带原始强度的点云
        processRawPointCloud(*msg);
    }
    
    void processedPointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // 保存处理后的点云
        processed_pointcloud_buffer_.push_back(*msg);
        if (processed_pointcloud_buffer_.size() > 100) {
            processed_pointcloud_buffer_.pop_front();
        }
        
        // 分析处理后的强度值
        analyzeIntensityValues(*msg, processed_intensity_stats_);
        
        // 处理并保存增强的点云
        processEnhancedPointCloud(*msg);
    }
    
    void loopClosureResultCallback(const std_msgs::Bool::ConstPtr& msg) {
        if (msg->data) {
            loop_closure_detected_ = true;
            ROS_INFO("Loop closure detected, saving enhanced PCD with preserved intensity");
            
            if (save_enabled_ && save_loop_corrected_) {
                saveLoopCorrectedPointCloud();
            }
        }
    }
    
    void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        pose_buffer_.push_back(*msg);
        if (pose_buffer_.size() > 1000) {
            pose_buffer_.pop_front();
        }
    }
    
    void processRawPointCloud(const sensor_msgs::PointCloud2& msg) {
        // 转换为PCL点云
        pcl::PointCloud<pcl::PointXYZI>::Ptr raw_cloud(new pcl::PointCloud<pcl::PointXYZI>());
        pcl::fromROSMsg(msg, *raw_cloud);
        
        // 创建增强点云
        pcl::PointCloud<PointXYZIWithOriginalIntensity>::Ptr enhanced_cloud(
            new pcl::PointCloud<PointXYZIWithOriginalIntensity>());
        
        enhanced_cloud->reserve(raw_cloud->size());
        
        for (const auto& point : raw_cloud->points) {
            PointXYZIWithOriginalIntensity enhanced_point;
            enhanced_point.x = point.x;
            enhanced_point.y = point.y;
            enhanced_point.z = point.z;
            enhanced_point.intensity = point.intensity * intensity_scale_factor_;
            enhanced_point.original_intensity = point.intensity;  // 保存原始强度
            enhanced_point.timestamp = msg.header.stamp.toNSec();
            
            enhanced_cloud->points.push_back(enhanced_point);
        }
        
        enhanced_cloud->width = enhanced_cloud->points.size();
        enhanced_cloud->height = 1;
        enhanced_cloud->is_dense = false;
        
        // 累积到全局点云
        *global_pointcloud_ += *enhanced_cloud;
        
        // 发布增强点云
        publishEnhancedPointCloud(enhanced_cloud, msg.header);
        
        // 定期保存
        if (save_enabled_ && global_pointcloud_->size() > max_points_per_file_) {
            saveGlobalPointCloud("incremental");
            global_pointcloud_->clear();
        }
    }
    
    void processEnhancedPointCloud(const sensor_msgs::PointCloud2& msg) {
        // 处理经过SLAM优化的点云，保持强度值
        pcl::PointCloud<pcl::PointXYZI>::Ptr processed_cloud(new pcl::PointCloud<pcl::PointXYZI>());
        pcl::fromROSMsg(msg, *processed_cloud);
        
        // 如果有对应的原始点云，尝试恢复原始强度值
        if (!raw_pointcloud_buffer_.empty()) {
            restoreOriginalIntensity(processed_cloud);
        }
        
        // 保存处理后的点云
        if (save_enabled_) {
            saveProcessedPointCloud(processed_cloud, msg.header);
        }
    }
    
    void restoreOriginalIntensity(pcl::PointCloud<pcl::PointXYZI>::Ptr& processed_cloud) {
        // 尝试从原始点云缓冲区中恢复强度值
        if (raw_pointcloud_buffer_.empty()) return;
        
        // 获取最近的原始点云
        pcl::PointCloud<pcl::PointXYZI>::Ptr raw_cloud(new pcl::PointCloud<pcl::PointXYZI>());
        pcl::fromROSMsg(raw_pointcloud_buffer_.back(), *raw_cloud);
        
        // 使用KD树进行最近邻搜索来匹配点
        pcl::KdTreeFLANN<pcl::PointXYZI> kdtree;
        kdtree.setInputCloud(raw_cloud);
        
        for (auto& point : processed_cloud->points) {
            std::vector<int> indices(1);
            std::vector<float> distances(1);
            
            if (kdtree.nearestKSearch(point, 1, indices, distances) > 0) {
                if (distances[0] < 0.1) {  // 距离阈值0.1米
                    // 恢复原始强度值
                    point.intensity = raw_cloud->points[indices[0]].intensity;
                }
            }
        }
    }
    
    void analyzeIntensityValues(const sensor_msgs::PointCloud2& msg, IntensityStats& stats) {
        pcl::PointCloud<pcl::PointXYZI>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZI>());
        pcl::fromROSMsg(msg, *cloud);
        
        if (cloud->empty()) return;
        
        double sum_intensity = 0.0;
        for (const auto& point : cloud->points) {
            double intensity = point.intensity;
            
            stats.min_intensity = std::min(stats.min_intensity, intensity);
            stats.max_intensity = std::max(stats.max_intensity, intensity);
            sum_intensity += intensity;
            
            // 更新直方图
            int bin = std::min(255, std::max(0, static_cast<int>(intensity)));
            if (bin < stats.intensity_histogram.size()) {
                stats.intensity_histogram[bin]++;
            }
        }
        
        stats.point_count += cloud->size();
        stats.mean_intensity = sum_intensity / cloud->size();
        
        // 发布统计信息
        publishIntensityStatistics();
    }
    
    void publishEnhancedPointCloud(
        const pcl::PointCloud<PointXYZIWithOriginalIntensity>::Ptr& cloud,
        const std_msgs::Header& header) {
        
        sensor_msgs::PointCloud2 msg;
        pcl::toROSMsg(*cloud, msg);
        msg.header = header;
        msg.header.frame_id = "velodyne";
        
        enhanced_pointcloud_pub_.publish(msg);
    }
    
    void publishIntensityStatistics() {
        std_msgs::String stats_msg;
        std::stringstream ss;
        
        ss << "Intensity Statistics:\n";
        ss << "Raw - Min: " << raw_intensity_stats_.min_intensity 
           << ", Max: " << raw_intensity_stats_.max_intensity
           << ", Mean: " << raw_intensity_stats_.mean_intensity
           << ", Points: " << raw_intensity_stats_.point_count << "\n";
        ss << "Processed - Min: " << processed_intensity_stats_.min_intensity
           << ", Max: " << processed_intensity_stats_.max_intensity
           << ", Mean: " << processed_intensity_stats_.mean_intensity
           << ", Points: " << processed_intensity_stats_.point_count;
        
        stats_msg.data = ss.str();
        intensity_stats_pub_.publish(stats_msg);
    }
    
    void saveGlobalPointCloud(const std::string& suffix) {
        if (!save_enabled_ || global_pointcloud_->empty()) return;
        
        std::stringstream filename;
        filename << save_directory_ << "/global_map_with_intensity_"
                 << std::setfill('0') << std::setw(6) << saved_file_count_
                 << "_" << suffix << ".pcd";
        
        try {
            pcl::io::savePCDFileBinary(filename.str(), *global_pointcloud_);
            ROS_INFO("Saved global map with preserved intensity: %s (Points: %zu)", 
                     filename.str().c_str(), global_pointcloud_->size());
            saved_file_count_++;
        } catch (const std::exception& e) {
            ROS_ERROR("Failed to save global map: %s", e.what());
        }
    }
    
    void saveProcessedPointCloud(
        const pcl::PointCloud<pcl::PointXYZI>::Ptr& cloud,
        const std_msgs::Header& header) {
        
        std::stringstream filename;
        filename << save_directory_ << "/processed_intensity/processed_"
                 << header.stamp.toNSec() << ".pcd";
        
        try {
            pcl::io::savePCDFileBinary(filename.str(), *cloud);
            ROS_DEBUG("Saved processed pointcloud: %s", filename.str().c_str());
        } catch (const std::exception& e) {
            ROS_ERROR("Failed to save processed pointcloud: %s", e.what());
        }
    }
    
    void saveLoopCorrectedPointCloud() {
        if (global_pointcloud_->empty()) return;
        
        std::stringstream filename;
        filename << save_directory_ << "/loop_corrected/loop_corrected_"
                 << ros::Time::now().toNSec() << ".pcd";
        
        try {
            pcl::io::savePCDFileBinary(filename.str(), *global_pointcloud_);
            ROS_INFO("Saved loop-corrected pointcloud with preserved intensity: %s (Points: %zu)",
                     filename.str().c_str(), global_pointcloud_->size());
        } catch (const std::exception& e) {
            ROS_ERROR("Failed to save loop-corrected pointcloud: %s", e.what());
        }
    }
    
    ~IntensityPreservingPCDSaver() {
        // 保存最终的全局点云
        if (save_enabled_ && !global_pointcloud_->empty()) {
            saveGlobalPointCloud("final");
        }
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "intensity_preserving_pcd_saver");
    
    try {
        IntensityPreservingPCDSaver saver;
        
        ROS_INFO("Intensity Preserving PCD Saver running...");
        ROS_INFO("Preserving original intensity values in all saved PCD files");
        
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Intensity Preserving PCD Saver exception: %s", e.what());
        return -1;
    }
    
    ROS_INFO("Intensity Preserving PCD Saver stopped");
    return 0;
}
