#!/bin/bash

# GPS软约束SLAM简化启动脚本

echo "=========================================="
echo "🎯 GPS软约束SLAM系统 - 简化启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_soft_simple_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "🚀 启动GPS软约束SLAM系统（简化版）"
echo ""

# 设置基本参数
echo "设置系统参数..."
rosparam set /state_estimation_node/enable_gps false  # 先禁用GPS避免崩溃
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/gps/use_soft_constraint true

# 启动SLAM核心节点
echo "启动SLAM核心节点..."
rosrun state_estimation state_estimation_node &
SLAM_PID=$!
sleep 5

# 检查SLAM是否启动成功
if ! ps -p $SLAM_PID > /dev/null; then
    echo "❌ SLAM核心节点启动失败"
    echo "尝试查看错误日志："
    echo "  tail ~/.ros/log/latest/state_estimation_node-*.log"
    exit 1
fi
echo "✅ SLAM核心节点启动成功"

# 启动GPS软约束检测器
echo "启动GPS软约束检测器..."
rosrun state_estimation gps_soft_loop_detector.py &
GPS_PID=$!
sleep 3
echo "✅ GPS软约束检测器启动"

# 启动GPS质量分析器
echo "启动GPS质量分析器..."
rosrun state_estimation gps_quality_analyzer.py &
QUALITY_PID=$!
sleep 2
echo "✅ GPS质量分析器启动"

# 启动软约束处理器
echo "启动软约束处理器..."
rosrun state_estimation soft_constraint_loop_processor.py &
PROCESSOR_PID=$!
sleep 2
echo "✅ 软约束处理器启动"

# 启动强度保持模块
echo "启动强度保持模块..."
rosrun state_estimation intensity_preserving_pcd_saver \
    _save_directory:="$OUTPUT_DIR" \
    _save_interval:=10.0 &
INTENSITY_PID=$!
sleep 2
echo "✅ 强度保持模块启动"

# 启动智能检测器
echo "启动智能检测器..."
rosrun state_estimation intelligent_start_end_detector.py \
    _departure_threshold:=25.0 \
    _return_threshold:=35.0 &
DETECTOR_PID=$!
sleep 2
echo "✅ 智能检测器启动"

# 现在启用GPS功能
echo "启用GPS软约束功能..."
rosparam set /state_estimation_node/enable_gps true
rosparam set /gps_soft_loop_detector/gps_high_quality_threshold 4
rosparam set /gps_soft_loop_detector/gps_medium_quality_threshold 1
rosparam set /gps_soft_loop_detector/high_quality_proximity 12.0
rosparam set /gps_soft_loop_detector/medium_quality_proximity 20.0
rosparam set /gps_soft_loop_detector/low_quality_proximity 30.0
rosparam set /soft_constraint_loop_processor/confidence_threshold 0.6
rosparam set /soft_constraint_loop_processor/correction_weight 0.3

echo ""
echo "=========================================="
echo "🎉 GPS软约束SLAM系统启动完成"
echo "=========================================="
echo ""
echo "系统状态："
echo "  输出目录: $OUTPUT_DIR"
echo "  SLAM核心: 运行中 (PID: $SLAM_PID)"
echo "  GPS检测器: 运行中 (PID: $GPS_PID)"
echo "  质量分析器: 运行中 (PID: $QUALITY_PID)"
echo "  软约束处理器: 运行中 (PID: $PROCESSOR_PID)"
echo "  强度保持: 运行中 (PID: $INTENSITY_PID)"
echo "  智能检测器: 运行中 (PID: $DETECTOR_PID)"
echo ""
echo "系统特性："
echo "✅ GPS软约束回环检测 - 不干扰点云拼接"
echo "✅ 实时GPS质量分析和自适应调整"
echo "✅ 渐进式轨迹校正 - 保持SLAM连续性"
echo "✅ 完整强度值保持"
echo ""
echo "监控命令："
echo "  rostopic echo /gps_soft_loop_trigger"
echo "  rostopic echo /gps_quality_report"
echo "  rostopic echo /loop_confidence_score"
echo "  rostopic echo /correction_status"
echo "  rostopic echo /aft_mapped_to_init"
echo ""
echo "检查系统状态："
echo "  rostopic list | grep gps"
echo "  rosnode list"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果："
echo "✅ 根据GPS质量自动调整回环检测策略"
echo "✅ 软约束校正不会破坏点云拼接连续性"
echo "✅ 首尾偏差显著减少，保持轨迹平滑"
echo ""
echo "按 Ctrl+C 停止系统"

# 创建停止函数
cleanup() {
    echo ""
    echo "正在停止所有组件..."
    kill $SLAM_PID $GPS_PID $QUALITY_PID $PROCESSOR_PID $INTENSITY_PID $DETECTOR_PID 2>/dev/null
    echo "✅ 所有组件已停止"
    exit 0
}

# 捕获Ctrl+C信号
trap cleanup SIGINT

# 等待用户中断
wait
