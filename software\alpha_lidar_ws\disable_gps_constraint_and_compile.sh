#!/bin/bash

# 禁用GPS约束功能并编译的备用脚本

echo "=========================================="
echo "🔧 禁用GPS约束功能并编译"
echo "=========================================="
echo ""
echo "此脚本将临时禁用GPS约束功能以避免编译错误"
echo "您仍然可以使用其他SLAM功能"
echo ""

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 备份原始文件"
cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup
echo "✓ 已备份voxelMapping.cpp"

echo ""
echo "步骤2: 禁用GPS约束相关代码"

# 创建一个简化版本的voxelMapping.cpp
cat > temp_disable_gps.patch << 'EOF'
# 禁用GPS约束的临时补丁
# 注释掉GPS约束相关的代码
EOF

# 注释掉GPS约束相关的全局变量
sed -i 's/^bool enable_gps_plane_constraint = true;/\/\/ bool enable_gps_plane_constraint = true;  \/\/ DISABLED/' src/state_estimation/src/voxelMapping.cpp
sed -i 's/^bool dynamic_constraint_control = true;/\/\/ bool dynamic_constraint_control = true;  \/\/ DISABLED/' src/state_estimation/src/voxelMapping.cpp

# 注释掉约束控制订阅器
sed -i '/ros::Subscriber sub_constraint_control/,/});/c\
    // GPS约束控制订阅器已禁用\
    // ros::Subscriber sub_constraint_control = ...' src/state_estimation/src/voxelMapping.cpp

echo "✓ GPS约束相关代码已禁用"

echo ""
echo "步骤3: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤4: 清理并编译"
rm -rf build devel
catkin_make -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    source devel/setup.bash
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "🎉 系统已准备就绪 (GPS约束功能已禁用)"
    echo ""
    echo "可用功能:"
    echo "✅ 基础SLAM功能"
    echo "✅ 强度值保持"
    echo "✅ 智能首尾检测"
    echo "✅ 回环检测"
    echo "❌ GPS平面约束 (已禁用)"
    echo ""
    echo "启动选项:"
    echo "1. 基础SLAM系统:"
    echo "   roslaunch state_estimation mapping_robosense.launch"
    echo ""
    echo "2. 智能检测系统:"
    echo "   ./start_without_force_matcher.sh"
    echo ""
    echo "如果需要恢复GPS约束功能:"
    echo "   cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp"
    echo "   然后重新编译"
    
else
    echo ""
    echo "❌ 编译仍然失败!"
    echo ""
    echo "恢复原始文件..."
    cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp
    
    echo ""
    echo "建议的解决方案:"
    echo "1. 检查ROS安装是否完整:"
    echo "   sudo apt install ros-noetic-desktop-full"
    echo ""
    echo "2. 检查PCL安装:"
    echo "   sudo apt install libpcl-dev pcl-tools"
    echo ""
    echo "3. 检查编译器版本:"
    echo "   g++ --version"
    echo "   (需要支持C++14或更高版本)"
    echo ""
    echo "4. 尝试最小化编译:"
    echo "   catkin_make --only-pkg-with-deps state_estimation -DCMAKE_BUILD_TYPE=Release"
    echo ""
    echo "5. 如果仍有问题，可以尝试使用预编译的ROS包"
    
    exit 1
fi

echo ""
echo "=========================================="
echo "编译完成! (GPS约束功能已禁用)"
echo "=========================================="
