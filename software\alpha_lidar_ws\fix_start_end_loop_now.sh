#!/bin/bash

# 立即修复首尾回环检测参数的脚本
# 在系统运行时动态调整参数

echo "=========================================="
echo "立即修复首尾回环检测参数"
echo "=========================================="

echo "当前参数状态："
echo "距离阈值: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 2>/dev/null || echo '未设置')"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius 2>/dev/null || echo '未设置')"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold 2>/dev/null || echo '未设置')"

echo ""
echo "正在应用优化参数..."

# 设置GPS回环检测器参数
echo "1. 设置GPS回环检测器参数..."
rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 40.0
rosparam set /enhanced_gps_loop_closure_optimizer/min_trajectory_length 100.0
rosparam set /enhanced_gps_loop_closure_optimizer/intermediate_loop_threshold 8.0
rosparam set /enhanced_gps_loop_closure_optimizer/revisit_threshold 12.0
rosparam set /enhanced_gps_loop_closure_optimizer/loop_detection_cooldown 1.0
rosparam set /enhanced_gps_loop_closure_optimizer/gps_quality_threshold -1

# 设置SLAM集成模块参数
echo "2. 设置SLAM集成模块参数..."
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 50.0
rosparam set /enhanced_slam_loop_closure_integration/intermediate_search_radius 25.0
rosparam set /enhanced_slam_loop_closure_integration/revisit_search_radius 20.0
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.80
rosparam set /enhanced_slam_loop_closure_integration/intermediate_score_threshold 0.45
rosparam set /enhanced_slam_loop_closure_integration/revisit_score_threshold 0.40
rosparam set /enhanced_slam_loop_closure_integration/voxel_leaf_size 0.25
rosparam set /enhanced_slam_loop_closure_integration/max_search_candidates 20

# 设置点云处理参数
echo "3. 设置点云处理参数..."
rosparam set /enhanced_slam_loop_closure_integration/ndt_transformation_epsilon 0.01
rosparam set /enhanced_slam_loop_closure_integration/ndt_step_size 0.1
rosparam set /enhanced_slam_loop_closure_integration/ndt_resolution 1.0
rosparam set /enhanced_slam_loop_closure_integration/ndt_max_iterations 35

echo ""
echo "参数更新完成！新参数："
echo "距离阈值: $(rosparam get /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold)"
echo "搜索半径: $(rosparam get /enhanced_slam_loop_closure_integration/force_search_radius)"
echo "匹配阈值: $(rosparam get /enhanced_slam_loop_closure_integration/start_end_score_threshold)"

echo ""
echo "=========================================="
echo "参数已优化！现在应该能检测到首尾回环了"
echo "=========================================="

echo ""
echo "监控命令："
echo "rostopic echo /enhanced_gps_loop_closure_status"
echo "rostopic echo /force_loop_closure"

echo ""
echo "如果仍然检测不到，可以进一步放宽参数："
echo "rosparam set /enhanced_gps_loop_closure_optimizer/loop_closure_distance_threshold 60.0"
echo "rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 70.0"
echo "rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.90"
