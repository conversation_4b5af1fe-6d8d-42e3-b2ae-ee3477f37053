#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度优化的GPS引导软约束回环检测器
综合考虑GPS质量变化，实现智能回环检测
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
import json
import threading
from collections import deque
import math
from scipy.spatial.distance import cdist
from sklearn.cluster import DBSCAN

class AdvancedGPSGuidedLoopDetector:
    def __init__(self):
        rospy.init_node('advanced_gps_guided_loop_detector', anonymous=True)
        
        # GPS质量分级参数
        self.gps_quality_levels = {
            'excellent': {'status_min': 2, 'weight': 1.0, 'threshold_factor': 1.0},
            'good': {'status_min': 1, 'weight': 0.8, 'threshold_factor': 1.2},
            'fair': {'status_min': 0, 'weight': 0.6, 'threshold_factor': 1.5},
            'poor': {'status_min': -1, 'weight': 0.3, 'threshold_factor': 2.0}
        }
        
        # 动态阈值参数
        self.base_gps_threshold = rospy.get_param('~base_gps_threshold', 15.0)
        self.base_slam_threshold = rospy.get_param('~base_slam_threshold', 10.0)
        self.min_time_gap = rospy.get_param('~min_time_gap', 30.0)
        self.max_time_gap = rospy.get_param('~max_time_gap', 300.0)
        
        # 轨迹分析参数
        self.trajectory_smoothing_window = rospy.get_param('~smoothing_window', 5)
        self.velocity_consistency_threshold = rospy.get_param('~velocity_threshold', 2.0)
        self.heading_consistency_threshold = rospy.get_param('~heading_threshold', 45.0)
        
        # 置信度计算参数
        self.confidence_weights = {
            'gps_quality': 0.25,
            'distance_consistency': 0.25,
            'trajectory_similarity': 0.20,
            'temporal_consistency': 0.15,
            'geometric_consistency': 0.15
        }
        
        # 数据存储
        self.gps_trajectory = deque(maxlen=2000)
        self.slam_trajectory = deque(maxlen=2000)
        self.quality_history = deque(maxlen=2000)
        self.velocity_history = deque(maxlen=100)
        
        # 统计信息
        self.detection_stats = {
            'total_detections': 0,
            'high_confidence_detections': 0,
            'quality_based_rejections': 0,
            'geometric_rejections': 0,
            'temporal_rejections': 0
        }
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 初始化订阅器和发布器
        self.init_subscribers()
        self.init_publishers()
        self.init_timers()
        
        rospy.loginfo("Advanced GPS Guided Loop Detector Started")
        rospy.loginfo("Base GPS threshold: %.1f m, Base SLAM threshold: %.1f m", 
                     self.base_gps_threshold, self.base_slam_threshold)
    
    def init_subscribers(self):
        """初始化订阅器"""
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback, queue_size=20)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback, queue_size=20)
        self.path_sub = rospy.Subscriber('/path', Path, self.path_callback, queue_size=1)
    
    def init_publishers(self):
        """初始化发布器"""
        self.loop_trigger_pub = rospy.Publisher('/advanced_gps_loop_trigger', String, queue_size=1)
        self.confidence_pub = rospy.Publisher('/loop_detection_confidence', Float64, queue_size=1)
        self.quality_analysis_pub = rospy.Publisher('/gps_quality_analysis', String, queue_size=1)
        self.status_pub = rospy.Publisher('/advanced_gps_loop_status', String, queue_size=1)
    
    def init_timers(self):
        """初始化定时器"""
        self.detection_timer = rospy.Timer(rospy.Duration(1.5), self.advanced_loop_detection)
        self.quality_timer = rospy.Timer(rospy.Duration(5.0), self.analyze_gps_quality)
        self.status_timer = rospy.Timer(rospy.Duration(15.0), self.publish_comprehensive_status)
    
    def gps_callback(self, msg):
        """处理GPS数据，包含质量分析"""
        with self.data_lock:
            current_time = rospy.Time.now().to_sec()
            
            # GPS质量评估
            quality_info = self.assess_gps_quality(msg)
            
            # 坐标转换
            if len(self.gps_trajectory) == 0:
                self.origin_lat = msg.latitude
                self.origin_lon = msg.longitude
                self.origin_alt = msg.altitude
                local_x, local_y = 0.0, 0.0
            else:
                local_x, local_y = self.gps_to_local(msg.latitude, msg.longitude)
            
            gps_point = {
                'x': local_x,
                'y': local_y,
                'z': msg.altitude - self.origin_alt,
                'timestamp': current_time,
                'raw_status': msg.status.status,
                'quality_level': quality_info['level'],
                'quality_weight': quality_info['weight'],
                'covariance': self.extract_covariance(msg),
                'lat': msg.latitude,
                'lon': msg.longitude
            }
            
            self.gps_trajectory.append(gps_point)
            self.quality_history.append(quality_info)
    
    def pose_callback(self, msg):
        """处理SLAM位姿数据"""
        with self.data_lock:
            current_time = msg.header.stamp.to_sec()
            
            slam_point = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'timestamp': current_time,
                'orientation': {
                    'x': msg.pose.orientation.x,
                    'y': msg.pose.orientation.y,
                    'z': msg.pose.orientation.z,
                    'w': msg.pose.orientation.w
                }
            }
            
            # 计算速度
            if len(self.slam_trajectory) > 0:
                prev_point = self.slam_trajectory[-1]
                dt = current_time - prev_point['timestamp']
                if dt > 0:
                    dx = slam_point['x'] - prev_point['x']
                    dy = slam_point['y'] - prev_point['y']
                    velocity = math.sqrt(dx*dx + dy*dy) / dt
                    self.velocity_history.append(velocity)
            
            self.slam_trajectory.append(slam_point)
    
    def path_callback(self, msg):
        """处理路径数据用于轨迹分析"""
        # 可用于更复杂的轨迹分析
        pass
    
    def assess_gps_quality(self, gps_msg):
        """评估GPS质量"""
        status = gps_msg.status.status
        
        # 基于状态的质量分级
        if status >= 2:
            level = 'excellent'
        elif status >= 1:
            level = 'good'
        elif status >= 0:
            level = 'fair'
        else:
            level = 'poor'
        
        quality_info = self.gps_quality_levels[level].copy()
        quality_info['level'] = level
        
        # 基于协方差的质量调整
        covariance = self.extract_covariance(gps_msg)
        if covariance['position_accuracy'] > 0:
            # 根据位置精度调整权重
            accuracy_factor = min(1.0, 5.0 / covariance['position_accuracy'])
            quality_info['weight'] *= accuracy_factor
        
        return quality_info
    
    def extract_covariance(self, gps_msg):
        """提取GPS协方差信息"""
        cov = gps_msg.position_covariance
        return {
            'position_accuracy': math.sqrt(cov[0] + cov[4]) if cov[0] > 0 else 0.0,
            'altitude_accuracy': math.sqrt(cov[8]) if cov[8] > 0 else 0.0,
            'covariance_type': gps_msg.position_covariance_type
        }
    
    def gps_to_local(self, lat, lon):
        """高精度GPS坐标转换"""
        lat_diff = lat - self.origin_lat
        lon_diff = lon - self.origin_lon
        
        # 使用更精确的转换
        lat_rad = math.radians(self.origin_lat)
        x = lon_diff * 111320.0 * math.cos(lat_rad)
        y = lat_diff * 110540.0
        
        return x, y
    
    def advanced_loop_detection(self, event):
        """高级回环检测算法"""
        try:
            with self.data_lock:
                if len(self.gps_trajectory) < 20 or len(self.slam_trajectory) < 20:
                    return
                
                current_time = rospy.Time.now().to_sec()
                current_gps = self.gps_trajectory[-1]
                current_slam = self.slam_trajectory[-1]
                
                # 多层次候选筛选
                candidates = self.multi_stage_candidate_selection(current_gps, current_slam)
                
                if candidates:
                    # 深度验证和置信度计算
                    best_candidate = self.deep_candidate_validation(current_gps, current_slam, candidates)
                    
                    if best_candidate and best_candidate['confidence'] > 0.7:
                        self.trigger_high_confidence_loop(current_gps, current_slam, best_candidate)
                        
        except Exception as e:
            rospy.logerr("Error in advanced loop detection: %s", str(e))
    
    def multi_stage_candidate_selection(self, current_gps, current_slam):
        """多阶段候选选择"""
        candidates = []
        current_time = current_gps['timestamp']
        
        # 阶段1: GPS距离筛选（动态阈值）
        gps_threshold = self.base_gps_threshold * current_gps['quality_weight']
        
        for i, gps_point in enumerate(self.gps_trajectory[:-20]):
            time_gap = current_time - gps_point['timestamp']
            
            # 时间窗口检查
            if time_gap < self.min_time_gap or time_gap > self.max_time_gap:
                continue
            
            # GPS距离检查
            gps_distance = self.calculate_3d_distance(current_gps, gps_point)
            adaptive_threshold = gps_threshold * gps_point.get('quality_weight', 0.5)
            
            if gps_distance <= adaptive_threshold:
                candidates.append({
                    'index': i,
                    'gps_point': gps_point,
                    'gps_distance': gps_distance,
                    'time_gap': time_gap,
                    'quality_score': (current_gps['quality_weight'] + gps_point['quality_weight']) / 2
                })
        
        # 阶段2: SLAM轨迹一致性检查
        validated_candidates = []
        for candidate in candidates:
            if candidate['index'] < len(self.slam_trajectory):
                historical_slam = self.slam_trajectory[candidate['index']]
                slam_distance = self.calculate_3d_distance(current_slam, historical_slam)
                
                # 动态SLAM阈值
                slam_threshold = self.base_slam_threshold * candidate['quality_score']
                
                if slam_distance <= slam_threshold:
                    candidate['slam_point'] = historical_slam
                    candidate['slam_distance'] = slam_distance
                    validated_candidates.append(candidate)
        
        # 阶段3: 轨迹几何一致性
        geometric_candidates = []
        for candidate in validated_candidates:
            geometric_score = self.calculate_geometric_consistency(
                current_gps, current_slam, candidate['gps_point'], candidate['slam_point'])
            
            if geometric_score > 0.5:
                candidate['geometric_score'] = geometric_score
                geometric_candidates.append(candidate)
        
        return geometric_candidates[:3]  # 返回最佳3个候选
    
    def deep_candidate_validation(self, current_gps, current_slam, candidates):
        """深度候选验证和置信度计算"""
        best_candidate = None
        best_confidence = 0.0
        
        for candidate in candidates:
            confidence_scores = {}
            
            # 1. GPS质量置信度
            confidence_scores['gps_quality'] = candidate['quality_score']
            
            # 2. 距离一致性置信度
            gps_ratio = min(1.0, self.base_gps_threshold / max(candidate['gps_distance'], 0.1))
            slam_ratio = min(1.0, self.base_slam_threshold / max(candidate['slam_distance'], 0.1))
            confidence_scores['distance_consistency'] = (gps_ratio + slam_ratio) / 2
            
            # 3. 轨迹相似性置信度
            confidence_scores['trajectory_similarity'] = self.calculate_trajectory_similarity(
                candidate['index'], len(self.slam_trajectory) - 1)
            
            # 4. 时间一致性置信度
            time_factor = min(1.0, candidate['time_gap'] / 120.0)  # 2分钟内为最佳
            confidence_scores['temporal_consistency'] = time_factor
            
            # 5. 几何一致性置信度
            confidence_scores['geometric_consistency'] = candidate.get('geometric_score', 0.5)
            
            # 综合置信度计算
            total_confidence = sum(
                confidence_scores[key] * self.confidence_weights[key] 
                for key in confidence_scores
            )
            
            candidate['confidence'] = total_confidence
            candidate['confidence_breakdown'] = confidence_scores
            
            if total_confidence > best_confidence:
                best_confidence = total_confidence
                best_candidate = candidate
        
        return best_candidate
    
    def calculate_3d_distance(self, point1, point2):
        """计算3D距离"""
        dx = point1['x'] - point2['x']
        dy = point1['y'] - point2['y']
        dz = point1.get('z', 0) - point2.get('z', 0)
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def calculate_geometric_consistency(self, gps1, slam1, gps2, slam2):
        """计算几何一致性"""
        # GPS向量
        gps_vector = np.array([gps1['x'] - gps2['x'], gps1['y'] - gps2['y']])
        # SLAM向量
        slam_vector = np.array([slam1['x'] - slam2['x'], slam1['y'] - slam2['y']])
        
        # 向量长度
        gps_length = np.linalg.norm(gps_vector)
        slam_length = np.linalg.norm(slam_vector)
        
        if gps_length < 0.1 or slam_length < 0.1:
            return 0.5
        
        # 长度比例一致性
        length_ratio = min(gps_length, slam_length) / max(gps_length, slam_length)
        
        # 方向一致性
        cos_angle = np.dot(gps_vector, slam_vector) / (gps_length * slam_length)
        angle_consistency = (cos_angle + 1) / 2  # 归一化到[0,1]
        
        return (length_ratio + angle_consistency) / 2
    
    def calculate_trajectory_similarity(self, hist_index, curr_index):
        """计算轨迹相似性"""
        if hist_index < 10 or curr_index < 10:
            return 0.5
        
        # 提取轨迹段
        hist_segment = self.slam_trajectory[max(0, hist_index-5):hist_index+5]
        curr_segment = self.slam_trajectory[max(0, curr_index-5):curr_index+5]
        
        if len(hist_segment) < 5 or len(curr_segment) < 5:
            return 0.5
        
        # 计算轨迹特征相似性
        hist_curvature = self.calculate_trajectory_curvature(hist_segment)
        curr_curvature = self.calculate_trajectory_curvature(curr_segment)
        
        curvature_similarity = 1.0 - min(1.0, abs(hist_curvature - curr_curvature) / math.pi)
        
        return curvature_similarity
    
    def calculate_trajectory_curvature(self, trajectory_segment):
        """计算轨迹曲率"""
        if len(trajectory_segment) < 3:
            return 0.0
        
        angles = []
        for i in range(1, len(trajectory_segment) - 1):
            p1 = trajectory_segment[i-1]
            p2 = trajectory_segment[i]
            p3 = trajectory_segment[i+1]
            
            v1 = np.array([p2['x'] - p1['x'], p2['y'] - p1['y']])
            v2 = np.array([p3['x'] - p2['x'], p3['y'] - p2['y']])
            
            if np.linalg.norm(v1) > 0.1 and np.linalg.norm(v2) > 0.1:
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                angle = math.acos(np.clip(cos_angle, -1, 1))
                angles.append(angle)
        
        return np.mean(angles) if angles else 0.0

    def trigger_high_confidence_loop(self, current_gps, current_slam, best_candidate):
        """触发高置信度回环检测"""
        try:
            current_time = rospy.Time.now().to_sec()
            self.detection_stats['total_detections'] += 1

            if best_candidate['confidence'] > 0.8:
                self.detection_stats['high_confidence_detections'] += 1

            # 构建详细的回环数据
            loop_data = {
                'type': 'advanced_gps_guided_loop_closure',
                'timestamp': current_time,
                'confidence': best_candidate['confidence'],
                'confidence_breakdown': best_candidate['confidence_breakdown'],
                'current_pose': {
                    'gps': {'x': current_gps['x'], 'y': current_gps['y'], 'z': current_gps.get('z', 0)},
                    'slam': {'x': current_slam['x'], 'y': current_slam['y'], 'z': current_slam['z']}
                },
                'target_pose': {
                    'gps': {'x': best_candidate['gps_point']['x'], 'y': best_candidate['gps_point']['y'], 'z': best_candidate['gps_point'].get('z', 0)},
                    'slam': {'x': best_candidate['slam_point']['x'], 'y': best_candidate['slam_point']['y'], 'z': best_candidate['slam_point']['z']}
                },
                'metrics': {
                    'gps_distance': best_candidate['gps_distance'],
                    'slam_distance': best_candidate['slam_distance'],
                    'time_gap': best_candidate['time_gap'],
                    'quality_score': best_candidate['quality_score'],
                    'geometric_score': best_candidate['geometric_score']
                },
                'quality_info': {
                    'current_gps_quality': current_gps['quality_level'],
                    'target_gps_quality': best_candidate['gps_point']['quality_level'],
                    'combined_weight': best_candidate['quality_score']
                }
            }

            # 发布回环触发信号
            trigger_msg = String()
            trigger_msg.data = json.dumps(loop_data, indent=2)
            self.loop_trigger_pub.publish(trigger_msg)

            # 发布置信度
            confidence_msg = Float64()
            confidence_msg.data = best_candidate['confidence']
            self.confidence_pub.publish(confidence_msg)

            rospy.loginfo("🎯 Advanced GPS Loop Closure Triggered!")
            rospy.loginfo("Confidence: %.3f | GPS: %.2fm | SLAM: %.2fm | Quality: %s->%s",
                         best_candidate['confidence'],
                         best_candidate['gps_distance'],
                         best_candidate['slam_distance'],
                         best_candidate['gps_point']['quality_level'],
                         current_gps['quality_level'])

        except Exception as e:
            rospy.logerr("Error triggering high confidence loop: %s", str(e))

    def analyze_gps_quality(self, event):
        """分析GPS质量趋势"""
        try:
            with self.data_lock:
                if len(self.quality_history) < 10:
                    return

                recent_quality = list(self.quality_history)[-50:]  # 最近50个点

                # 质量统计
                quality_stats = {
                    'excellent': sum(1 for q in recent_quality if q['level'] == 'excellent'),
                    'good': sum(1 for q in recent_quality if q['level'] == 'good'),
                    'fair': sum(1 for q in recent_quality if q['level'] == 'fair'),
                    'poor': sum(1 for q in recent_quality if q['level'] == 'poor')
                }

                total_points = len(recent_quality)
                quality_percentages = {k: (v/total_points)*100 for k, v in quality_stats.items()}

                # 平均权重
                avg_weight = np.mean([q['weight'] for q in recent_quality])

                # 质量趋势分析
                if len(recent_quality) >= 20:
                    first_half = recent_quality[:len(recent_quality)//2]
                    second_half = recent_quality[len(recent_quality)//2:]

                    first_avg = np.mean([q['weight'] for q in first_half])
                    second_avg = np.mean([q['weight'] for q in second_half])

                    trend = "improving" if second_avg > first_avg else "degrading" if second_avg < first_avg else "stable"
                else:
                    trend = "insufficient_data"

                quality_analysis = {
                    'timestamp': rospy.Time.now().to_sec(),
                    'quality_distribution': quality_percentages,
                    'average_weight': avg_weight,
                    'trend': trend,
                    'recommendation': self.get_quality_recommendation(avg_weight, quality_percentages)
                }

                # 发布质量分析
                analysis_msg = String()
                analysis_msg.data = json.dumps(quality_analysis, indent=2)
                self.quality_analysis_pub.publish(analysis_msg)

        except Exception as e:
            rospy.logerr("Error in GPS quality analysis: %s", str(e))

    def get_quality_recommendation(self, avg_weight, quality_percentages):
        """获取质量建议"""
        if avg_weight > 0.8:
            return "GPS quality excellent - use standard thresholds"
        elif avg_weight > 0.6:
            return "GPS quality good - slightly relax thresholds"
        elif avg_weight > 0.4:
            return "GPS quality fair - increase thresholds and rely more on SLAM"
        else:
            return "GPS quality poor - consider disabling GPS guidance temporarily"

    def publish_comprehensive_status(self, event):
        """发布综合状态报告"""
        try:
            with self.data_lock:
                current_time = rospy.Time.now().to_sec()

                # 计算轨迹统计
                trajectory_length = 0.0
                if len(self.slam_trajectory) > 1:
                    for i in range(1, len(self.slam_trajectory)):
                        trajectory_length += self.calculate_3d_distance(
                            self.slam_trajectory[i-1], self.slam_trajectory[i])

                # 速度统计
                avg_velocity = np.mean(self.velocity_history) if self.velocity_history else 0.0
                max_velocity = np.max(self.velocity_history) if self.velocity_history else 0.0

                # GPS质量统计
                recent_quality = list(self.quality_history)[-100:] if self.quality_history else []
                avg_gps_weight = np.mean([q['weight'] for q in recent_quality]) if recent_quality else 0.0

                status_report = {
                    'timestamp': current_time,
                    'detector_type': 'advanced_gps_guided_loop_detector',
                    'data_statistics': {
                        'gps_points': len(self.gps_trajectory),
                        'slam_points': len(self.slam_trajectory),
                        'trajectory_length_m': trajectory_length,
                        'avg_velocity_ms': avg_velocity,
                        'max_velocity_ms': max_velocity
                    },
                    'detection_statistics': self.detection_stats.copy(),
                    'gps_quality': {
                        'average_weight': avg_gps_weight,
                        'recent_samples': len(recent_quality)
                    },
                    'performance_metrics': {
                        'detection_rate': self.detection_stats['total_detections'] / max(1, len(self.gps_trajectory) / 100),
                        'high_confidence_rate': self.detection_stats['high_confidence_detections'] / max(1, self.detection_stats['total_detections']),
                        'rejection_rate': (self.detection_stats['quality_based_rejections'] +
                                         self.detection_stats['geometric_rejections']) / max(1, self.detection_stats['total_detections'])
                    },
                    'current_parameters': {
                        'base_gps_threshold': self.base_gps_threshold,
                        'base_slam_threshold': self.base_slam_threshold,
                        'min_time_gap': self.min_time_gap,
                        'confidence_weights': self.confidence_weights
                    }
                }

                status_msg = String()
                status_msg.data = json.dumps(status_report, indent=2)
                self.status_pub.publish(status_msg)

                # 控制台输出关键信息
                rospy.loginfo("Advanced GPS Loop Detector Status:")
                rospy.loginfo("  GPS Quality: %.2f | Detections: %d (High Conf: %d)",
                             avg_gps_weight,
                             self.detection_stats['total_detections'],
                             self.detection_stats['high_confidence_detections'])
                rospy.loginfo("  Trajectory: %.1fm | Velocity: %.2fm/s | Points: GPS=%d, SLAM=%d",
                             trajectory_length, avg_velocity,
                             len(self.gps_trajectory), len(self.slam_trajectory))

        except Exception as e:
            rospy.logerr("Error publishing comprehensive status: %s", str(e))

def main():
    try:
        detector = AdvancedGPSGuidedLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Advanced GPS Guided Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
