#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS轨迹分析工具 - 简化版本
从ROS bag文件中提取GPS数据，使用matplotlib进行2D可视化分析
支持不同GPS质量的颜色编码和统计分析
"""

try:
    import rosbag
    USE_ROSBAG = True
except ImportError:
    try:
        import bagpy
        USE_ROSBAG = False
        print("⚠️  使用bagpy替代rosbag (Windows兼容模式)")
    except ImportError:
        print("❌ 错误: 需要安装rosbag或bagpy")
        print("请运行: pip install bagpy")
        exit(1)

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import argparse
import sys
import os
from collections import defaultdict
import math
import pandas as pd

class GPSTrajectoryAnalyzer:
    def __init__(self):
        self.gps_data = []
        self.trajectory_points = []
        # GPS质量颜色映射
        self.quality_colors = {
            -1: 'red',      # NO_FIX: 红色
            0:  'green',    # RTK_FIXED: 绿色 (固定解)
            1:  'blue',     # SBAS_FIX: 蓝色
            2:  'yellow',   # GBAS_FIX: 黄色
            3:  'purple',   # 其他: 紫色
        }
        self.quality_names = {
            -1: "NO_FIX",
            0:  "RTK_FIXED",
            1:  "SBAS_FIX", 
            2:  "GBAS_FIX",
            3:  "OTHER"
        }
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
    def extract_gps_from_bag(self, bag_file, topic_name="/rtk/gnss"):
        """从bag文件中提取GPS数据"""
        print(f"🔍 正在从 {bag_file} 提取GPS数据...")
        
        if USE_ROSBAG:
            return self._extract_with_rosbag(bag_file, topic_name)
        else:
            return self._extract_with_bagpy(bag_file, topic_name)
    
    def _extract_with_rosbag(self, bag_file, topic_name):
        """使用rosbag提取GPS数据"""
        try:
            bag = rosbag.Bag(bag_file, 'r')
            gps_count = 0
            
            for topic, msg, t in bag.read_messages(topics=[topic_name]):
                gps_info = {
                    'timestamp': t.to_sec(),
                    'latitude': msg.latitude,
                    'longitude': msg.longitude,
                    'altitude': msg.altitude,
                    'status': msg.status.status,
                    'service': msg.status.service,
                }
                self.gps_data.append(gps_info)
                gps_count += 1
                
                if gps_count % 500 == 0:
                    print(f"  已提取 {gps_count} 个GPS点...")
                    
            bag.close()
            print(f"✅ GPS数据提取完成，共 {len(self.gps_data)} 个点")
            
        except Exception as e:
            print(f"❌ 读取bag文件失败: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def _extract_with_bagpy(self, bag_file, topic_name):
        """使用bagpy提取GPS数据"""
        try:
            # 使用bagpy读取bag文件
            bag = bagpy.bagreader(bag_file)
            
            # 读取GPS数据
            print(f"📡 正在读取topic: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            
            # 读取CSV文件
            df = pd.read_csv(gps_csv)
            
            gps_count = 0
            for index, row in df.iterrows():
                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1
                    
                    if gps_count % 500 == 0:
                        print(f"  已提取 {gps_count} 个GPS点...")
                        
                except (ValueError, KeyError) as e:
                    continue
                    
            print(f"✅ GPS数据提取完成，共 {len(self.gps_data)} 个点")
            
        except Exception as e:
            print(f"❌ 读取bag文件失败: {e}")
            return False
            
        return len(self.gps_data) > 0
    
    def convert_to_local_coordinates(self):
        """将GPS坐标转换为本地坐标系"""
        if not self.gps_data:
            return
            
        # 使用第一个有效GPS点作为原点
        for gps in self.gps_data:
            if gps['status'] >= 0:  # 有效GPS
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude'] 
                self.origin_alt = gps['altitude']
                break
                
        if self.origin_lat is None:
            # 使用第一个点作为原点
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return
                
        print(f"📍 GPS原点设置为: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}, alt={self.origin_alt:.3f}")
        
        # 转换为本地坐标
        for gps in self.gps_data:
            # 简化的坐标转换（适用于小范围）
            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt
            
            # 转换为米 (简化公式，适用于小范围)
            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff
            
            point_info = {
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)
    
    def analyze_gps_quality(self):
        """分析GPS质量统计"""
        if not self.gps_data:
            return {}
            
        status_count = defaultdict(int)
        total_points = len(self.gps_data)
        
        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3  # 其他状态
            status_count[status] += 1
            
        print("\n" + "="*50)
        print("📊 GPS质量分析")
        print("="*50)
        print(f"总GPS点数: {total_points:,}")
        print("-" * 40)
        
        for status, count in sorted(status_count.items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "UNKNOWN")
            color = self.quality_colors.get(status, 'gray')
            print(f"{quality_name:12}: {count:6,d} 点 ({percentage:5.1f}%) - {color}")
            
        # 计算轨迹统计
        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])
            trajectory_length = self.calculate_trajectory_length()
            
            print("\n" + "="*50)
            print("📏 轨迹统计")
            print("="*50)
            print(f"轨迹总长度: {trajectory_length:.1f} 米")
            print(f"X范围: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} 米")
            print(f"Y范围: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} 米") 
            print(f"Z范围: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} 米")
            
            # 计算首尾距离
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                print(f"首尾距离: {start_end_distance:.1f} 米")
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0
                print(f"闭合误差: {closure_error:.2f}%")
            
        return status_count
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.trajectory_points) < 2:
            return 0.0
            
        total_length = 0.0
        for i in range(1, len(self.trajectory_points)):
            p1 = np.array(self.trajectory_points[i-1]['position'])
            p2 = np.array(self.trajectory_points[i]['position'])
            total_length += np.linalg.norm(p2 - p1)
            
        return total_length

    def create_trajectory_visualization(self):
        """创建轨迹可视化"""
        if not self.trajectory_points:
            print("❌ 没有轨迹点可以可视化")
            return False

        print("🎨 正在创建2D可视化...")

        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 按GPS状态分组
        status_groups = defaultdict(list)
        for i, point in enumerate(self.trajectory_points):
            status = point['status']
            if status not in self.quality_names:
                status = 3
            status_groups[status].append(i)

        # 绘制轨迹 - XY平面
        for status, indices in status_groups.items():
            if len(indices) < 2:
                continue

            points = [self.trajectory_points[i]['position'] for i in indices]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]

            color = self.quality_colors[status]
            quality_name = self.quality_names[status]

            if status == 0:  # RTK固定解用实线
                ax1.plot(x_coords, y_coords, color=color, linewidth=2,
                        label=f'{quality_name} ({len(indices)} 点)', alpha=0.8)
            else:  # 其他用虚线
                ax1.plot(x_coords, y_coords, color=color, linewidth=1.5,
                        linestyle='--', label=f'{quality_name} ({len(indices)} 点)', alpha=0.7)

        # 标记起点和终点
        if self.trajectory_points:
            start_pos = self.trajectory_points[0]['position']
            end_pos = self.trajectory_points[-1]['position']

            ax1.plot(start_pos[0], start_pos[1], 'go', markersize=10, label='起点')
            ax1.plot(end_pos[0], end_pos[1], 'ro', markersize=10, label='终点')

        ax1.set_xlabel('X (米)')
        ax1.set_ylabel('Y (米)')
        ax1.set_title('GPS轨迹 - 平面视图')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.axis('equal')

        # 绘制高度变化
        if self.trajectory_points:
            distances = [0]
            heights = [self.trajectory_points[0]['position'][2]]

            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                dist = np.linalg.norm(p2[:2] - p1[:2])  # 只计算XY距离
                distances.append(distances[-1] + dist)
                heights.append(p2[2])

            # 按状态着色高度曲线
            for status, indices in status_groups.items():
                if len(indices) < 2:
                    continue

                status_distances = [distances[i] for i in indices]
                status_heights = [heights[i] for i in indices]

                color = self.quality_colors[status]
                quality_name = self.quality_names[status]

                if status == 0:  # RTK固定解用实线
                    ax2.plot(status_distances, status_heights, color=color,
                            linewidth=2, label=quality_name, alpha=0.8)
                else:  # 其他用虚线
                    ax2.plot(status_distances, status_heights, color=color,
                            linewidth=1.5, linestyle='--', label=quality_name, alpha=0.7)

        ax2.set_xlabel('距离 (米)')
        ax2.set_ylabel('高度 (米)')
        ax2.set_title('GPS轨迹 - 高度变化')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        plt.tight_layout()
        return True

    def run_analysis(self, bag_file, topic_name="/rtk/gnss"):
        """运行完整的GPS分析"""
        print("🚀 GPS轨迹分析工具启动")
        print("="*60)

        # 检查文件是否存在
        if not os.path.exists(bag_file):
            print(f"❌ 错误: bag文件不存在: {bag_file}")
            return False

        # 提取GPS数据
        if not self.extract_gps_from_bag(bag_file, topic_name):
            print("❌ GPS数据提取失败")
            return False

        # 转换坐标
        self.convert_to_local_coordinates()

        # 分析质量
        quality_stats = self.analyze_gps_quality()

        # 创建可视化
        if self.create_trajectory_visualization():
            print("\n🖥️  2D可视化窗口已打开")
            print("💡 操作提示:")
            print("   - 关闭窗口退出程序")
            print("   - 可以缩放和平移图像")

            plt.show()
        else:
            print("❌ 可视化创建失败")
            return False

        print("\n✅ GPS轨迹分析完成")
        return True

def main():
    parser = argparse.ArgumentParser(
        description='GPS轨迹分析工具 - 简化版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 gps_analyzer_simple.py data.bag
  python3 gps_analyzer_simple.py data.bag --topic /gps/fix
        """
    )
    parser.add_argument('bag_file', help='ROS bag文件路径')
    parser.add_argument('--topic', default='/rtk/gnss',
                       help='GPS topic名称 (默认: /rtk/gnss)')

    args = parser.parse_args()

    # 运行分析
    analyzer = GPSTrajectoryAnalyzer()
    success = analyzer.run_analysis(args.bag_file, args.topic)

    if not success:
        print("❌ GPS轨迹分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
