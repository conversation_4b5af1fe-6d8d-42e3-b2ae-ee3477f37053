# αLiDAR项目Ubuntu完整编译和执行命令

## 项目路径说明

基于您的项目结构：
```
l:\WORK\alidar_AIcode\github_alidar03\alpha_lidar_GPS\
├── software/
│   └── alpha_lidar_ws/          # ROS工作空间
│       └── src/
│           └── state_estimation/  # 核心SLAM包
├── hardware/                    # 硬件设计文件
└── documents/                   # 文档和图片
```

## 第一部分：环境准备

### 1. 系统要求确认
```bash
# 确认Ubuntu版本
lsb_release -a
# 输出应显示: Ubuntu 20.04.x LTS

# 确认ROS版本
rosversion -d
# 输出应显示: noetic
```

### 2. 安装基础依赖
```bash
# 更新软件包列表
sudo apt update

# 安装ROS Noetic (如果未安装)
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654
sudo apt update
sudo apt install ros-noetic-desktop-full

# 安装编译工具
sudo apt install -y \
    build-essential \
    cmake \
    git \
    python3-catkin-tools \
    python3-rosdep \
    python3-rosinstall \
    python3-rosinstall-generator \
    python3-wstool
```

### 3. 安装αLiDAR特定依赖
```bash
# 安装PCL点云库
sudo apt install -y \
    libpcl-dev \
    pcl-tools

# 安装Eigen3数学库
sudo apt install -y libeigen3-dev

# 安装OpenCV
sudo apt install -y libopencv-dev

# 安装其他依赖
sudo apt install -y \
    libgoogle-glog-dev \
    libgflags-dev \
    libatlas-base-dev \
    libblas-dev \
    liblapack-dev
```

### 4. 安装GPS集成依赖 (新增)
```bash
# 安装GeographicLib用于GPS坐标转换
sudo apt install -y \
    libgeographic-dev \
    libgeographic19

# 验证安装
pkg-config --modversion geographic
```

## 第二部分：项目编译

### 1. 设置工作空间路径
```bash
# 设置项目路径变量 (根据您的实际路径调整)
export ALPHA_LIDAR_PATH="/path/to/your/alpha_lidar_GPS"
export WORKSPACE_PATH="$ALPHA_LIDAR_PATH/software/alpha_lidar_ws"

# 或者直接使用绝对路径
cd /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws
```

### 2. 初始化ROS环境
```bash
# 设置ROS环境
source /opt/ros/noetic/setup.bash

# 初始化rosdep (首次使用)
sudo rosdep init
rosdep update
```

### 3. 安装项目依赖
```bash
# 进入工作空间
cd $WORKSPACE_PATH

# 安装依赖包
rosdep install --from-paths src --ignore-src -r -y
```

### 4. 编译项目
```bash
# 清理之前的编译结果 (如果存在)
rm -rf build/ devel/

# 编译整个工作空间
catkin_make

# 或者只编译state_estimation包
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"

# 检查编译结果
echo "编译状态: $?"
if [ $? -eq 0 ]; then
    echo "✅ 编译成功!"
else
    echo "❌ 编译失败，请检查错误信息"
fi
```

### 5. 设置环境变量
```bash
# 设置工作空间环境
source $WORKSPACE_PATH/devel/setup.bash

# 添加到.bashrc (永久生效)
echo "source $WORKSPACE_PATH/devel/setup.bash" >> ~/.bashrc
```

## 第三部分：GPS集成部署 (可选)

### 1. 备份原始代码
```bash
cd $WORKSPACE_PATH/src/state_estimation/src
cp voxelMapping.cpp voxelMapping.cpp.original
cp ../CMakeLists.txt ../CMakeLists.txt.original
cp ../package.xml ../package.xml.original
```

### 2. 修改CMakeLists.txt添加GPS支持
```bash
cd $WORKSPACE_PATH/src/state_estimation

# 添加GeographicLib依赖
cat >> CMakeLists.txt << 'EOF'

# GPS集成相关依赖
find_package(PkgConfig REQUIRED)
pkg_check_modules(GEOGRAPHIC REQUIRED geographic)
include_directories(${GEOGRAPHIC_INCLUDE_DIRS})

# 更新链接库
target_link_libraries(state_estimation_node
  ${catkin_LIBRARIES}
  ${PCL_LIBRARIES}
  ${PYTHON_LIBRARIES}
  ${GEOGRAPHIC_LIBRARIES}
)
EOF
```

### 3. 修改package.xml
```bash
# 添加GPS依赖
sed -i '/<\/package>/i\  <build_depend>libgeographic-dev</build_depend>' package.xml
sed -i '/<\/package>/i\  <run_depend>libgeographic-dev</run_depend>' package.xml
```

### 4. 重新编译
```bash
cd $WORKSPACE_PATH
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"
```

## 第四部分：系统执行

### 方法1：标准执行 (推荐)

#### 终端1：启动roscore
```bash
# 新开终端1
roscore
```

#### 终端2：启动αLiDAR节点
```bash
# 新开终端2
cd /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 启动SLAM节点
roslaunch state_estimation mapping_robosense.launch

# 预期输出：
# - 节点启动信息
# - 等待数据的提示
# - 如果集成了GPS，会显示GPS相关信息
```

#### 终端3：播放数据集
```bash
# 新开终端3
cd /path/to/your/datasets

# 检查bag文件
ls -la *.bag

# 播放bag文件 (暂停模式)
rosbag play your_dataset.bag --pause

# 操作说明：
# - 按空格键开始/暂停播放
# - 按's'键单步播放
# - Ctrl+C停止播放
```

#### 终端4：监控系统 (可选)
```bash
# 新开终端4

# 查看所有topic
rostopic list

# 监控点云数据
rostopic hz /velodyne_points

# 监控IMU数据
rostopic hz /imu/data

# 监控里程计输出
rostopic echo /odometry

# 如果有GPS数据
rostopic hz /gps/fix
rostopic echo /gps/fix
```

### 方法2：Docker执行 (如果使用Docker)

```bash
# 进入Docker目录
cd /path/to/your/alpha_lidar_GPS/software/docker

# 构建Docker镜像
docker-compose build

# 启动Docker容器
docker-compose up

# 在容器内执行
docker-compose exec alpha-lidar bash
```

## 第五部分：可视化和监控

### 1. 启动RViz可视化
```bash
# 新开终端
source /opt/ros/noetic/setup.bash
source /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash

# 启动RViz
rviz

# 或者使用预配置的RViz
rviz -d /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config/rviz_cfg/alpha_lidar.rviz
```

### 2. 监控系统性能
```bash
# 监控CPU和内存使用
htop

# 监控ROS节点
rosnode list
rosnode info /state_estimation_node

# 监控topic频率
rostopic hz /velodyne_points
rostopic hz /imu/data

# 查看TF变换
rosrun tf tf_monitor
```

## 第六部分：数据记录和分析

### 1. 记录处理结果
```bash
# 记录SLAM输出
rosbag record -O slam_result.bag /odometry /path /tf

# 记录所有数据
rosbag record -a -O complete_result.bag
```

### 2. 运行评估脚本
```bash
cd /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/scripts

# 运行评估
python3 evaluation.py --result_path /path/to/result --gt_path /path/to/ground_truth
```

## 第七部分：故障排除

### 常见编译错误
```bash
# 错误1：找不到PCL
sudo apt install libpcl-dev pcl-tools

# 错误2：找不到Eigen3
sudo apt install libeigen3-dev

# 错误3：Python相关错误
sudo apt install python3-dev python3-numpy

# 错误4：ROS包依赖问题
rosdep install --from-paths src --ignore-src -r -y

# 重新编译
cd /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws
rm -rf build/ devel/
catkin_make
```

### 常见运行错误
```bash
# 错误1：找不到launch文件
rospack find state_estimation

# 错误2：topic不存在
rostopic list
rosbag info your_dataset.bag

# 错误3：权限问题
sudo chmod +x /path/to/scripts/*.py

# 错误4：环境变量问题
source /opt/ros/noetic/setup.bash
source /path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash
```

## 第八部分：性能优化

### 编译优化
```bash
# 使用Release模式编译
catkin_make -DCMAKE_BUILD_TYPE=Release

# 并行编译
catkin_make -j$(nproc)

# 只编译特定包
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"
```

### 运行优化
```bash
# 调整bag播放速率
rosbag play your_dataset.bag -r 0.5  # 0.5倍速

# 设置ROS参数
rosparam set /use_sim_time true

# 监控资源使用
top -p $(pgrep -f state_estimation)
```

## 快速启动脚本

创建一个快速启动脚本：

```bash
cat > ~/start_alpha_lidar.sh << 'EOF'
#!/bin/bash

# αLiDAR快速启动脚本
WORKSPACE_PATH="/path/to/your/alpha_lidar_GPS/software/alpha_lidar_ws"

# 设置环境
source /opt/ros/noetic/setup.bash
source $WORKSPACE_PATH/devel/setup.bash

echo "启动αLiDAR系统..."
echo "请在另一个终端运行: rosbag play your_dataset.bag --pause"

# 启动SLAM节点
roslaunch state_estimation mapping_robosense.launch
EOF

chmod +x ~/start_alpha_lidar.sh
```

使用脚本启动：
```bash
~/start_alpha_lidar.sh
```

这个完整的命令指南涵盖了从环境准备到系统运行的所有步骤。请根据您的实际路径调整相应的路径变量。
