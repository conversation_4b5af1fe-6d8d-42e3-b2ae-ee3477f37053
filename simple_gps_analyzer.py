#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版GPS数据分析工具
不依赖外部库，专门用于分析UM982 RTK数据
"""

import numpy as np
import matplotlib.pyplot as plt
import math
import random

class SimpleGPSAnalyzer:
    def __init__(self):
        self.gps_data = {}
        self.analysis_results = {}
        
    def simulate_gps_data(self, duration=600, frequency=10):
        """生成模拟GPS数据"""
        print("正在生成模拟GPS数据...")
        
        # 时间序列
        num_points = int(duration * frequency)
        timestamps = [i / frequency for i in range(num_points)]
        
        # 模拟回环轨迹参数
        center_lat, center_lon, center_alt = 39.9042, 116.4074, 50.0
        
        latitudes = []
        longitudes = []
        altitudes = []
        rtk_status = []
        
        for i, t in enumerate(timestamps):
            # 8字形轨迹
            lat = center_lat + 0.001 * math.sin(2 * math.pi * t / 300) * math.cos(2 * math.pi * t / 600)
            lon = center_lon + 0.0005 * math.sin(4 * math.pi * t / 300)
            
            # 高度变化 (地形 + 漂移 + 噪声)
            terrain = 2 * math.sin(2 * math.pi * t / 200) + 1 * math.cos(2 * math.pi * t / 150)
            drift = 0.001 * t  # 模拟系统漂移
            noise = random.gauss(0, 0.02)  # RTK噪声
            alt = center_alt + terrain + drift + noise
            
            # RTK状态 (80%固定解, 20%浮点解)
            status = 4 if random.random() < 0.8 else 5
            
            latitudes.append(lat)
            longitudes.append(lon)
            altitudes.append(alt)
            rtk_status.append(status)
        
        self.gps_data = {
            'timestamps': timestamps,
            'latitudes': latitudes,
            'longitudes': longitudes,
            'altitudes': altitudes,
            'status': rtk_status
        }
        
        print(f"生成了 {len(timestamps)} 个GPS数据点")
        return self.gps_data
    
    def analyze_height_consistency(self):
        """分析高度一致性"""
        print("\n=== 高度一致性分析 ===")
        
        altitudes = self.gps_data['altitudes']
        timestamps = self.gps_data['timestamps']
        
        # 基本统计
        min_alt = min(altitudes)
        max_alt = max(altitudes)
        mean_alt = sum(altitudes) / len(altitudes)
        
        # 计算标准差
        variance = sum((alt - mean_alt) ** 2 for alt in altitudes) / len(altitudes)
        std_alt = math.sqrt(variance)
        
        height_stats = {
            'min': min_alt,
            'max': max_alt,
            'mean': mean_alt,
            'std': std_alt,
            'range': max_alt - min_alt,
            'start_end_diff': altitudes[-1] - altitudes[0]
        }
        
        print(f"高度统计:")
        print(f"  最小值: {height_stats['min']:.3f} m")
        print(f"  最大值: {height_stats['max']:.3f} m")
        print(f"  平均值: {height_stats['mean']:.3f} m")
        print(f"  标准差: {height_stats['std']:.3f} m")
        print(f"  高度范围: {height_stats['range']:.3f} m")
        print(f"  首尾高度差: {height_stats['start_end_diff']:.3f} m")
        
        # 高度变化率分析
        height_rates = []
        for i in range(1, len(altitudes)):
            height_diff = altitudes[i] - altitudes[i-1]
            time_diff = timestamps[i] - timestamps[i-1]
            if time_diff > 0:
                height_rates.append(height_diff / time_diff)
        
        if height_rates:
            max_up_rate = max(height_rates)
            max_down_rate = min(height_rates)
            avg_rate = sum(abs(rate) for rate in height_rates) / len(height_rates)
            
            print(f"\n高度变化率分析:")
            print(f"  最大上升率: {max_up_rate:.3f} m/s")
            print(f"  最大下降率: {max_down_rate:.3f} m/s")
            print(f"  平均变化率: {avg_rate:.3f} m/s")
        
        self.analysis_results['height_stats'] = height_stats
        return height_stats
    
    def analyze_loop_closure_potential(self):
        """分析回环检测潜力"""
        print("\n=== 回环检测潜力分析 ===")
        
        # 转换为局部坐标
        positions = self.convert_to_local_coordinates()
        
        # 寻找潜在回环点
        loop_candidates = []
        min_time_gap = 60  # 最小时间间隔60秒
        max_distance = 5.0  # 最大距离5米
        
        for i in range(len(positions)):
            for j in range(i + int(min_time_gap * 10), len(positions)):  # 假设10Hz
                if j >= len(positions):
                    break
                
                # 计算距离
                dx = positions[i][0] - positions[j][0]
                dy = positions[i][1] - positions[j][1]
                distance = math.sqrt(dx*dx + dy*dy)
                
                if distance < max_distance:
                    time_gap = self.gps_data['timestamps'][j] - self.gps_data['timestamps'][i]
                    height_diff = abs(self.gps_data['altitudes'][j] - self.gps_data['altitudes'][i])
                    
                    loop_candidates.append({
                        'start_idx': i,
                        'end_idx': j,
                        'distance': distance,
                        'time_gap': time_gap,
                        'height_diff': height_diff
                    })
        
        print(f"发现 {len(loop_candidates)} 个潜在回环")
        
        if loop_candidates:
            # 分析最佳回环
            best_loops = sorted(loop_candidates, key=lambda x: x['distance'])[:5]
            print(f"\n最佳回环候选:")
            for i, loop in enumerate(best_loops):
                print(f"  回环 {i+1}: 距离={loop['distance']:.2f}m, "
                      f"时间间隔={loop['time_gap']:.1f}s, "
                      f"高度差={loop['height_diff']:.3f}m")
        
        self.analysis_results['loop_candidates'] = loop_candidates
        return loop_candidates
    
    def analyze_gps_quality(self):
        """分析GPS数据质量"""
        print("\n=== GPS数据质量分析 ===")
        
        status_list = self.gps_data['status']
        
        # RTK状态统计
        rtk_fixed_count = sum(1 for s in status_list if s == 4)
        rtk_float_count = sum(1 for s in status_list if s == 5)
        total_count = len(status_list)
        
        rtk_fixed_ratio = rtk_fixed_count / total_count
        rtk_float_ratio = rtk_float_count / total_count
        
        print(f"RTK状态分布:")
        print(f"  固定解比例: {rtk_fixed_ratio:.1%}")
        print(f"  浮点解比例: {rtk_float_ratio:.1%}")
        
        # 数据连续性分析
        timestamps = self.gps_data['timestamps']
        time_gaps = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
        
        max_gap = max(time_gaps) if time_gaps else 0
        avg_gap = sum(time_gaps) / len(time_gaps) if time_gaps else 0
        good_gaps = sum(1 for gap in time_gaps if gap < 0.2)
        continuity = good_gaps / len(time_gaps) * 100 if time_gaps else 0
        
        print(f"\n数据连续性:")
        print(f"  最大时间间隔: {max_gap:.2f} s")
        print(f"  平均时间间隔: {avg_gap:.2f} s")
        print(f"  数据完整性: {continuity:.1f}%")
        
        quality_metrics = {
            'rtk_fixed_ratio': rtk_fixed_ratio,
            'rtk_float_ratio': rtk_float_ratio,
            'max_time_gap': max_gap,
            'avg_time_gap': avg_gap,
            'data_continuity': continuity
        }
        
        self.analysis_results['quality_metrics'] = quality_metrics
        return quality_metrics
    
    def convert_to_local_coordinates(self):
        """转换GPS坐标到局部坐标"""
        latitudes = self.gps_data['latitudes']
        longitudes = self.gps_data['longitudes']
        
        # 使用第一个点作为原点
        lat_center = latitudes[0]
        lon_center = longitudes[0]
        
        # 简化的坐标转换 (适用于小范围)
        R = 6371000  # 地球半径(米)
        lat_center_rad = math.radians(lat_center)
        
        positions = []
        for lat, lon in zip(latitudes, longitudes):
            lat_rad = math.radians(lat)
            lon_rad = math.radians(lon)
            lon_center_rad = math.radians(lon_center)
            
            x = R * (lon_rad - lon_center_rad) * math.cos(lat_center_rad)
            y = R * (lat_rad - math.radians(lat_center))
            
            positions.append([x, y])
        
        return positions
    
    def estimate_slam_improvement(self):
        """估算SLAM改善潜力"""
        print("\n=== SLAM改善潜力评估 ===")
        
        height_stats = self.analysis_results.get('height_stats', {})
        quality_metrics = self.analysis_results.get('quality_metrics', {})
        loop_candidates = self.analysis_results.get('loop_candidates', [])
        
        # 高度校正潜力
        current_height_error = abs(height_stats.get('start_end_diff', 0))
        rtk_precision = 0.02 if quality_metrics.get('rtk_fixed_ratio', 0) > 0.5 else 0.1
        
        expected_improvement = max(0, current_height_error - rtk_precision)
        improvement_ratio = expected_improvement / current_height_error if current_height_error > 0 else 0
        
        print(f"高度精度改善评估:")
        print(f"  当前首尾高度差: {current_height_error:.3f} m")
        print(f"  GPS精度: {rtk_precision:.3f} m")
        print(f"  预期改善: {expected_improvement:.3f} m")
        print(f"  改善比例: {improvement_ratio:.1%}")
        
        # 回环约束潜力
        good_loops = [l for l in loop_candidates if l['distance'] < 2.0 and l['time_gap'] > 120]
        loop_quality = "强" if len(good_loops) > 3 else "中" if len(good_loops) > 1 else "弱"
        
        print(f"\n回环约束潜力:")
        print(f"  高质量回环数量: {len(good_loops)}")
        print(f"  回环约束强度: {loop_quality}")
        
        return {
            'height_improvement': expected_improvement,
            'improvement_ratio': improvement_ratio,
            'loop_quality': len(good_loops)
        }
    
    def generate_simple_visualization(self):
        """生成简单的可视化图表"""
        print("\n正在生成可视化图表...")
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # 1. 轨迹图
            positions = self.convert_to_local_coordinates()
            x_coords = [pos[0] for pos in positions]
            y_coords = [pos[1] for pos in positions]
            
            axes[0, 0].plot(x_coords, y_coords, 'b-', linewidth=1, alpha=0.7)
            axes[0, 0].plot(x_coords[0], y_coords[0], 'go', markersize=8, label='起点')
            axes[0, 0].plot(x_coords[-1], y_coords[-1], 'ro', markersize=8, label='终点')
            axes[0, 0].set_xlabel('东向距离 (m)')
            axes[0, 0].set_ylabel('北向距离 (m)')
            axes[0, 0].set_title('GPS轨迹图')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].axis('equal')
            
            # 2. 高度变化
            timestamps = self.gps_data['timestamps']
            altitudes = self.gps_data['altitudes']
            
            axes[0, 1].plot(timestamps, altitudes, 'b-', linewidth=1)
            axes[0, 1].axhline(y=altitudes[0], color='g', linestyle='--', 
                              label=f'起点: {altitudes[0]:.3f}m')
            axes[0, 1].axhline(y=altitudes[-1], color='r', linestyle='--', 
                              label=f'终点: {altitudes[-1]:.3f}m')
            axes[0, 1].set_xlabel('时间 (s)')
            axes[0, 1].set_ylabel('高度 (m)')
            axes[0, 1].set_title('高度变化曲线')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 高度差分析
            height_diff = [alt - altitudes[0] for alt in altitudes]
            axes[1, 0].plot(timestamps, height_diff, 'r-', linewidth=1)
            axes[1, 0].set_xlabel('时间 (s)')
            axes[1, 0].set_ylabel('相对高度差 (m)')
            axes[1, 0].set_title('相对于起点的高度差')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
            
            # 4. RTK状态分布
            status_list = self.gps_data['status']
            rtk_fixed_count = sum(1 for s in status_list if s == 4)
            rtk_float_count = sum(1 for s in status_list if s == 5)
            
            labels = ['RTK固定解', 'RTK浮点解']
            sizes = [rtk_fixed_count, rtk_float_count]
            colors = ['lightgreen', 'lightcoral']
            
            axes[1, 1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%')
            axes[1, 1].set_title('RTK状态分布')
            
            plt.tight_layout()
            plt.savefig('simple_gps_analysis.png', dpi=300, bbox_inches='tight')
            print("可视化图表已保存: simple_gps_analysis.png")
            
        except Exception as e:
            print(f"生成可视化图表时出错: {e}")
    
    def generate_text_report(self):
        """生成文本报告"""
        print("\n=== 生成分析报告 ===")
        
        report_lines = [
            "# GPS数据分析报告",
            "",
            "## 数据概况",
            f"- 数据点数量: {len(self.gps_data['timestamps'])}",
            f"- 时间跨度: {self.gps_data['timestamps'][-1] - self.gps_data['timestamps'][0]:.1f} 秒",
            f"- 采样频率: {len(self.gps_data['timestamps']) / (self.gps_data['timestamps'][-1] - self.gps_data['timestamps'][0]):.1f} Hz",
            "",
            "## 高度一致性分析"
        ]
        
        height_stats = self.analysis_results.get('height_stats', {})
        report_lines.extend([
            f"- 首尾高度差: {height_stats.get('start_end_diff', 0):.3f} m",
            f"- 高度标准差: {height_stats.get('std', 0):.3f} m",
            f"- 高度范围: {height_stats.get('range', 0):.3f} m",
            ""
        ])
        
        quality_metrics = self.analysis_results.get('quality_metrics', {})
        report_lines.extend([
            "## GPS质量评估",
            f"- RTK固定解比例: {quality_metrics.get('rtk_fixed_ratio', 0):.1%}",
            f"- 数据完整性: {quality_metrics.get('data_continuity', 0):.1f}%",
            ""
        ])
        
        loop_candidates = self.analysis_results.get('loop_candidates', [])
        good_loops = [l for l in loop_candidates if l['distance'] < 2.0]
        report_lines.extend([
            "## 回环检测潜力",
            f"- 潜在回环数量: {len(loop_candidates)}",
            f"- 高质量回环数量: {len(good_loops)}",
            f"- 回环检测可行性: {'高' if len(good_loops) > 3 else '中' if len(good_loops) > 1 else '低'}",
            "",
            "## SLAM改善建议",
            "基于分析结果，建议采用以下GPS集成策略：",
            "1. **高度校正**: 利用RTK高精度进行实时高度校正",
            "2. **回环约束**: 在检测到回环时添加GPS约束",
            "3. **质量监控**: 实时监控GPS质量，动态调整融合权重",
            "4. **渐进校正**: 采用平滑校正避免系统震荡"
        ])
        
        report_content = "\n".join(report_lines)
        
        with open('simple_gps_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print("分析报告已保存: simple_gps_analysis_report.md")
        return report_content

def main():
    analyzer = SimpleGPSAnalyzer()
    
    # 生成模拟数据
    analyzer.simulate_gps_data(duration=600, frequency=10)
    
    # 执行分析
    analyzer.analyze_height_consistency()
    analyzer.analyze_gps_quality()
    analyzer.analyze_loop_closure_potential()
    analyzer.estimate_slam_improvement()
    
    # 生成输出
    analyzer.generate_simple_visualization()
    analyzer.generate_text_report()
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- simple_gps_analysis.png (可视化图表)")
    print("- simple_gps_analysis_report.md (分析报告)")

if __name__ == "__main__":
    main()
