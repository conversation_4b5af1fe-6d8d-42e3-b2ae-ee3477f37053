#!/bin/bash

# 带安全检查的重新编译脚本

echo "=========================================="
echo "🔧 带安全检查的重新编译"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 完全清理"
echo "==============="
rm -rf build devel install .catkin_workspace
echo "✅ 清理完成"

echo ""
echo "步骤2: 检查和安装依赖"
echo "===================="

# 更新包列表
sudo apt update

# 安装调试工具
echo "安装调试工具..."
sudo apt install -y \
    gdb \
    valgrind \
    strace \
    ltrace

# 安装完整的ROS依赖
echo "安装ROS依赖..."
sudo apt install -y \
    ros-noetic-desktop-full \
    ros-noetic-pcl-ros \
    ros-noetic-pcl-conversions \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    ros-noetic-message-filters \
    ros-noetic-sensor-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-std-msgs

# 安装PCL和相关库
echo "安装PCL依赖..."
sudo apt install -y \
    libpcl-dev \
    libpcl1 \
    libpcl-common1.10 \
    libpcl-io1.10 \
    libpcl-filters1.10 \
    libpcl-registration1.10 \
    pcl-tools

# 安装其他必要库
echo "安装其他依赖..."
sudo apt install -y \
    libeigen3-dev \
    libboost-all-dev \
    libgoogle-glog-dev \
    libgflags-dev \
    libyaml-cpp-dev \
    build-essential \
    cmake

echo "✅ 依赖安装完成"

echo ""
echo "步骤3: 设置编译环境"
echo "=================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash

# 设置编译标志
export CMAKE_BUILD_TYPE=Debug
export CXXFLAGS="-g -O0 -fno-omit-frame-pointer -fsanitize=address"
export LDFLAGS="-fsanitize=address"

echo "编译环境设置:"
echo "  CMAKE_BUILD_TYPE: $CMAKE_BUILD_TYPE"
echo "  CXXFLAGS: $CXXFLAGS"
echo "  LDFLAGS: $LDFLAGS"

echo ""
echo "步骤4: 修复CMakeLists.txt"
echo "======================="

# 备份原始文件
cp src/state_estimation/CMakeLists.txt src/state_estimation/CMakeLists.txt.backup

# 检查并修复CMakeLists.txt
echo "检查CMakeLists.txt..."

# 确保包含必要的编译选项
if ! grep -q "CMAKE_CXX_STANDARD" src/state_estimation/CMakeLists.txt; then
    echo "添加C++标准设置..."
    sed -i '1a set(CMAKE_CXX_STANDARD 14)\nset(CMAKE_CXX_STANDARD_REQUIRED ON)' src/state_estimation/CMakeLists.txt
fi

# 添加调试和安全编译选项
if ! grep -q "CMAKE_CXX_FLAGS_DEBUG" src/state_estimation/CMakeLists.txt; then
    echo "添加调试编译选项..."
    cat >> src/state_estimation/CMakeLists.txt << 'EOF'

# 调试和安全编译选项
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -Wall -Wextra -fno-omit-frame-pointer")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2 -DNDEBUG")

# 启用AddressSanitizer（可选）
if(ENABLE_ASAN)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fno-omit-frame-pointer")
    set(CMAKE_LINKER_FLAGS "${CMAKE_LINKER_FLAGS} -fsanitize=address")
endif()

EOF
fi

echo "✅ CMakeLists.txt修复完成"

echo ""
echo "步骤5: 开始编译"
echo "==============="

echo "使用调试模式编译..."

# 方法1: 标准调试编译
catkin_make -DCMAKE_BUILD_TYPE=Debug -j1

COMPILE_RESULT=$?

if [ $COMPILE_RESULT -eq 0 ]; then
    echo ""
    echo "✅ 调试版本编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    echo ""
    echo "步骤6: 验证编译结果"
    echo "=================="
    
    # 检查可执行文件
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点编译成功"
        
        # 检查文件信息
        echo "可执行文件信息:"
        file devel/lib/state_estimation/state_estimation_node
        
        # 检查调试符号
        if objdump -h devel/lib/state_estimation/state_estimation_node | grep -q debug; then
            echo "✅ 包含调试符号"
        else
            echo "⚠️  缺少调试符号"
        fi
        
        # 检查依赖
        echo ""
        echo "动态库依赖检查:"
        ldd devel/lib/state_estimation/state_estimation_node | grep -E "(pcl|eigen|boost)" | head -10
        
    else
        echo "❌ SLAM核心节点编译失败"
        exit 1
    fi
    
    echo ""
    echo "步骤7: 创建安全启动配置"
    echo "======================"
    
    # 创建安全启动的launch文件
    cat > src/state_estimation/launch/slam_debug_safe.launch << 'EOF'
<?xml version="1.0"?>
<launch>
    <!-- SLAM调试安全启动配置 -->
    
    <node name="state_estimation_node" pkg="state_estimation" type="state_estimation_node" output="screen" launch-prefix="gdb -ex run --args">
        <!-- 基本topic配置 -->
        <param name="lidar_topic" value="/velodyne_points" />
        <param name="imu_topic" value="/imu/data" />
        <param name="gps_topic" value="/rtk/gnss" />
        
        <!-- 极度安全的处理参数 -->
        <param name="voxel_size" value="2.0" />
        <param name="downsample_ratio" value="0.1" />
        <param name="max_iterations" value="5" />
        <param name="transformation_epsilon" value="1e-2" />
        <param name="euclidean_fitness_epsilon" value="1e-2" />
        
        <!-- 点云安全参数 -->
        <param name="enable_point_cloud_validation" value="true" />
        <param name="min_points_threshold" value="2000" />
        <param name="max_points_threshold" value="50000" />
        <param name="skip_empty_scans" value="true" />
        <param name="wait_for_pointcloud" value="true" />
        <param name="pointcloud_timeout" value="30.0" />
        
        <!-- 禁用所有复杂功能 -->
        <param name="enable_gps" value="false" />
        <param name="enable_loop_closure" value="false" />
        <param name="enable_icp_loop_closure" value="false" />
        <param name="enable_intensity_processing" value="false" />
        <param name="enable_feature_extraction" value="false" />
        <param name="enable_plane_constraint" value="false" />
        
        <!-- 内存安全参数 -->
        <param name="max_memory_usage_mb" value="2048" />
        <param name="enable_memory_monitoring" value="true" />
        
        <!-- 错误处理参数 -->
        <param name="enable_error_recovery" value="true" />
        <param name="max_consecutive_errors" value="3" />
        
        <!-- 调试参数 -->
        <param name="enable_debug_output" value="true" />
        <param name="log_pointcloud_stats" value="true" />
        <param name="log_memory_usage" value="true" />
        <param name="log_processing_time" value="true" />
    </node>
    
</launch>
EOF
    
    echo "✅ 调试安全启动配置已创建"
    
    echo ""
    echo "🎉 带安全检查的重新编译完成!"
    echo ""
    echo "下一步使用方法:"
    echo "1. 深度诊断: ./deep_fix_slam_crash.sh <bag文件>"
    echo "2. 内存安全启动: ./start_slam_memory_safe.sh <bag文件>"
    echo "3. GDB调试: ./debug_slam_crash.sh <bag文件>"
    echo "4. 安全launch: roslaunch state_estimation slam_debug_safe.launch"
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "尝试不带AddressSanitizer编译..."
    
    # 方法2: 不带AddressSanitizer的编译
    unset CXXFLAGS LDFLAGS
    catkin_make -DCMAKE_BUILD_TYPE=Debug -j1
    
    if [ $? -eq 0 ]; then
        echo "✅ 简化调试版本编译成功!"
        source devel/setup.bash
    else
        echo "❌ 编译仍然失败!"
        echo ""
        echo "可能的解决方案:"
        echo "1. 检查依赖: sudo apt install ros-noetic-desktop-full libpcl-dev"
        echo "2. 检查磁盘空间: df -h"
        echo "3. 检查内存: free -h"
        echo "4. 查看详细错误信息"
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "重新编译完成"
echo "=========================================="
