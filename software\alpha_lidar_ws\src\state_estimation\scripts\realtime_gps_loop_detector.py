#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时GPS位置回环检测器
基于当前GPS位置与起点GPS位置的直线距离进行强制首尾回环检测
"""

import rospy
import numpy as np
import math
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import String, Bool
from geometry_msgs.msg import Point
import json
import time
from collections import deque

class RealtimeGPSLoopDetector:
    def __init__(self):
        rospy.init_node('realtime_gps_loop_detector', anonymous=True)
        
        # 参数配置
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        self.force_loop_topic = rospy.get_param('~force_loop_topic', '/force_loop_closure')
        self.status_topic = rospy.get_param('~status_topic', '/realtime_gps_loop_status')
        
        # 关键参数
        self.distance_threshold = rospy.get_param('~distance_threshold', 50.0)  # 触发距离阈值(米)
        self.min_trajectory_points = rospy.get_param('~min_trajectory_points', 100)  # 最小轨迹点数
        self.quality_threshold = rospy.get_param('~quality_threshold', -1)  # GPS质量阈值(-1接受所有)
        self.check_interval = rospy.get_param('~check_interval', 2.0)  # 检查间隔(秒)
        self.cooldown_time = rospy.get_param('~cooldown_time', 30.0)  # 冷却时间(秒)
        
        # 状态变量
        self.start_gps_position = None
        self.start_gps_set = False
        self.current_gps_position = None
        self.trajectory_points = []
        self.last_force_time = 0
        self.total_forced_loops = 0
        
        # GPS质量统计
        self.gps_quality_history = deque(maxlen=50)
        self.valid_gps_count = 0
        self.total_gps_count = 0
        
        # 订阅器和发布器
        self.gps_sub = rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)
        self.force_loop_pub = rospy.Publisher(self.force_loop_topic, String, queue_size=1)
        self.status_pub = rospy.Publisher(self.status_topic, String, queue_size=1)
        
        # 定时器
        self.check_timer = rospy.Timer(rospy.Duration(self.check_interval), self.check_loop_condition)
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("Realtime GPS Loop Detector Started")
        rospy.loginfo("GPS Topic: %s", self.gps_topic)
        rospy.loginfo("Distance Threshold: %.1f meters", self.distance_threshold)
        rospy.loginfo("Quality Threshold: %d", self.quality_threshold)
    
    def gps_callback(self, msg):
        """处理GPS数据"""
        try:
            self.total_gps_count += 1
            
            # 检查GPS数据有效性
            if not self.is_valid_gps(msg):
                return
            
            self.valid_gps_count += 1
            current_pos = {
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status,
                'timestamp': msg.header.stamp.to_sec()
            }
            
            # 更新当前GPS位置
            self.current_gps_position = current_pos
            self.trajectory_points.append(current_pos)
            
            # 记录GPS质量
            self.gps_quality_history.append(msg.status.status)
            
            # 设置起点GPS位置
            if not self.start_gps_set:
                self.set_start_position(current_pos)
            
            # 限制轨迹点数量
            if len(self.trajectory_points) > 2000:
                self.trajectory_points.pop(0)
                
        except Exception as e:
            rospy.logerr("Error in GPS callback: %s", str(e))
    
    def is_valid_gps(self, msg):
        """检查GPS数据是否有效"""
        # 检查坐标范围
        if (abs(msg.latitude) < 0.001 and abs(msg.longitude) < 0.001):
            return False
        
        if (abs(msg.latitude) > 90 or abs(msg.longitude) > 180):
            return False
        
        # 检查GPS质量
        if self.quality_threshold >= 0 and msg.status.status < self.quality_threshold:
            return False
        
        return True
    
    def set_start_position(self, gps_pos):
        """设置起点GPS位置"""
        self.start_gps_position = gps_pos.copy()
        self.start_gps_set = True
        
        rospy.loginfo("起点GPS位置已设置:")
        rospy.loginfo("  纬度: %.8f", gps_pos['latitude'])
        rospy.loginfo("  经度: %.8f", gps_pos['longitude'])
        rospy.loginfo("  高度: %.2f", gps_pos['altitude'])
        rospy.loginfo("  GPS状态: %d", gps_pos['status'])
    
    def calculate_distance(self, pos1, pos2):
        """计算两个GPS位置之间的直线距离(米)"""
        if not pos1 or not pos2:
            return float('inf')
        
        # 使用Haversine公式计算球面距离
        lat1, lon1 = math.radians(pos1['latitude']), math.radians(pos1['longitude'])
        lat2, lon2 = math.radians(pos2['latitude']), math.radians(pos2['longitude'])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径(米)
        earth_radius = 6371000
        distance = earth_radius * c
        
        # 考虑高度差
        if 'altitude' in pos1 and 'altitude' in pos2:
            altitude_diff = pos2['altitude'] - pos1['altitude']
            distance = math.sqrt(distance**2 + altitude_diff**2)
        
        return distance
    
    def check_loop_condition(self, event):
        """检查是否满足强制回环条件"""
        try:
            # 检查基本条件
            if not self.start_gps_set or not self.current_gps_position:
                return
            
            if len(self.trajectory_points) < self.min_trajectory_points:
                return
            
            # 检查冷却时间
            current_time = time.time()
            if current_time - self.last_force_time < self.cooldown_time:
                return
            
            # 计算当前位置与起点的距离
            distance = self.calculate_distance(self.start_gps_position, self.current_gps_position)
            
            # 检查是否满足触发条件
            if distance <= self.distance_threshold:
                self.trigger_force_loop_closure(distance)
            
        except Exception as e:
            rospy.logerr("Error in loop condition check: %s", str(e))
    
    def trigger_force_loop_closure(self, distance):
        """触发强制首尾回环检测"""
        try:
            current_time = time.time()
            self.last_force_time = current_time
            self.total_forced_loops += 1
            
            # 构建强制回环消息
            force_msg = {
                'type': 'realtime_gps_start_end',
                'trigger_reason': 'gps_distance_threshold',
                'distance_to_start': distance,
                'distance_threshold': self.distance_threshold,
                'start_position': self.start_gps_position,
                'current_position': self.current_gps_position,
                'trajectory_length': len(self.trajectory_points),
                'timestamp': current_time,
                'force_id': self.total_forced_loops
            }
            
            # 发布强制回环消息
            msg = String()
            msg.data = json.dumps(force_msg, indent=2)
            self.force_loop_pub.publish(msg)
            
            rospy.logwarn("🎯 触发实时GPS强制首尾回环!")
            rospy.logwarn("距离起点: %.2f米 (阈值: %.1f米)", distance, self.distance_threshold)
            rospy.logwarn("轨迹点数: %d", len(self.trajectory_points))
            rospy.logwarn("起点GPS: (%.6f, %.6f, %.1fm, status=%d)", 
                         self.start_gps_position['latitude'],
                         self.start_gps_position['longitude'], 
                         self.start_gps_position['altitude'],
                         self.start_gps_position['status'])
            rospy.logwarn("当前GPS: (%.6f, %.6f, %.1fm, status=%d)", 
                         self.current_gps_position['latitude'],
                         self.current_gps_position['longitude'], 
                         self.current_gps_position['altitude'],
                         self.current_gps_position['status'])
            
        except Exception as e:
            rospy.logerr("Error triggering force loop closure: %s", str(e))
    
    def publish_status(self, event):
        """发布状态信息"""
        try:
            if not self.start_gps_set:
                return
            
            # 计算当前距离
            current_distance = float('inf')
            if self.current_gps_position:
                current_distance = self.calculate_distance(self.start_gps_position, self.current_gps_position)
            
            # 计算GPS质量统计
            avg_gps_quality = 0
            if self.gps_quality_history:
                avg_gps_quality = sum(self.gps_quality_history) / len(self.gps_quality_history)
            
            # 构建状态报告
            status = {
                'detector_type': 'realtime_gps_loop_detector',
                'start_position_set': self.start_gps_set,
                'trajectory_points': len(self.trajectory_points),
                'current_distance_to_start': current_distance,
                'distance_threshold': self.distance_threshold,
                'total_forced_loops': self.total_forced_loops,
                'gps_statistics': {
                    'total_gps_messages': self.total_gps_count,
                    'valid_gps_messages': self.valid_gps_count,
                    'gps_validity_rate': self.valid_gps_count / max(self.total_gps_count, 1),
                    'average_gps_quality': avg_gps_quality
                },
                'start_gps_info': self.start_gps_position,
                'current_gps_info': self.current_gps_position,
                'next_check_available': time.time() - self.last_force_time >= self.cooldown_time,
                'time_until_next_check': max(0, self.cooldown_time - (time.time() - self.last_force_time))
            }
            
            # 发布状态
            status_msg = String()
            status_msg.data = json.dumps(status, indent=2, default=str)
            self.status_pub.publish(status_msg)
            
            # 控制台输出简化状态
            rospy.loginfo("=== 实时GPS回环检测状态 ===")
            rospy.loginfo("轨迹点数: %d", len(self.trajectory_points))
            rospy.loginfo("距离起点: %.2f米 (阈值: %.1f米)", current_distance, self.distance_threshold)
            rospy.loginfo("已触发强制回环: %d次", self.total_forced_loops)
            rospy.loginfo("GPS有效率: %.1f%% (%d/%d)", 
                         status['gps_statistics']['gps_validity_rate'] * 100,
                         self.valid_gps_count, self.total_gps_count)
            
            if current_distance <= self.distance_threshold:
                rospy.loginfo("🎯 满足触发条件! 等待冷却时间: %.1f秒", 
                             status['time_until_next_check'])
            
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        detector = RealtimeGPSLoopDetector()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Realtime GPS Loop Detector: %s", str(e))

if __name__ == '__main__':
    main()
