# αLiDAR Ubuntu快速启动命令

## 🚀 一键部署 (推荐)

### 第一步：下载并运行自动化脚本
```bash
# 设置执行权限
chmod +x alpha_lidar_build_and_run.sh

# 运行自动化脚本 (会自动安装依赖、编译项目、创建启动脚本)
./alpha_lidar_build_and_run.sh
```

## 📋 手动执行步骤

### 第一步：环境准备
```bash
# 1. 确认系统版本
lsb_release -a  # 应显示 Ubuntu 20.04

# 2. 安装ROS Noetic (如果未安装)
sudo apt update
sudo apt install ros-noetic-desktop-full

# 3. 安装编译依赖
sudo apt install -y \
    build-essential cmake git \
    python3-catkin-tools python3-rosdep \
    libpcl-dev libeigen3-dev libopencv-dev \
    libgoogle-glog-dev libgflags-dev

# 4. 设置ROS环境
source /opt/ros/noetic/setup.bash
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
```

### 第二步：编译项目
```bash
# 1. 进入工作空间 (根据您的实际路径调整)
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws

# 2. 安装ROS包依赖
rosdep install --from-paths src --ignore-src -r -y

# 3. 编译项目
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"

# 4. 设置工作空间环境
source devel/setup.bash
echo "source ~/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash" >> ~/.bashrc
```

## 🎯 系统运行 (4个终端窗口)

### 终端1：启动roscore
```bash
roscore
```

### 终端2：启动αLiDAR SLAM节点
```bash
# 设置环境
source /opt/ros/noetic/setup.bash
source ~/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash

# 启动SLAM系统
roslaunch state_estimation mapping_robosense.launch

# 预期输出：
# [INFO] [timestamp]: state_estimation_node started
# [INFO] [timestamp]: Waiting for point cloud data...
```

### 终端3：播放数据集
```bash
# 进入数据集目录
cd ~/datasets  # 或您的数据集路径

# 检查可用的bag文件
ls -la *.bag

# 播放数据集 (暂停模式)
rosbag play your_dataset.bag --pause

# 操作说明：
# - 按空格键开始播放
# - 按空格键暂停/继续
# - 按's'键单步播放
# - Ctrl+C停止播放
```

### 终端4：系统监控 (可选)
```bash
# 查看所有topic
rostopic list

# 监控关键数据频率
rostopic hz /velodyne_points  # 点云数据
rostopic hz /imu/data         # IMU数据
rostopic hz /odometry         # 里程计输出

# 查看节点状态
rosnode list
rosnode info /state_estimation_node
```

## 📊 可视化 (可选)

### 启动RViz
```bash
# 新开终端
source /opt/ros/noetic/setup.bash
source ~/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash

# 启动RViz
rviz

# 或使用预配置文件
rviz -d ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config/rviz_cfg/alpha_lidar.rviz
```

### RViz配置
在RViz中添加以下显示项：
- **PointCloud2**: topic `/velodyne_points` (原始点云)
- **PointCloud2**: topic `/cloud_registered` (配准后点云)
- **Path**: topic `/path` (轨迹路径)
- **Odometry**: topic `/odometry` (里程计)
- **TF**: 显示坐标变换

## 🔧 常用命令

### 数据集相关
```bash
# 查看bag文件信息
rosbag info your_dataset.bag

# 播放指定时间段
rosbag play your_dataset.bag -s 10 -u 60  # 从10秒播放到60秒

# 调整播放速度
rosbag play your_dataset.bag -r 0.5  # 0.5倍速播放
```

### 系统监控
```bash
# 监控系统资源
htop

# 查看ROS日志
tail -f ~/.ros/log/latest/rosout.log

# 监控TF变换
rosrun tf tf_monitor

# 查看参数
rosparam list
rosparam get /use_sim_time
```

### 数据记录
```bash
# 记录SLAM结果
rosbag record -O slam_result.bag /odometry /path /tf

# 记录所有数据
rosbag record -a -O complete_result.bag
```

## 🛠️ 故障排除

### 编译错误
```bash
# 清理并重新编译
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws
rm -rf build/ devel/
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"

# 检查依赖
rosdep check --from-paths src --ignore-src
```

### 运行错误
```bash
# 检查ROS环境
echo $ROS_DISTRO  # 应显示 noetic
rospack find state_estimation

# 检查topic连接
rostopic list
rostopic info /velodyne_points

# 重启roscore
killall roscore rosmaster
roscore
```

### 性能问题
```bash
# 降低播放速度
rosbag play your_dataset.bag -r 0.3

# 检查系统资源
free -h
df -h
```

## 📁 重要文件路径

### 配置文件
```bash
# SLAM参数配置
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config/rs16_rotation_v2.yaml

# Launch文件
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/launch/mapping_robosense.launch

# RViz配置
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config/rviz_cfg/
```

### 源代码
```bash
# 主要SLAM代码
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/src/voxelMapping.cpp

# 预处理代码
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/src/preprocess.cpp

# IMU处理
~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/src/IMU_Processing.hpp
```

### 日志和结果
```bash
# ROS日志
~/.ros/log/

# 编译日志
~/alpha_lidar_GPS/software/alpha_lidar_ws/build/

# 结果输出 (如果配置了)
/tmp/alpha_lidar/
```

## ✅ 成功运行标志

### 系统启动成功
- roscore正常运行
- state_estimation节点启动无错误
- 显示"Waiting for point cloud data..."

### 数据处理正常
- 点云数据频率稳定 (通常10Hz)
- IMU数据频率正常 (通常100-200Hz)
- 里程计有输出 (/odometry topic)

### 可视化正常
- RViz中能看到点云数据
- 轨迹路径在更新
- TF变换正常

## 🎯 下一步

### 参数调优
根据您的具体数据集和环境，可能需要调整：
- 点云下采样参数
- IMU噪声参数
- 体素地图分辨率
- 迭代次数等

### 结果评估
使用评估脚本分析SLAM精度：
```bash
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/scripts
python3 evaluation.py --result_path /path/to/result
```

这个快速启动指南提供了最直接的执行路径。如果遇到问题，请参考详细的故障排除部分或查看完整的技术文档。
