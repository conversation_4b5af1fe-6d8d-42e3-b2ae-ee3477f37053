#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix bagpy import issue for PyInstaller
Create a patched version that handles missing version file
"""

import os
import shutil

def create_bagpy_hook():
    """创建PyInstaller hook文件来修复bagpy问题"""
    
    # 创建hooks目录
    hooks_dir = "hooks"
    if not os.path.exists(hooks_dir):
        os.makedirs(hooks_dir)
    
    # 创建bagpy hook文件
    hook_content = '''# PyInstaller hook for bagpy
from PyInstaller.utils.hooks import collect_all, collect_data_files

# 收集bagpy的所有数据文件
datas, binaries, hiddenimports = collect_all('bagpy')

# 特别添加version文件
try:
    import bagpy
    bagpy_path = os.path.dirname(bagpy.__file__)
    version_file = os.path.join(bagpy_path, 'version')
    if os.path.exists(version_file):
        datas.append((version_file, 'bagpy'))
except:
    pass

# 添加其他可能需要的文件
additional_datas = collect_data_files('bagpy', include_py_files=True)
datas.extend(additional_datas)
'''
    
    with open(os.path.join(hooks_dir, "hook-bagpy.py"), "w") as f:
        f.write(hook_content)
    
    print("✅ 创建bagpy hook文件")
    return True

def create_fixed_spec_file():
    """创建修复的spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

block_cipher = None

# 添加hooks路径
hookspath = ['hooks']

# 隐藏导入
hiddenimports = [
    'bagpy',
    'bagpy.bagreader',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    'pandas',
    'numpy',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'threading',
    'collections',
    'math',
    'time',
    'locale',
]

# 数据文件
datas = []

# 尝试添加bagpy版本文件
try:
    import bagpy
    bagpy_path = os.path.dirname(bagpy.__file__)
    version_file = os.path.join(bagpy_path, 'version')
    if os.path.exists(version_file):
        datas.append((version_file, 'bagpy'))
        print(f"Added bagpy version file: {version_file}")
    else:
        # 创建一个临时版本文件
        temp_version = 'temp_version'
        with open(temp_version, 'w') as f:
            f.write('0.5.0')
        datas.append((temp_version, 'bagpy'))
        print("Created temporary version file")
except Exception as e:
    print(f"Warning: Could not handle bagpy version file: {e}")

# 二进制文件
binaries = []

a = Analysis(
    ['gps_gui_analyzer_fixed.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=hookspath,
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GPS_3D_Analyzer_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("GPS_3D_Analyzer_Fixed.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 创建修复的spec文件")
    return True

def create_temp_version_file():
    """创建临时版本文件"""
    try:
        import bagpy
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        if not os.path.exists(version_file):
            # 创建版本文件
            with open(version_file, 'w') as f:
                f.write('0.5.0')
            print(f"✅ 创建bagpy版本文件: {version_file}")
        else:
            print(f"✅ bagpy版本文件已存在: {version_file}")
            
        return True
    except Exception as e:
        print(f"⚠️  无法创建bagpy版本文件: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 修复bagpy打包问题...")
    print("=" * 50)
    
    # 创建hook文件
    create_bagpy_hook()
    
    # 创建临时版本文件
    create_temp_version_file()
    
    # 创建修复的spec文件
    create_fixed_spec_file()
    
    print("\n✅ 修复文件创建完成")
    print("=" * 50)
    print("📋 下一步操作:")
    print("1. 运行: pyinstaller GPS_3D_Analyzer_Fixed.spec")
    print("2. 或运行: python rebuild_exe_fixed.py")
    
if __name__ == "__main__":
    main()
