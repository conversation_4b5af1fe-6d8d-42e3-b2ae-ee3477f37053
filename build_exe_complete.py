#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Build Script for GPS 3D Trajectory Analyzer
Creates standalone executable for Windows 10 64-bit with all dependencies
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class GPSAnalyzerBuilder:
    def __init__(self):
        self.project_name = "GPS_3D_Analyzer"
        self.main_script = "gps_gui_analyzer_fixed.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.release_dir = f"{self.project_name}_Release"
        
    def print_header(self, title):
        """打印标题"""
        print("\n" + "=" * 60)
        print(f"🚀 {title}")
        print("=" * 60)
        
    def print_step(self, step):
        """打印步骤"""
        print(f"\n📋 {step}...")
        
    def check_python_version(self):
        """检查Python版本"""
        self.print_step("检查Python版本")
        
        if sys.version_info < (3, 7):
            print("❌ 需要Python 3.7或更高版本")
            return False
            
        print(f"✅ Python版本: {sys.version}")
        print(f"✅ 平台: {sys.platform}")
        return True
        
    def install_dependencies(self):
        """安装依赖"""
        self.print_step("安装依赖包")
        
        dependencies = [
            "bagpy>=0.5.0",
            "numpy>=1.21.0", 
            "pandas>=1.3.0",
            "matplotlib>=3.5.0",
            "pyinstaller>=5.0.0",
            "pillow>=8.0.0",  # 图像处理支持
        ]
        
        try:
            for dep in dependencies:
                print(f"  安装 {dep}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", dep
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
            print("✅ 所有依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
            
    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        self.print_step("创建PyInstaller配置文件")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

block_cipher = None

# 添加隐藏导入
hiddenimports = [
    'bagpy',
    'bagpy.bagreader',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    'pandas',
    'numpy',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'threading',
    'collections',
    'math',
    'time',
    'locale',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
]

# 数据文件
datas = []

# 二进制文件
binaries = []

a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.project_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='icon.ico' if Path('icon.ico').exists() else None,
)
'''
        
        with open(f"{self.project_name}.spec", "w", encoding="utf-8") as f:
            f.write(spec_content)
            
        print("✅ PyInstaller配置文件创建完成")
        return True
        
    def create_version_info(self):
        """创建版本信息文件"""
        self.print_step("创建版本信息")
        
        version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'GPS Analyzer Team'),
        StringStruct(u'FileDescription', u'GPS 3D Trajectory Analyzer'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'GPS_3D_Analyzer'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'GPS_3D_Analyzer.exe'),
        StringStruct(u'ProductName', u'GPS 3D Trajectory Analyzer'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)
            
        print("✅ 版本信息文件创建完成")
        return True
        
    def create_icon(self):
        """创建应用图标"""
        self.print_step("创建应用图标")
        
        try:
            from PIL import Image, ImageDraw
            
            # 创建简单的图标
            size = 256
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 绘制GPS图标样式
            # 外圆
            draw.ellipse([20, 20, size-20, size-20], fill=(0, 200, 81, 255), outline=(255, 255, 255, 255), width=8)
            
            # 内部十字
            center = size // 2
            cross_size = 60
            draw.rectangle([center-5, center-cross_size, center+5, center+cross_size], fill=(255, 255, 255, 255))
            draw.rectangle([center-cross_size, center-5, center+cross_size, center+5], fill=(255, 255, 255, 255))
            
            # 保存为ICO格式
            img.save("icon.ico", format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            print("✅ 应用图标创建完成")
            return True
            
        except ImportError:
            print("⚠️  PIL未安装，跳过图标创建")
            return True
        except Exception as e:
            print(f"⚠️  图标创建失败: {e}")
            return True
            
    def clean_build_dirs(self):
        """清理构建目录"""
        self.print_step("清理构建目录")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, self.release_dir]
        
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"  清理 {dir_name}/")
                
        print("✅ 构建目录清理完成")
        
    def build_executable(self):
        """构建可执行文件"""
        self.print_step("构建可执行文件")
        
        try:
            # 使用spec文件构建
            cmd = [
                "pyinstaller",
                "--clean",
                "--noconfirm", 
                f"{self.project_name}.spec"
            ]
            
            print("  执行PyInstaller...")
            print(f"  命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("✅ 可执行文件构建完成")
                return True
            else:
                print(f"❌ 构建失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("❌ PyInstaller未找到，正在安装...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            return self.build_executable()
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
            
    def create_release_package(self):
        """创建发布包"""
        self.print_step("创建发布包")
        
        # 检查exe文件是否存在
        exe_path = os.path.join(self.dist_dir, f"{self.project_name}.exe")
        if not os.path.exists(exe_path):
            print(f"❌ 可执行文件不存在: {exe_path}")
            return False
            
        # 创建发布目录
        os.makedirs(self.release_dir, exist_ok=True)
        
        # 复制可执行文件
        shutil.copy2(exe_path, self.release_dir)
        print(f"  复制 {self.project_name}.exe")
        
        # 创建README文件
        readme_content = f'''# GPS 3D 轨迹分析器

## 📋 软件介绍
GPS 3D轨迹分析器是一个专业的GPS轨迹分析工具，具有以下特点：

### ✨ 主要功能
- 📁 从ROS bag文件提取GPS数据
- 🎨 3D轨迹可视化（深灰色主题 + 深绿色文字）
- 📊 详细的GPS质量分析（中文显示）
- 💾 导出轨迹为文本文件（帧编号 X Y Z格式）
- 🎮 交互式3D控制（鼠标拖拽旋转、滚轮缩放）

### 🎯 GPS质量颜色编码
- 🟢 青绿色实线: RTK固定解（最高精度）
- 🔵 蓝色虚线: SBAS定位
- 🟠 橙色虚线: GBAS定位
- 🔴 红色虚线: 无定位解
- 🟣 紫色虚线: 其他状态

### 🚀 使用方法
1. 双击 `{self.project_name}.exe` 启动程序
2. 点击"浏览"选择ROS bag文件
3. 设置GPS话题（默认: /rtk/gnss）
4. 选择输出文件位置
5. 点击"开始分析"
6. 查看3D轨迹和分析结果

### 🎮 3D控制说明
- **鼠标拖拽**: 旋转3D视角
- **鼠标滚轮**: 缩放视图
- **俯视按钮**: 切换到俯视角度
- **侧视按钮**: 切换到侧视角度
- **正视按钮**: 切换到正视角度
- **复位按钮**: 恢复默认视角
- **放大/缩小**: 手动缩放控制
- **适应按钮**: 自动适应窗口大小

### 📄 输出文件格式
```
# GPS轨迹数据
# 格式: 帧编号 X Y Z
# 坐标系: 本地ENU (东-北-上)
# 原点: 纬度=30.73767602, 经度=103.97089927, 高度=522.005
# 单位: 米
#
1 0.000000 0.000000 0.000000
2 0.123456 0.234567 0.012345
3 0.246912 0.469134 0.024690
...
```

### 💻 系统要求
- Windows 10 64位
- 最小4GB内存
- 支持OpenGL的显卡
- 100MB可用磁盘空间

### 🎨 界面特色
- 深灰色专业主题
- 深绿色文字显示
- 深色选择背景
- 无中文乱码
- 流畅的3D交互

### 📞 技术支持
如遇问题，请检查：
1. bag文件格式是否正确
2. GPS话题名称是否匹配
3. 系统是否满足最低要求
4. 终端输出中的错误信息

### 📝 版本信息
- 版本: 1.0.0
- 构建日期: {time.strftime("%Y-%m-%d")}
- 平台: Windows 10 64位
- Python版本: {sys.version.split()[0]}

---
© 2024 GPS Analyzer Team. All rights reserved.
'''
        
        with open(os.path.join(self.release_dir, "README.txt"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        print("  创建 README.txt")
        
        # 创建示例数据说明
        example_content = '''# 示例使用说明

## 📁 支持的文件格式
- ROS bag文件 (*.bag)
- 包含GPS消息的话题

## 🛰️ 支持的GPS话题格式
程序支持标准的ROS GPS消息格式，包含以下字段：
- latitude: 纬度
- longitude: 经度  
- altitude: 高度
- status.status: GPS状态
- status.service: GPS服务类型

## 📊 GPS状态说明
- -1: NO_FIX (无定位)
- 0: RTK_FIXED (RTK固定解)
- 1: SBAS_FIX (SBAS定位)
- 2: GBAS_FIX (GBAS定位)
- 3: OTHER (其他)

## 🎯 使用技巧
1. 确保bag文件包含GPS数据
2. 检查GPS话题名称是否正确
3. 选择合适的输出文件位置
4. 观察终端输出了解处理进度
5. 使用3D控制按钮获得最佳视角
'''
        
        with open(os.path.join(self.release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
            f.write(example_content)
        print("  创建 使用说明.txt")
        
        # 获取文件大小
        exe_size = os.path.getsize(os.path.join(self.release_dir, f"{self.project_name}.exe"))
        exe_size_mb = exe_size / (1024 * 1024)
        
        print(f"✅ 发布包创建完成")
        print(f"  📁 发布目录: {self.release_dir}/")
        print(f"  📦 可执行文件大小: {exe_size_mb:.1f} MB")
        
        return True
        
    def run_build(self):
        """运行完整构建过程"""
        self.print_header("GPS 3D 轨迹分析器 - 完整打包构建")
        
        # 检查主脚本是否存在
        if not os.path.exists(self.main_script):
            print(f"❌ 主脚本文件不存在: {self.main_script}")
            return False
            
        # 构建步骤
        steps = [
            ("检查Python环境", self.check_python_version),
            ("安装依赖包", self.install_dependencies),
            ("创建版本信息", self.create_version_info),
            ("创建应用图标", self.create_icon),
            ("清理构建目录", self.clean_build_dirs),
            ("创建PyInstaller配置", self.create_spec_file),
            ("构建可执行文件", self.build_executable),
            ("创建发布包", self.create_release_package),
        ]
        
        for step_name, step_func in steps:
            if not step_func():
                print(f"\n❌ 构建失败于步骤: {step_name}")
                return False
                
        # 构建成功
        self.print_header("构建完成")
        print("🎉 GPS 3D轨迹分析器构建成功!")
        print(f"📁 发布文件位置: {os.path.abspath(self.release_dir)}/")
        print(f"🚀 可执行文件: {self.project_name}.exe")
        print("\n📋 发布包内容:")
        for item in os.listdir(self.release_dir):
            item_path = os.path.join(self.release_dir, item)
            if os.path.isfile(item_path):
                size = os.path.getsize(item_path) / (1024 * 1024)
                print(f"  📄 {item} ({size:.1f} MB)")
                
        print(f"\n✅ 现在可以分发 {self.release_dir}/ 文件夹")
        print("✅ 用户只需双击 GPS_3D_Analyzer.exe 即可运行")
        
        return True

def main():
    """主函数"""
    builder = GPSAnalyzerBuilder()
    success = builder.run_build()
    
    if not success:
        print("\n❌ 构建失败")
        sys.exit(1)
    else:
        print("\n🎯 构建成功完成!")

if __name__ == "__main__":
    main()
