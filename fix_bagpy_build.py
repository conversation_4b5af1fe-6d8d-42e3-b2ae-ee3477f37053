#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix bagpy packaging issue for PyInstaller
Create a custom spec file with proper bagpy data files
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def find_bagpy_data_files():
    """查找bagpy需要的数据文件"""
    try:
        import bagpy
        bagpy_path = Path(bagpy.__file__).parent
        
        data_files = []
        
        # 查找version文件
        version_file = bagpy_path / 'version'
        if version_file.exists():
            data_files.append((str(version_file), 'bagpy'))
            print(f"✅ 找到version文件: {version_file}")
        else:
            print(f"⚠️  version文件不存在: {version_file}")
            # 创建一个临时version文件
            with open(version_file, 'w') as f:
                f.write("0.5.0")
            data_files.append((str(version_file), 'bagpy'))
            print(f"✅ 创建临时version文件: {version_file}")
        
        # 查找其他可能需要的文件
        for pattern in ['*.txt', '*.cfg', '*.yaml', '*.yml']:
            for file_path in bagpy_path.glob(pattern):
                data_files.append((str(file_path), 'bagpy'))
                print(f"✅ 找到数据文件: {file_path}")
        
        return data_files
        
    except ImportError:
        print("❌ bagpy未安装")
        return []
    except Exception as e:
        print(f"❌ 查找bagpy文件时出错: {e}")
        return []

def create_fixed_spec_file():
    """创建修复的spec文件"""
    print("📝 创建修复的PyInstaller spec文件...")
    
    # 查找bagpy数据文件
    bagpy_data_files = find_bagpy_data_files()
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

block_cipher = None

# 添加隐藏导入
hiddenimports = [
    'bagpy',
    'bagpy.bagreader',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    'pandas',
    'numpy',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'threading',
    'collections',
    'math',
    'time',
    'locale',
]

# 数据文件 - 包含bagpy需要的文件
datas = [
    # bagpy数据文件
{chr(10).join([f"    (r'{data_file[0]}', '{data_file[1]}')," for data_file in bagpy_data_files])}
]

# 二进制文件
binaries = []

a = Analysis(
    ['gps_gui_analyzer_fixed.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GPS_3D_Analyzer_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("GPS_3D_Analyzer_Fixed.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
        
    print("✅ 修复的spec文件创建完成")
    return True

def build_fixed_exe():
    """使用修复的spec文件构建exe"""
    print("🚀 使用修复的spec文件构建exe...")
    
    try:
        # 清理之前的构建
        if os.path.exists("build"):
            shutil.rmtree("build")
        if os.path.exists("dist"):
            shutil.rmtree("dist")
            
        # 使用spec文件构建
        cmd = [
            "pyinstaller",
            "--clean",
            "--noconfirm",
            "GPS_3D_Analyzer_Fixed.spec"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 修复版exe构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_fixed_release():
    """创建修复版发布包"""
    print("📦 创建修复版发布包...")
    
    # 检查exe文件是否存在
    exe_path = "dist/GPS_3D_Analyzer_Fixed.exe"
    if not os.path.exists(exe_path):
        print(f"❌ 修复版exe文件不存在: {exe_path}")
        return False
    
    # 创建发布目录
    release_dir = "GPS_3D_Analyzer_Fixed_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy2(exe_path, release_dir)
    print(f"✅ 复制修复版exe文件")
    
    # 创建README文件
    readme_content = f"""# GPS 3D 轨迹分析器 - 修复版

## 🔧 修复内容
本版本修复了bagpy库在PyInstaller打包时的兼容性问题。

### ✨ 主要功能
- 📁 从ROS bag文件提取GPS数据
- 🎨 3D轨迹可视化（深灰色主题 + 深绿色文字）
- 📊 详细的GPS质量分析（中文显示）
- 💾 导出轨迹为文本文件（帧编号 X Y Z格式）
- 🎮 交互式3D控制（鼠标拖拽旋转、滚轮缩放）

### 🎯 GPS质量颜色编码
- 🟢 青绿色实线: RTK固定解（最高精度）
- 🔵 蓝色虚线: SBAS定位
- 🟠 橙色虚线: GBAS定位
- 🔴 红色虚线: 无定位解
- 🟣 紫色虚线: 其他状态

### 🚀 使用方法
1. 双击 GPS_3D_Analyzer_Fixed.exe 启动程序
2. 点击"浏览"选择ROS bag文件
3. 设置GPS话题（默认: /rtk/gnss）
4. 选择输出文件位置
5. 点击"开始分析"
6. 查看3D轨迹和分析结果

### 🎮 3D控制说明
- **鼠标拖拽**: 旋转3D视角
- **鼠标滚轮**: 缩放视图
- **俯视按钮**: 切换到俯视角度
- **侧视按钮**: 切换到侧视角度
- **正视按钮**: 切换到正视角度
- **复位按钮**: 恢复默认视角

### 💻 系统要求
- Windows 10 64位
- 最小4GB内存
- 支持OpenGL的显卡
- 100MB可用磁盘空间

### 🎨 界面特色
- 深灰色专业主题
- 深绿色文字显示
- 深色选择背景
- 无中文乱码
- 流畅的3D交互

### 🔧 修复说明
- 修复了bagpy库的version文件缺失问题
- 确保所有依赖文件正确打包
- 提高了exe文件的稳定性

### 📝 版本信息
- 版本: 1.0.1 (修复版)
- 构建日期: {__import__('time').strftime("%Y-%m-%d")}
- 平台: Windows 10 64位

---
© 2024 GPS Analyzer Team. All rights reserved.
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ 创建README.txt")
    
    # 获取文件大小
    exe_size = os.path.getsize(os.path.join(release_dir, "GPS_3D_Analyzer_Fixed.exe"))
    exe_size_mb = exe_size / (1024 * 1024)
    
    print(f"\n🎉 修复版发布包创建完成!")
    print(f"📁 发布目录: {release_dir}/")
    print(f"📦 可执行文件大小: {exe_size_mb:.1f} MB")
    
    return True

def main():
    """主函数"""
    print("🔧 GPS 3D轨迹分析器 - bagpy修复构建")
    print("=" * 50)
    
    # 检查主脚本是否存在
    if not os.path.exists("gps_gui_analyzer_fixed.py"):
        print("❌ 主脚本文件不存在: gps_gui_analyzer_fixed.py")
        return False
    
    # 构建步骤
    steps = [
        ("创建修复的spec文件", create_fixed_spec_file),
        ("构建修复版exe", build_fixed_exe),
        ("创建修复版发布包", create_fixed_release),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ 失败于步骤: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 bagpy修复构建完成!")
    print("=" * 50)
    print("📁 修复版发布包: GPS_3D_Analyzer_Fixed_Release/")
    print("🚀 修复版exe: GPS_3D_Analyzer_Fixed.exe")
    print("\n✅ 现在可以测试修复版exe文件")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
