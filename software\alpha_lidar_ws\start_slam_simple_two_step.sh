#!/bin/bash

# 简单两步启动SLAM脚本

echo "=========================================="
echo "🎯 简单两步启动SLAM"
echo "=========================================="

# 检查参数
BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "用法: $0 <bag文件路径>"
    echo ""
    echo "示例:"
    echo "  $0 ~/slam_share/aLidar/input_bag/UM982loop_715std_maximum_synced.bag"
    echo ""
    exit 1
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo ""
echo "选择启动方式："
echo "1) 第一步：仅启动SLAM节点"
echo "2) 第二步：播放bag文件"
echo "3) 一键启动（自动执行两步）"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🚀 第一步：启动SLAM节点"
        echo "======================"
        
        # 设置SLAM参数
        echo "设置SLAM参数..."
        rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
        rosparam set /state_estimation_node/imu_topic "/imu/data"
        rosparam set /state_estimation_node/gps_topic "/rtk/gnss"
        rosparam set /state_estimation_node/enable_point_cloud_validation true
        rosparam set /state_estimation_node/min_points_threshold 100
        rosparam set /state_estimation_node/skip_empty_scans true
        rosparam set /state_estimation_node/voxel_size 0.6
        rosparam set /state_estimation_node/enable_gps false
        rosparam set /state_estimation_node/enable_loop_closure false
        
        echo "启动SLAM节点..."
        rosrun state_estimation state_estimation_node &
        SLAM_PID=$!
        
        echo ""
        echo "✅ SLAM节点已启动 (PID: $SLAM_PID)"
        echo ""
        echo "现在请在另一个终端运行第二步："
        echo "  $0 $BAG_FILE"
        echo "  然后选择选项2"
        echo ""
        echo "或者直接播放bag文件："
        echo "  rosbag play $BAG_FILE"
        echo ""
        echo "按 Ctrl+C 停止SLAM节点"
        
        # 等待用户中断
        trap 'kill $SLAM_PID 2>/dev/null; echo "SLAM节点已停止"; exit 0' SIGINT
        wait
        ;;
        
    2)
        echo ""
        echo "🎬 第二步：播放bag文件"
        echo "===================="
        
        # 检查SLAM节点是否运行
        if ! pgrep -f "state_estimation_node" > /dev/null; then
            echo "⚠️  未检测到SLAM节点运行"
            echo "请先运行第一步启动SLAM节点"
            echo ""
            read -p "是否现在启动SLAM节点? (y/n): " start_slam
            if [ "$start_slam" = "y" ]; then
                echo "启动SLAM节点..."
                rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
                rosparam set /state_estimation_node/imu_topic "/imu/data"
                rosparam set /state_estimation_node/enable_gps false
                rosparam set /state_estimation_node/skip_empty_scans true
                rosrun state_estimation state_estimation_node &
                SLAM_PID=$!
                sleep 5
                echo "✅ SLAM节点已启动"
            else
                exit 1
            fi
        else
            echo "✅ 检测到SLAM节点正在运行"
        fi
        
        echo ""
        echo "播放bag文件..."
        echo "控制说明："
        echo "  空格键 - 播放/暂停"
        echo "  s键    - 单步播放"
        echo "  Ctrl+C - 停止播放"
        echo ""
        
        rosbag play "$BAG_FILE" --clock
        ;;
        
    3)
        echo ""
        echo "🚀 一键启动（自动执行两步）"
        echo "========================="
        
        # 创建输出目录
        OUTPUT_DIR="/home/<USER>/slam_share/aLidar/two_step_slam_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$OUTPUT_DIR"
        echo "输出目录: $OUTPUT_DIR"
        
        echo ""
        echo "步骤1: 设置参数并启动SLAM节点"
        rosparam set /state_estimation_node/lidar_topic "/velodyne_points"
        rosparam set /state_estimation_node/imu_topic "/imu/data"
        rosparam set /state_estimation_node/gps_topic "/rtk/gnss"
        rosparam set /state_estimation_node/enable_point_cloud_validation true
        rosparam set /state_estimation_node/min_points_threshold 100
        rosparam set /state_estimation_node/skip_empty_scans true
        rosparam set /state_estimation_node/wait_for_pointcloud true
        rosparam set /state_estimation_node/voxel_size 0.6
        rosparam set /state_estimation_node/downsample_ratio 0.4
        rosparam set /state_estimation_node/max_iterations 20
        rosparam set /state_estimation_node/enable_gps false
        rosparam set /state_estimation_node/enable_loop_closure false
        rosparam set /state_estimation_node/enable_intensity_processing false
        
        echo "启动SLAM节点..."
        rosrun state_estimation state_estimation_node &
        SLAM_PID=$!
        
        echo "等待SLAM节点初始化..."
        sleep 8
        
        if ! ps -p $SLAM_PID > /dev/null; then
            echo "❌ SLAM节点启动失败"
            exit 1
        fi
        
        echo "✅ SLAM节点启动成功 (PID: $SLAM_PID)"
        
        echo ""
        echo "步骤2: 播放bag文件"
        echo "开始播放bag文件..."
        
        rosbag play "$BAG_FILE" --clock &
        BAG_PID=$!
        
        echo "✅ bag文件开始播放 (PID: $BAG_PID)"
        
        echo ""
        echo "步骤3: 监控系统运行"
        sleep 10
        
        if ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; then
            echo "✅ 系统运行正常"
            
            # 检查数据流
            echo "检查数据流..."
            if timeout 5 rostopic hz /velodyne_points 2>/dev/null | grep -q "average rate"; then
                echo "✅ 点云数据流正常"
            else
                echo "⚠️  点云数据流异常"
            fi
            
            # 检查输出
            if rostopic list | grep -q "/aft_mapped_to_init"; then
                echo "✅ SLAM位姿输出正常"
            else
                echo "⚠️  SLAM位姿输出异常"
            fi
            
            echo ""
            echo "🎉 系统启动成功!"
            echo ""
            echo "系统状态："
            echo "  SLAM节点: 运行中 (PID: $SLAM_PID)"
            echo "  bag播放: 运行中 (PID: $BAG_PID)"
            echo "  输出目录: $OUTPUT_DIR"
            echo ""
            echo "监控命令："
            echo "  rostopic echo /aft_mapped_to_init"
            echo "  rostopic hz /velodyne_points"
            echo ""
            echo "按 Ctrl+C 停止系统"
            
            # 创建停止函数
            cleanup() {
                echo ""
                echo "正在停止系统..."
                kill $SLAM_PID $BAG_PID 2>/dev/null
                echo "✅ 系统已停止"
                exit 0
            }
            
            # 捕获Ctrl+C信号
            trap cleanup SIGINT
            
            # 等待进程结束
            while ps -p $SLAM_PID > /dev/null && ps -p $BAG_PID > /dev/null; do
                sleep 5
            done
            
            if ! ps -p $SLAM_PID > /dev/null; then
                echo "❌ SLAM节点意外停止"
            fi
            
            if ! ps -p $BAG_PID > /dev/null; then
                echo "✅ bag文件播放完成"
            fi
            
            cleanup
            
        else
            echo "❌ 系统启动失败"
            if ! ps -p $SLAM_PID > /dev/null; then
                echo "SLAM节点已停止"
            fi
            if ! ps -p $BAG_PID > /dev/null; then
                echo "bag播放已停止"
            fi
            exit 1
        fi
        ;;
        
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "启动完成"
echo "=========================================="
