#!/bin/bash

# 解决首尾区域十米偏差问题的专用启动脚本

echo "=========================================="
echo "🎯 解决首尾区域偏差问题"
echo "=========================================="
echo ""
echo "问题分析："
echo "✅ GPS距离: 0.45米 (很接近)"
echo "❌ SLAM偏差: ~10米 (需要修正)"
echo "🎯 解决方案: 强制首尾帧精细匹配"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/offset_solution_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"
echo ""

echo "选择解决方案强度："
echo "1) 标准解决方案 (体素0.05m, 阈值0.5)"
echo "2) 高精度解决方案 (体素0.03m, 阈值0.3)"
echo "3) 超高精度解决方案 (体素0.02m, 阈值0.2)"
echo "4) 极限精度解决方案 (体素0.01m, 阈值0.1)"
echo ""

read -p "请选择解决方案 (1-4): " choice

case $choice in
    1)
        echo "使用标准解决方案..."
        VOXEL_SIZE=0.05
        THRESHOLD=0.5
        MAX_ITER=200
        ;;
    2)
        echo "使用高精度解决方案..."
        VOXEL_SIZE=0.03
        THRESHOLD=0.3
        MAX_ITER=300
        ;;
    3)
        echo "使用超高精度解决方案..."
        VOXEL_SIZE=0.02
        THRESHOLD=0.2
        MAX_ITER=500
        ;;
    4)
        echo "使用极限精度解决方案..."
        VOXEL_SIZE=0.01
        THRESHOLD=0.1
        MAX_ITER=1000
        ;;
    *)
        echo "使用默认标准解决方案..."
        VOXEL_SIZE=0.05
        THRESHOLD=0.5
        MAX_ITER=200
        ;;
esac

echo ""
echo "解决方案参数："
echo "  体素大小: ${VOXEL_SIZE}米 (越小越精确)"
echo "  匹配阈值: ${THRESHOLD} (越小越严格)"
echo "  最大迭代: ${MAX_ITER}次"
echo "  GPS触发阈值: 2.0米 (更敏感)"
echo ""

# 启动解决方案系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=true \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 8

# 设置更敏感的智能检测参数
echo "设置更敏感的检测参数..."
rosparam set /intelligent_start_end_detector/departure_threshold 20.0
rosparam set /intelligent_start_end_detector/return_threshold 2.0  # 非常敏感

# 设置高精度匹配参数
echo "设置高精度匹配参数..."
rosparam set /force_start_end_loop_matcher/voxel_size $VOXEL_SIZE
rosparam set /force_start_end_loop_matcher/outlier_rejection_threshold $THRESHOLD
rosparam set /force_start_end_loop_matcher/max_iterations $MAX_ITER
rosparam set /force_start_end_loop_matcher/max_correspondence_distance 0.5

# 设置超宽松的SLAM参数
echo "设置超宽松的SLAM参数..."
rosparam set /enhanced_slam_loop_closure_integration/force_search_radius 80.0
rosparam set /enhanced_slam_loop_closure_integration/start_end_score_threshold 0.95

echo ""
echo "=========================================="
echo "🚀 偏差解决系统已启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "🎯 GPS距离<2米时立即触发强制匹配"
echo "🔥 多算法精细匹配 (ICP + NDT + GICP)"
echo "💎 超高精度体素滤波 (${VOXEL_SIZE}米)"
echo "⚡ 严格匹配阈值 (${THRESHOLD})"
echo "📊 实时匹配结果监控"
echo ""
echo "监控命令："
echo "  智能检测状态: rostopic echo /intelligent_detector_status"
echo "  强制匹配结果: rostopic echo /force_match_score"
echo "  匹配调试信息: rostopic echo /force_match_debug"
echo "  回环结果:     rostopic echo /force_loop_result"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果："
echo "✅ GPS接近时自动触发精细匹配"
echo "✅ 多算法确保最佳匹配结果"
echo "✅ 首尾偏差从10米降低到<1米"
echo "✅ 完整保持强度值"
echo ""
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "按 Ctrl+C 停止系统"

# 启动监控
echo ""
echo "启动实时监控..."
sleep 2

# 在后台监控关键事件
(
    echo "监控强制匹配事件..."
    rostopic echo /force_match_score | while read line; do
        if [[ $line == *"success"* ]]; then
            echo "$(date '+%H:%M:%S') - 🎯 强制匹配事件: $line"
        fi
    done
) &

# 等待用户中断
wait
