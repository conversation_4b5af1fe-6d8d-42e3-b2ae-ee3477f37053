#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能GPS约束控制器
根据SLAM匹配质量自动开启/关闭GPS平面约束
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool, Float64
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
import json
import threading
from collections import deque

class SmartGPSConstraintController:
    def __init__(self):
        rospy.init_node('smart_gps_constraint_controller', anonymous=True)
        
        # 参数配置
        self.matching_quality_threshold = rospy.get_param('~matching_quality_threshold', 0.7)
        self.constraint_enable_delay = rospy.get_param('~constraint_enable_delay', 10.0)  # 秒
        self.constraint_disable_delay = rospy.get_param('~constraint_disable_delay', 5.0)  # 秒
        self.evaluation_window = rospy.get_param('~evaluation_window', 20)
        
        # 状态变量
        self.current_constraint_enabled = True
        self.last_constraint_change_time = 0
        self.matching_quality_history = deque(maxlen=self.evaluation_window)
        self.pose_history = deque(maxlen=50)
        
        # 统计信息
        self.total_enable_switches = 0
        self.total_disable_switches = 0
        self.current_matching_quality = 0.0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 订阅器
        self.analysis_sub = rospy.Subscriber('/gps_constraint_analysis', String, 
                                           self.analysis_callback, queue_size=1)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, 
                                       self.pose_callback, queue_size=10)
        self.pointcloud_sub = rospy.Subscriber('/cloud_registered', PointCloud2, 
                                             self.pointcloud_callback, queue_size=1)
        
        # 发布器
        self.constraint_control_pub = rospy.Publisher('/gps_constraint_control', Bool, queue_size=1)
        self.status_pub = rospy.Publisher('/smart_constraint_status', String, queue_size=1)
        self.quality_pub = rospy.Publisher('/slam_matching_quality', Float64, queue_size=1)
        
        # 定时器
        self.control_timer = rospy.Timer(rospy.Duration(2.0), self.evaluate_and_control)
        self.status_timer = rospy.Timer(rospy.Duration(10.0), self.publish_status)
        
        rospy.loginfo("Smart GPS Constraint Controller Started")
        rospy.loginfo("Quality threshold: %.3f", self.matching_quality_threshold)
        rospy.loginfo("Enable delay: %.1f seconds", self.constraint_enable_delay)
        rospy.loginfo("Disable delay: %.1f seconds", self.constraint_disable_delay)
    
    def analysis_callback(self, msg):
        """GPS约束分析结果回调"""
        try:
            analysis_data = json.loads(msg.data)
            
            with self.data_lock:
                # 提取匹配质量指标
                constraint_impact = analysis_data.get('constraint_impact', {})
                time_sync = analysis_data.get('time_synchronization', {})
                coord_consistency = analysis_data.get('coordinate_consistency', {})
                
                # 计算综合匹配质量分数
                quality_score = self.calculate_quality_score(constraint_impact, time_sync, coord_consistency)
                self.matching_quality_history.append(quality_score)
                self.current_matching_quality = quality_score
                
                # 发布质量分数
                quality_msg = Float64()
                quality_msg.data = quality_score
                self.quality_pub.publish(quality_msg)
                
        except Exception as e:
            rospy.logerr("Error processing analysis data: %s", str(e))
    
    def pose_callback(self, msg):
        """SLAM位姿回调"""
        with self.data_lock:
            pose_data = {
                'timestamp': msg.header.stamp.to_sec(),
                'position': [msg.pose.position.x, msg.pose.position.y, msg.pose.position.z]
            }
            self.pose_history.append(pose_data)
    
    def pointcloud_callback(self, msg):
        """点云回调 - 用于评估匹配质量"""
        # 简化的匹配质量评估
        current_time = rospy.Time.now().to_sec()
        
        # 基于点云处理频率评估匹配质量
        if len(self.pose_history) >= 2:
            recent_poses = list(self.pose_history)[-2:]
            time_diff = recent_poses[1]['timestamp'] - recent_poses[0]['timestamp']
            
            # 如果处理时间异常，可能是匹配有问题
            if time_diff > 0.2:  # 超过200ms认为处理慢
                quality_penalty = min(0.3, time_diff - 0.1)
                with self.data_lock:
                    if len(self.matching_quality_history) > 0:
                        adjusted_quality = max(0.0, self.matching_quality_history[-1] - quality_penalty)
                        self.matching_quality_history[-1] = adjusted_quality
    
    def calculate_quality_score(self, constraint_impact, time_sync, coord_consistency):
        """计算综合匹配质量分数"""
        score = 1.0  # 基础分数
        
        # 约束影响评分
        if isinstance(constraint_impact, dict):
            anomaly_ratio = constraint_impact.get('anomaly_ratio', 0)
            trajectory_smoothness = constraint_impact.get('trajectory_smoothness', 1.0)
            
            # 异常比例惩罚
            score -= anomaly_ratio * 0.5
            
            # 轨迹平滑度奖励
            score *= trajectory_smoothness
        
        # 时间同步评分
        if isinstance(time_sync, dict):
            sync_quality = time_sync.get('sync_quality', 'good')
            if sync_quality == 'poor':
                score *= 0.6
            elif sync_quality == 'good':
                score *= 1.0
        
        # 坐标一致性评分
        if isinstance(coord_consistency, dict):
            consistency_score = coord_consistency.get('consistency_score', 1.0)
            score *= max(0.5, consistency_score)
        
        return max(0.0, min(1.0, score))
    
    def evaluate_and_control(self, event):
        """评估并控制GPS约束"""
        with self.data_lock:
            if len(self.matching_quality_history) < 5:
                return
            
            # 计算平均匹配质量
            avg_quality = np.mean(list(self.matching_quality_history))
            current_time = rospy.Time.now().to_sec()
            
            # 决策逻辑
            should_enable = avg_quality >= self.matching_quality_threshold
            time_since_last_change = current_time - self.last_constraint_change_time
            
            # 状态切换逻辑
            if should_enable and not self.current_constraint_enabled:
                # 需要启用约束
                if time_since_last_change >= self.constraint_enable_delay:
                    self.enable_constraint()
                    self.last_constraint_change_time = current_time
                    self.total_enable_switches += 1
                    rospy.loginfo("🟢 GPS Constraint ENABLED - Quality: %.3f", avg_quality)
            
            elif not should_enable and self.current_constraint_enabled:
                # 需要禁用约束
                if time_since_last_change >= self.constraint_disable_delay:
                    self.disable_constraint()
                    self.last_constraint_change_time = current_time
                    self.total_disable_switches += 1
                    rospy.logwarn("🔴 GPS Constraint DISABLED - Quality: %.3f", avg_quality)
    
    def enable_constraint(self):
        """启用GPS约束"""
        self.current_constraint_enabled = True
        control_msg = Bool()
        control_msg.data = True
        self.constraint_control_pub.publish(control_msg)
        
        # 设置ROS参数
        try:
            rospy.set_param('/gps/enable_plane_constraint', True)
            rospy.set_param('/gps/plane_constraint_weight', 0.2)
        except Exception as e:
            rospy.logwarn("Failed to set GPS constraint parameters: %s", str(e))
    
    def disable_constraint(self):
        """禁用GPS约束"""
        self.current_constraint_enabled = False
        control_msg = Bool()
        control_msg.data = False
        self.constraint_control_pub.publish(control_msg)
        
        # 设置ROS参数
        try:
            rospy.set_param('/gps/enable_plane_constraint', False)
            rospy.set_param('/gps/plane_constraint_weight', 0.0)
        except Exception as e:
            rospy.logwarn("Failed to set GPS constraint parameters: %s", str(e))
    
    def publish_status(self, event):
        """发布状态信息"""
        with self.data_lock:
            avg_quality = np.mean(list(self.matching_quality_history)) if self.matching_quality_history else 0.0
            
            status_data = {
                'timestamp': rospy.Time.now().to_sec(),
                'constraint_enabled': self.current_constraint_enabled,
                'current_quality': self.current_matching_quality,
                'average_quality': avg_quality,
                'quality_threshold': self.matching_quality_threshold,
                'total_enable_switches': self.total_enable_switches,
                'total_disable_switches': self.total_disable_switches,
                'quality_history_size': len(self.matching_quality_history),
                'recommendation': self.get_current_recommendation(avg_quality)
            }
            
            status_msg = String()
            status_msg.data = json.dumps(status_data, indent=2)
            self.status_pub.publish(status_msg)
            
            # 控制台输出
            status_icon = "🟢" if self.current_constraint_enabled else "🔴"
            rospy.loginfo("%s GPS Constraint: %s | Quality: %.3f | Switches: %d/%d", 
                         status_icon,
                         "ENABLED" if self.current_constraint_enabled else "DISABLED",
                         avg_quality,
                         self.total_enable_switches,
                         self.total_disable_switches)
    
    def get_current_recommendation(self, avg_quality):
        """获取当前建议"""
        if avg_quality >= 0.8:
            return "Excellent matching quality - GPS constraints safe to use"
        elif avg_quality >= 0.6:
            return "Good matching quality - GPS constraints recommended"
        elif avg_quality >= 0.4:
            return "Moderate matching quality - Use GPS constraints with caution"
        else:
            return "Poor matching quality - Disable GPS constraints"

def main():
    try:
        controller = SmartGPSConstraintController()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Error in Smart GPS Constraint Controller: %s", str(e))

if __name__ == '__main__':
    main()
