<?xml version="1.0"?>
<launch>
    <!-- 智能首尾回环检测SLAM系统启动文件 -->
    <!-- 实现您提出的优化策略 -->
    
    <!-- 参数配置 -->
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="gps_loop_preset" default="poor_gps_preset" />
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/intelligent_output" />
    
    <!-- 智能检测参数 -->
    <arg name="start_departure_threshold" default="30.0" />    <!-- 起点离开阈值(米) -->
    <arg name="return_detection_threshold" default="50.0" />   <!-- 返回检测阈值(米) -->
    <arg name="gps_quality_threshold" default="0" />           <!-- GPS质量阈值(0=接受质量非0) -->
    <arg name="min_trajectory_length" default="100.0" />       <!-- 最小轨迹长度(米) -->
    
    <!-- 功能开关 -->
    <arg name="enable_gps_loop_closure" default="true" />
    <arg name="enable_intensity_preservation" default="true" />
    <arg name="enable_adaptive_optimization" default="true" />
    <arg name="enable_intelligent_detection" default="true" />
    <arg name="enable_performance_monitoring" default="true" />
    
    <!-- 配置文件路径 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="gps_config_file" default="$(find state_estimation)/config/gps_loop_closure_params.yaml" />
    
    <!-- 基础SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch" />
    
    <!-- 智能首尾回环检测器 -->
    <group if="$(arg enable_intelligent_detection)">
        <node name="intelligent_start_end_detector" pkg="state_estimation" type="intelligent_start_end_detector.py" output="screen">
            <param name="start_departure_threshold" value="$(arg start_departure_threshold)" />
            <param name="return_detection_threshold" value="$(arg return_detection_threshold)" />
            <param name="gps_quality_threshold" value="$(arg gps_quality_threshold)" />
            <param name="min_trajectory_length" value="$(arg min_trajectory_length)" />
            <param name="check_interval" value="1.0" />
        </node>
    </group>
    
    <!-- GPS回环检测系统 -->
    <group if="$(arg enable_gps_loop_closure)">
        <!-- 加载GPS回环检测配置 -->
        <rosparam file="$(arg gps_config_file)" command="load" />
        <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)" />
        
        <!-- 增强GPS回环检测优化器 -->
        <node name="enhanced_gps_loop_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_gps_loop_closure_optimizer" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_gps_loop_closure_optimizer" />
            <remap from="/rtk/gnss" to="/rtk/gnss" />
        </node>
        
        <!-- SLAM回环检测集成模块 -->
        <node name="enhanced_slam_loop_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_slam_loop_closure_integration" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_slam_loop_closure_integration" />
            
            <!-- 智能检测专用参数 -->
            <param name="enable_intelligent_matching" value="true" />
            <param name="intelligent_search_radius_multiplier" value="2.0" />
            <param name="intelligent_matching_precision" value="high" />
        </node>
    </group>
    
    <!-- 强度值保持系统 -->
    <group if="$(arg enable_intensity_preservation)">
        <!-- 加载强度保持配置 -->
        <rosparam file="$(arg intensity_config_file)" command="load" />
        <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
        
        <!-- 强度保持PCD保存模块 -->
        <node name="intensity_preserving_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <param name="save_directory" value="$(arg save_directory)" />
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
        
        <!-- 强度值质量监控 -->
        <node name="intensity_quality_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
            <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="statistics_topic" value="/intensity_statistics" />
            <param name="quality_report_topic" value="/pointcloud_quality_report" />
            <param name="report_interval" value="10.0" />
        </node>
    </group>
    
    <!-- 自适应参数优化系统 -->
    <group if="$(arg enable_adaptive_optimization)">
        <node name="adaptive_parameter_optimizer" pkg="state_estimation" type="adaptive_parameter_optimizer" output="screen">
            <param name="optimization_interval" value="30.0" />
            <param name="learning_rate" value="0.1" />
            <param name="stability_weight" value="0.6" />
            <param name="performance_weight" value="0.4" />
            <param name="history_window_size" value="100" />
            
            <!-- 智能检测优化参数 -->
            <param name="enable_intelligent_optimization" value="true" />
            <param name="intelligent_feedback_weight" value="0.3" />
        </node>
    </group>
    
    <!-- 性能监控系统 -->
    <group if="$(arg enable_performance_monitoring)">
        <node name="simple_performance_monitor" pkg="state_estimation" type="simple_performance_monitor.py" output="screen">
            <param name="report_interval" value="30.0" />
            <param name="max_history" value="100" />
        </node>
        
        <!-- 简化强度分析器 -->
        <node name="simple_intensity_analyzer" pkg="state_estimation" type="simple_intensity_analyzer.py" output="screen">
            <param name="input_topic" value="/velodyne_points" />
            <param name="analysis_report_topic" value="/intensity_analysis_report" />
            <param name="analysis_window_size" value="50" />
            <param name="report_interval" value="15.0" />
        </node>
    </group>
    
    <!-- 全局参数配置 -->
    <rosparam>
        # 智能首尾回环检测系统配置
        intelligent_slam_system:
            # 智能检测配置
            intelligent_detection:
                enable_gps_return_monitoring: true
                enable_quality_adaptive_detection: true
                enable_trajectory_length_validation: true
                enable_force_global_matching: true
                
            # 强度值保持配置
            intensity_preservation:
                preserve_original_intensity: true
                enable_intensity_restoration: true
                save_with_loop_correction: true
                intensity_range_validation: true
                
            # GPS回环检测配置
            gps_loop_closure:
                enable_intelligent_triggering: true
                enable_adaptive_thresholds: true
                quality_based_weighting: true
                multi_hypothesis_tracking: true
                
            # 自适应优化配置
            adaptive_optimization:
                enable_intelligent_feedback: true
                performance_based_adjustment: true
                quality_feedback_loop: true
                resource_aware_optimization: true
    </rosparam>
    
</launch>
