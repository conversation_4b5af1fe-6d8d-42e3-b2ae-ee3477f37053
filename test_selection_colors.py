#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify selection colors in dark theme
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

def test_selection_colors():
    """Test selection colors in various widgets"""
    
    # Create test window
    root = tk.Tk()
    root.title("选择颜色测试")
    root.geometry("800x600")
    root.configure(bg='#2E2E2E')
    
    # Define colors
    colors = {
        'bg': '#2E2E2E',
        'fg': '#FFFFFF',
        'entry_bg': '#3C3C3C',
        'entry_fg': '#FFFFFF',
        'select_bg': '#404040',
        'select_fg': '#FFFFFF',
        'terminal_bg': '#1E1E1E',
        'terminal_fg': '#00FF41',
    }
    
    # Configure ttk style
    style = ttk.Style()
    style.theme_use('clam')
    
    # Configure Entry style
    style.configure('Dark.TEntry',
                   background=colors['entry_bg'],
                   foreground=colors['entry_fg'],
                   relief='flat',
                   borderwidth=1,
                   selectbackground=colors['select_bg'],
                   selectforeground=colors['select_fg'],
                   insertcolor=colors['fg'])
    
    style.map('Dark.TEntry',
             focuscolor=[('!focus', 'none')],
             selectbackground=[('focus', colors['select_bg'])],
             selectforeground=[('focus', colors['select_fg'])])
    
    # Create main frame
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Title
    title_label = tk.Label(main_frame, text="选择颜色测试", 
                          font=('Microsoft YaHei', 16, 'bold'),
                          bg=colors['bg'], fg=colors['fg'])
    title_label.pack(pady=(0, 20))
    
    # Test Entry widgets
    entry_frame = tk.Frame(main_frame, bg=colors['bg'])
    entry_frame.pack(fill=tk.X, pady=10)
    
    tk.Label(entry_frame, text="输入框测试 (选择文本查看背景色):", 
             bg=colors['bg'], fg=colors['fg']).pack(anchor=tk.W)
    
    test_entry1 = ttk.Entry(entry_frame, style='Dark.TEntry', width=50)
    test_entry1.pack(fill=tk.X, pady=5)
    test_entry1.insert(0, "这是一个测试输入框，请选择这段文字查看选择背景色")
    
    test_entry2 = ttk.Entry(entry_frame, style='Dark.TEntry', width=50)
    test_entry2.pack(fill=tk.X, pady=5)
    test_entry2.insert(0, "/rtk/gnss")
    
    test_entry3 = ttk.Entry(entry_frame, style='Dark.TEntry', width=50)
    test_entry3.pack(fill=tk.X, pady=5)
    test_entry3.insert(0, "C:\\path\\to\\output\\file.txt")
    
    # Test ScrolledText widget (Analysis)
    analysis_frame = tk.Frame(main_frame, bg=colors['bg'])
    analysis_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    tk.Label(analysis_frame, text="分析结果文本框 (选择文本查看背景色):", 
             bg=colors['bg'], fg=colors['fg']).pack(anchor=tk.W)
    
    analysis_text = scrolledtext.ScrolledText(
        analysis_frame,
        height=8,
        font=('Microsoft YaHei', 10),
        bg=colors['entry_bg'],
        fg=colors['entry_fg'],
        insertbackground=colors['fg'],
        selectbackground=colors['select_bg'],
        selectforeground=colors['select_fg'],
        relief='flat',
        borderwidth=1
    )
    analysis_text.pack(fill=tk.BOTH, expand=True, pady=5)
    
    # Insert test text
    analysis_text.insert(tk.END, """=" * 45
📊 GPS 质量分析
=" * 45

🛰️ GPS质量分布:
RTK固定解: 13,182 点 (100.0%)
青绿色 实线

📏 轨迹统计
总点数: 13,182
轨迹长度: 1,532.5 米
首尾距离: 0.6 米
闭合误差: 0.039%

请选择这些文字来测试选择背景色是否为深色""")
    
    # Test Terminal ScrolledText
    terminal_frame = tk.Frame(main_frame, bg=colors['bg'])
    terminal_frame.pack(fill=tk.X, pady=10)
    
    tk.Label(terminal_frame, text="终端输出文本框 (选择文本查看背景色):", 
             bg=colors['bg'], fg=colors['fg']).pack(anchor=tk.W)
    
    terminal_text = scrolledtext.ScrolledText(
        terminal_frame,
        height=6,
        font=('Consolas', 10),
        bg=colors['terminal_bg'],
        fg=colors['terminal_fg'],
        insertbackground=colors['terminal_fg'],
        selectbackground=colors['select_bg'],
        selectforeground=colors['select_fg'],
        relief='flat',
        borderwidth=1
    )
    terminal_text.pack(fill=tk.X, pady=5)
    
    # Insert terminal test text
    terminal_text.insert(tk.END, """[10:30:15] 开始提取GPS数据...
[10:30:16] 已提取 1000 个GPS点...
[10:30:17] 已提取 2000 个GPS点...
[10:30:18] GPS数据提取完成: 13182 个点
[10:30:19] 转换为本地坐标...
[10:30:20] 分析GPS质量...
请选择这些文字来测试终端选择背景色""")
    
    # Instructions
    instruction_frame = tk.Frame(main_frame, bg=colors['bg'])
    instruction_frame.pack(fill=tk.X, pady=10)
    
    instructions = tk.Label(instruction_frame, 
                           text="测试说明:\n"
                                "1. 在输入框中选择文字，背景应该是深灰色 (#404040)\n"
                                "2. 在分析结果文本框中选择文字，背景应该是深灰色\n"
                                "3. 在终端输出文本框中选择文字，背景应该是深灰色\n"
                                "4. 选择的文字应该是白色 (#FFFFFF)",
                           bg=colors['bg'], fg=colors['fg'],
                           justify=tk.LEFT)
    instructions.pack(anchor=tk.W)
    
    # Color reference
    color_frame = tk.Frame(main_frame, bg=colors['bg'])
    color_frame.pack(fill=tk.X, pady=10)
    
    tk.Label(color_frame, text="颜色参考:", 
             bg=colors['bg'], fg=colors['fg'], font=('Microsoft YaHei', 12, 'bold')).pack(anchor=tk.W)
    
    # Create color swatches
    swatch_frame = tk.Frame(color_frame, bg=colors['bg'])
    swatch_frame.pack(fill=tk.X, pady=5)
    
    # Selection background swatch
    select_swatch = tk.Frame(swatch_frame, bg=colors['select_bg'], width=50, height=30, relief='solid', borderwidth=1)
    select_swatch.pack(side=tk.LEFT, padx=5)
    tk.Label(swatch_frame, text=f"选择背景色: {colors['select_bg']}", 
             bg=colors['bg'], fg=colors['fg']).pack(side=tk.LEFT, padx=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_selection_colors()
