#!/bin/bash

# 专门测试首尾回环检测的启动脚本
# 使用优化的参数配置

echo "=========================================="
echo "首尾回环检测优化测试"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "使用首尾回环优化预设启动系统..."

# 启动优化的SLAM系统，专门针对首尾回环检测
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=start_end_optimized_preset \
    save_directory:=/home/<USER>/slam_share/aLidar/start_end_test_output \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_advanced_analysis:=false \
    enable_performance_dashboard:=true

echo "系统已启动，专门优化首尾回环检测"
echo "监控命令："
echo "  rostopic echo /enhanced_gps_loop_closure_status"
echo "  rostopic echo /force_loop_closure"
echo "  rostopic echo /performance_monitor_report"
