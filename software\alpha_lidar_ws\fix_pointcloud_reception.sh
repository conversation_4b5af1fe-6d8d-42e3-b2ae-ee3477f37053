#!/bin/bash

# 点云数据接收修复脚本

echo "=========================================="
echo "🔧 点云数据接收问题修复"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

echo "步骤1: 分析bag文件数据"
echo "===================="

BAG_FILE="$1"
if [ -z "$BAG_FILE" ]; then
    echo "请提供bag文件路径:"
    echo "用法: $0 /path/to/your.bag"
    echo ""
    echo "或者使用默认路径:"
    read -p "输入bag文件路径: " BAG_FILE
fi

if [ ! -f "$BAG_FILE" ]; then
    echo "❌ bag文件不存在: $BAG_FILE"
    exit 1
fi

echo "分析bag文件: $BAG_FILE"
rosbag info "$BAG_FILE"

echo ""
echo "步骤2: 检查点云数据详情"
echo "======================"

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo "播放bag文件进行分析..."
rosbag play "$BAG_FILE" --pause &
BAG_PID=$!
sleep 3

echo ""
echo "检查可用topic:"
rostopic list

echo ""
echo "检查点云topic详情:"
if rostopic list | grep -q "/velodyne_points"; then
    echo "✅ 找到 /velodyne_points topic"
    
    echo ""
    echo "检查点云消息类型:"
    rostopic type /velodyne_points
    
    echo ""
    echo "恢复播放并检查点云数据..."
    rosservice call /rosbag/unpause
    
    echo "等待点云数据..."
    sleep 5
    
    echo "检查点云数据内容:"
    timeout 10 rostopic echo /velodyne_points -n 1 > /tmp/pointcloud_sample.txt 2>&1
    
    if [ -s /tmp/pointcloud_sample.txt ]; then
        echo "✅ 成功接收到点云数据"
        
        # 分析点云数据
        WIDTH=$(grep "width:" /tmp/pointcloud_sample.txt | awk '{print $2}')
        HEIGHT=$(grep "height:" /tmp/pointcloud_sample.txt | awk '{print $2}')
        
        if [ -n "$WIDTH" ] && [ -n "$HEIGHT" ]; then
            TOTAL_POINTS=$((WIDTH * HEIGHT))
            echo "点云尺寸: ${WIDTH} x ${HEIGHT} = ${TOTAL_POINTS} 点"
            
            if [ $TOTAL_POINTS -gt 0 ]; then
                echo "✅ 点云数据有效"
            else
                echo "❌ 点云数据为空"
            fi
        fi
        
        # 检查点云字段
        if grep -q "intensity" /tmp/pointcloud_sample.txt; then
            echo "✅ 包含强度字段"
        else
            echo "⚠️  不包含强度字段"
        fi
        
    else
        echo "❌ 无法接收点云数据"
        echo "可能的原因:"
        echo "1. bag文件播放速度问题"
        echo "2. topic名称不匹配"
        echo "3. 消息格式问题"
    fi
    
else
    echo "❌ 未找到 /velodyne_points topic"
    echo ""
    echo "可用的点云相关topic:"
    rostopic list | grep -E "(point|cloud|velodyne|lidar)" || echo "无点云topic"
fi

# 停止bag播放
kill $BAG_PID 2>/dev/null

echo ""
echo "步骤3: 创建点云接收测试"
echo "======================"

# 创建点云接收测试脚本
cat > test_pointcloud_reception.py << 'EOF'
#!/usr/bin/env python3
import rospy
from sensor_msgs.msg import PointCloud2
import sys

class PointCloudTester:
    def __init__(self):
        rospy.init_node('pointcloud_tester', anonymous=True)
        self.received_count = 0
        self.last_msg_time = None
        
        # 订阅点云
        self.sub = rospy.Subscriber('/velodyne_points', PointCloud2, self.pointcloud_callback, queue_size=1)
        
        rospy.loginfo("点云接收测试器启动")
        rospy.loginfo("等待点云数据...")
        
    def pointcloud_callback(self, msg):
        self.received_count += 1
        self.last_msg_time = rospy.Time.now()
        
        total_points = msg.width * msg.height
        
        rospy.loginfo(f"接收到点云 #{self.received_count}: {msg.width}x{msg.height}={total_points}点")
        
        if total_points == 0:
            rospy.logwarn("⚠️  接收到空点云!")
        elif total_points < 1000:
            rospy.logwarn(f"⚠️  点云密度较低: {total_points}点")
        else:
            rospy.loginfo(f"✅ 点云数据正常: {total_points}点")
        
        # 检查字段
        field_names = [field.name for field in msg.fields]
        rospy.loginfo(f"点云字段: {field_names}")
        
        if self.received_count >= 5:
            rospy.loginfo("测试完成，接收到5帧点云数据")
            rospy.signal_shutdown("测试完成")

if __name__ == '__main__':
    try:
        tester = PointCloudTester()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr(f"测试错误: {e}")
EOF

chmod +x test_pointcloud_reception.py

echo "✅ 点云接收测试脚本已创建"

echo ""
echo "步骤4: 运行点云接收测试"
echo "======================"

echo "选择测试方式:"
echo "1) 运行点云接收测试"
echo "2) 跳过测试，直接修复"
echo ""

read -p "请选择 (1-2): " test_choice

if [ "$test_choice" = "1" ]; then
    echo ""
    echo "🧪 运行点云接收测试"
    echo "=================="
    
    echo "重新播放bag文件..."
    rosbag play "$BAG_FILE" --loop &
    BAG_PID=$!
    sleep 3
    
    echo "启动点云接收测试器..."
    python3 test_pointcloud_reception.py &
    TEST_PID=$!
    
    echo "等待测试结果..."
    sleep 15
    
    # 停止测试
    kill $TEST_PID $BAG_PID 2>/dev/null
    
    echo "测试完成"
fi

echo ""
echo "步骤5: 创建修复配置"
echo "=================="

# 创建专门的点云处理配置
cat > src/state_estimation/launch/pointcloud_safe_slam.launch << 'EOF'
<?xml version="1.0"?>
<launch>
    <!-- 点云安全SLAM配置 -->
    
    <node name="state_estimation_node" pkg="state_estimation" type="state_estimation_node" output="screen">
        <!-- 点云topic配置 -->
        <param name="lidar_topic" value="/velodyne_points" />
        <param name="imu_topic" value="/imu/data" />
        <param name="gps_topic" value="/rtk/gnss" />
        
        <!-- 点云处理安全参数 -->
        <param name="enable_point_cloud_validation" value="true" />
        <param name="min_points_threshold" value="100" />
        <param name="max_points_threshold" value="200000" />
        <param name="skip_empty_scans" value="true" />
        <param name="wait_for_pointcloud" value="true" />
        <param name="pointcloud_timeout" value="5.0" />
        
        <!-- 下采样参数 -->
        <param name="voxel_size" value="0.8" />
        <param name="downsample_ratio" value="0.5" />
        <param name="enable_adaptive_downsampling" value="true" />
        
        <!-- 处理参数 -->
        <param name="max_iterations" value="15" />
        <param name="transformation_epsilon" value="1e-4" />
        <param name="euclidean_fitness_epsilon" value="1e-4" />
        
        <!-- 禁用复杂功能 -->
        <param name="enable_gps" value="false" />
        <param name="enable_loop_closure" value="false" />
        <param name="enable_intensity_processing" value="false" />
        
        <!-- 内存管理 -->
        <param name="max_memory_usage_mb" value="2048" />
        <param name="enable_memory_monitoring" value="true" />
        
        <!-- 调试选项 -->
        <param name="enable_debug_output" value="true" />
        <param name="log_pointcloud_stats" value="true" />
    </node>
    
</launch>
EOF

echo "✅ 点云安全SLAM配置已创建"

echo ""
echo "步骤6: 创建同步启动脚本"
echo "======================"

# 创建同步启动脚本
cat > start_synchronized_slam.sh << EOF
#!/bin/bash

# 同步启动SLAM脚本

echo "🚀 启动同步SLAM系统"
echo "=================="

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

echo ""
echo "步骤1: 预播放bag文件检查数据"
rosbag play "$BAG_FILE" --pause &
BAG_PID=\$!
sleep 3

echo "检查topic可用性..."
if rostopic list | grep -q "/velodyne_points"; then
    echo "✅ 点云topic可用"
else
    echo "❌ 点云topic不可用"
    kill \$BAG_PID 2>/dev/null
    exit 1
fi

echo ""
echo "步骤2: 启动SLAM节点"
roslaunch state_estimation pointcloud_safe_slam.launch &
SLAM_PID=\$!
sleep 5

echo ""
echo "步骤3: 恢复bag播放"
rosservice call /rosbag/unpause
echo "✅ bag文件播放已恢复"

echo ""
echo "步骤4: 监控系统状态"
echo "等待系统稳定..."
sleep 10

if ps -p \$SLAM_PID > /dev/null; then
    echo "✅ SLAM系统运行正常"
    echo ""
    echo "监控命令:"
    echo "  rostopic hz /velodyne_points"
    echo "  rostopic echo /aft_mapped_to_init"
    echo ""
    echo "按 Ctrl+C 停止系统"
    
    # 等待用户中断
    trap 'kill \$SLAM_PID \$BAG_PID 2>/dev/null; exit 0' SIGINT
    wait
else
    echo "❌ SLAM系统启动失败"
    kill \$BAG_PID 2>/dev/null
fi
EOF

chmod +x start_synchronized_slam.sh

echo "✅ 同步启动脚本已创建"

echo ""
echo "=========================================="
echo "点云数据接收修复完成"
echo "=========================================="
echo ""
echo "🎯 修复方案:"
echo ""
echo "1. 使用点云安全SLAM配置:"
echo "   roslaunch state_estimation pointcloud_safe_slam.launch"
echo ""
echo "2. 使用同步启动脚本:"
echo "   ./start_synchronized_slam.sh"
echo ""
echo "3. 手动同步启动:"
echo "   # 终端1: 预播放bag"
echo "   rosbag play $BAG_FILE --pause"
echo "   # 终端2: 启动SLAM"
echo "   roslaunch state_estimation pointcloud_safe_slam.launch"
echo "   # 终端1: 恢复播放"
echo "   rosservice call /rosbag/unpause"
echo ""
echo "4. 如果仍然崩溃，使用调试模式:"
echo "   gdb --args devel/lib/state_estimation/state_estimation_node"
