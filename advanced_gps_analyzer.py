#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级GPS数据分析工具
专门用于分析UM982 RTK数据并评估GPS集成的可行性
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy import signal
from scipy.spatial.distance import cdist
import argparse
import os

class AdvancedGPSAnalyzer:
    def __init__(self):
        self.gps_data = None
        self.analysis_results = {}
        
    def simulate_gps_data(self, duration=600, frequency=10):
        """
        模拟UM982 RTK GPS数据用于分析
        基于典型的回环轨迹模式
        """
        print("正在生成模拟GPS数据...")
        
        # 时间序列
        t = np.linspace(0, duration, int(duration * frequency))
        
        # 模拟回环轨迹 (8字形)
        center_lat, center_lon, center_alt = 39.9042, 116.4074, 50.0  # 北京坐标
        
        # 8字形轨迹参数
        a, b = 0.001, 0.0005  # 纬度和经度的变化幅度
        
        # 生成轨迹
        lat = center_lat + a * np.sin(2 * np.pi * t / 300) * np.cos(2 * np.pi * t / 600)
        lon = center_lon + b * np.sin(4 * np.pi * t / 300)
        
        # 高度变化 (模拟地形起伏 + 系统漂移)
        terrain_height = 2 * np.sin(2 * np.pi * t / 200) + 1 * np.cos(2 * np.pi * t / 150)
        system_drift = 0.001 * t  # 模拟系统漂移
        noise = np.random.normal(0, 0.02, len(t))  # RTK噪声
        alt = center_alt + terrain_height + system_drift + noise
        
        # GPS状态 (模拟RTK固定解和浮点解)
        rtk_status = np.random.choice([4, 5], len(t), p=[0.8, 0.2])  # 4=RTK固定, 5=RTK浮点
        
        # 精度信息
        hdop = np.random.uniform(0.8, 2.0, len(t))
        vdop = np.random.uniform(1.0, 3.0, len(t))
        satellites = np.random.randint(8, 15, len(t))
        
        self.gps_data = pd.DataFrame({
            'timestamp': t,
            'latitude': lat,
            'longitude': lon,
            'altitude': alt,
            'status': rtk_status,
            'hdop': hdop,
            'vdop': vdop,
            'satellites': satellites
        })
        
        print(f"生成了 {len(self.gps_data)} 个GPS数据点")
        return self.gps_data
    
    def analyze_height_consistency(self):
        """分析高度一致性"""
        print("\n=== 高度一致性分析 ===")
        
        altitudes = self.gps_data['altitude'].values
        timestamps = self.gps_data['timestamp'].values
        
        # 基本统计
        height_stats = {
            'min': np.min(altitudes),
            'max': np.max(altitudes),
            'mean': np.mean(altitudes),
            'std': np.std(altitudes),
            'range': np.max(altitudes) - np.min(altitudes),
            'start_end_diff': altitudes[-1] - altitudes[0]
        }
        
        print(f"高度统计:")
        print(f"  最小值: {height_stats['min']:.3f} m")
        print(f"  最大值: {height_stats['max']:.3f} m")
        print(f"  平均值: {height_stats['mean']:.3f} m")
        print(f"  标准差: {height_stats['std']:.3f} m")
        print(f"  高度范围: {height_stats['range']:.3f} m")
        print(f"  首尾高度差: {height_stats['start_end_diff']:.3f} m")
        
        # 高度变化率分析
        height_diff = np.diff(altitudes)
        time_diff = np.diff(timestamps)
        height_rate = height_diff / time_diff
        
        print(f"\n高度变化率分析:")
        print(f"  最大上升率: {np.max(height_rate):.3f} m/s")
        print(f"  最大下降率: {np.min(height_rate):.3f} m/s")
        print(f"  平均变化率: {np.mean(np.abs(height_rate)):.3f} m/s")
        
        self.analysis_results['height_stats'] = height_stats
        self.analysis_results['height_rate'] = height_rate
        
        return height_stats
    
    def analyze_loop_closure_potential(self):
        """分析回环检测潜力"""
        print("\n=== 回环检测潜力分析 ===")
        
        # 转换为UTM坐标进行距离计算
        positions = self.convert_to_utm()
        
        # 寻找潜在的回环点
        loop_candidates = []
        min_time_gap = 60  # 最小时间间隔60秒
        max_distance = 5.0  # 最大距离5米
        
        for i in range(len(positions)):
            for j in range(i + int(min_time_gap * 10), len(positions)):  # 假设10Hz
                distance = np.linalg.norm(positions[i] - positions[j])
                if distance < max_distance:
                    time_gap = self.gps_data.iloc[j]['timestamp'] - self.gps_data.iloc[i]['timestamp']
                    loop_candidates.append({
                        'start_idx': i,
                        'end_idx': j,
                        'distance': distance,
                        'time_gap': time_gap,
                        'height_diff': abs(self.gps_data.iloc[j]['altitude'] - self.gps_data.iloc[i]['altitude'])
                    })
        
        print(f"发现 {len(loop_candidates)} 个潜在回环")
        
        if loop_candidates:
            # 分析最佳回环
            best_loops = sorted(loop_candidates, key=lambda x: x['distance'])[:5]
            print(f"\n最佳回环候选:")
            for i, loop in enumerate(best_loops):
                print(f"  回环 {i+1}: 距离={loop['distance']:.2f}m, "
                      f"时间间隔={loop['time_gap']:.1f}s, "
                      f"高度差={loop['height_diff']:.3f}m")
        
        self.analysis_results['loop_candidates'] = loop_candidates
        return loop_candidates
    
    def analyze_gps_quality(self):
        """分析GPS数据质量"""
        print("\n=== GPS数据质量分析 ===")
        
        # RTK固定解比例
        rtk_fixed_ratio = np.sum(self.gps_data['status'] == 4) / len(self.gps_data)
        rtk_float_ratio = np.sum(self.gps_data['status'] == 5) / len(self.gps_data)
        
        print(f"RTK状态分布:")
        print(f"  固定解比例: {rtk_fixed_ratio:.1%}")
        print(f"  浮点解比例: {rtk_float_ratio:.1%}")
        
        # 精度因子分析
        avg_hdop = np.mean(self.gps_data['hdop'])
        avg_vdop = np.mean(self.gps_data['vdop'])
        avg_satellites = np.mean(self.gps_data['satellites'])
        
        print(f"\n精度指标:")
        print(f"  平均HDOP: {avg_hdop:.2f}")
        print(f"  平均VDOP: {avg_vdop:.2f}")
        print(f"  平均卫星数: {avg_satellites:.1f}")
        
        # 数据连续性分析
        time_gaps = np.diff(self.gps_data['timestamp'])
        max_gap = np.max(time_gaps)
        avg_gap = np.mean(time_gaps)
        
        print(f"\n数据连续性:")
        print(f"  最大时间间隔: {max_gap:.2f} s")
        print(f"  平均时间间隔: {avg_gap:.2f} s")
        print(f"  数据完整性: {100 * np.sum(time_gaps < 0.2) / len(time_gaps):.1f}%")
        
        quality_metrics = {
            'rtk_fixed_ratio': rtk_fixed_ratio,
            'avg_hdop': avg_hdop,
            'avg_vdop': avg_vdop,
            'avg_satellites': avg_satellites,
            'max_time_gap': max_gap
        }
        
        self.analysis_results['quality_metrics'] = quality_metrics
        return quality_metrics
    
    def convert_to_utm(self):
        """转换GPS坐标到UTM坐标"""
        # 简化的坐标转换 (实际应用中应使用更精确的转换)
        lat_rad = np.radians(self.gps_data['latitude'])
        lon_rad = np.radians(self.gps_data['longitude'])
        
        # 近似转换到局部坐标系 (米)
        R = 6371000  # 地球半径
        lat_center = np.radians(np.mean(self.gps_data['latitude']))
        lon_center = np.radians(np.mean(self.gps_data['longitude']))
        
        x = R * (lon_rad - lon_center) * np.cos(lat_center)
        y = R * (lat_rad - lat_center)
        z = self.gps_data['altitude'].values
        
        return np.column_stack([x, y, z])
    
    def estimate_slam_improvement(self):
        """估算SLAM改善潜力"""
        print("\n=== SLAM改善潜力评估 ===")
        
        height_stats = self.analysis_results.get('height_stats', {})
        quality_metrics = self.analysis_results.get('quality_metrics', {})
        loop_candidates = self.analysis_results.get('loop_candidates', [])
        
        # 高度校正潜力
        current_height_error = abs(height_stats.get('start_end_diff', 0))
        rtk_precision = 0.02 if quality_metrics.get('rtk_fixed_ratio', 0) > 0.5 else 0.1
        
        expected_improvement = max(0, current_height_error - rtk_precision)
        improvement_ratio = expected_improvement / current_height_error if current_height_error > 0 else 0
        
        print(f"高度精度改善评估:")
        print(f"  当前首尾高度差: {current_height_error:.3f} m")
        print(f"  GPS精度: {rtk_precision:.3f} m")
        print(f"  预期改善: {expected_improvement:.3f} m")
        print(f"  改善比例: {improvement_ratio:.1%}")
        
        # 回环约束潜力
        good_loops = [l for l in loop_candidates if l['distance'] < 2.0 and l['time_gap'] > 120]
        print(f"\n回环约束潜力:")
        print(f"  高质量回环数量: {len(good_loops)}")
        print(f"  回环约束强度: {'强' if len(good_loops) > 3 else '中' if len(good_loops) > 1 else '弱'}")
        
        return {
            'height_improvement': expected_improvement,
            'improvement_ratio': improvement_ratio,
            'loop_quality': len(good_loops)
        }
    
    def generate_visualization(self):
        """生成可视化图表"""
        print("\n正在生成可视化图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 轨迹图
        positions = self.convert_to_utm()
        axes[0, 0].plot(positions[:, 0], positions[:, 1], 'b-', linewidth=1, alpha=0.7)
        axes[0, 0].plot(positions[0, 0], positions[0, 1], 'go', markersize=8, label='起点')
        axes[0, 0].plot(positions[-1, 0], positions[-1, 1], 'ro', markersize=8, label='终点')
        axes[0, 0].set_xlabel('东向距离 (m)')
        axes[0, 0].set_ylabel('北向距离 (m)')
        axes[0, 0].set_title('GPS轨迹图')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axis('equal')
        
        # 2. 高度变化
        axes[0, 1].plot(self.gps_data['timestamp'], self.gps_data['altitude'], 'b-', linewidth=1)
        axes[0, 1].axhline(y=self.gps_data['altitude'].iloc[0], color='g', linestyle='--', 
                          label=f'起点: {self.gps_data["altitude"].iloc[0]:.3f}m')
        axes[0, 1].axhline(y=self.gps_data['altitude'].iloc[-1], color='r', linestyle='--', 
                          label=f'终点: {self.gps_data["altitude"].iloc[-1]:.3f}m')
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('高度 (m)')
        axes[0, 1].set_title('高度变化曲线')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. GPS质量指标
        axes[1, 0].plot(self.gps_data['timestamp'], self.gps_data['hdop'], 'r-', label='HDOP')
        axes[1, 0].plot(self.gps_data['timestamp'], self.gps_data['vdop'], 'b-', label='VDOP')
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('精度因子')
        axes[1, 0].set_title('GPS精度因子变化')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. RTK状态分布
        rtk_status_counts = self.gps_data['status'].value_counts()
        status_labels = {4: 'RTK固定解', 5: 'RTK浮点解', 1: '3D定位', 0: '无定位'}
        labels = [status_labels.get(status, f'状态{status}') for status in rtk_status_counts.index]
        axes[1, 1].pie(rtk_status_counts.values, labels=labels, autopct='%1.1f%%')
        axes[1, 1].set_title('RTK状态分布')
        
        plt.tight_layout()
        plt.savefig('gps_analysis_results.png', dpi=300, bbox_inches='tight')
        print("可视化图表已保存: gps_analysis_results.png")
        
        return fig
    
    def generate_report(self):
        """生成分析报告"""
        print("\n=== 生成分析报告 ===")
        
        report = f"""
# GPS数据分析报告

## 数据概况
- 数据点数量: {len(self.gps_data)}
- 时间跨度: {self.gps_data['timestamp'].iloc[-1] - self.gps_data['timestamp'].iloc[0]:.1f} 秒
- 采样频率: {len(self.gps_data) / (self.gps_data['timestamp'].iloc[-1] - self.gps_data['timestamp'].iloc[0]):.1f} Hz

## 高度一致性分析
{self.format_height_analysis()}

## GPS质量评估
{self.format_quality_analysis()}

## 回环检测潜力
{self.format_loop_analysis()}

## SLAM改善建议
{self.format_improvement_suggestions()}
"""
        
        with open('gps_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("分析报告已保存: gps_analysis_report.md")
        return report
    
    def format_height_analysis(self):
        height_stats = self.analysis_results.get('height_stats', {})
        return f"""
- 首尾高度差: {height_stats.get('start_end_diff', 0):.3f} m
- 高度标准差: {height_stats.get('std', 0):.3f} m
- 高度范围: {height_stats.get('range', 0):.3f} m
"""
    
    def format_quality_analysis(self):
        quality = self.analysis_results.get('quality_metrics', {})
        return f"""
- RTK固定解比例: {quality.get('rtk_fixed_ratio', 0):.1%}
- 平均HDOP: {quality.get('avg_hdop', 0):.2f}
- 平均VDOP: {quality.get('avg_vdop', 0):.2f}
- 平均卫星数: {quality.get('avg_satellites', 0):.1f}
"""
    
    def format_loop_analysis(self):
        loops = self.analysis_results.get('loop_candidates', [])
        good_loops = [l for l in loops if l['distance'] < 2.0]
        return f"""
- 潜在回环数量: {len(loops)}
- 高质量回环数量: {len(good_loops)}
- 回环检测可行性: {'高' if len(good_loops) > 3 else '中' if len(good_loops) > 1 else '低'}
"""
    
    def format_improvement_suggestions(self):
        return """
基于分析结果，建议采用以下GPS集成策略：

1. **高度校正**: 利用RTK高精度进行实时高度校正
2. **回环约束**: 在检测到回环时添加GPS约束
3. **质量监控**: 实时监控GPS质量，动态调整融合权重
4. **渐进校正**: 采用平滑校正避免系统震荡
"""

def main():
    parser = argparse.ArgumentParser(description='GPS数据分析工具')
    parser.add_argument('--simulate', action='store_true', help='使用模拟数据')
    parser.add_argument('--duration', type=int, default=600, help='模拟数据时长(秒)')
    parser.add_argument('--frequency', type=int, default=10, help='模拟数据频率(Hz)')
    
    args = parser.parse_args()
    
    analyzer = AdvancedGPSAnalyzer()
    
    if args.simulate:
        analyzer.simulate_gps_data(args.duration, args.frequency)
    else:
        print("请提供GPS数据文件或使用 --simulate 参数生成模拟数据")
        return
    
    # 执行分析
    analyzer.analyze_height_consistency()
    analyzer.analyze_gps_quality()
    analyzer.analyze_loop_closure_potential()
    analyzer.estimate_slam_improvement()
    
    # 生成输出
    analyzer.generate_visualization()
    analyzer.generate_report()
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- gps_analysis_results.png (可视化图表)")
    print("- gps_analysis_report.md (分析报告)")

if __name__ == "__main__":
    main()
