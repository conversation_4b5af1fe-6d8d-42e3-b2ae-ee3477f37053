#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create release package for GPS 3D Analyzer
"""

import os
import shutil
import time
import sys

def create_release_package():
    """创建发布包"""
    print("📦 创建GPS 3D轨迹分析器发布包...")
    
    # 检查exe文件是否存在
    exe_path = "dist/GPS_3D_Analyzer.exe"
    if not os.path.exists(exe_path):
        print(f"❌ 可执行文件不存在: {exe_path}")
        print("请先运行PyInstaller构建")
        return False
    
    # 创建发布目录
    release_dir = "GPS_3D_Analyzer_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy2(exe_path, release_dir)
    print(f"✅ 复制可执行文件")
    
    # 创建README文件
    readme_content = f"""# GPS 3D 轨迹分析器

## 📋 软件介绍
GPS 3D轨迹分析器是一个专业的GPS轨迹分析工具，具有以下特点：

### ✨ 主要功能
- 📁 从ROS bag文件提取GPS数据
- 🎨 3D轨迹可视化（深灰色主题 + 深绿色文字）
- 📊 详细的GPS质量分析（中文显示）
- 💾 导出轨迹为文本文件（帧编号 X Y Z格式）
- 🎮 交互式3D控制（鼠标拖拽旋转、滚轮缩放）

### 🎯 GPS质量颜色编码
- 🟢 青绿色实线: RTK固定解（最高精度）
- 🔵 蓝色虚线: SBAS定位
- 🟠 橙色虚线: GBAS定位
- 🔴 红色虚线: 无定位解
- 🟣 紫色虚线: 其他状态

### 🚀 使用方法
1. 双击 GPS_3D_Analyzer.exe 启动程序
2. 点击"浏览"选择ROS bag文件
3. 设置GPS话题（默认: /rtk/gnss）
4. 选择输出文件位置
5. 点击"开始分析"
6. 查看3D轨迹和分析结果

### 🎮 3D控制说明
- **鼠标拖拽**: 旋转3D视角
- **鼠标滚轮**: 缩放视图
- **俯视按钮**: 切换到俯视角度
- **侧视按钮**: 切换到侧视角度
- **正视按钮**: 切换到正视角度
- **复位按钮**: 恢复默认视角
- **放大/缩小**: 手动缩放控制
- **适应按钮**: 自动适应窗口大小

### 📄 输出文件格式
```
# GPS轨迹数据
# 格式: 帧编号 X Y Z
# 坐标系: 本地ENU (东-北-上)
# 原点: 纬度=30.73767602, 经度=103.97089927, 高度=522.005
# 单位: 米
#
1 0.000000 0.000000 0.000000
2 0.123456 0.234567 0.012345
3 0.246912 0.469134 0.024690
...
```

### 💻 系统要求
- Windows 10 64位
- 最小4GB内存
- 支持OpenGL的显卡
- 100MB可用磁盘空间

### 🎨 界面特色
- 深灰色专业主题
- 深绿色文字显示
- 深色选择背景
- 无中文乱码
- 流畅的3D交互

### 📞 技术支持
如遇问题，请检查：
1. bag文件格式是否正确
2. GPS话题名称是否匹配
3. 系统是否满足最低要求
4. 终端输出中的错误信息

### 📝 版本信息
- 版本: 1.0.0
- 构建日期: {time.strftime("%Y-%m-%d")}
- 平台: Windows 10 64位
- Python版本: {sys.version.split()[0]}

---
© 2024 GPS Analyzer Team. All rights reserved.
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ 创建README.txt")
    
    # 创建使用说明
    usage_content = """# 使用说明

## 📁 支持的文件格式
- ROS bag文件 (*.bag)
- 包含GPS消息的话题

## 🛰️ 支持的GPS话题格式
程序支持标准的ROS GPS消息格式，包含以下字段：
- latitude: 纬度
- longitude: 经度  
- altitude: 高度
- status.status: GPS状态
- status.service: GPS服务类型

## 📊 GPS状态说明
- -1: NO_FIX (无定位)
- 0: RTK_FIXED (RTK固定解)
- 1: SBAS_FIX (SBAS定位)
- 2: GBAS_FIX (GBAS定位)
- 3: OTHER (其他)

## 🎯 使用技巧
1. 确保bag文件包含GPS数据
2. 检查GPS话题名称是否正确
3. 选择合适的输出文件位置
4. 观察终端输出了解处理进度
5. 使用3D控制按钮获得最佳视角

## 🎨 界面说明
- 深灰色背景 + 深绿色文字
- 选择文字时显示深色背景
- 所有控件统一深色主题
- 支持完整中文显示

## 🎮 3D交互控制
- 左键拖拽: 旋转视角
- 滚轮: 缩放视图
- 控制按钮: 快速切换视角
- 复位功能: 恢复默认设置
"""
    
    with open(os.path.join(release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
        f.write(usage_content)
    print("✅ 创建使用说明.txt")
    
    # 获取文件信息
    exe_size = os.path.getsize(os.path.join(release_dir, "GPS_3D_Analyzer.exe"))
    exe_size_mb = exe_size / (1024 * 1024)
    
    print(f"\n🎉 发布包创建完成!")
    print(f"📁 发布目录: {release_dir}/")
    print(f"📦 可执行文件大小: {exe_size_mb:.1f} MB")
    print(f"\n📋 发布包内容:")
    
    for item in os.listdir(release_dir):
        item_path = os.path.join(release_dir, item)
        if os.path.isfile(item_path):
            size = os.path.getsize(item_path) / (1024 * 1024)
            print(f"  📄 {item} ({size:.1f} MB)")
    
    print(f"\n✅ 现在可以分发 {release_dir}/ 文件夹")
    print("✅ 用户只需双击 GPS_3D_Analyzer.exe 即可运行")
    
    return True

if __name__ == "__main__":
    create_release_package()
