#!/bin/bash

# αLiDAR GPS集成环境检查脚本
# Ubuntu 20.04 + ROS Noetic

echo "=========================================="
echo "αLiDAR GPS集成环境检查"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_pass() {
    echo -e "${GREEN}✅ $1${NC}"
}

check_fail() {
    echo -e "${RED}❌ $1${NC}"
}

check_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 1. 检查系统版本
echo -e "\n1. 检查系统版本..."
if lsb_release -d | grep -q "Ubuntu 20.04"; then
    check_pass "Ubuntu 20.04 LTS"
else
    check_warn "系统版本: $(lsb_release -d | cut -f2)"
    echo "   建议使用Ubuntu 20.04 LTS"
fi

# 2. 检查ROS版本
echo -e "\n2. 检查ROS版本..."
if command -v rosversion &> /dev/null; then
    ROS_VERSION=$(rosversion -d)
    if [ "$ROS_VERSION" = "noetic" ]; then
        check_pass "ROS Noetic"
    else
        check_warn "ROS版本: $ROS_VERSION (建议使用Noetic)"
    fi
else
    check_fail "ROS未安装或未正确配置"
    echo "   请安装ROS Noetic: http://wiki.ros.org/noetic/Installation/Ubuntu"
fi

# 3. 检查工作空间
echo -e "\n3. 检查αLiDAR工作空间..."
WORKSPACE_PATH="$HOME/alpha_lidar_GPS/software/alpha_lidar_ws"
if [ -d "$WORKSPACE_PATH" ]; then
    check_pass "工作空间存在: $WORKSPACE_PATH"
    
    # 检查关键目录
    if [ -d "$WORKSPACE_PATH/src/state_estimation" ]; then
        check_pass "state_estimation包存在"
    else
        check_fail "state_estimation包不存在"
    fi
else
    check_fail "工作空间不存在: $WORKSPACE_PATH"
    echo "   请确认αLiDAR项目路径正确"
fi

# 4. 检查数据集
echo -e "\n4. 检查数据集..."
BAG_FILE="$HOME/datasets/UM982loop_715std_maximum_synced.bag"
if [ -f "$BAG_FILE" ]; then
    BAG_SIZE=$(du -h "$BAG_FILE" | cut -f1)
    check_pass "数据集存在: $BAG_FILE ($BAG_SIZE)"
else
    check_fail "数据集不存在: $BAG_FILE"
    echo "   请确认bag文件路径正确"
fi

# 5. 检查必要的系统依赖
echo -e "\n5. 检查系统依赖..."

# 检查GeographicLib
if pkg-config --exists geographic; then
    GEOGRAPHIC_VERSION=$(pkg-config --modversion geographic)
    check_pass "GeographicLib: $GEOGRAPHIC_VERSION"
else
    check_fail "GeographicLib未安装"
    echo "   安装命令: sudo apt install libgeographic-dev"
fi

# 检查Eigen3
if pkg-config --exists eigen3; then
    EIGEN_VERSION=$(pkg-config --modversion eigen3)
    check_pass "Eigen3: $EIGEN_VERSION"
else
    check_warn "Eigen3未找到 (可能已安装但未配置pkg-config)"
fi

# 检查PCL
if pkg-config --exists pcl_common-1.10; then
    check_pass "PCL 1.10"
elif pkg-config --exists pcl_common; then
    PCL_VERSION=$(pkg-config --modversion pcl_common)
    check_pass "PCL: $PCL_VERSION"
else
    check_fail "PCL未安装"
    echo "   安装命令: sudo apt install libpcl-dev"
fi

# 检查OpenCV
if pkg-config --exists opencv4; then
    OPENCV_VERSION=$(pkg-config --modversion opencv4)
    check_pass "OpenCV: $OPENCV_VERSION"
elif pkg-config --exists opencv; then
    OPENCV_VERSION=$(pkg-config --modversion opencv)
    check_pass "OpenCV: $OPENCV_VERSION"
else
    check_warn "OpenCV未找到"
fi

# 6. 检查Python依赖
echo -e "\n6. 检查Python依赖..."
python3 -c "import numpy" 2>/dev/null && check_pass "NumPy" || check_fail "NumPy未安装"
python3 -c "import matplotlib" 2>/dev/null && check_pass "Matplotlib" || check_fail "Matplotlib未安装"

# 7. 检查ROS环境
echo -e "\n7. 检查ROS环境..."
if [ -n "$ROS_DISTRO" ]; then
    check_pass "ROS环境已设置: $ROS_DISTRO"
else
    check_warn "ROS环境未设置"
    echo "   运行: source /opt/ros/noetic/setup.bash"
fi

# 检查roscore是否可以启动
if command -v roscore &> /dev/null; then
    check_pass "roscore命令可用"
else
    check_fail "roscore命令不可用"
fi

# 8. 检查磁盘空间
echo -e "\n8. 检查磁盘空间..."
AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
AVAILABLE_SPACE_GB=$(df . | awk 'NR==2 {print int($4/1024/1024)}')

if [ $AVAILABLE_SPACE_GB -gt 10 ]; then
    check_pass "可用磁盘空间: $AVAILABLE_SPACE"
else
    check_warn "可用磁盘空间较少: $AVAILABLE_SPACE"
    echo "   建议至少保留10GB空间用于编译和运行"
fi

# 9. 检查内存
echo -e "\n9. 检查系统内存..."
TOTAL_MEM=$(free -h | awk 'NR==2{print $2}')
AVAILABLE_MEM=$(free -h | awk 'NR==2{print $7}')
AVAILABLE_MEM_GB=$(free | awk 'NR==2{print int($7/1024/1024)}')

if [ $AVAILABLE_MEM_GB -gt 4 ]; then
    check_pass "系统内存: $TOTAL_MEM (可用: $AVAILABLE_MEM)"
else
    check_warn "可用内存较少: $AVAILABLE_MEM"
    echo "   建议至少4GB可用内存用于编译和运行"
fi

# 10. 生成安装缺失依赖的命令
echo -e "\n=========================================="
echo "缺失依赖安装命令:"
echo "=========================================="

echo "# 更新软件包列表"
echo "sudo apt update"
echo ""

echo "# 安装所有必要依赖"
echo "sudo apt install -y \\"
echo "    libgeographic-dev libgeographic19 \\"
echo "    libeigen3-dev \\"
echo "    libpcl-dev \\"
echo "    libopencv-dev \\"
echo "    python3-matplotlib \\"
echo "    python3-numpy \\"
echo "    build-essential \\"
echo "    cmake"
echo ""

# 11. 生成快速启动命令
echo "=========================================="
echo "快速启动命令 (在不同终端窗口中执行):"
echo "=========================================="

echo "# 窗口1: 启动roscore"
echo "roscore"
echo ""

echo "# 窗口2: 编译和启动αLiDAR"
echo "cd ~/alpha_lidar_GPS/software/alpha_lidar_ws"
echo "source /opt/ros/noetic/setup.bash"
echo "catkin_make -DCATKIN_WHITELIST_PACKAGES=\"state_estimation\""
echo "source devel/setup.bash"
echo "roslaunch state_estimation mapping_robosense.launch"
echo ""

echo "# 窗口3: 播放bag文件"
echo "cd ~/datasets"
echo "rosbag play UM982loop_715std_maximum_synced.bag --pause"
echo "# 按空格键开始播放"
echo ""

echo "# 窗口4: 监控GPS数据 (可选)"
echo "rostopic list | grep -i gps"
echo "rostopic echo /ublox_gps/fix  # 根据实际topic名称调整"
echo ""

# 12. 检查总结
echo "=========================================="
echo "环境检查总结:"
echo "=========================================="

# 统计检查结果
TOTAL_CHECKS=10
PASSED_CHECKS=0

# 这里应该根据实际检查结果计算，简化处理
if [ -d "$WORKSPACE_PATH" ] && [ -f "$BAG_FILE" ] && command -v rosversion &> /dev/null; then
    echo -e "${GREEN}✅ 基本环境检查通过${NC}"
    echo "可以开始GPS集成实施"
else
    echo -e "${RED}❌ 环境检查发现问题${NC}"
    echo "请先解决上述问题后再继续"
fi

echo ""
echo "详细实施指南请参考: Ubuntu_Implementation_Guide.md"
echo "如有问题，请检查上述输出中的错误信息"
