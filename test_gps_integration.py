#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
αLiDAR GPS集成效果测试脚本
用于验证GPS集成是否正常工作并评估改善效果
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
from sensor_msgs.msg import NavSatFix
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseStamped
import tf
import time
from collections import deque
import threading

class GPSIntegrationTester:
    def __init__(self):
        rospy.init_node('gps_integration_tester', anonymous=True)
        
        # 数据存储
        self.gps_data = deque(maxlen=10000)
        self.slam_data = deque(maxlen=10000)
        self.gps_lock = threading.Lock()
        self.slam_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'gps_count': 0,
            'slam_count': 0,
            'rtk_fixed_count': 0,
            'height_corrections': 0,
            'loop_closures': 0
        }
        
        # 订阅者
        self.gps_sub = rospy.Subscriber('/gps/fix', NavSatFix, self.gps_callback)
        self.slam_sub = rospy.Subscriber('/odometry', Odometry, self.slam_callback)
        
        # 或者尝试其他可能的topic
        # self.slam_sub = rospy.Subscriber('/state_estimation', PoseStamped, self.pose_callback)
        
        # TF监听器
        self.tf_listener = tf.TransformListener()
        
        # 起始位置
        self.start_gps_pos = None
        self.start_slam_pos = None
        self.start_time = None
        
        print("GPS集成测试器已启动")
        print("正在监听以下topic:")
        print("- GPS: /gps/fix")
        print("- SLAM: /odometry")
        print("按Ctrl+C停止测试并生成报告")
    
    def gps_callback(self, msg):
        """GPS数据回调"""
        with self.gps_lock:
            timestamp = msg.header.stamp.to_sec()
            
            # 转换GPS坐标到局部坐标 (简化处理)
            if self.start_gps_pos is None and msg.status.status >= 1:
                self.start_gps_pos = [msg.latitude, msg.longitude, msg.altitude]
                self.start_time = timestamp
                print(f"GPS起始位置已记录: lat={msg.latitude:.8f}, lon={msg.longitude:.8f}, alt={msg.altitude:.3f}")
            
            # 存储GPS数据
            gps_point = {
                'timestamp': timestamp,
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status,
                'covariance': msg.position_covariance if len(msg.position_covariance) > 0 else None
            }
            
            self.gps_data.append(gps_point)
            self.stats['gps_count'] += 1
            
            # 统计RTK固定解
            if msg.status.status == 4:  # STATUS_GBAS_FIX (RTK固定解)
                self.stats['rtk_fixed_count'] += 1
    
    def slam_callback(self, msg):
        """SLAM里程计回调"""
        with self.slam_lock:
            timestamp = msg.header.stamp.to_sec()
            
            pos = msg.pose.pose.position
            
            if self.start_slam_pos is None:
                self.start_slam_pos = [pos.x, pos.y, pos.z]
                print(f"SLAM起始位置已记录: x={pos.x:.3f}, y={pos.y:.3f}, z={pos.z:.3f}")
            
            # 存储SLAM数据
            slam_point = {
                'timestamp': timestamp,
                'x': pos.x,
                'y': pos.y,
                'z': pos.z,
                'covariance': msg.pose.covariance if len(msg.pose.covariance) > 0 else None
            }
            
            self.slam_data.append(slam_point)
            self.stats['slam_count'] += 1
    
    def pose_callback(self, msg):
        """SLAM位姿回调 (备用)"""
        with self.slam_lock:
            timestamp = msg.header.stamp.to_sec()
            
            pos = msg.pose.position
            
            if self.start_slam_pos is None:
                self.start_slam_pos = [pos.x, pos.y, pos.z]
                print(f"SLAM起始位置已记录: x={pos.x:.3f}, y={pos.y:.3f}, z={pos.z:.3f}")
            
            slam_point = {
                'timestamp': timestamp,
                'x': pos.x,
                'y': pos.y,
                'z': pos.z
            }
            
            self.slam_data.append(slam_point)
            self.stats['slam_count'] += 1
    
    def convert_gps_to_local(self, lat, lon, alt):
        """将GPS坐标转换为局部坐标 (简化版本)"""
        if self.start_gps_pos is None:
            return 0, 0, 0
        
        # 简化的坐标转换
        R = 6371000  # 地球半径
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        start_lat_rad = np.radians(self.start_gps_pos[0])
        start_lon_rad = np.radians(self.start_gps_pos[1])
        
        x = R * (lon_rad - start_lon_rad) * np.cos(start_lat_rad)
        y = R * (lat_rad - start_lat_rad)
        z = alt - self.start_gps_pos[2]
        
        return x, y, z
    
    def analyze_data(self):
        """分析收集的数据"""
        print("\n" + "="*50)
        print("数据分析结果")
        print("="*50)
        
        # 基本统计
        print(f"GPS数据点数量: {self.stats['gps_count']}")
        print(f"SLAM数据点数量: {self.stats['slam_count']}")
        
        if self.stats['gps_count'] > 0:
            rtk_ratio = self.stats['rtk_fixed_count'] / self.stats['gps_count']
            print(f"RTK固定解比例: {rtk_ratio:.1%}")
        
        # 分析GPS数据质量
        if len(self.gps_data) > 10:
            self.analyze_gps_quality()
        
        # 分析轨迹一致性
        if len(self.gps_data) > 10 and len(self.slam_data) > 10:
            self.analyze_trajectory_consistency()
        
        # 分析高度一致性
        if len(self.gps_data) > 10 and len(self.slam_data) > 10:
            self.analyze_height_consistency()
    
    def analyze_gps_quality(self):
        """分析GPS数据质量"""
        print(f"\nGPS数据质量分析:")
        
        with self.gps_lock:
            gps_list = list(self.gps_data)
        
        if len(gps_list) < 2:
            return
        
        # 时间连续性
        time_gaps = []
        for i in range(1, len(gps_list)):
            gap = gps_list[i]['timestamp'] - gps_list[i-1]['timestamp']
            time_gaps.append(gap)
        
        avg_gap = np.mean(time_gaps)
        max_gap = np.max(time_gaps)
        
        print(f"  平均时间间隔: {avg_gap:.3f} 秒")
        print(f"  最大时间间隔: {max_gap:.3f} 秒")
        print(f"  数据频率: {1/avg_gap:.1f} Hz")
        
        # 高度变化
        altitudes = [point['altitude'] for point in gps_list]
        height_std = np.std(altitudes)
        height_range = np.max(altitudes) - np.min(altitudes)
        
        print(f"  高度标准差: {height_std:.3f} m")
        print(f"  高度范围: {height_range:.3f} m")
        
        if len(altitudes) > 1:
            height_drift = altitudes[-1] - altitudes[0]
            print(f"  首尾高度差: {height_drift:.3f} m")
    
    def analyze_trajectory_consistency(self):
        """分析轨迹一致性"""
        print(f"\n轨迹一致性分析:")
        
        # 同步GPS和SLAM数据
        gps_positions = []
        slam_positions = []
        
        with self.gps_lock:
            gps_list = list(self.gps_data)
        with self.slam_lock:
            slam_list = list(self.slam_data)
        
        # 简单的时间匹配
        for gps_point in gps_list[::10]:  # 每10个点取一个
            gps_time = gps_point['timestamp']
            
            # 找到最近的SLAM数据点
            closest_slam = None
            min_time_diff = float('inf')
            
            for slam_point in slam_list:
                time_diff = abs(slam_point['timestamp'] - gps_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_slam = slam_point
            
            if closest_slam and min_time_diff < 0.5:  # 0.5秒内的匹配
                gps_x, gps_y, gps_z = self.convert_gps_to_local(
                    gps_point['latitude'], gps_point['longitude'], gps_point['altitude'])
                
                gps_positions.append([gps_x, gps_y, gps_z])
                slam_positions.append([closest_slam['x'], closest_slam['y'], closest_slam['z']])
        
        if len(gps_positions) > 5:
            gps_positions = np.array(gps_positions)
            slam_positions = np.array(slam_positions)
            
            # 计算位置差异
            position_diffs = np.linalg.norm(gps_positions - slam_positions, axis=1)
            
            print(f"  匹配点对数量: {len(position_diffs)}")
            print(f"  平均位置差异: {np.mean(position_diffs):.3f} m")
            print(f"  最大位置差异: {np.max(position_diffs):.3f} m")
            print(f"  位置差异标准差: {np.std(position_diffs):.3f} m")
    
    def analyze_height_consistency(self):
        """分析高度一致性"""
        print(f"\n高度一致性分析:")
        
        with self.gps_lock:
            gps_list = list(self.gps_data)
        with self.slam_lock:
            slam_list = list(self.slam_data)
        
        if len(gps_list) < 2 or len(slam_list) < 2:
            return
        
        # GPS高度变化
        gps_heights = [point['altitude'] for point in gps_list]
        gps_height_change = gps_heights[-1] - gps_heights[0]
        
        # SLAM高度变化
        slam_heights = [point['z'] for point in slam_list]
        slam_height_change = slam_heights[-1] - slam_heights[0]
        
        height_consistency = abs(gps_height_change - slam_height_change)
        
        print(f"  GPS首尾高度差: {gps_height_change:.3f} m")
        print(f"  SLAM首尾高度差: {slam_height_change:.3f} m")
        print(f"  高度一致性误差: {height_consistency:.3f} m")
        
        if height_consistency < 0.1:
            print("  ✅ 高度一致性良好")
        elif height_consistency < 0.5:
            print("  ⚠️  高度一致性一般")
        else:
            print("  ❌ 高度一致性较差")
    
    def generate_plots(self):
        """生成可视化图表"""
        try:
            print("\n生成可视化图表...")
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # 1. GPS轨迹图
            with self.gps_lock:
                gps_list = list(self.gps_data)
            
            if len(gps_list) > 1:
                gps_positions = []
                for point in gps_list:
                    x, y, z = self.convert_gps_to_local(
                        point['latitude'], point['longitude'], point['altitude'])
                    gps_positions.append([x, y])
                
                gps_positions = np.array(gps_positions)
                axes[0, 0].plot(gps_positions[:, 0], gps_positions[:, 1], 'b-', alpha=0.7, label='GPS轨迹')
                axes[0, 0].plot(gps_positions[0, 0], gps_positions[0, 1], 'go', markersize=8, label='起点')
                axes[0, 0].plot(gps_positions[-1, 0], gps_positions[-1, 1], 'ro', markersize=8, label='终点')
                axes[0, 0].set_xlabel('东向距离 (m)')
                axes[0, 0].set_ylabel('北向距离 (m)')
                axes[0, 0].set_title('GPS轨迹')
                axes[0, 0].legend()
                axes[0, 0].grid(True, alpha=0.3)
                axes[0, 0].axis('equal')
            
            # 2. 高度变化对比
            if len(gps_list) > 1:
                gps_times = [(point['timestamp'] - gps_list[0]['timestamp']) for point in gps_list]
                gps_heights = [point['altitude'] for point in gps_list]
                
                axes[0, 1].plot(gps_times, gps_heights, 'b-', label='GPS高度')
                axes[0, 1].set_xlabel('时间 (s)')
                axes[0, 1].set_ylabel('高度 (m)')
                axes[0, 1].set_title('高度变化')
                axes[0, 1].legend()
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. RTK状态分布
            if len(gps_list) > 1:
                status_counts = {}
                for point in gps_list:
                    status = point['status']
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                status_labels = {
                    0: '无定位',
                    1: '3D定位', 
                    2: 'DGPS',
                    4: 'RTK固定解',
                    5: 'RTK浮点解'
                }
                
                labels = [status_labels.get(s, f'状态{s}') for s in status_counts.keys()]
                sizes = list(status_counts.values())
                
                axes[1, 0].pie(sizes, labels=labels, autopct='%1.1f%%')
                axes[1, 0].set_title('GPS状态分布')
            
            # 4. 数据接收频率
            axes[1, 1].text(0.1, 0.8, f"GPS数据点: {self.stats['gps_count']}", fontsize=12)
            axes[1, 1].text(0.1, 0.7, f"SLAM数据点: {self.stats['slam_count']}", fontsize=12)
            axes[1, 1].text(0.1, 0.6, f"RTK固定解: {self.stats['rtk_fixed_count']}", fontsize=12)
            
            if self.stats['gps_count'] > 0:
                rtk_ratio = self.stats['rtk_fixed_count'] / self.stats['gps_count']
                axes[1, 1].text(0.1, 0.5, f"RTK比例: {rtk_ratio:.1%}", fontsize=12)
            
            axes[1, 1].set_xlim(0, 1)
            axes[1, 1].set_ylim(0, 1)
            axes[1, 1].set_title('统计信息')
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            plt.savefig('gps_integration_test_results.png', dpi=300, bbox_inches='tight')
            print("可视化图表已保存: gps_integration_test_results.png")
            
        except Exception as e:
            print(f"生成图表时出错: {e}")
    
    def run(self):
        """运行测试"""
        try:
            print("开始数据收集...")
            print("请在另一个终端启动αLiDAR系统并播放bag文件")
            
            # 等待数据
            rate = rospy.Rate(1)  # 1Hz
            while not rospy.is_shutdown():
                if self.stats['gps_count'] > 0 or self.stats['slam_count'] > 0:
                    print(f"\r收集数据中... GPS: {self.stats['gps_count']}, SLAM: {self.stats['slam_count']}", end='')
                rate.sleep()
                
        except KeyboardInterrupt:
            print("\n\n测试停止，正在生成报告...")
            self.analyze_data()
            self.generate_plots()
            print("\n测试完成!")

def main():
    try:
        tester = GPSIntegrationTester()
        tester.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()
