#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the fixed GPS GUI Analyzer
Quick test to verify encoding and theme fixes
"""

import sys
import traceback

def test_imports():
    """Test all required imports"""
    print("测试导入模块...")
    
    try:
        import tkinter as tk
        print("✅ tkinter")
    except ImportError as e:
        print(f"❌ tkinter: {e}")
        return False
    
    try:
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter 模块")
    except ImportError as e:
        print(f"❌ tkinter 模块: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib.pyplot")
    except ImportError as e:
        print(f"❌ matplotlib.pyplot: {e}")
        return False
    
    try:
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        print("✅ matplotlib backend")
    except ImportError as e:
        print(f"❌ matplotlib backend: {e}")
        return False
    
    try:
        from mpl_toolkits.mplot3d import Axes3D
        print("✅ mplot3d")
    except ImportError as e:
        print(f"❌ mplot3d: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy")
    except ImportError as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas")
    except ImportError as e:
        print(f"❌ pandas: {e}")
        return False
    
    try:
        import bagpy
        print("✅ bagpy")
    except ImportError as e:
        print(f"❌ bagpy: {e}")
        print("  安装命令: pip install bagpy")
        return False
    
    return True

def test_encoding():
    """Test Chinese encoding"""
    print("\n测试中文编码...")
    
    try:
        # Test Chinese characters
        test_strings = [
            "GPS 3D 轨迹分析器",
            "控制面板",
            "3D可视化与分析结果",
            "终端输出",
            "开始分析",
            "停止分析"
        ]
        
        for test_str in test_strings:
            encoded = test_str.encode('utf-8').decode('utf-8')
            if encoded == test_str:
                print(f"✅ {test_str}")
            else:
                print(f"❌ {test_str}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ 编码测试失败: {e}")
        return False

def test_gui_creation():
    """Test basic GUI creation with dark theme"""
    print("\n测试GUI创建...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create test window
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("600x400")
        root.configure(bg='#2E2E2E')
        
        # Test ttk styling
        style = ttk.Style()
        style.theme_use('clam')
        
        # Test Chinese labels
        label = ttk.Label(root, text="测试标签", background='#2E2E2E', foreground='#FFFFFF')
        label.pack(pady=10)
        
        button = ttk.Button(root, text="测试按钮")
        button.pack(pady=10)
        
        # Test matplotlib integration with dark theme
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        from mpl_toolkits.mplot3d import Axes3D
        
        plt.style.use('dark_background')
        
        fig = plt.Figure(figsize=(5, 4), dpi=100, facecolor='#2b2b2b')
        ax = fig.add_subplot(111, projection='3d')
        ax.set_facecolor('#2b2b2b')
        
        # Test 3D plot
        x = [1, 2, 3, 4]
        y = [1, 2, 3, 4]
        z = [1, 2, 3, 4]
        ax.plot(x, y, z, color='#4ECDC4', linewidth=3)
        ax.set_title('测试3D图表', color='white')
        
        canvas = FigureCanvasTkAgg(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack()
        
        print("✅ GUI创建成功")
        
        # Don't show the window, just destroy it
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("GPS GUI 分析器 - 修复版本测试")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print()
    
    # Test imports
    if not test_imports():
        print("\n❌ 导入测试失败")
        print("请安装缺少的依赖:")
        print("pip install -r requirements.txt")
        return False
    
    # Test encoding
    if not test_encoding():
        print("\n❌ 编码测试失败")
        return False
    
    # Test GUI creation
    if not test_gui_creation():
        print("\n❌ GUI测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有测试通过!")
    print("✅ 修复版GPS GUI分析器应该可以正常工作")
    print("=" * 60)
    print("\n主要修复:")
    print("• 修复了中文编码问题")
    print("• 优化了深灰色主题")
    print("• 修复了鼠标3D控制问题")
    print("• 改进了界面布局和字体")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
