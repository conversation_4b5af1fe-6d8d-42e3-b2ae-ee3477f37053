@echo off
echo ========================================
echo GPS 3D 轨迹分析器 - EXE打包构建
echo ========================================
echo.
echo 🎯 目标: 创建Win10 64位独立exe文件
echo 📦 特性: 深色主题 + 绿色文字 + 修复3D控制
echo.

echo 📋 检查构建环境...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 📦 检查主程序文件...
if not exist "gps_gui_analyzer_fixed.py" (
    echo ❌ 主程序文件不存在: gps_gui_analyzer_fixed.py
    echo 请确保文件在当前目录中
    pause
    exit /b 1
)

echo ✅ 主程序文件存在
echo.

echo 🔧 安装构建依赖...
pip install pyinstaller bagpy numpy pandas matplotlib pillow
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成
echo.

echo 🚀 开始PyInstaller构建...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --onefile --windowed --name=GPS_3D_Analyzer ^
    --hidden-import=bagpy ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=mpl_toolkits.mplot3d ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.scrolledtext ^
    --collect-all=matplotlib ^
    --collect-all=bagpy ^
    --exclude-module=PyQt5 ^
    --exclude-module=PyQt6 ^
    --exclude-module=PySide2 ^
    --exclude-module=PySide6 ^
    gps_gui_analyzer_fixed.py

if %ERRORLEVEL% NEQ 0 (
    echo ❌ PyInstaller构建失败
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo ✅ PyInstaller构建完成
echo.

echo 📁 创建发布包...
python create_release.py

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 发布包创建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 构建完成！
echo ========================================
echo.

if exist "GPS_3D_Analyzer_Release\GPS_3D_Analyzer.exe" (
    echo ✅ 可执行文件: GPS_3D_Analyzer_Release\GPS_3D_Analyzer.exe
    
    for %%f in ("GPS_3D_Analyzer_Release\GPS_3D_Analyzer.exe") do (
        set size=%%~zf
        set /a size_mb=!size!/1024/1024
        echo 📦 文件大小: !size_mb! MB
    )
    
    echo.
    echo 📋 发布包内容:
    dir "GPS_3D_Analyzer_Release" /b
    
    echo.
    echo 🎯 使用说明:
    echo 1. 将 GPS_3D_Analyzer_Release 文件夹分发给用户
    echo 2. 用户双击 GPS_3D_Analyzer.exe 即可运行
    echo 3. 无需安装Python或其他依赖
    echo 4. 支持Windows 10 64位系统
    
    echo.
    echo 🎨 界面特色:
    echo • 深灰色专业主题
    echo • 深绿色文字显示
    echo • 深色选择背景
    echo • 修复的鼠标3D控制
    echo • 完整中文支持
    
) else (
    echo ❌ 可执行文件未找到
    echo 构建可能失败，请检查错误信息
)

echo.
pause
