#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析ROS bag文件中的RTK/GNSS数据
"""
import struct
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import sys
import os

def analyze_bag_file_basic(bag_path):
    """基本的bag文件分析"""
    print(f"正在分析bag文件: {bag_path}")

    try:
        file_size = os.path.getsize(bag_path) / (1024*1024*1024)
        print(f"\n=== BAG文件基本信息 ===")
        print(f"文件大小: {file_size:.2f} GB")
        print(f"文件路径: {bag_path}")

        # 由于没有rosbag库，我们提供分析建议
        print(f"\n=== 分析建议 ===")
        print("由于环境限制，无法直接解析bag文件内容")
        print("建议使用以下方法分析RTK/GNSS数据：")
        print("1. 在ROS环境中使用: rosbag info <bag_file>")
        print("2. 使用: rostopic echo /gps_topic")
        print("3. 或者安装rosbag库: pip install bagpy")

        # 基于文件名推测可能的GPS数据
        if "UM982" in bag_path:
            print(f"\n=== 基于文件名的推测 ===")
            print("UM982是一款高精度RTK GNSS接收机")
            print("可能包含的GPS相关topic:")
            print("- /gps/fix (sensor_msgs/NavSatFix)")
            print("- /gps/vel (geometry_msgs/TwistWithCovarianceStamped)")
            print("- /rtk/fix (sensor_msgs/NavSatFix)")
            print("- /navsat/fix")
            print("- /ublox_gps/fix")

        return True

    except Exception as e:
        print(f"分析bag文件时出错: {e}")
        return False

def create_gps_height_correction_analysis():
    """创建GPS高度校正分析"""
    print(f"\n=== GPS高度校正分析 ===")

    # 模拟典型的GPS高度数据分析
    print("基于UM982 RTK接收机的典型性能分析:")
    print("- 水平精度: ±(8mm + 1ppm)")
    print("- 垂直精度: ±(15mm + 1ppm)")
    print("- RTK固定解时精度可达厘米级")

    # 创建示例高度校正代码
    correction_code = '''
# GPS高度校正策略分析

## 1. 首尾高度差问题的根本原因
- IMU漂移累积：长时间运行导致的姿态和位置漂移
- 气压高度计误差：环境气压变化影响
- LiDAR里程计累积误差：特征匹配不准确导致的漂移

## 2. GPS/RTK数据的优势
- 绝对位置参考：提供全局坐标系下的绝对位置
- 高精度：RTK模式下可达厘米级精度
- 长期稳定：不存在累积漂移问题

## 3. 集成策略建议

### 3.1 松耦合方案（推荐）
```cpp
// 定期使用GPS位置校正累积误差
void correctWithGPS(double timestamp) {
    if (hasValidGPSFix(timestamp)) {
        Eigen::Vector3d gps_position = getGPSPosition(timestamp);
        Eigen::Vector3d slam_position = getCurrentSLAMPosition();

        Eigen::Vector3d drift = gps_position - slam_position;

        // 如果漂移超过阈值，进行校正
        if (drift.norm() > correction_threshold) {
            applySmoothCorrection(drift);
        }
    }
}
```

### 3.2 紧耦合方案（高精度）
```cpp
// 将GPS作为观测量直接融入卡尔曼滤波
void integrateGPSObservation() {
    // 构建GPS观测模型
    Eigen::VectorXd z_gps(3);  // GPS观测
    Eigen::MatrixXd H_gps(3, state_dim);  // 观测矩阵
    Eigen::MatrixXd R_gps(3, 3);  // GPS噪声协方差

    // 更新滤波器
    kf.update(z_gps, H_gps, R_gps);
}
```

### 3.3 回环约束方案
```cpp
// 利用GPS检测回环并添加约束
void addGPSLoopConstraint() {
    if (isNearStartPosition()) {
        Eigen::Vector3d start_gps = getStartGPSPosition();
        Eigen::Vector3d current_gps = getCurrentGPSPosition();

        // 添加回环约束
        addLoopClosureConstraint(start_gps, current_gps);
    }
}
```

## 4. 实施建议

### 4.1 数据预处理
- GPS状态检查：只使用RTK固定解数据
- 异常值检测：过滤GPS跳跃和多路径干扰
- 时间同步：确保GPS和LiDAR数据时间对齐

### 4.2 坐标系转换
- WGS84 → UTM：统一坐标系
- 高程基准：考虑大地高与正常高的差异
- 局部坐标系：建立以起点为原点的局部坐标系

### 4.3 权重策略
- 动态权重：根据GPS精度动态调整融合权重
- 环境自适应：室内外环境下采用不同策略
- 置信度评估：基于GPS状态和协方差调整信任度
'''

    with open('gps_height_correction_analysis.md', 'w', encoding='utf-8') as f:
        f.write(correction_code)

    print("GPS高度校正分析已保存到: gps_height_correction_analysis.md")

def generate_gps_integration_code():
    """生成GPS集成代码示例"""
    code = '''
// GPS数据集成到αLiDAR系统的建议代码

class GPSProcessor {
private:
    std::deque<sensor_msgs::NavSatFix> gps_buffer;
    std::mutex gps_mutex;
    
    // GPS坐标转换参数
    double utm_zone;
    bool is_utm_initialized = false;
    Eigen::Vector3d utm_origin;
    
public:
    void gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
        std::lock_guard<std::mutex> lock(gps_mutex);
        gps_buffer.push_back(*gps_msg);
        
        // 保持缓冲区大小
        if (gps_buffer.size() > 1000) {
            gps_buffer.pop_front();
        }
    }
    
    bool getGPSConstraint(double timestamp, Eigen::Vector3d& gps_position, 
                         Eigen::Matrix3d& gps_covariance) {
        std::lock_guard<std::mutex> lock(gps_mutex);
        
        // 查找时间戳最近的GPS数据
        auto closest_gps = findClosestGPS(timestamp);
        if (!closest_gps) return false;
        
        // 转换GPS坐标到UTM
        if (!is_utm_initialized) {
            initializeUTM(closest_gps->latitude, closest_gps->longitude);
        }
        
        gps_position = convertToUTM(closest_gps->latitude, 
                                   closest_gps->longitude, 
                                   closest_gps->altitude);
        
        // 设置GPS协方差
        if (closest_gps->position_covariance_type != 0) {
            // 使用GPS提供的协方差
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    gps_covariance(i, j) = closest_gps->position_covariance[i*3 + j];
                }
            }
        } else {
            // 使用默认协方差
            gps_covariance = Eigen::Matrix3d::Identity() * 1.0; // 1米标准差
        }
        
        return true;
    }
    
    // 在状态估计中集成GPS约束
    void integrateGPSConstraint(esekfom::esekf<state_ikfom, 12, input_ikfom>& kf,
                               double timestamp) {
        Eigen::Vector3d gps_position;
        Eigen::Matrix3d gps_covariance;
        
        if (getGPSConstraint(timestamp, gps_position, gps_covariance)) {
            // 获取当前状态估计的位置
            state_ikfom current_state = kf.get_x();
            Eigen::Vector3d estimated_position = current_state.pos;
            
            // 计算GPS约束残差
            Eigen::Vector3d residual = gps_position - estimated_position;
            
            // 如果残差过大，可能是GPS跳跃，需要谨慎处理
            if (residual.norm() > 10.0) {
                ROS_WARN("Large GPS residual: %.3f m, skipping GPS update", 
                        residual.norm());
                return;
            }
            
            // 构建GPS观测模型 H矩阵
            Eigen::MatrixXd H = Eigen::MatrixXd::Zero(3, 12);
            H.block<3, 3>(0, 0) = Eigen::Matrix3d::Identity(); // 位置部分
            
            // 更新卡尔曼滤波器
            kf.update_iterated_dyn_share_GPS(residual, H, gps_covariance);
        }
    }
};

// 在主循环中集成GPS
void execute_with_gps() {
    // ... 原有的IMU和LiDAR处理 ...
    
    // 添加GPS约束
    if (gps_processor && enable_gps_constraint) {
        gps_processor->integrateGPSConstraint(kf, lidar_end_time);
    }
    
    // ... 继续原有的地图更新等 ...
}
'''
    
    with open('gps_integration_example.cpp', 'w', encoding='utf-8') as f:
        f.write(code)
    
    print("GPS集成代码示例已保存到: gps_integration_example.cpp")

if __name__ == "__main__":
    bag_path = "datasets/UM982loop_715std_maximum_synced.bag"

    if not os.path.exists(bag_path):
        print(f"错误: 找不到bag文件 {bag_path}")
        sys.exit(1)

    analyze_bag_file_basic(bag_path)
    create_gps_height_correction_analysis()
    generate_gps_integration_code()
