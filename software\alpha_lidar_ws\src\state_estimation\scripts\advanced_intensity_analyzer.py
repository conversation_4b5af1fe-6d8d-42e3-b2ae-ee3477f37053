#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级强度值分析和恢复模块
提供深度强度值分析、异常检测、智能恢复和质量评估功能
"""

import rospy
import numpy as np
from sensor_msgs.msg import PointCloud2
from std_msgs.msg import String, Float64MultiArray
import sensor_msgs.point_cloud2 as pc2
import json
import time
from collections import deque, defaultdict
import threading
import warnings
warnings.filterwarnings('ignore')

# 可选导入 - 如果缺少依赖包则使用简化版本
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    rospy.logwarn("pandas not available, using simplified analysis")

try:
    from sklearn.cluster import DBSCAN
    from sklearn.preprocessing import StandardScaler
    from sklearn.ensemble import IsolationForest
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    rospy.logwarn("scikit-learn not available, using basic anomaly detection")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    rospy.logwarn("matplotlib/seaborn not available, disabling plotting")

try:
    from scipy import stats
    from scipy.spatial import KDTree
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    rospy.logwarn("scipy not available, using basic statistics")

class AdvancedIntensityAnalyzer:
    def __init__(self):
        rospy.init_node('advanced_intensity_analyzer', anonymous=True)
        
        # 参数配置
        self.input_topic = rospy.get_param('~input_topic', '/velodyne_points')
        self.output_topic = rospy.get_param('~output_topic', '/enhanced_pointcloud_with_intensity')
        self.analysis_report_topic = rospy.get_param('~analysis_report_topic', '/intensity_analysis_report')
        self.corrected_intensity_topic = rospy.get_param('~corrected_intensity_topic', '/corrected_intensity_cloud')
        
        # 分析参数
        self.analysis_window_size = rospy.get_param('~analysis_window_size', 50)
        self.anomaly_detection_threshold = rospy.get_param('~anomaly_detection_threshold', 0.1)
        self.clustering_eps = rospy.get_param('~clustering_eps', 0.3)
        self.clustering_min_samples = rospy.get_param('~clustering_min_samples', 10)
        self.report_interval = rospy.get_param('~report_interval', 15.0)
        
        # 强度值范围
        self.expected_intensity_min = rospy.get_param('~expected_intensity_min', 0.0)
        self.expected_intensity_max = rospy.get_param('~expected_intensity_max', 255.0)
        self.intensity_outlier_factor = rospy.get_param('~intensity_outlier_factor', 3.0)
        
        # 数据存储
        self.intensity_history = deque(maxlen=self.analysis_window_size)
        self.spatial_history = deque(maxlen=self.analysis_window_size)
        self.timestamp_history = deque(maxlen=self.analysis_window_size)
        self.quality_metrics_history = deque(maxlen=100)
        
        # 分析模型（仅在sklearn可用时创建）
        if SKLEARN_AVAILABLE:
            self.anomaly_detector = IsolationForest(
                contamination=self.anomaly_detection_threshold,
                random_state=42
            )
            self.scaler = StandardScaler()
        else:
            self.anomaly_detector = None
            self.scaler = None
        self.intensity_model_trained = False
        
        # 统计信息
        self.total_points_analyzed = 0
        self.total_anomalies_detected = 0
        self.total_corrections_applied = 0
        self.analysis_start_time = time.time()
        
        # 强度值分布模型
        self.intensity_distribution_model = None
        self.spatial_intensity_model = None
        self.temporal_intensity_model = None
        
        # 线程锁
        self.data_lock = threading.Lock()
        self.model_lock = threading.Lock()
        
        # 订阅器和发布器
        self.pointcloud_sub = rospy.Subscriber(
            self.input_topic, PointCloud2, self.pointcloud_callback, queue_size=1)
        
        self.analysis_report_pub = rospy.Publisher(
            self.analysis_report_topic, String, queue_size=1)
        self.corrected_cloud_pub = rospy.Publisher(
            self.corrected_intensity_topic, PointCloud2, queue_size=1)
        
        # 定时器
        self.report_timer = rospy.Timer(
            rospy.Duration(self.report_interval), self.publish_analysis_report)
        
        rospy.loginfo("Advanced Intensity Analyzer Started")
        rospy.loginfo("Input topic: %s", self.input_topic)
        rospy.loginfo("Analysis window size: %d", self.analysis_window_size)
        rospy.loginfo("Anomaly detection threshold: %.3f", self.anomaly_detection_threshold)
    
    def pointcloud_callback(self, msg):
        """处理点云消息并进行强度值分析"""
        try:
            # 解析点云数据
            points = list(pc2.read_points(msg, field_names=("x", "y", "z", "intensity"), skip_nans=True))
            
            if not points:
                rospy.logwarn("Received empty pointcloud")
                return
            
            # 转换为numpy数组
            points_array = np.array(points)
            spatial_data = points_array[:, :3]  # x, y, z
            intensity_data = points_array[:, 3]  # intensity
            
            # 更新历史数据
            with self.data_lock:
                self.intensity_history.append(intensity_data)
                self.spatial_history.append(spatial_data)
                self.timestamp_history.append(msg.header.stamp.to_sec())
                self.total_points_analyzed += len(points)
            
            # 执行分析
            analysis_results = self.analyze_intensity_data(spatial_data, intensity_data)
            
            # 应用修正
            corrected_intensity = self.apply_intensity_corrections(
                spatial_data, intensity_data, analysis_results)
            
            # 发布修正后的点云
            self.publish_corrected_pointcloud(msg, spatial_data, corrected_intensity)
            
            # 更新质量指标
            self.update_quality_metrics(analysis_results)
            
        except Exception as e:
            rospy.logerr("Error in pointcloud analysis: %s", str(e))
    
    def analyze_intensity_data(self, spatial_data, intensity_data):
        """深度分析强度值数据"""
        analysis_results = {
            'anomalies': [],
            'clusters': [],
            'statistics': {},
            'quality_score': 0.0,
            'recommendations': []
        }
        
        try:
            # 1. 基础统计分析
            analysis_results['statistics'] = self.compute_intensity_statistics(intensity_data)
            
            # 2. 异常检测
            anomalies = self.detect_intensity_anomalies(spatial_data, intensity_data)
            analysis_results['anomalies'] = anomalies
            
            # 3. 空间聚类分析
            clusters = self.perform_spatial_clustering(spatial_data, intensity_data)
            analysis_results['clusters'] = clusters
            
            # 4. 时序分析（如果有足够历史数据）
            if len(self.intensity_history) >= 10:
                temporal_analysis = self.analyze_temporal_patterns()
                analysis_results['temporal_patterns'] = temporal_analysis
            
            # 5. 质量评分
            quality_score = self.calculate_quality_score(analysis_results)
            analysis_results['quality_score'] = quality_score
            
            # 6. 生成建议
            recommendations = self.generate_recommendations(analysis_results)
            analysis_results['recommendations'] = recommendations
            
        except Exception as e:
            rospy.logerr("Error in intensity analysis: %s", str(e))
        
        return analysis_results
    
    def compute_intensity_statistics(self, intensity_data):
        """计算强度值统计信息"""
        stats_dict = {
            'count': len(intensity_data),
            'mean': float(np.mean(intensity_data)),
            'std': float(np.std(intensity_data)),
            'min': float(np.min(intensity_data)),
            'max': float(np.max(intensity_data)),
            'median': float(np.median(intensity_data)),
            'q25': float(np.percentile(intensity_data, 25)),
            'q75': float(np.percentile(intensity_data, 75)),
            'skewness': float(stats.skew(intensity_data)),
            'kurtosis': float(stats.kurtosis(intensity_data))
        }
        
        # 计算分布特征
        stats_dict['range'] = stats_dict['max'] - stats_dict['min']
        stats_dict['iqr'] = stats_dict['q75'] - stats_dict['q25']
        stats_dict['cv'] = stats_dict['std'] / stats_dict['mean'] if stats_dict['mean'] != 0 else 0
        
        # 检查是否在预期范围内
        stats_dict['in_expected_range'] = (
            stats_dict['min'] >= self.expected_intensity_min and 
            stats_dict['max'] <= self.expected_intensity_max
        )
        
        return stats_dict
    
    def detect_intensity_anomalies(self, spatial_data, intensity_data):
        """检测强度值异常"""
        anomalies = {
            'outliers': [],
            'spatial_anomalies': [],
            'statistical_anomalies': [],
            'total_count': 0
        }
        
        try:
            # 1. 统计异常检测（基于Z-score）
            if SCIPY_AVAILABLE:
                z_scores = np.abs(stats.zscore(intensity_data))
                statistical_outliers = np.where(z_scores > self.intensity_outlier_factor)[0]
                anomalies['statistical_anomalies'] = statistical_outliers.tolist()
            else:
                # 简化的Z-score计算
                mean_val = np.mean(intensity_data)
                std_val = np.std(intensity_data)
                if std_val > 0:
                    z_scores = np.abs((intensity_data - mean_val) / std_val)
                    statistical_outliers = np.where(z_scores > self.intensity_outlier_factor)[0]
                    anomalies['statistical_anomalies'] = statistical_outliers.tolist()

            # 2. 基于IQR的异常检测
            q25, q75 = np.percentile(intensity_data, [25, 75])
            iqr = q75 - q25
            lower_bound = q25 - 1.5 * iqr
            upper_bound = q75 + 1.5 * iqr
            iqr_outliers = np.where(
                (intensity_data < lower_bound) | (intensity_data > upper_bound))[0]
            anomalies['outliers'] = iqr_outliers.tolist()

            # 3. 空间异常检测（如果有足够数据）
            if len(intensity_data) > 100:
                spatial_anomalies = self.detect_spatial_anomalies(spatial_data, intensity_data)
                anomalies['spatial_anomalies'] = spatial_anomalies

            # 4. 机器学习异常检测（仅在sklearn可用时）
            if SKLEARN_AVAILABLE and len(intensity_data) > 50:
                ml_anomalies = self.detect_ml_anomalies(spatial_data, intensity_data)
                anomalies['ml_anomalies'] = ml_anomalies
            else:
                anomalies['ml_anomalies'] = []
            
            anomalies['total_count'] = len(set(
                anomalies['outliers'] + 
                anomalies['spatial_anomalies'] + 
                anomalies['statistical_anomalies']
            ))
            
            self.total_anomalies_detected += anomalies['total_count']
            
        except Exception as e:
            rospy.logerr("Error in anomaly detection: %s", str(e))
        
        return anomalies
    
    def detect_spatial_anomalies(self, spatial_data, intensity_data):
        """检测空间相关的强度异常"""
        spatial_anomalies = []
        
        try:
            # 构建KD树进行邻域搜索（仅在scipy可用时）
            if not SCIPY_AVAILABLE:
                return spatial_anomalies

            kdtree = KDTree(spatial_data)
            
            for i, (point, intensity) in enumerate(zip(spatial_data, intensity_data)):
                # 查找最近的k个邻居
                distances, indices = kdtree.query(point, k=min(10, len(spatial_data)))
                
                if len(indices) > 1:
                    neighbor_intensities = intensity_data[indices[1:]]  # 排除自己
                    neighbor_mean = np.mean(neighbor_intensities)
                    neighbor_std = np.std(neighbor_intensities)
                    
                    # 如果当前点的强度值与邻居差异过大
                    if neighbor_std > 0:
                        z_score = abs(intensity - neighbor_mean) / neighbor_std
                        if z_score > 2.5:  # 空间异常阈值
                            spatial_anomalies.append(i)
            
        except Exception as e:
            rospy.logerr("Error in spatial anomaly detection: %s", str(e))
        
        return spatial_anomalies
    
    def detect_ml_anomalies(self, spatial_data, intensity_data):
        """使用机器学习方法检测异常"""
        ml_anomalies = []
        
        try:
            if not SKLEARN_AVAILABLE:
                return ml_anomalies

            # 准备特征数据
            features = np.column_stack([spatial_data, intensity_data.reshape(-1, 1)])

            # 标准化特征
            features_scaled = self.scaler.fit_transform(features)

            # 异常检测
            anomaly_labels = self.anomaly_detector.fit_predict(features_scaled)
            ml_anomalies = np.where(anomaly_labels == -1)[0].tolist()
            
        except Exception as e:
            rospy.logerr("Error in ML anomaly detection: %s", str(e))
        
        return ml_anomalies
    
    def perform_spatial_clustering(self, spatial_data, intensity_data):
        """执行空间聚类分析"""
        clusters = {
            'cluster_labels': [],
            'cluster_stats': {},
            'n_clusters': 0
        }
        
        try:
            if not SKLEARN_AVAILABLE:
                return clusters

            # 使用DBSCAN进行聚类
            clustering = DBSCAN(
                eps=self.clustering_eps,
                min_samples=self.clustering_min_samples
            )

            cluster_labels = clustering.fit_predict(spatial_data)
            clusters['cluster_labels'] = cluster_labels.tolist()
            clusters['n_clusters'] = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
            
            # 计算每个聚类的强度统计
            cluster_stats = {}
            for cluster_id in set(cluster_labels):
                if cluster_id != -1:  # 排除噪声点
                    cluster_mask = cluster_labels == cluster_id
                    cluster_intensities = intensity_data[cluster_mask]
                    
                    cluster_stats[int(cluster_id)] = {
                        'size': int(np.sum(cluster_mask)),
                        'mean_intensity': float(np.mean(cluster_intensities)),
                        'std_intensity': float(np.std(cluster_intensities)),
                        'min_intensity': float(np.min(cluster_intensities)),
                        'max_intensity': float(np.max(cluster_intensities))
                    }
            
            clusters['cluster_stats'] = cluster_stats
            
        except Exception as e:
            rospy.logerr("Error in spatial clustering: %s", str(e))
        
        return clusters
    
    def analyze_temporal_patterns(self):
        """分析时序模式"""
        temporal_analysis = {
            'trend': 'stable',
            'seasonality': False,
            'drift': 0.0,
            'stability_score': 0.0
        }
        
        try:
            with self.data_lock:
                if len(self.intensity_history) < 10:
                    return temporal_analysis
                
                # 计算每帧的平均强度
                frame_means = [np.mean(frame_data) for frame_data in self.intensity_history]
                
                # 趋势分析
                if len(frame_means) > 5:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(
                        range(len(frame_means)), frame_means)
                    
                    temporal_analysis['drift'] = float(slope)
                    temporal_analysis['trend_strength'] = float(abs(r_value))
                    
                    if abs(slope) > 0.1:
                        temporal_analysis['trend'] = 'increasing' if slope > 0 else 'decreasing'
                    else:
                        temporal_analysis['trend'] = 'stable'
                
                # 稳定性评分
                if len(frame_means) > 1:
                    stability = 1.0 - (np.std(frame_means) / np.mean(frame_means))
                    temporal_analysis['stability_score'] = max(0.0, float(stability))
            
        except Exception as e:
            rospy.logerr("Error in temporal analysis: %s", str(e))
        
        return temporal_analysis
    
    def calculate_quality_score(self, analysis_results):
        """计算整体质量分数"""
        quality_score = 1.0
        
        try:
            # 基于异常比例的扣分
            if 'anomalies' in analysis_results:
                total_anomalies = analysis_results['anomalies']['total_count']
                total_points = analysis_results['statistics']['count']
                anomaly_ratio = total_anomalies / total_points if total_points > 0 else 0
                quality_score -= anomaly_ratio * 0.5
            
            # 基于强度范围的评分
            if 'statistics' in analysis_results:
                stats_data = analysis_results['statistics']
                if not stats_data['in_expected_range']:
                    quality_score -= 0.2
                
                # 基于变异系数的评分
                cv = stats_data['cv']
                if cv > 1.0:  # 变异系数过大
                    quality_score -= 0.2
                elif cv < 0.1:  # 变异系数过小（可能数据异常）
                    quality_score -= 0.1
            
            # 基于聚类质量的评分
            if 'clusters' in analysis_results:
                n_clusters = analysis_results['clusters']['n_clusters']
                if n_clusters == 0:  # 没有有效聚类
                    quality_score -= 0.1
            
            quality_score = max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            rospy.logerr("Error in quality score calculation: %s", str(e))
            quality_score = 0.5  # 默认中等质量
        
        return float(quality_score)
    
    def generate_recommendations(self, analysis_results):
        """生成改进建议"""
        recommendations = []
        
        try:
            # 基于异常检测结果的建议
            if 'anomalies' in analysis_results:
                anomaly_ratio = (analysis_results['anomalies']['total_count'] / 
                               analysis_results['statistics']['count'])
                
                if anomaly_ratio > 0.1:
                    recommendations.append("High anomaly rate detected. Consider intensity calibration.")
                elif anomaly_ratio > 0.05:
                    recommendations.append("Moderate anomalies detected. Monitor intensity quality.")
            
            # 基于统计特征的建议
            if 'statistics' in analysis_results:
                stats_data = analysis_results['statistics']
                
                if not stats_data['in_expected_range']:
                    recommendations.append("Intensity values outside expected range. Check sensor calibration.")
                
                if stats_data['cv'] > 1.0:
                    recommendations.append("High intensity variation. Consider environmental factors.")
                
                if abs(stats_data['skewness']) > 2.0:
                    recommendations.append("Highly skewed intensity distribution. Check for systematic bias.")
            
            # 基于质量分数的建议
            quality_score = analysis_results['quality_score']
            if quality_score < 0.5:
                recommendations.append("Low overall quality. Comprehensive system check recommended.")
            elif quality_score < 0.7:
                recommendations.append("Moderate quality issues. Fine-tuning recommended.")
            
            # 基于时序分析的建议
            if 'temporal_patterns' in analysis_results:
                temporal = analysis_results['temporal_patterns']
                if temporal['stability_score'] < 0.7:
                    recommendations.append("Intensity instability detected. Check sensor stability.")
                
                if abs(temporal['drift']) > 0.5:
                    recommendations.append("Intensity drift detected. Sensor recalibration may be needed.")
            
        except Exception as e:
            rospy.logerr("Error generating recommendations: %s", str(e))
        
        return recommendations
    
    def apply_intensity_corrections(self, spatial_data, intensity_data, analysis_results):
        """应用强度值修正"""
        corrected_intensity = intensity_data.copy()
        corrections_applied = 0
        
        try:
            # 1. 修正统计异常值
            if 'anomalies' in analysis_results:
                outliers = analysis_results['anomalies']['outliers']
                
                for outlier_idx in outliers:
                    if outlier_idx < len(corrected_intensity):
                        # 使用邻域中值替换异常值
                        corrected_value = self.get_neighborhood_median(
                            spatial_data, intensity_data, outlier_idx)
                        
                        if corrected_value is not None:
                            corrected_intensity[outlier_idx] = corrected_value
                            corrections_applied += 1
            
            # 2. 应用空间平滑
            if analysis_results['quality_score'] < 0.6:
                corrected_intensity = self.apply_spatial_smoothing(
                    spatial_data, corrected_intensity)
                corrections_applied += len(intensity_data)  # 全部点都被处理
            
            self.total_corrections_applied += corrections_applied
            
        except Exception as e:
            rospy.logerr("Error applying corrections: %s", str(e))
        
        return corrected_intensity
    
    def get_neighborhood_median(self, spatial_data, intensity_data, point_idx, k=5):
        """获取邻域中值"""
        try:
            kdtree = KDTree(spatial_data)
            distances, indices = kdtree.query(spatial_data[point_idx], k=k+1)
            
            # 排除自己，取邻居的中值
            neighbor_intensities = intensity_data[indices[1:]]
            return np.median(neighbor_intensities) if len(neighbor_intensities) > 0 else None
            
        except Exception as e:
            rospy.logerr("Error getting neighborhood median: %s", str(e))
            return None
    
    def apply_spatial_smoothing(self, spatial_data, intensity_data, radius=0.5):
        """应用空间平滑"""
        smoothed_intensity = intensity_data.copy()
        
        try:
            kdtree = KDTree(spatial_data)
            
            for i, point in enumerate(spatial_data):
                # 查找半径内的邻居
                indices = kdtree.query_ball_point(point, radius)
                
                if len(indices) > 1:
                    neighbor_intensities = intensity_data[indices]
                    # 使用加权平均（距离越近权重越大）
                    distances = [np.linalg.norm(spatial_data[idx] - point) for idx in indices]
                    weights = [1.0 / (d + 1e-6) for d in distances]
                    
                    weighted_avg = np.average(neighbor_intensities, weights=weights)
                    smoothed_intensity[i] = weighted_avg
            
        except Exception as e:
            rospy.logerr("Error in spatial smoothing: %s", str(e))
        
        return smoothed_intensity
    
    def publish_corrected_pointcloud(self, original_msg, spatial_data, corrected_intensity):
        """发布修正后的点云"""
        try:
            # 创建修正后的点云消息
            corrected_msg = PointCloud2()
            corrected_msg.header = original_msg.header
            corrected_msg.header.frame_id = original_msg.header.frame_id
            
            # 组合空间和强度数据
            corrected_points = []
            for i in range(len(spatial_data)):
                corrected_points.append([
                    spatial_data[i][0],  # x
                    spatial_data[i][1],  # y
                    spatial_data[i][2],  # z
                    corrected_intensity[i]  # corrected intensity
                ])
            
            # 转换为ROS消息
            corrected_msg = pc2.create_cloud_xyz32(
                corrected_msg.header, corrected_points)
            
            self.corrected_cloud_pub.publish(corrected_msg)
            
        except Exception as e:
            rospy.logerr("Error publishing corrected pointcloud: %s", str(e))
    
    def update_quality_metrics(self, analysis_results):
        """更新质量指标历史"""
        with self.data_lock:
            quality_metrics = {
                'timestamp': time.time(),
                'quality_score': analysis_results['quality_score'],
                'anomaly_count': analysis_results['anomalies']['total_count'],
                'total_points': analysis_results['statistics']['count'],
                'mean_intensity': analysis_results['statistics']['mean'],
                'std_intensity': analysis_results['statistics']['std']
            }
            
            self.quality_metrics_history.append(quality_metrics)
    
    def publish_analysis_report(self, event):
        """发布分析报告"""
        try:
            # 生成综合报告
            report = self.generate_comprehensive_report()
            
            # 发布报告
            report_msg = String()
            report_msg.data = json.dumps(report, indent=2)
            self.analysis_report_pub.publish(report_msg)
            
            # 打印摘要
            self.print_analysis_summary(report)
            
        except Exception as e:
            rospy.logerr("Error publishing analysis report: %s", str(e))
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        current_time = time.time()
        elapsed_time = current_time - self.analysis_start_time
        
        report = {
            'timestamp': current_time,
            'elapsed_time_seconds': elapsed_time,
            'processing_statistics': {
                'total_points_analyzed': self.total_points_analyzed,
                'total_anomalies_detected': self.total_anomalies_detected,
                'total_corrections_applied': self.total_corrections_applied,
                'anomaly_rate': (self.total_anomalies_detected / self.total_points_analyzed 
                               if self.total_points_analyzed > 0 else 0),
                'correction_rate': (self.total_corrections_applied / self.total_points_analyzed 
                                  if self.total_points_analyzed > 0 else 0)
            },
            'quality_trends': self.analyze_quality_trends(),
            'system_health': self.assess_system_health(),
            'recommendations': self.get_system_recommendations()
        }
        
        return report
    
    def analyze_quality_trends(self):
        """分析质量趋势"""
        trends = {
            'quality_trend': 'stable',
            'average_quality': 0.0,
            'quality_stability': 0.0
        }
        
        try:
            with self.data_lock:
                if len(self.quality_metrics_history) < 5:
                    return trends
                
                recent_qualities = [m['quality_score'] for m in self.quality_metrics_history]
                trends['average_quality'] = float(np.mean(recent_qualities))
                trends['quality_stability'] = float(1.0 - np.std(recent_qualities))
                
                # 趋势分析
                if len(recent_qualities) > 10:
                    slope, _, r_value, _, _ = stats.linregress(
                        range(len(recent_qualities)), recent_qualities)
                    
                    if abs(slope) > 0.01:
                        trends['quality_trend'] = 'improving' if slope > 0 else 'degrading'
                    else:
                        trends['quality_trend'] = 'stable'
                    
                    trends['trend_strength'] = float(abs(r_value))
        
        except Exception as e:
            rospy.logerr("Error analyzing quality trends: %s", str(e))
        
        return trends
    
    def assess_system_health(self):
        """评估系统健康状态"""
        health = {
            'overall_health': 'good',
            'data_flow_healthy': True,
            'quality_acceptable': True,
            'anomaly_rate_acceptable': True,
            'processing_stable': True
        }
        
        try:
            # 检查数据流
            health['data_flow_healthy'] = len(self.intensity_history) > 0
            
            # 检查质量
            if self.quality_metrics_history:
                avg_quality = np.mean([m['quality_score'] for m in self.quality_metrics_history])
                health['quality_acceptable'] = avg_quality > 0.6
            
            # 检查异常率
            if self.total_points_analyzed > 0:
                anomaly_rate = self.total_anomalies_detected / self.total_points_analyzed
                health['anomaly_rate_acceptable'] = anomaly_rate < 0.1
            
            # 总体健康评估
            health_checks = [
                health['data_flow_healthy'],
                health['quality_acceptable'],
                health['anomaly_rate_acceptable'],
                health['processing_stable']
            ]
            
            if all(health_checks):
                health['overall_health'] = 'excellent'
            elif sum(health_checks) >= 3:
                health['overall_health'] = 'good'
            elif sum(health_checks) >= 2:
                health['overall_health'] = 'fair'
            else:
                health['overall_health'] = 'poor'
        
        except Exception as e:
            rospy.logerr("Error assessing system health: %s", str(e))
        
        return health
    
    def get_system_recommendations(self):
        """获取系统级建议"""
        recommendations = []
        
        try:
            # 基于异常率的建议
            if self.total_points_analyzed > 0:
                anomaly_rate = self.total_anomalies_detected / self.total_points_analyzed
                
                if anomaly_rate > 0.15:
                    recommendations.append("Critical: Very high anomaly rate. Immediate sensor inspection required.")
                elif anomaly_rate > 0.1:
                    recommendations.append("Warning: High anomaly rate. Consider sensor calibration.")
                elif anomaly_rate > 0.05:
                    recommendations.append("Info: Moderate anomaly rate. Monitor system performance.")
            
            # 基于质量趋势的建议
            if self.quality_metrics_history:
                recent_qualities = [m['quality_score'] for m in self.quality_metrics_history]
                avg_quality = np.mean(recent_qualities)
                
                if avg_quality < 0.5:
                    recommendations.append("Critical: Low quality scores. System maintenance required.")
                elif avg_quality < 0.7:
                    recommendations.append("Warning: Moderate quality issues. Performance tuning recommended.")
            
            # 基于处理效率的建议
            if elapsed_time := time.time() - self.analysis_start_time > 300:  # 5分钟后
                processing_rate = self.total_points_analyzed / elapsed_time
                if processing_rate < 1000:  # 每秒少于1000点
                    recommendations.append("Info: Low processing rate. Consider system optimization.")
        
        except Exception as e:
            rospy.logerr("Error generating system recommendations: %s", str(e))
        
        return recommendations
    
    def print_analysis_summary(self, report):
        """打印分析摘要"""
        rospy.loginfo("=== Advanced Intensity Analysis Report ===")
        
        # 处理统计
        proc_stats = report['processing_statistics']
        rospy.loginfo("Processing Statistics:")
        rospy.loginfo("  Points analyzed: %d", proc_stats['total_points_analyzed'])
        rospy.loginfo("  Anomalies detected: %d (%.2f%%)", 
                     proc_stats['total_anomalies_detected'],
                     proc_stats['anomaly_rate'] * 100)
        rospy.loginfo("  Corrections applied: %d (%.2f%%)",
                     proc_stats['total_corrections_applied'],
                     proc_stats['correction_rate'] * 100)
        
        # 质量趋势
        quality_trends = report['quality_trends']
        rospy.loginfo("Quality Trends:")
        rospy.loginfo("  Average quality: %.3f", quality_trends['average_quality'])
        rospy.loginfo("  Quality trend: %s", quality_trends['quality_trend'])
        rospy.loginfo("  Quality stability: %.3f", quality_trends['quality_stability'])
        
        # 系统健康
        health = report['system_health']
        rospy.loginfo("System Health: %s", health['overall_health'].upper())
        
        # 建议
        if report['recommendations']:
            rospy.loginfo("Recommendations:")
            for rec in report['recommendations']:
                rospy.loginfo("  - %s", rec)
        
        rospy.loginfo("=" * 50)

def main():
    try:
        analyzer = AdvancedIntensityAnalyzer()
        
        rospy.loginfo("Advanced Intensity Analyzer running...")
        rospy.loginfo("Performing deep intensity analysis and intelligent corrections")
        
        rospy.spin()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("Advanced Intensity Analyzer stopped")
    except Exception as e:
        rospy.logerr("Advanced Intensity Analyzer exception: %s", str(e))

if __name__ == '__main__':
    main()
