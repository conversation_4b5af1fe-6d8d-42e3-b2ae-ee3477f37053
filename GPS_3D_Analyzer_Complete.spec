# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

block_cipher = None

# 添加隐藏导入
hiddenimports = [
    'bagpy',
    'bagpy.bagreader',
    'bagpy.bagpy',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    'pandas',
    'numpy',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'threading',
    'collections',
    'math',
    'time',
    'locale',
    'pathlib',
    'pkg_resources',
]

# 数据文件 - 包含bagpy需要的所有文件
datas = [
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\version', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\README.md', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\version', r'bagpy'),
]

# 二进制文件
binaries = []

a = Analysis(
    ['gps_gui_analyzer_fixed.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[r'hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GPS_3D_Analyzer_Complete',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
