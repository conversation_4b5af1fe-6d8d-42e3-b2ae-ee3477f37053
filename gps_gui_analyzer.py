#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analyzer GUI
Complete GUI application for GPS trajectory analysis with 3D visualization
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pandas as pd
import os
import sys
import threading
import time
from collections import defaultdict
import math

# Import GPS analysis modules
try:
    import bagpy
    USE_BAGPY = True
except ImportError:
    USE_BAGPY = False

class GPSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPS 3D Trajectory Analyzer")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # Variables
        self.bag_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.gps_topic = tk.StringVar(value="/rtk/gnss")
        self.is_processing = False
        self.gps_data = []
        self.trajectory_points = []
        
        # GPS quality settings
        self.quality_colors = {
            -1: 'red',      # NO_FIX
            0:  'green',    # RTK_FIXED
            1:  'blue',     # SBAS_FIX
            2:  'yellow',   # GBAS_FIX
            3:  'purple',   # OTHER
        }
        self.quality_names = {
            -1: "NO_FIX",
            0:  "RTK_FIXED",
            1:  "SBAS_FIX", 
            2:  "GBAS_FIX",
            3:  "OTHER"
        }
        
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI layout"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="GPS 3D Trajectory Analyzer", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control panel (left side)
        self.setup_control_panel(main_frame)
        
        # Visualization and analysis panel (right side)
        self.setup_visualization_panel(main_frame)
        
        # Terminal output (bottom)
        self.setup_terminal_panel(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="Control Panel", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # File selection
        ttk.Label(control_frame, text="Bag File:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(control_frame, textvariable=self.bag_file_path, width=40).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="Browse", command=self.browse_bag_file).grid(row=0, column=2, padx=5)
        
        # GPS Topic
        ttk.Label(control_frame, text="GPS Topic:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(control_frame, textvariable=self.gps_topic, width=40).grid(row=1, column=1, padx=5)
        
        # Output file
        ttk.Label(control_frame, text="Output File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(control_frame, textvariable=self.output_file_path, width=40).grid(row=2, column=1, padx=5)
        ttk.Button(control_frame, text="Browse", command=self.browse_output_file).grid(row=2, column=2, padx=5)
        
        # Control buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        self.start_button = ttk.Button(button_frame, text="Start Analysis", 
                                      command=self.start_analysis, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop Analysis", 
                                     command=self.stop_analysis, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text="Ready", foreground='green')
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5)
        
    def setup_visualization_panel(self, parent):
        """Setup visualization and analysis panel"""
        viz_frame = ttk.LabelFrame(parent, text="3D Visualization & Analysis", padding="10")
        viz_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        viz_frame.columnconfigure(0, weight=2)
        viz_frame.columnconfigure(1, weight=1)
        viz_frame.rowconfigure(1, weight=1)

        # 3D Plot frame with controls
        plot_container = ttk.Frame(viz_frame)
        plot_container.grid(row=0, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        plot_container.columnconfigure(0, weight=1)
        plot_container.rowconfigure(1, weight=1)

        # 3D Control buttons frame
        control_frame = ttk.Frame(plot_container)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # Control buttons
        ttk.Button(control_frame, text="复位视角", command=self.reset_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="顶视图", command=self.top_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="侧视图", command=self.side_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="前视图", command=self.front_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="等轴显示", command=self.equal_axis).pack(side=tk.LEFT, padx=2)

        # Zoom control frame
        zoom_frame = ttk.Frame(control_frame)
        zoom_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Label(zoom_frame, text="缩放:").pack(side=tk.LEFT)
        ttk.Button(zoom_frame, text="+", command=self.zoom_in, width=3).pack(side=tk.LEFT, padx=1)
        ttk.Button(zoom_frame, text="-", command=self.zoom_out, width=3).pack(side=tk.LEFT, padx=1)

        # 3D Plot frame
        plot_frame = ttk.Frame(plot_container)
        plot_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Create matplotlib figure
        self.fig = plt.Figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111, projection='3d')
        self.ax.set_title('GPS 3D Trajectory', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X (meters)', fontsize=12)
        self.ax.set_ylabel('Y (meters)', fontsize=12)
        self.ax.set_zlabel('Z (meters)', fontsize=12)

        # Enable mouse interaction
        self.ax.mouse_init()

        # Canvas for matplotlib
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Bind mouse wheel for zooming
        self.canvas.get_tk_widget().bind("<MouseWheel>", self.on_mouse_wheel)
        self.canvas.get_tk_widget().bind("<Button-4>", self.on_mouse_wheel)  # Linux
        self.canvas.get_tk_widget().bind("<Button-5>", self.on_mouse_wheel)  # Linux

        # Store initial view parameters
        self.initial_elev = 30
        self.initial_azim = -60
        self.initial_dist = 10

        # Analysis results frame
        analysis_frame = ttk.LabelFrame(viz_frame, text="分析结果", padding="10")
        analysis_frame.grid(row=0, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(0, weight=1)

        # Analysis text widget with Chinese font
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, width=35, height=30,
                                                      font=('Microsoft YaHei', 9), wrap=tk.WORD)
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_terminal_panel(self, parent):
        """Setup terminal output panel"""
        terminal_frame = ttk.LabelFrame(parent, text="Terminal Output", padding="10")
        terminal_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        terminal_frame.columnconfigure(0, weight=1)
        terminal_frame.rowconfigure(0, weight=1)
        
        # Terminal text widget
        self.terminal_text = scrolledtext.ScrolledText(terminal_frame, height=8, 
                                                      font=('Courier', 9), bg='black', fg='white')
        self.terminal_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """Add message to terminal output"""
        timestamp = time.strftime("%H:%M:%S")
        self.terminal_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.terminal_text.see(tk.END)
        self.root.update_idletasks()
        
    def browse_bag_file(self):
        """Browse for bag file"""
        filename = filedialog.askopenfilename(
            title="Select ROS Bag File",
            filetypes=[("Bag files", "*.bag"), ("All files", "*.*")]
        )
        if filename:
            self.bag_file_path.set(filename)
            # Auto-generate output filename
            base_name = os.path.splitext(os.path.basename(filename))[0]
            output_path = os.path.join(os.path.dirname(filename), f"{base_name}_trajectory.txt")
            self.output_file_path.set(output_path)
            
    def browse_output_file(self):
        """Browse for output file"""
        filename = filedialog.asksaveasfilename(
            title="Save Trajectory File",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.output_file_path.set(filename)
            
    def start_analysis(self):
        """Start GPS analysis in separate thread"""
        if not self.bag_file_path.get():
            messagebox.showerror("Error", "Please select a bag file")
            return
            
        if not self.output_file_path.get():
            messagebox.showerror("Error", "Please specify output file path")
            return
            
        self.is_processing = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress.start()
        self.status_label.config(text="Processing...", foreground='orange')
        
        # Clear previous results
        self.ax.clear()
        self.canvas.draw()
        self.analysis_text.delete(1.0, tk.END)
        self.terminal_text.delete(1.0, tk.END)
        
        # Start processing in separate thread
        self.processing_thread = threading.Thread(target=self.process_gps_data)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
    def stop_analysis(self):
        """Stop GPS analysis"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Stopped", foreground='red')
        self.log_message("Analysis stopped by user")

    def process_gps_data(self):
        """Process GPS data in background thread"""
        try:
            self.log_message("Starting GPS data extraction...")

            # Extract GPS data
            if not self.extract_gps_from_bag():
                return

            if not self.is_processing:
                return

            self.log_message("Converting to local coordinates...")
            self.convert_to_local_coordinates()

            if not self.is_processing:
                return

            self.log_message("Analyzing GPS quality...")
            analysis = self.analyze_gps_quality()

            if not self.is_processing:
                return

            self.log_message("Creating 3D visualization...")
            self.root.after(0, self.update_visualization, analysis)

            if not self.is_processing:
                return

            self.log_message("Saving trajectory to file...")
            self.save_trajectory_file()

            # Complete
            self.root.after(0, self.analysis_complete)

        except Exception as e:
            self.log_message(f"Error during processing: {str(e)}")
            self.root.after(0, self.analysis_error, str(e))

    def extract_gps_from_bag(self):
        """Extract GPS data from bag file"""
        if not USE_BAGPY:
            self.log_message("Error: bagpy not available. Please install: pip install bagpy")
            return False

        try:
            bag_file = self.bag_file_path.get()
            topic_name = self.gps_topic.get()

            self.log_message(f"Reading bag file: {os.path.basename(bag_file)}")
            bag = bagpy.bagreader(bag_file)

            self.log_message(f"Extracting topic: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            df = pd.read_csv(gps_csv)

            self.gps_data = []
            gps_count = 0

            for index, row in df.iterrows():
                if not self.is_processing:
                    return False

                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 1000 == 0:
                        self.log_message(f"Extracted {gps_count} GPS points...")

                except (ValueError, KeyError):
                    continue

            self.log_message(f"GPS data extraction completed: {len(self.gps_data)} points")
            return len(self.gps_data) > 0

        except Exception as e:
            self.log_message(f"Failed to read bag file: {str(e)}")
            return False

    def convert_to_local_coordinates(self):
        """Convert GPS coordinates to local coordinate system"""
        if not self.gps_data:
            return

        # Find origin
        for gps in self.gps_data:
            if gps['status'] >= 0:
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude']
                self.origin_alt = gps['altitude']
                break

        if self.origin_lat is None:
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return

        self.log_message(f"GPS origin: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}")

        # Convert to local coordinates
        self.trajectory_points = []
        for i, gps in enumerate(self.gps_data):
            if not self.is_processing:
                return

            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt

            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff

            point_info = {
                'frame_id': i + 1,  # Frame number starting from 1
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)

    def analyze_gps_quality(self):
        """Analyze GPS quality statistics"""
        status_count = defaultdict(int)
        total_points = len(self.gps_data)

        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3
            status_count[status] += 1

        # Calculate trajectory statistics
        trajectory_length = 0.0
        start_end_distance = 0.0
        closure_error = 0.0

        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])

            # Calculate trajectory length
            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                trajectory_length += np.linalg.norm(p2 - p1)

            # Calculate start-end distance
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0

        return {
            'status_count': status_count,
            'total_points': total_points,
            'trajectory_length': trajectory_length,
            'start_end_distance': start_end_distance,
            'closure_error': closure_error,
            'positions': np.array([p['position'] for p in self.trajectory_points]) if self.trajectory_points else None
        }

    def update_visualization(self, analysis):
        """Update 3D visualization and analysis results"""
        try:
            # Clear previous plot
            self.ax.clear()

            # Group by GPS status
            status_groups = defaultdict(list)
            for i, point in enumerate(self.trajectory_points):
                status = point['status']
                if status not in self.quality_names:
                    status = 3
                status_groups[status].append(i)

            # Plot 3D trajectory with different colors
            for status, indices in status_groups.items():
                if len(indices) < 2:
                    continue

                points = [self.trajectory_points[i]['position'] for i in indices]
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                z_coords = [p[2] for p in points]

                color = self.quality_colors[status]
                quality_name = self.quality_names[status]

                # RTK fixed: thick solid line, others: dashed line
                if status == 0:  # RTK_FIXED
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=3, linestyle='-',
                               alpha=0.9, label=f'{quality_name}')
                else:
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=2, linestyle='--',
                               alpha=0.8, label=f'{quality_name}')

            # Mark start and end points
            if self.trajectory_points:
                start_pos = self.trajectory_points[0]['position']
                end_pos = self.trajectory_points[-1]['position']

                self.ax.scatter(start_pos[0], start_pos[1], start_pos[2],
                              c='lime', s=100, marker='o', edgecolors='black',
                              label='Start', alpha=1.0, zorder=10)
                self.ax.scatter(end_pos[0], end_pos[1], end_pos[2],
                              c='red', s=100, marker='s', edgecolors='black',
                              label='End', alpha=1.0, zorder=10)

            # Set labels and title
            self.ax.set_xlabel('X (meters)')
            self.ax.set_ylabel('Y (meters)')
            self.ax.set_zlabel('Z (meters)')
            self.ax.set_title('GPS 3D Trajectory')
            self.ax.legend()
            self.ax.grid(True, alpha=0.3)

            # Set equal aspect ratio
            if analysis['positions'] is not None:
                positions = analysis['positions']
                max_range = np.array([positions[:, 0].max() - positions[:, 0].min(),
                                    positions[:, 1].max() - positions[:, 1].min(),
                                    positions[:, 2].max() - positions[:, 2].min()]).max() / 2.0

                mid_x = (positions[:, 0].max() + positions[:, 0].min()) * 0.5
                mid_y = (positions[:, 1].max() + positions[:, 1].min()) * 0.5
                mid_z = (positions[:, 2].max() + positions[:, 2].min()) * 0.5

                self.ax.set_xlim(mid_x - max_range, mid_x + max_range)
                self.ax.set_ylim(mid_y - max_range, mid_y + max_range)
                self.ax.set_zlim(mid_z - max_range, mid_z + max_range)

            # Update canvas
            self.canvas.draw()

            # Update analysis results
            self.update_analysis_text(analysis)

        except Exception as e:
            self.log_message(f"Error updating visualization: {str(e)}")

    def update_analysis_text(self, analysis):
        """Update analysis results text"""
        self.analysis_text.delete(1.0, tk.END)

        # Format analysis text
        text_lines = []
        text_lines.append("=" * 40)
        text_lines.append("GPS QUALITY ANALYSIS")
        text_lines.append("=" * 40)
        text_lines.append("")

        # GPS Quality Statistics
        text_lines.append("GPS Quality Distribution:")
        text_lines.append("-" * 30)
        total_points = analysis['total_points']

        for status, count in sorted(analysis['status_count'].items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "UNKNOWN")
            color = self.quality_colors.get(status, 'gray')
            line_style = "Solid" if status == 0 else "Dashed"
            text_lines.append(f"{quality_name:12}: {count:6,d} pts")
            text_lines.append(f"{'':14} ({percentage:5.1f}%)")
            text_lines.append(f"{'':14} {color.title()} {line_style}")
            text_lines.append("")

        text_lines.append("=" * 40)
        text_lines.append("TRAJECTORY STATISTICS")
        text_lines.append("=" * 40)
        text_lines.append("")

        # Trajectory Statistics
        text_lines.append(f"Total Points:      {total_points:,}")
        text_lines.append(f"Trajectory Length: {analysis['trajectory_length']:.1f} m")
        text_lines.append(f"Start-End Distance: {analysis['start_end_distance']:.1f} m")
        text_lines.append(f"Closure Error:     {analysis['closure_error']:.3f}%")
        text_lines.append("")

        # Coordinate ranges
        if analysis['positions'] is not None:
            positions = analysis['positions']
            text_lines.append("COORDINATE RANGES:")
            text_lines.append("-" * 20)
            text_lines.append(f"X: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} m")
            text_lines.append(f"Y: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} m")
            text_lines.append(f"Z: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} m")
            text_lines.append("")

        # GPS Origin
        text_lines.append("GPS ORIGIN:")
        text_lines.append("-" * 12)
        text_lines.append(f"Lat: {self.origin_lat:.8f}°")
        text_lines.append(f"Lon: {self.origin_lon:.8f}°")
        text_lines.append(f"Alt: {self.origin_alt:.3f} m")
        text_lines.append("")

        # Quality Assessment
        text_lines.append("QUALITY ASSESSMENT:")
        text_lines.append("-" * 18)
        if analysis['closure_error'] < 0.1:
            text_lines.append("Closure: EXCELLENT")
        elif analysis['closure_error'] < 0.5:
            text_lines.append("Closure: GOOD")
        elif analysis['closure_error'] < 1.0:
            text_lines.append("Closure: FAIR")
        else:
            text_lines.append("Closure: POOR")

        rtk_percentage = analysis['status_count'].get(0, 0) / total_points * 100
        if rtk_percentage > 95:
            text_lines.append("GPS Quality: EXCELLENT")
        elif rtk_percentage > 80:
            text_lines.append("GPS Quality: GOOD")
        elif rtk_percentage > 50:
            text_lines.append("GPS Quality: FAIR")
        else:
            text_lines.append("GPS Quality: POOR")

        # Output file info
        text_lines.append("")
        text_lines.append("OUTPUT FILE:")
        text_lines.append("-" * 12)
        output_file = self.output_file_path.get()
        text_lines.append(f"Path: {os.path.basename(output_file)}")
        text_lines.append(f"Format: Frame_ID X Y Z")
        text_lines.append(f"Points: {len(self.trajectory_points)}")

        # Insert text
        analysis_text = "\n".join(text_lines)
        self.analysis_text.insert(1.0, analysis_text)

    def save_trajectory_file(self):
        """Save trajectory to text file in format: Frame_ID X Y Z"""
        try:
            output_file = self.output_file_path.get()

            with open(output_file, 'w') as f:
                # Write header
                f.write("# GPS Trajectory Data\n")
                f.write("# Format: Frame_ID X Y Z\n")
                f.write("# Coordinate system: Local ENU (East-North-Up)\n")
                f.write(f"# Origin: lat={self.origin_lat:.8f}, lon={self.origin_lon:.8f}, alt={self.origin_alt:.3f}\n")
                f.write("# Units: meters\n")
                f.write("#\n")

                # Write trajectory points
                for point in self.trajectory_points:
                    frame_id = point['frame_id']
                    x, y, z = point['position']
                    f.write(f"{frame_id} {x:.6f} {y:.6f} {z:.6f}\n")

            self.log_message(f"Trajectory saved to: {os.path.basename(output_file)}")
            self.log_message(f"Total points saved: {len(self.trajectory_points)}")

        except Exception as e:
            self.log_message(f"Error saving trajectory file: {str(e)}")

    def analysis_complete(self):
        """Handle analysis completion"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Analysis Complete", foreground='green')
        self.log_message("GPS trajectory analysis completed successfully!")

        # Show completion message
        messagebox.showinfo("Analysis Complete",
                           f"GPS trajectory analysis completed!\n\n"
                           f"Points processed: {len(self.trajectory_points):,}\n"
                           f"Output file: {os.path.basename(self.output_file_path.get())}")

    def analysis_error(self, error_msg):
        """Handle analysis error"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="Error", foreground='red')
        messagebox.showerror("Analysis Error", f"An error occurred during analysis:\n\n{error_msg}")

def main():
    """Main function to run the GUI application"""
    # Check dependencies
    if not USE_BAGPY:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Missing Dependency",
                           "bagpy is required but not installed.\n\n"
                           "Please install it using:\n"
                           "pip install bagpy")
        return

    # Create and run GUI
    root = tk.Tk()
    app = GPSAnalyzerGUI(root)

    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
