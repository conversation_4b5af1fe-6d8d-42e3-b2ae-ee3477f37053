# GPS 3D Trajectory Analyzer - Build Instructions

## 📋 Overview
This document provides complete instructions for building the GPS 3D Trajectory Analyzer into a standalone Windows 10 64-bit executable.

## 🎯 Features
- **GUI Interface**: Complete tkinter-based GUI with file selection, 3D visualization, and analysis results
- **3D Trajectory Visualization**: Interactive 3D plot with GPS quality color coding
- **Real-time Terminal Output**: Complete terminal display showing processing progress
- **File Export**: Save trajectory as text file in format: Frame_ID X Y Z
- **Analysis Results**: Detailed GPS quality analysis and trajectory statistics

## 📁 Project Files

### Core Application
- `gps_gui_analyzer.py` - Main GUI application
- `requirements.txt` - Python dependencies
- `gps_analyzer.spec` - PyInstaller specification file

### Build Tools
- `build_exe.py` - Automated build script
- `test_gui.py` - Dependency and functionality test script

### Documentation
- `BUILD_INSTRUCTIONS.md` - This file
- `README.txt` - End-user documentation (generated during build)

## 🔧 Prerequisites

### System Requirements
- Windows 10 64-bit
- Python 3.7 or higher
- Minimum 4GB RAM
- Graphics card supporting OpenGL

### Python Dependencies
```
bagpy>=0.5.0
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
pyinstaller>=5.0.0
```

## 🚀 Build Process

### Method 1: Automated Build (Recommended)

1. **Install Dependencies**
   ```cmd
   pip install -r requirements.txt
   ```

2. **Run Build Script**
   ```cmd
   python build_exe.py
   ```

3. **Find Output**
   - Executable: `GPS_3D_Analyzer_Release/GPS_3D_Analyzer.exe`
   - Documentation: `GPS_3D_Analyzer_Release/README.txt`

### Method 2: Manual Build

1. **Install Dependencies**
   ```cmd
   pip install -r requirements.txt
   ```

2. **Test Dependencies**
   ```cmd
   python test_gui.py
   ```

3. **Build with PyInstaller**
   ```cmd
   pyinstaller gps_analyzer.spec
   ```

4. **Package Release**
   ```cmd
   mkdir GPS_3D_Analyzer_Release
   copy dist\GPS_3D_Analyzer.exe GPS_3D_Analyzer_Release\
   ```

### Method 3: Direct PyInstaller Command

```cmd
pyinstaller --onefile --windowed --name=GPS_3D_Analyzer ^
    --hidden-import=bagpy ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=mpl_toolkits.mplot3d ^
    --collect-all=matplotlib ^
    --collect-all=bagpy ^
    gps_gui_analyzer.py
```

## 🧪 Testing

### Pre-build Testing
```cmd
# Test all dependencies
python test_gui.py

# Test GUI application
python gps_gui_analyzer.py
```

### Post-build Testing
1. Run `GPS_3D_Analyzer.exe`
2. Select a test bag file
3. Verify 3D visualization works
4. Check terminal output
5. Verify file export functionality

## 📦 Distribution Package

The final distribution package includes:

```
GPS_3D_Analyzer_Release/
├── GPS_3D_Analyzer.exe    # Main executable (15-50MB)
└── README.txt             # User documentation
```

## 🎮 Usage Instructions

### For End Users

1. **Launch Application**
   - Double-click `GPS_3D_Analyzer.exe`
   - No installation required

2. **Select Input File**
   - Click "Browse" next to "Bag File"
   - Select your ROS bag file (.bag)

3. **Configure Settings**
   - GPS Topic: Default `/rtk/gnss` (change if needed)
   - Output File: Auto-generated or browse to select

4. **Start Analysis**
   - Click "Start Analysis"
   - Monitor progress in terminal output
   - View 3D trajectory and analysis results

5. **Results**
   - 3D visualization with quality color coding
   - Detailed analysis in right panel
   - Trajectory saved to text file

### GPS Quality Color Coding
- **Green Solid**: RTK Fixed (Highest accuracy)
- **Blue Dashed**: SBAS Fix
- **Yellow Dashed**: GBAS Fix
- **Red Dashed**: No Fix
- **Purple Dashed**: Other

### Output File Format
```
# GPS Trajectory Data
# Format: Frame_ID X Y Z
# Coordinate system: Local ENU (East-North-Up)
# Origin: lat=30.73767602, lon=103.97089927, alt=522.005
# Units: meters
#
1 0.000000 0.000000 0.000000
2 0.123456 0.234567 0.012345
3 0.246912 0.469134 0.024690
...
```

## 🔍 Troubleshooting

### Common Build Issues

1. **Missing Dependencies**
   ```
   Error: ModuleNotFoundError: No module named 'bagpy'
   Solution: pip install bagpy
   ```

2. **PyInstaller Not Found**
   ```
   Error: 'pyinstaller' is not recognized
   Solution: pip install pyinstaller
   ```

3. **Large Executable Size**
   - Normal size: 15-50MB
   - Includes Python runtime and all dependencies
   - Use `--exclude-module` to reduce size if needed

### Runtime Issues

1. **GUI Not Showing**
   - Check Windows Defender/Antivirus
   - Run as Administrator if needed

2. **Bag File Reading Error**
   - Ensure bag file is valid
   - Check GPS topic name
   - Verify file permissions

3. **3D Visualization Issues**
   - Update graphics drivers
   - Check OpenGL support
   - Try running on different machine

## 📊 Performance Notes

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Any modern x64 processor
- **GPU**: Basic OpenGL support for 3D visualization
- **Storage**: 100MB free space

### Processing Performance
- **Small datasets** (<1000 points): <10 seconds
- **Medium datasets** (1000-10000 points): 10-60 seconds
- **Large datasets** (>10000 points): 1-5 minutes

## 🔒 Security Notes

### Antivirus Considerations
- Some antivirus software may flag PyInstaller executables
- Add exception for the executable if needed
- This is a false positive due to Python runtime bundling

### File Permissions
- Application needs read access to bag files
- Application needs write access to output directory
- No network access required

## 📞 Support

### For Build Issues
1. Check Python version (3.7+ required)
2. Verify all dependencies installed
3. Run test script: `python test_gui.py`
4. Check build logs for specific errors

### For Runtime Issues
1. Check terminal output for error messages
2. Verify input file format and permissions
3. Test with smaller bag files first
4. Check system requirements

## 🎯 Advanced Configuration

### Custom PyInstaller Options
```python
# In gps_analyzer.spec, modify these options:
exe = EXE(
    # ... other options ...
    console=True,           # Show console for debugging
    debug=True,            # Enable debug mode
    upx=False,             # Disable UPX compression
)
```

### Adding Custom Icons
1. Create `icon.ico` file (256x256 recommended)
2. Add to PyInstaller command: `--icon=icon.ico`
3. Update spec file: `icon='icon.ico'`

### Reducing Executable Size
```cmd
# Exclude unnecessary modules
pyinstaller --exclude-module=PIL --exclude-module=scipy gps_gui_analyzer.py
```

This completes the comprehensive build instructions for the GPS 3D Trajectory Analyzer!
