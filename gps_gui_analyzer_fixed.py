#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS 3D Trajectory Analyzer GUI - Fixed Version
Complete GUI application with dark gray theme, fixed encoding, and stable 3D controls
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pandas as pd
import os
import sys
import threading
import time
from collections import defaultdict
import math

# 设置编码和字体
import locale
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 设置深灰色主题
plt.style.use('dark_background')
matplotlib.rcParams['figure.facecolor'] = '#2b2b2b'
matplotlib.rcParams['axes.facecolor'] = '#2b2b2b'

# 设置系统编码
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

class GPSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPS 3D 轨迹分析器")
        self.root.geometry("1600x1000")
        
        # 设置字体
        self.setup_fonts()
        
        # 设置深灰色主题
        self.setup_dark_theme()
        
        # Variables
        self.bag_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.gps_topic = tk.StringVar(value="/rtk/gnss")
        self.is_processing = False
        self.gps_data = []
        self.trajectory_points = []
        
        # 3D视图控制变量
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.mouse_pressed = False
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        # GPS quality settings
        self.quality_colors = {
            -1: '#FF6B6B',  # 红色 - NO_FIX
            0:  '#4ECDC4',  # 青绿色 - RTK_FIXED
            1:  '#45B7D1',  # 蓝色 - SBAS_FIX
            2:  '#FFA07A',  # 橙色 - GBAS_FIX
            3:  '#DDA0DD',  # 紫色 - OTHER
        }
        self.quality_names = {
            -1: "无定位",
            0:  "RTK固定解",
            1:  "SBAS定位", 
            2:  "GBAS定位",
            3:  "其他"
        }
        
        self.origin_lat = None
        self.origin_lon = None
        self.origin_alt = None
        
        self.setup_gui()
        
    def setup_fonts(self):
        """设置字体以避免乱码"""
        try:
            # 设置默认字体
            self.default_font = ('Microsoft YaHei', 10)
            self.title_font = ('Microsoft YaHei', 16, 'bold')
            self.label_font = ('Microsoft YaHei', 9)
            self.button_font = ('Microsoft YaHei', 9)
        except:
            # 备用字体
            self.default_font = ('Arial', 10)
            self.title_font = ('Arial', 16, 'bold')
            self.label_font = ('Arial', 9)
            self.button_font = ('Arial', 9)
        
    def setup_dark_theme(self):
        """设置深灰色主题"""
        # 深灰色主题配色
        self.colors = {
            'bg': '#2E2E2E',           # 主背景 - 深灰
            'fg': '#00C851',           # 主前景 - 深绿色
            'select_bg': '#404040',    # 选中背景 - 深灰
            'select_fg': '#00C851',    # 选中前景 - 深绿色
            'button_bg': '#404040',    # 按钮背景
            'button_fg': '#00C851',    # 按钮前景 - 深绿色
            'entry_bg': '#3C3C3C',     # 输入框背景 - 深灰
            'entry_fg': '#00C851',     # 输入框前景 - 深绿色
            'frame_bg': '#353535',     # 框架背景
            'accent': '#0078D4',       # 强调色 - 蓝色
            'success': '#00C851',      # 成功色 - 绿色
            'warning': '#FF8800',      # 警告色 - 橙色
            'error': '#FF4444',        # 错误色 - 红色
            'terminal_bg': '#1E1E1E',  # 终端背景
            'terminal_fg': '#00FF41',  # 终端前景 - 绿色
            'text_green': '#00C851',   # 文字绿色
        }
        
        # 配置根窗口
        self.root.configure(bg=self.colors['bg'])

        # 设置tkinter默认选项以确保深色主题
        self.root.option_add('*TkFDialog*foreground', self.colors['text_green'])
        self.root.option_add('*TkFDialog*background', self.colors['bg'])
        self.root.option_add('*TkChooseDir*foreground', self.colors['text_green'])
        self.root.option_add('*TkChooseDir*background', self.colors['bg'])

        # 设置文件对话框的深色主题
        self.root.option_add('*Dialog*foreground', self.colors['text_green'])
        self.root.option_add('*Dialog*background', self.colors['bg'])
        self.root.option_add('*Dialog*selectBackground', self.colors['select_bg'])
        self.root.option_add('*Dialog*selectForeground', self.colors['text_green'])
        
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置各种控件样式
        style.configure('Dark.TFrame', 
                       background=self.colors['frame_bg'],
                       relief='flat')
        
        style.configure('Dark.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['text_green'],  # 深绿色文字
                       font=self.label_font)

        style.configure('Dark.TButton',
                       background=self.colors['button_bg'],
                       foreground=self.colors['text_green'],  # 深绿色文字
                       font=self.button_font,
                       relief='flat',
                       borderwidth=1,
                       focuscolor='none')

        # 按钮悬停效果
        style.map('Dark.TButton',
                 background=[('active', '#505050'),
                           ('pressed', '#606060')],
                 foreground=[('active', self.colors['text_green']),
                           ('pressed', self.colors['text_green'])])

        style.configure('Dark.TEntry',
                       background=self.colors['entry_bg'],
                       foreground=self.colors['text_green'],  # 深绿色文字
                       font=self.default_font,
                       relief='flat',
                       borderwidth=1,
                       selectbackground=self.colors['select_bg'],  # 深色选择背景
                       selectforeground=self.colors['text_green'],  # 深绿色选择文字
                       insertcolor=self.colors['text_green'])  # 深绿色光标

        # 配置Entry的焦点状态
        style.map('Dark.TEntry',
                 focuscolor=[('!focus', 'none')],
                 selectbackground=[('focus', self.colors['select_bg'])],
                 selectforeground=[('focus', self.colors['text_green'])],
                 bordercolor=[('focus', self.colors['text_green'])])
        
        style.configure('Accent.TButton', 
                       background=self.colors['accent'], 
                       foreground=self.colors['fg'],
                       font=self.button_font,
                       relief='flat',
                       borderwidth=1)
        
        style.configure('Success.TLabel', 
                       background=self.colors['bg'], 
                       foreground=self.colors['success'],
                       font=self.label_font)
        
        style.configure('Warning.TLabel', 
                       background=self.colors['bg'], 
                       foreground=self.colors['warning'],
                       font=self.label_font)
        
        style.configure('Error.TLabel', 
                       background=self.colors['bg'], 
                       foreground=self.colors['error'],
                       font=self.label_font)
        
        # 配置LabelFrame
        style.configure('Dark.TLabelframe',
                       background=self.colors['bg'],
                       foreground=self.colors['text_green'],  # 深绿色文字
                       relief='flat',
                       borderwidth=1)

        style.configure('Dark.TLabelframe.Label',
                       background=self.colors['bg'],
                       foreground=self.colors['text_green'],  # 深绿色文字
                       font=self.label_font)
        
        # 配置进度条
        style.configure('Dark.Horizontal.TProgressbar',
                       background=self.colors['accent'],
                       troughcolor=self.colors['frame_bg'],
                       relief='flat',
                       borderwidth=0)
        
    def setup_gui(self):
        """Setup the GUI layout"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="15", style='Dark.TFrame')
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="GPS 3D 轨迹分析器", 
                               font=self.title_font, style='Dark.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))
        
        # Control panel (left side)
        self.setup_control_panel(main_frame)
        
        # Visualization and analysis panel (right side)
        self.setup_visualization_panel(main_frame)
        
        # Terminal output (bottom)
        self.setup_terminal_panel(main_frame)
        
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="15", style='Dark.TLabelframe')
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        
        # File selection
        ttk.Label(control_frame, text="Bag文件:", style='Dark.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8)
        bag_entry = ttk.Entry(control_frame, textvariable=self.bag_file_path, width=35, style='Dark.TEntry')
        bag_entry.grid(row=0, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_bag_file, style='Dark.TButton').grid(row=0, column=2, padx=8)

        # GPS Topic
        ttk.Label(control_frame, text="GPS话题:", style='Dark.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8)
        topic_entry = ttk.Entry(control_frame, textvariable=self.gps_topic, width=35, style='Dark.TEntry')
        topic_entry.grid(row=1, column=1, padx=8)

        # Output file
        ttk.Label(control_frame, text="输出文件:", style='Dark.TLabel').grid(row=2, column=0, sticky=tk.W, pady=8)
        output_entry = ttk.Entry(control_frame, textvariable=self.output_file_path, width=35, style='Dark.TEntry')
        output_entry.grid(row=2, column=1, padx=8)
        ttk.Button(control_frame, text="浏览", command=self.browse_output_file, style='Dark.TButton').grid(row=2, column=2, padx=8)

        # ttk.Entry的选择颜色已在样式中配置
        
        # Control buttons
        button_frame = ttk.Frame(control_frame, style='Dark.TFrame')
        button_frame.grid(row=3, column=0, columnspan=3, pady=25)
        
        self.start_button = ttk.Button(button_frame, text="开始分析", 
                                      command=self.start_analysis, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=8)
        
        self.stop_button = ttk.Button(button_frame, text="停止分析",
                                     command=self.stop_analysis, state='disabled', style='Dark.TButton')
        self.stop_button.pack(side=tk.LEFT, padx=8)

        # 演示数据按钮（兼容模式下特别有用）
        if not USE_BAGPY:
            self.demo_button = ttk.Button(button_frame, text="演示数据",
                                         command=self.load_demo_and_analyze, style='Dark.TButton')
            self.demo_button.pack(side=tk.LEFT, padx=8)

        # 3D视图控制按钮
        view_frame = ttk.LabelFrame(control_frame, text="3D视图控制", padding="12", style='Dark.TLabelframe')
        view_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 10))

        # 视角控制
        ttk.Label(view_frame, text="视角控制:", style='Dark.TLabel').grid(row=0, column=0, columnspan=4, pady=5)

        view_buttons_frame = ttk.Frame(view_frame, style='Dark.TFrame')
        view_buttons_frame.grid(row=1, column=0, columnspan=4, pady=5)

        ttk.Button(view_buttons_frame, text="俯视", command=self.view_top, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="侧视", command=self.view_side, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="正视", command=self.view_front, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(view_buttons_frame, text="复位", command=self.reset_view, style='Dark.TButton').pack(side=tk.LEFT, padx=3)

        # 缩放控制
        zoom_frame = ttk.Frame(view_frame, style='Dark.TFrame')
        zoom_frame.grid(row=2, column=0, columnspan=4, pady=10)

        ttk.Label(zoom_frame, text="缩放:", style='Dark.TLabel').pack(side=tk.LEFT, padx=5)
        ttk.Button(zoom_frame, text="放大", command=self.zoom_in, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(zoom_frame, text="缩小", command=self.zoom_out, style='Dark.TButton').pack(side=tk.LEFT, padx=3)
        ttk.Button(zoom_frame, text="适应", command=self.zoom_fit, style='Dark.TButton').pack(side=tk.LEFT, padx=3)

        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate', style='Dark.Horizontal.TProgressbar')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)

        # Status label - 根据bagpy状态显示不同信息
        if USE_BAGPY:
            status_text = "就绪 - 完整功能模式"
            status_style = 'Success.TLabel'
        else:
            status_text = "就绪 - 兼容模式（bagpy不可用，可使用演示数据）"
            status_style = 'Warning.TLabel'

        self.status_label = ttk.Label(control_frame, text=status_text, style=status_style)
        self.status_label.grid(row=6, column=0, columnspan=3, pady=8)

    def setup_visualization_panel(self, parent):
        """Setup visualization and analysis panel"""
        viz_frame = ttk.LabelFrame(parent, text="3D可视化与分析结果", padding="15", style='Dark.TLabelframe')
        viz_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        viz_frame.columnconfigure(0, weight=2)
        viz_frame.columnconfigure(1, weight=1)
        viz_frame.rowconfigure(0, weight=1)

        # 3D Plot frame
        plot_frame = ttk.Frame(viz_frame, style='Dark.TFrame')
        plot_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        plot_frame.columnconfigure(0, weight=1)
        plot_frame.rowconfigure(0, weight=1)

        # Create matplotlib figure with dark theme
        self.fig = plt.Figure(figsize=(10, 8), dpi=100, facecolor='#2b2b2b')
        self.ax = self.fig.add_subplot(111, projection='3d')
        self.ax.set_facecolor('#2b2b2b')

        # 设置3D图表样式
        self.setup_3d_plot_style()

        # Canvas for matplotlib
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 绑定鼠标事件 - 修复鼠标控制
        self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        # Analysis results frame
        analysis_frame = ttk.LabelFrame(viz_frame, text="分析结果", padding="15", style='Dark.TLabelframe')
        analysis_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Analysis text widget with dark theme
        self.analysis_text = scrolledtext.ScrolledText(
            analysis_frame,
            width=45,
            height=30,
            font=self.default_font,
            bg=self.colors['entry_bg'],
            fg=self.colors['text_green'],  # 深绿色文字
            insertbackground=self.colors['text_green'],  # 深绿色光标
            selectbackground=self.colors['select_bg'],  # 深色选择背景
            selectforeground=self.colors['text_green'],  # 深绿色选择文字
            relief='flat',
            borderwidth=1
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True)

    def setup_terminal_panel(self, parent):
        """Setup terminal output panel"""
        terminal_frame = ttk.LabelFrame(parent, text="终端输出", padding="15", style='Dark.TLabelframe')
        terminal_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        terminal_frame.columnconfigure(0, weight=1)
        terminal_frame.rowconfigure(0, weight=1)

        # Terminal text widget with dark theme
        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            height=10,
            font=('Consolas', 10),
            bg=self.colors['terminal_bg'],
            fg=self.colors['terminal_fg'],
            insertbackground=self.colors['terminal_fg'],
            selectbackground=self.colors['select_bg'],  # 深色选择背景
            selectforeground=self.colors['select_fg'],  # 白色选择文字
            relief='flat',
            borderwidth=1
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True)

    def setup_3d_plot_style(self):
        """设置3D图表样式"""
        self.ax.set_facecolor('#2b2b2b')
        self.ax.set_title('GPS 3D 轨迹可视化', color='white', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X (米)', color='white', fontsize=12)
        self.ax.set_ylabel('Y (米)', color='white', fontsize=12)
        self.ax.set_zlabel('Z (米)', color='white', fontsize=12)

        # 设置坐标轴颜色
        self.ax.tick_params(colors='white')
        self.ax.xaxis.label.set_color('white')
        self.ax.yaxis.label.set_color('white')
        self.ax.zaxis.label.set_color('white')

        # 设置网格
        self.ax.grid(True, alpha=0.3, color='white')

        # 设置背景颜色
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False

        # 设置坐标轴线条颜色
        self.ax.xaxis.pane.set_edgecolor('white')
        self.ax.yaxis.pane.set_edgecolor('white')
        self.ax.zaxis.pane.set_edgecolor('white')
        self.ax.xaxis.pane.set_alpha(0.1)
        self.ax.yaxis.pane.set_alpha(0.1)
        self.ax.zaxis.pane.set_alpha(0.1)

    # 修复的3D视图控制方法
    def on_scroll(self, event):
        """处理鼠标滚轮缩放 - 修复版本"""
        if event.inaxes == self.ax:
            if event.button == 'up':
                self.zoom_in()
            elif event.button == 'down':
                self.zoom_out()

    def on_mouse_press(self, event):
        """处理鼠标按下事件 - 修复版本"""
        if event.inaxes == self.ax and event.button == 1:  # 只响应左键
            self.mouse_pressed = True
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def on_mouse_release(self, event):
        """处理鼠标释放事件 - 新增"""
        self.mouse_pressed = False

    def on_mouse_motion(self, event):
        """处理鼠标移动事件 - 修复版本"""
        if (self.mouse_pressed and event.inaxes == self.ax and
            hasattr(self, 'last_mouse_x') and hasattr(self, 'last_mouse_y')):

            # 计算鼠标移动距离
            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y

            # 更新视角 - 降低灵敏度
            self.view_azimuth += dx * 0.3
            self.view_elevation -= dy * 0.3  # 注意这里是减号

            # 限制仰角范围
            self.view_elevation = max(-90, min(90, self.view_elevation))

            # 更新3D视图
            self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
            self.canvas.draw_idle()  # 使用draw_idle提高性能

            # 更新鼠标位置
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y

    def view_top(self):
        """俯视图"""
        self.view_elevation = 90
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_side(self):
        """侧视图"""
        self.view_elevation = 0
        self.view_azimuth = 0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def view_front(self):
        """正视图"""
        self.view_elevation = 0
        self.view_azimuth = 90
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.canvas.draw()

    def reset_view(self):
        """复位视图"""
        self.view_elevation = 30
        self.view_azimuth = 45
        self.zoom_factor = 1.0
        self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)
        self.zoom_fit()

    def zoom_in(self):
        """放大"""
        self.zoom_factor *= 0.8
        self.apply_zoom()

    def zoom_out(self):
        """缩小"""
        self.zoom_factor *= 1.25
        self.apply_zoom()

    def zoom_fit(self):
        """适应窗口"""
        if hasattr(self, 'original_limits'):
            self.zoom_factor = 1.0
            self.apply_zoom()

    def apply_zoom(self):
        """应用缩放"""
        if hasattr(self, 'original_limits'):
            xlim, ylim, zlim = self.original_limits

            # 计算中心点
            x_center = (xlim[0] + xlim[1]) / 2
            y_center = (ylim[0] + ylim[1]) / 2
            z_center = (zlim[0] + zlim[1]) / 2

            # 计算新的范围
            x_range = (xlim[1] - xlim[0]) * self.zoom_factor / 2
            y_range = (ylim[1] - ylim[0]) * self.zoom_factor / 2
            z_range = (zlim[1] - zlim[0]) * self.zoom_factor / 2

            # 设置新的限制
            self.ax.set_xlim(x_center - x_range, x_center + x_range)
            self.ax.set_ylim(y_center - y_range, y_center + y_range)
            self.ax.set_zlim(z_center - z_range, z_center + z_range)

            self.canvas.draw_idle()

    def log_message(self, message):
        """添加消息到终端输出"""
        timestamp = time.strftime("%H:%M:%S")
        self.terminal_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.terminal_text.see(tk.END)
        self.root.update_idletasks()

    def browse_bag_file(self):
        """浏览数据文件"""
        if USE_BAGPY:
            filetypes = [
                ("ROS Bag文件", "*.bag"),
                ("CSV文件", "*.csv"),
                ("所有文件", "*.*")
            ]
            title = "选择ROS Bag文件或CSV文件"
        else:
            filetypes = [
                ("CSV文件", "*.csv"),
                ("ROS Bag文件", "*.bag"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
            title = "选择GPS数据文件（CSV/Bag/TXT）"

        filename = filedialog.askopenfilename(
            title=title,
            filetypes=filetypes,
            parent=self.root
        )
        if filename:
            self.bag_file_path.set(filename)
            # 自动生成输出文件名
            base_name = os.path.splitext(os.path.basename(filename))[0]
            output_path = os.path.join(os.path.dirname(filename), f"{base_name}_轨迹.txt")
            self.output_file_path.set(output_path)

    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存轨迹文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            parent=self.root
        )
        if filename:
            self.output_file_path.set(filename)

    def start_analysis(self):
        """开始GPS分析"""
        bag_file = self.bag_file_path.get()

        # 检查是否选择了文件
        if not bag_file:
            if not USE_BAGPY:
                # 兼容模式下，提示用户选择文件或使用演示数据
                result = messagebox.askyesno("选择数据源",
                                           "请选择数据文件或使用演示数据。\n\n"
                                           "点击'是'使用演示数据\n"
                                           "点击'否'选择数据文件")
                if result:
                    self.bag_file_path.set("演示数据模式")
                else:
                    messagebox.showinfo("提示", "请点击'浏览'选择bag文件或CSV文件")
                    return
            else:
                messagebox.showerror("错误", "请选择一个数据文件")
                return

        if not self.output_file_path.get():
            messagebox.showerror("错误", "请指定输出文件路径")
            return

        self.is_processing = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress.start()
        self.status_label.config(text="处理中...", style='Warning.TLabel')

        # 清除之前的结果
        self.ax.clear()
        self.setup_3d_plot_style()
        self.canvas.draw()
        self.analysis_text.delete(1.0, tk.END)
        self.terminal_text.delete(1.0, tk.END)

        # 在单独线程中开始处理
        self.processing_thread = threading.Thread(target=self.process_gps_data)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def stop_analysis(self):
        """停止GPS分析"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="已停止", style='Error.TLabel')
        self.log_message("用户停止了分析")

    def process_gps_data(self):
        """在后台线程中处理GPS数据"""
        try:
            self.log_message("开始提取GPS数据...")

            # 提取GPS数据
            if not self.extract_gps_from_bag():
                return

            if not self.is_processing:
                return

            self.log_message("转换为本地坐标...")
            self.convert_to_local_coordinates()

            if not self.is_processing:
                return

            self.log_message("分析GPS质量...")
            analysis = self.analyze_gps_quality()

            if not self.is_processing:
                return

            self.log_message("创建3D可视化...")
            self.root.after(0, self.update_visualization, analysis)

            if not self.is_processing:
                return

            self.log_message("保存轨迹到文件...")
            self.save_trajectory_file()

            # 完成
            self.root.after(0, self.analysis_complete)

        except Exception as e:
            self.log_message(f"处理过程中出现错误: {str(e)}")
            self.root.after(0, self.analysis_error, str(e))

    def extract_gps_from_bag(self):
        """从bag文件中提取GPS数据"""
        bag_file = self.bag_file_path.get()

        # 如果是演示数据模式
        if bag_file == "演示数据模式":
            self.log_message("使用演示数据模式...")
            return self.load_demo_gps_data()

        self.log_message(f"开始处理文件: {bag_file}")
        self.log_message(f"USE_BAGPY状态: {USE_BAGPY}")

        # 首先尝试使用bagpy
        if USE_BAGPY:
            self.log_message("尝试使用bagpy处理...")
            try:
                import bagpy
                result = self.extract_with_bagpy(bagpy, bag_file)
                if result:
                    self.log_message(f"✅ bagpy处理成功，提取了 {len(self.gps_data)} 个GPS点")
                    return True
                else:
                    self.log_message("❌ bagpy处理失败，尝试其他方法")
            except Exception as e:
                self.log_message(f"❌ bagpy处理异常: {e}")
                import traceback
                self.log_message(f"详细错误: {traceback.format_exc()}")

        # 如果bagpy不可用或失败，尝试直接读取bag文件
        self.log_message("⚠️  尝试直接读取bag文件...")
        result = self.extract_with_rosbag(bag_file)
        if result:
            self.log_message(f"✅ 直接读取成功，提取了 {len(self.gps_data)} 个GPS点")
            return True

        # 最后尝试作为CSV处理
        self.log_message("⚠️  尝试作为CSV文件处理...")
        result = self.extract_from_csv(bag_file)
        if result:
            self.log_message(f"✅ CSV处理成功，提取了 {len(self.gps_data)} 个GPS点")
            return True

        # 所有方法都失败，使用演示数据
        self.log_message("❌ 所有处理方法都失败，使用演示数据")
        return self.load_demo_gps_data()

        try:
            bag_file = self.bag_file_path.get()
            topic_name = self.gps_topic.get()

            self.log_message(f"读取bag文件: {os.path.basename(bag_file)}")
            bag = bagpy.bagreader(bag_file)

            self.log_message(f"提取话题: {topic_name}")
            gps_csv = bag.message_by_topic(topic_name)
            df = pd.read_csv(gps_csv)

            self.gps_data = []
            gps_count = 0

            for index, row in df.iterrows():
                if not self.is_processing:
                    return False

                try:
                    gps_info = {
                        'timestamp': float(row['Time']),
                        'latitude': float(row['latitude']),
                        'longitude': float(row['longitude']),
                        'altitude': float(row['altitude']),
                        'status': int(row['status.status']),
                        'service': int(row['status.service']) if 'status.service' in row else 0,
                    }
                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 1000 == 0:
                        self.log_message(f"已提取 {gps_count} 个GPS点...")

                except (ValueError, KeyError):
                    continue

            self.log_message(f"GPS数据提取完成: {len(self.gps_data)} 个点")
            return len(self.gps_data) > 0

        except Exception as e:
            self.log_message(f"读取bag文件失败: {str(e)}")
            self.log_message("切换到演示模式...")
            return self.load_demo_gps_data()

    def extract_with_bagpy(self, bagpy, bag_file):
        """使用bagpy提取GPS数据"""
        try:
            topic_name = self.gps_topic.get()

            self.log_message(f"📁 读取bag文件: {os.path.basename(bag_file)}")
            self.log_message(f"📡 目标话题: {topic_name}")

            # 创建bagpy读取器
            bag = bagpy.bagreader(bag_file)
            self.log_message("✅ bagpy读取器创建成功")

            # 获取话题列表
            try:
                topics = bag.topic_table
                self.log_message(f"📋 可用话题: {list(topics['Topics'])}")

                if topic_name not in list(topics['Topics']):
                    self.log_message(f"❌ 话题 {topic_name} 不存在于bag文件中")
                    available_topics = [t for t in topics['Topics'] if 'gps' in t.lower() or 'gnss' in t.lower()]
                    if available_topics:
                        self.log_message(f"💡 建议的GPS话题: {available_topics}")
                    return False
            except Exception as e:
                self.log_message(f"⚠️  无法获取话题列表: {e}")

            # 提取话题数据
            self.log_message(f"🔄 开始提取话题数据...")
            gps_csv = bag.message_by_topic(topic_name)
            self.log_message(f"✅ 话题数据提取完成，CSV文件: {gps_csv}")

            # 读取CSV数据
            df = pd.read_csv(gps_csv)
            self.log_message(f"📊 CSV数据读取完成，共 {len(df)} 行")
            self.log_message(f"📋 CSV列名: {list(df.columns)}")

            self.gps_data = []
            gps_count = 0
            error_count = 0

            for index, row in df.iterrows():
                if not self.is_processing:
                    return False

                try:
                    # 尝试不同的列名组合
                    timestamp = None
                    latitude = None
                    longitude = None
                    altitude = None
                    status = 0

                    # 时间戳
                    for time_col in ['Time', 'timestamp', 'time', 'header.stamp.secs']:
                        if time_col in df.columns:
                            timestamp = float(row[time_col])
                            break

                    # 纬度
                    for lat_col in ['latitude', 'lat', 'Latitude']:
                        if lat_col in df.columns:
                            latitude = float(row[lat_col])
                            break

                    # 经度
                    for lon_col in ['longitude', 'lon', 'Longitude']:
                        if lon_col in df.columns:
                            longitude = float(row[lon_col])
                            break

                    # 高度
                    for alt_col in ['altitude', 'alt', 'Altitude', 'height']:
                        if alt_col in df.columns:
                            altitude = float(row[alt_col])
                            break

                    # GPS状态
                    for status_col in ['status.status', 'status', 'fix_type', 'position_covariance_type']:
                        if status_col in df.columns:
                            status = int(row[status_col])
                            break

                    # 检查必要字段
                    if latitude is None or longitude is None:
                        error_count += 1
                        continue

                    if timestamp is None:
                        timestamp = float(index)
                    if altitude is None:
                        altitude = 0.0

                    gps_info = {
                        'timestamp': timestamp,
                        'latitude': latitude,
                        'longitude': longitude,
                        'altitude': altitude,
                        'status': status
                    }

                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 500 == 0:
                        self.log_message(f"📈 已处理 {gps_count} 个GPS点...")

                except (ValueError, KeyError, TypeError) as e:
                    error_count += 1
                    if error_count <= 5:  # 只显示前5个错误
                        self.log_message(f"⚠️  数据解析错误 (行{index}): {e}")
                    continue

            if error_count > 5:
                self.log_message(f"⚠️  总共跳过了 {error_count} 个无效数据行")

            self.log_message(f"🎉 GPS数据提取完成: {len(self.gps_data)} 个有效点")
            return len(self.gps_data) > 0

        except Exception as e:
            self.log_message(f"❌ bagpy提取失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            return False

    def extract_with_rosbag(self, bag_file):
        """直接使用rosbag库提取GPS数据"""
        try:
            # 尝试导入rosbag
            import rosbag
            self.log_message("🔧 使用rosbag库直接读取...")

            topic_name = self.gps_topic.get()
            self.log_message(f"📁 读取bag文件: {os.path.basename(bag_file)}")
            self.log_message(f"📡 目标话题: {topic_name}")

            self.gps_data = []
            gps_count = 0
            error_count = 0

            with rosbag.Bag(bag_file, 'r') as bag:
                # 获取话题信息
                topics_info = bag.get_type_and_topic_info()[1]
                available_topics = list(topics_info.keys())

                self.log_message(f"📋 可用话题: {available_topics}")

                if topic_name not in topics_info:
                    self.log_message(f"❌ 话题 {topic_name} 不存在于bag文件中")
                    # 查找可能的GPS话题
                    gps_topics = [t for t in available_topics if any(keyword in t.lower() for keyword in ['gps', 'gnss', 'fix', 'nav'])]
                    if gps_topics:
                        self.log_message(f"💡 建议的GPS话题: {gps_topics}")
                        # 尝试使用第一个找到的GPS话题
                        topic_name = gps_topics[0]
                        self.log_message(f"🔄 自动切换到话题: {topic_name}")
                    else:
                        return False

                # 获取消息总数
                topic_info = topics_info[topic_name]
                message_count = topic_info.message_count
                self.log_message(f"📊 话题 {topic_name} 包含 {message_count} 条消息")

                self.log_message(f"🔄 开始提取GPS数据...")

                for topic, msg, t in bag.read_messages(topics=[topic_name]):
                    if not self.is_processing:
                        return False

                    try:
                        # 提取GPS数据 - 尝试不同的消息格式
                        timestamp = t.to_sec()
                        latitude = None
                        longitude = None
                        altitude = None
                        status = 0

                        # 尝试标准GPS消息格式
                        if hasattr(msg, 'latitude') and hasattr(msg, 'longitude'):
                            latitude = float(msg.latitude)
                            longitude = float(msg.longitude)
                            altitude = float(getattr(msg, 'altitude', 0.0))
                            status = int(getattr(msg, 'status', getattr(msg, 'position_covariance_type', 0)))

                        # 尝试NavSatFix格式
                        elif hasattr(msg, 'latitude') and hasattr(msg, 'longitude'):
                            latitude = float(msg.latitude)
                            longitude = float(msg.longitude)
                            altitude = float(getattr(msg, 'altitude', 0.0))
                            if hasattr(msg, 'status'):
                                status = int(getattr(msg.status, 'status', 0))

                        # 尝试其他可能的格式
                        elif hasattr(msg, 'pose') and hasattr(msg.pose, 'position'):
                            # 可能是PoseStamped或类似格式，需要转换
                            continue

                        else:
                            error_count += 1
                            if error_count <= 5:
                                self.log_message(f"⚠️  未识别的消息格式: {type(msg)}")
                            continue

                        if latitude is None or longitude is None:
                            error_count += 1
                            continue

                        gps_info = {
                            'timestamp': timestamp,
                            'latitude': latitude,
                            'longitude': longitude,
                            'altitude': altitude,
                            'status': status
                        }

                        self.gps_data.append(gps_info)
                        gps_count += 1

                        if gps_count % 500 == 0:
                            progress = (gps_count / message_count) * 100 if message_count > 0 else 0
                            self.log_message(f"📈 已处理 {gps_count}/{message_count} 个GPS点 ({progress:.1f}%)...")

                    except (AttributeError, ValueError, TypeError) as e:
                        error_count += 1
                        if error_count <= 5:
                            self.log_message(f"⚠️  数据解析错误: {e}")
                        continue

            if error_count > 5:
                self.log_message(f"⚠️  总共跳过了 {error_count} 个无效消息")

            self.log_message(f"🎉 GPS数据提取完成: {len(self.gps_data)} 个有效点")
            return len(self.gps_data) > 0

        except ImportError:
            self.log_message("❌ rosbag库不可用，尝试CSV格式...")
            return False
        except Exception as e:
            self.log_message(f"❌ rosbag提取失败: {str(e)}")
            import traceback
            self.log_message(f"详细错误: {traceback.format_exc()}")
            return False

    def extract_from_csv(self, file_path):
        """从CSV文件提取GPS数据"""
        try:
            self.log_message("尝试作为CSV文件读取...")

            # 尝试不同的CSV格式
            encodings = ['utf-8', 'gbk', 'latin-1']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except:
                    continue

            if df is None:
                self.log_message("无法读取文件，切换到演示模式")
                return self.load_demo_gps_data()

            self.log_message(f"CSV文件读取成功，共 {len(df)} 行")

            # 查找GPS相关列
            lat_cols = [col for col in df.columns if 'lat' in col.lower()]
            lon_cols = [col for col in df.columns if 'lon' in col.lower()]
            alt_cols = [col for col in df.columns if 'alt' in col.lower()]

            if not lat_cols or not lon_cols:
                self.log_message("未找到GPS坐标列，切换到演示模式")
                return self.load_demo_gps_data()

            lat_col = lat_cols[0]
            lon_col = lon_cols[0]
            alt_col = alt_cols[0] if alt_cols else None

            self.log_message(f"使用列: 纬度={lat_col}, 经度={lon_col}, 高度={alt_col}")

            self.gps_data = []
            gps_count = 0

            for index, row in df.iterrows():
                if not self.is_processing:
                    return False

                try:
                    gps_info = {
                        'timestamp': float(index),
                        'latitude': float(row[lat_col]),
                        'longitude': float(row[lon_col]),
                        'altitude': float(row[alt_col]) if alt_col else 0.0,
                        'status': 0  # 默认为RTK固定解
                    }

                    self.gps_data.append(gps_info)
                    gps_count += 1

                    if gps_count % 100 == 0:
                        self.log_message(f"已处理 {gps_count} 个GPS点...")

                except (ValueError, KeyError):
                    continue

            self.log_message(f"CSV数据提取完成: {len(self.gps_data)} 个点")
            return len(self.gps_data) > 0

        except Exception as e:
            self.log_message(f"CSV提取失败: {str(e)}")
            self.log_message("所有方法都失败，使用演示数据")
            return self.load_demo_gps_data()

    def load_demo_gps_data(self):
        """加载演示GPS数据（当bagpy不可用时）"""
        try:
            self.log_message("生成演示GPS轨迹数据...")

            # 生成一个简单的GPS轨迹演示数据
            import random
            import math

            # 设置起始位置（北京附近）
            start_lat = 39.9042
            start_lon = 116.4074
            start_alt = 50.0

            self.gps_data = []
            num_points = 100

            for i in range(num_points):
                # 生成螺旋形轨迹
                angle = i * 0.1
                radius = i * 0.0001

                lat = start_lat + radius * math.cos(angle)
                lon = start_lon + radius * math.sin(angle)
                alt = start_alt + i * 0.1 + random.uniform(-2, 2)

                # 随机GPS质量状态
                status = random.choice([0, 0, 0, 1, 2, -1])  # 主要是RTK固定解

                gps_info = {
                    'timestamp': i * 1.0,
                    'latitude': lat,
                    'longitude': lon,
                    'altitude': alt,
                    'status': status
                }

                self.gps_data.append(gps_info)

            self.log_message(f"✅ 生成了 {len(self.gps_data)} 个演示GPS点")
            return True

        except Exception as e:
            self.log_message(f"生成演示数据时出现错误: {str(e)}")
            return False

    def load_demo_and_analyze(self):
        """加载演示数据并开始分析"""
        try:
            # 设置输出文件路径
            import tempfile
            temp_dir = tempfile.gettempdir()
            demo_output = os.path.join(temp_dir, "GPS_演示轨迹.txt")
            self.output_file_path.set(demo_output)

            # 清空bag文件路径（因为我们使用演示数据）
            self.bag_file_path.set("演示数据模式")

            # 开始分析
            self.start_analysis()

        except Exception as e:
            messagebox.showerror("错误", f"加载演示数据失败: {str(e)}")

    def convert_to_local_coordinates(self):
        """转换GPS坐标到本地坐标系"""
        if not self.gps_data:
            return

        # 寻找原点
        for gps in self.gps_data:
            if gps['status'] >= 0:
                self.origin_lat = gps['latitude']
                self.origin_lon = gps['longitude']
                self.origin_alt = gps['altitude']
                break

        if self.origin_lat is None:
            if self.gps_data:
                self.origin_lat = self.gps_data[0]['latitude']
                self.origin_lon = self.gps_data[0]['longitude']
                self.origin_alt = self.gps_data[0]['altitude']
            else:
                return

        self.log_message(f"GPS原点: 纬度={self.origin_lat:.8f}, 经度={self.origin_lon:.8f}")

        # 转换为本地坐标
        self.trajectory_points = []
        for i, gps in enumerate(self.gps_data):
            if not self.is_processing:
                return

            lat_diff = gps['latitude'] - self.origin_lat
            lon_diff = gps['longitude'] - self.origin_lon
            alt_diff = gps['altitude'] - self.origin_alt

            x = lon_diff * 111320.0 * math.cos(math.radians(self.origin_lat))
            y = lat_diff * 110540.0
            z = alt_diff

            point_info = {
                'frame_id': i + 1,  # 帧编号从1开始
                'position': [x, y, z],
                'status': gps['status'],
                'timestamp': gps['timestamp'],
                'original_gps': [gps['latitude'], gps['longitude'], gps['altitude']]
            }
            self.trajectory_points.append(point_info)

    def analyze_gps_quality(self):
        """分析GPS质量统计"""
        status_count = defaultdict(int)
        total_points = len(self.gps_data)

        for gps in self.gps_data:
            status = gps['status']
            if status not in self.quality_names:
                status = 3
            status_count[status] += 1

        # 计算轨迹统计
        trajectory_length = 0.0
        start_end_distance = 0.0
        closure_error = 0.0

        if self.trajectory_points:
            positions = np.array([p['position'] for p in self.trajectory_points])

            # 计算轨迹长度
            for i in range(1, len(self.trajectory_points)):
                p1 = np.array(self.trajectory_points[i-1]['position'])
                p2 = np.array(self.trajectory_points[i]['position'])
                trajectory_length += np.linalg.norm(p2 - p1)

            # 计算首尾距离
            if len(positions) > 1:
                start_end_distance = np.linalg.norm(positions[-1] - positions[0])
                closure_error = (start_end_distance / trajectory_length) * 100 if trajectory_length > 0 else 0

        return {
            'status_count': status_count,
            'total_points': total_points,
            'trajectory_length': trajectory_length,
            'start_end_distance': start_end_distance,
            'closure_error': closure_error,
            'positions': np.array([p['position'] for p in self.trajectory_points]) if self.trajectory_points else None
        }

    def update_visualization(self, analysis):
        """更新3D可视化和分析结果"""
        try:
            # 清除之前的图表
            self.ax.clear()
            self.setup_3d_plot_style()

            # 按GPS状态分组
            status_groups = defaultdict(list)
            for i, point in enumerate(self.trajectory_points):
                status = point['status']
                if status not in self.quality_names:
                    status = 3
                status_groups[status].append(i)

            # 绘制3D轨迹，不同颜色表示不同GPS质量
            for status, indices in status_groups.items():
                if len(indices) < 2:
                    continue

                points = [self.trajectory_points[i]['position'] for i in indices]
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                z_coords = [p[2] for p in points]

                color = self.quality_colors[status]
                quality_name = self.quality_names[status]

                # RTK固定解用粗实线，其他用虚线
                if status == 0:  # RTK_FIXED
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=4, linestyle='-',
                               alpha=0.9, label=f'{quality_name}')
                else:
                    self.ax.plot(x_coords, y_coords, z_coords,
                               color=color, linewidth=2.5, linestyle='--',
                               alpha=0.8, label=f'{quality_name}')

            # 标记起点和终点
            if self.trajectory_points:
                start_pos = self.trajectory_points[0]['position']
                end_pos = self.trajectory_points[-1]['position']

                self.ax.scatter(start_pos[0], start_pos[1], start_pos[2],
                              c='#00FF41', s=200, marker='o', edgecolors='white', linewidth=2,
                              label='起点', alpha=1.0, zorder=10)
                self.ax.scatter(end_pos[0], end_pos[1], end_pos[2],
                              c='#FF4444', s=200, marker='s', edgecolors='white', linewidth=2,
                              label='终点', alpha=1.0, zorder=10)

            # 添加图例
            legend = self.ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
            legend.get_frame().set_facecolor('#404040')
            legend.get_frame().set_edgecolor('white')
            for text in legend.get_texts():
                text.set_color('white')

            # 设置相等的坐标轴比例并保存原始限制
            if analysis['positions'] is not None:
                positions = analysis['positions']
                max_range = np.array([positions[:, 0].max() - positions[:, 0].min(),
                                    positions[:, 1].max() - positions[:, 1].min(),
                                    positions[:, 2].max() - positions[:, 2].min()]).max() / 2.0

                mid_x = (positions[:, 0].max() + positions[:, 0].min()) * 0.5
                mid_y = (positions[:, 1].max() + positions[:, 1].min()) * 0.5
                mid_z = (positions[:, 2].max() + positions[:, 2].min()) * 0.5

                self.ax.set_xlim(mid_x - max_range, mid_x + max_range)
                self.ax.set_ylim(mid_y - max_range, mid_y + max_range)
                self.ax.set_zlim(mid_z - max_range, mid_z + max_range)

                # 保存原始限制用于缩放
                self.original_limits = (
                    (mid_x - max_range, mid_x + max_range),
                    (mid_y - max_range, mid_y + max_range),
                    (mid_z - max_range, mid_z + max_range)
                )

            # 设置初始视角
            self.ax.view_init(elev=self.view_elevation, azim=self.view_azimuth)

            # 更新画布
            self.canvas.draw()

            # 更新分析结果
            self.update_analysis_text(analysis)

        except Exception as e:
            self.log_message(f"更新可视化时出错: {str(e)}")

    def update_analysis_text(self, analysis):
        """更新分析结果文本（中文显示）"""
        self.analysis_text.delete(1.0, tk.END)

        # 格式化分析文本
        text_lines = []
        text_lines.append("=" * 45)
        text_lines.append("📊 GPS 质量分析")
        text_lines.append("=" * 45)
        text_lines.append("")

        # GPS质量统计
        text_lines.append("🛰️ GPS质量分布:")
        text_lines.append("-" * 35)
        total_points = analysis['total_points']

        for status, count in sorted(analysis['status_count'].items()):
            percentage = (count / total_points) * 100
            quality_name = self.quality_names.get(status, "未知")
            color_name = {
                '#FF6B6B': '红色',
                '#4ECDC4': '青绿色',
                '#45B7D1': '蓝色',
                '#FFA07A': '橙色',
                '#DDA0DD': '紫色'
            }.get(self.quality_colors.get(status), '灰色')

            line_style = "实线" if status == 0 else "虚线"
            text_lines.append(f"{quality_name:8}: {count:6,d} 点")
            text_lines.append(f"{'':10} ({percentage:5.1f}%)")
            text_lines.append(f"{'':10} {color_name} {line_style}")
            text_lines.append("")

        text_lines.append("=" * 45)
        text_lines.append("📏 轨迹统计")
        text_lines.append("=" * 45)
        text_lines.append("")

        # 轨迹统计
        text_lines.append(f"总点数:        {total_points:,}")
        text_lines.append(f"轨迹长度:      {analysis['trajectory_length']:.1f} 米")
        text_lines.append(f"首尾距离:      {analysis['start_end_distance']:.1f} 米")
        text_lines.append(f"闭合误差:      {analysis['closure_error']:.3f}%")
        text_lines.append("")

        # 坐标范围
        if analysis['positions'] is not None:
            positions = analysis['positions']
            text_lines.append("📐 坐标范围:")
            text_lines.append("-" * 25)
            text_lines.append(f"X: {positions[:, 0].min():.1f} ~ {positions[:, 0].max():.1f} 米")
            text_lines.append(f"Y: {positions[:, 1].min():.1f} ~ {positions[:, 1].max():.1f} 米")
            text_lines.append(f"Z: {positions[:, 2].min():.1f} ~ {positions[:, 2].max():.1f} 米")
            text_lines.append("")

        # GPS原点
        text_lines.append("🌍 GPS原点:")
        text_lines.append("-" * 18)
        text_lines.append(f"纬度:  {self.origin_lat:.8f}°")
        text_lines.append(f"经度:  {self.origin_lon:.8f}°")
        text_lines.append(f"高度:  {self.origin_alt:.3f} 米")
        text_lines.append("")

        # 质量评估
        text_lines.append("✅ 质量评估:")
        text_lines.append("-" * 20)
        if analysis['closure_error'] < 0.1:
            text_lines.append("闭合误差: 优秀")
        elif analysis['closure_error'] < 0.5:
            text_lines.append("闭合误差: 良好")
        elif analysis['closure_error'] < 1.0:
            text_lines.append("闭合误差: 一般")
        else:
            text_lines.append("闭合误差: 较差")

        rtk_percentage = analysis['status_count'].get(0, 0) / total_points * 100
        if rtk_percentage > 95:
            text_lines.append("GPS质量: 优秀")
        elif rtk_percentage > 80:
            text_lines.append("GPS质量: 良好")
        elif rtk_percentage > 50:
            text_lines.append("GPS质量: 一般")
        else:
            text_lines.append("GPS质量: 较差")

        # 输出文件信息
        text_lines.append("")
        text_lines.append("💾 输出文件:")
        text_lines.append("-" * 18)
        output_file = self.output_file_path.get()
        text_lines.append(f"路径: {os.path.basename(output_file)}")
        text_lines.append(f"格式: 帧编号 X Y Z")
        text_lines.append(f"点数: {len(self.trajectory_points)}")

        # 3D控制说明
        text_lines.append("")
        text_lines.append("🎮 3D控制:")
        text_lines.append("-" * 15)
        text_lines.append("• 鼠标拖拽: 旋转视角")
        text_lines.append("• 滚轮: 缩放")
        text_lines.append("• 左侧按钮: 视角控制")

        # 插入文本
        analysis_text = "\n".join(text_lines)
        self.analysis_text.insert(1.0, analysis_text)

    def save_trajectory_file(self):
        """保存轨迹到文本文件，格式: 帧编号 X Y Z"""
        try:
            output_file = self.output_file_path.get()

            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入文件头
                f.write("# GPS轨迹数据\n")
                f.write("# 格式: 帧编号 X Y Z\n")
                f.write("# 坐标系: 本地ENU (东-北-上)\n")
                f.write(f"# 原点: 纬度={self.origin_lat:.8f}, 经度={self.origin_lon:.8f}, 高度={self.origin_alt:.3f}\n")
                f.write("# 单位: 米\n")
                f.write("#\n")

                # 写入轨迹点
                for point in self.trajectory_points:
                    frame_id = point['frame_id']
                    x, y, z = point['position']
                    f.write(f"{frame_id} {x:.6f} {y:.6f} {z:.6f}\n")

            self.log_message(f"轨迹已保存到: {os.path.basename(output_file)}")
            self.log_message(f"保存的总点数: {len(self.trajectory_points)}")

        except Exception as e:
            self.log_message(f"保存轨迹文件时出错: {str(e)}")

    def analysis_complete(self):
        """处理分析完成"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="分析完成", style='Success.TLabel')
        self.log_message("GPS轨迹分析成功完成！")

        # 显示完成消息
        messagebox.showinfo("分析完成",
                           f"GPS轨迹分析完成！\n\n"
                           f"处理的点数: {len(self.trajectory_points):,}\n"
                           f"输出文件: {os.path.basename(self.output_file_path.get())}")

    def analysis_error(self, error_msg):
        """处理分析错误"""
        self.is_processing = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_label.config(text="错误", style='Error.TLabel')
        messagebox.showerror("分析错误", f"分析过程中发生错误:\n\n{error_msg}")

# 🔧 修复bagpy导入问题
def patch_bagpy_import():
    """直接修复bagpy在PyInstaller中的导入问题"""
    try:
        # 首先尝试正常导入
        import bagpy
        return True, bagpy
    except Exception as e:
        print(f"正常导入bagpy失败: {e}")

        # 尝试修复version文件问题
        try:
            # 先导入bagpy模块
            import bagpy

            # 获取bagpy路径
            if hasattr(bagpy, '__path__') and bagpy.__path__:
                bagpy_path = bagpy.__path__[0]
                version_file = os.path.join(bagpy_path, 'version')

                # 检查version文件是否存在
                if not os.path.exists(version_file):
                    # 尝试创建目录（如果不存在）
                    os.makedirs(bagpy_path, exist_ok=True)

                    # 创建version文件
                    try:
                        with open(version_file, 'w') as f:
                            f.write('0.5.0\n')
                        print(f"✅ 创建bagpy version文件: {version_file}")
                    except Exception as write_error:
                        print(f"⚠️  无法写入version文件: {write_error}")

                        # 尝试在当前目录创建临时version文件
                        temp_version = os.path.join(os.getcwd(), 'bagpy_version_temp')
                        try:
                            with open(temp_version, 'w') as f:
                                f.write('0.5.0\n')
                            print(f"✅ 创建临时version文件: {temp_version}")
                        except:
                            pass
                else:
                    print(f"✅ bagpy version文件已存在: {version_file}")

            # 尝试再次导入
            import bagpy
            return True, bagpy

        except Exception as e2:
            print(f"修复bagpy失败: {e2}")

            # 最后的尝试：monkey patch bagpy
            try:
                import bagpy
                # 直接设置版本信息
                if not hasattr(bagpy, '__version__'):
                    bagpy.__version__ = '0.5.0'
                return True, bagpy
            except:
                return False, None

# 尝试导入bagpy
USE_BAGPY, bagpy_module = patch_bagpy_import()
if USE_BAGPY:
    print("✅ bagpy导入成功")
    bagpy = bagpy_module
else:
    print("❌ bagpy导入失败，将使用兼容模式")
    bagpy = None

def main():
    """主函数运行GUI应用程序"""
    try:
        print("🔧 GPS 3D轨迹分析器启动中...")

        # 创建主窗口
        root = tk.Tk()

        # 检查依赖并显示状态
        if not USE_BAGPY:
            print("⚠️  bagpy不可用，使用兼容模式")
        else:
            print("✅ bagpy可用，完整功能模式")

        # 创建应用程序实例
        print("🚀 创建GUI界面...")
        app = GPSAnalyzerGUI(root)

        # 窗口居中显示
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
        y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
        root.geometry(f"+{x}+{y}")

        # 设置窗口最小尺寸
        root.minsize(1200, 800)

        print("✅ GUI界面启动完成")
        root.mainloop()

    except Exception as e:
        print(f"❌ 程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()

        # 尝试显示错误对话框
        try:
            if 'root' in locals():
                messagebox.showerror("运行错误", f"程序运行时发生错误:\n\n{str(e)}")
        except:
            pass
    finally:
        try:
            if 'root' in locals():
                root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()
