# αLiDAR GPS集成完整执行指南

## 快速开始 (5分钟部署)

### 第一步：环境检查
```bash
# 下载并运行环境检查脚本
chmod +x check_environment.sh
./check_environment.sh
```

### 第二步：自动部署
```bash
# 运行自动部署脚本
chmod +x deploy_gps_integration.sh
./deploy_gps_integration.sh
```

### 第三步：启动系统
```bash
# 方法1: 使用生成的启动脚本
~/start_alpha_lidar_gps.sh

# 方法2: 手动启动 (推荐用于调试)
# 见下面的详细步骤
```

## 详细执行步骤

### 准备阶段

#### 1. 确认文件位置
```bash
# 确认工作空间
ls -la ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/

# 确认数据集
ls -la ~/datasets/UM982loop_715std_maximum_synced.bag

# 确认脚本文件
ls -la check_environment.sh deploy_gps_integration.sh
```

#### 2. 检查ROS环境
```bash
# 检查ROS版本
rosversion -d  # 应该显示 noetic

# 设置ROS环境 (如果未设置)
source /opt/ros/noetic/setup.bash
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
```

### 部署阶段

#### 1. 运行环境检查
```bash
chmod +x check_environment.sh
./check_environment.sh

# 如果发现问题，按照提示安装缺失的依赖
sudo apt update
sudo apt install -y libgeographic-dev libgeographic19 libeigen3-dev libpcl-dev
```

#### 2. 执行自动部署
```bash
chmod +x deploy_gps_integration.sh
./deploy_gps_integration.sh

# 脚本会自动：
# - 安装依赖
# - 备份原始代码
# - 修改配置文件
# - 编译系统
# - 创建启动脚本
```

#### 3. 手动修改源代码 (如果需要)
```bash
# 如果自动部署没有替换源代码，手动执行：
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/src
cp voxelMapping.cpp voxelMapping.cpp.original
cp /path/to/voxelMapping_gps_integration.cpp voxelMapping.cpp

# 重新编译
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"
```

### 执行阶段

#### 方法1：分窗口手动执行 (推荐)

**窗口1：启动roscore**
```bash
# 新开终端窗口1
roscore
```

**窗口2：启动αLiDAR节点**
```bash
# 新开终端窗口2
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 启动αLiDAR处理节点
roslaunch state_estimation mapping_robosense.launch

# 预期看到的输出：
# - "GPS集成已启用"
# - 等待GPS数据的提示
```

**窗口3：检查GPS topic**
```bash
# 新开终端窗口3
# 首先分析bag文件中的GPS topic
rosbag info ~/datasets/UM982loop_715std_maximum_synced.bag | grep -i -E "(gps|rtk|fix|navsat)"

# 根据输出调整GPS topic名称，可能的名称：
# /ublox_gps/fix
# /gps/fix  
# /rtk/fix
# /navsat/fix
```

**窗口4：播放bag文件**
```bash
# 新开终端窗口4
cd ~/datasets

# 播放bag文件 (暂停模式)
rosbag play UM982loop_715std_maximum_synced.bag --pause

# 操作说明：
# - 按空格键开始播放
# - 按空格键暂停/继续
# - 按's'键单步播放
# - Ctrl+C停止播放
```

**窗口5：监控系统状态**
```bash
# 新开终端窗口5
# 监控GPS数据
rostopic echo /ublox_gps/fix | head -20

# 监控GPS频率
rostopic hz /ublox_gps/fix

# 监控所有topic
rostopic list

# 监控节点状态
rosnode list
rosnode info /state_estimation_node
```

#### 方法2：使用测试脚本
```bash
# 在窗口1-2启动系统后，在新窗口运行测试脚本
python3 test_gps_integration.py

# 然后在另一个窗口播放bag文件
rosbag play ~/datasets/UM982loop_715std_maximum_synced.bag
```

### 验证阶段

#### 1. 检查系统启动成功
在αLiDAR节点的输出中应该看到：
```
[INFO] GPS集成已启用 - 校正阈值: 0.30m, 校正率: 10%
[INFO] GPS坐标原点已设置: lat=39.90420000, lon=116.40740000, alt=50.000
[INFO] 起始位置已记录 - SLAM: [0.000,0.000,0.000], GPS: [0.000,0.000,0.000]
```

#### 2. 检查GPS数据接收
```bash
# 检查GPS topic是否有数据
rostopic hz /ublox_gps/fix
# 应该显示类似: average rate: 10.000

# 检查GPS数据内容
rostopic echo /ublox_gps/fix -n 1
# 应该显示GPS坐标和状态信息
```

#### 3. 检查GPS校正工作
在系统运行过程中应该看到：
```
[INFO] GPS高度校正: SLAM=50.123, GPS=50.456, 校正后=50.156
[INFO] GPS回环检测: 距离起点2.34m, 约束向量[0.012,0.034,0.056]
[INFO] 应用GPS回环约束校正
```

#### 4. 运行完整测试
```bash
# 播放完整数据集后检查结果
ls /tmp/alpha_lidar/  # 查看生成的结果文件

# 运行测试脚本分析效果
python3 test_gps_integration.py
# 会生成 gps_integration_test_results.png 分析图表
```

## 故障排除

### 常见问题1：编译失败
```bash
# 错误：找不到GeographicLib
sudo apt install libgeographic-dev libgeographic19
pkg-config --libs geographic

# 错误：找不到头文件
sudo apt install libeigen3-dev libpcl-dev

# 重新编译
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws
rm -rf build/ devel/
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation"
```

### 常见问题2：GPS topic不存在
```bash
# 检查bag文件中的实际topic
rosbag info ~/datasets/UM982loop_715std_maximum_synced.bag

# 修改launch文件中的topic重映射
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/launch
nano mapping_robosense.launch
# 修改: <remap from="/gps/fix" to="/actual_gps_topic_name"/>
```

### 常见问题3：没有GPS校正日志
```bash
# 检查GPS数据是否有效
rostopic echo /ublox_gps/fix -n 1
# 检查status字段，应该 >= 1

# 检查参数设置
rosparam get /gps/enable_correction  # 应该是true
rosparam get /gps/height_correction_threshold  # 应该是0.3

# 增加调试输出
rosparam set /gps/height_correction_threshold 0.1  # 降低阈值
```

### 常见问题4：系统运行缓慢
```bash
# 检查系统资源
htop

# 降低bag播放速率
rosbag play ~/datasets/UM982loop_715std_maximum_synced.bag -r 0.5  # 0.5倍速

# 检查磁盘空间
df -h
```

## 性能调优

### 参数调整
```bash
# 实时调整参数 (系统运行时)
rosparam set /gps/height_correction_threshold 0.2  # 降低校正阈值
rosparam set /gps/correction_rate 0.05             # 降低校正速率

# 永久修改参数
nano ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config/rs16_rotation_v2.yaml
```

### 监控和分析
```bash
# 实时监控GPS质量
~/monitor_gps.sh

# 记录处理结果用于分析
rosbag record -O result_with_gps.bag /odometry /tf /gps/fix

# 分析处理效果
python3 test_gps_integration.py
```

## 成功标准

### 系统启动成功标志
- [x] roscore正常运行
- [x] state_estimation节点启动无错误  
- [x] 显示"GPS集成已启用"
- [x] 能够接收GPS数据 (rostopic hz > 0)

### GPS集成成功标志  
- [x] 显示"GPS坐标原点已设置"
- [x] 显示"起始位置已记录"
- [x] 显示"GPS高度校正"信息
- [x] 显示"GPS回环检测"信息

### 效果验证标志
- [x] 首尾高度差显著减小 (从米级到厘米级)
- [x] 轨迹整体一致性提升
- [x] 系统实时性能保持良好
- [x] 生成完整的分析报告和图表

## 下一步

### 短期优化
1. **参数精调**: 根据实际效果调整校正阈值和速率
2. **异常处理**: 完善GPS信号丢失时的处理机制  
3. **性能优化**: 优化GPS处理的计算效率

### 长期扩展
1. **多GNSS支持**: 集成北斗、GLONASS等卫星系统
2. **紧耦合融合**: 实现GPS-IMU-LiDAR紧耦合
3. **智能权重**: 基于环境的自适应融合策略

这个完整的执行指南提供了从环境准备到效果验证的全流程操作步骤。建议严格按照步骤执行，每个阶段都要验证成功后再进行下一步。
