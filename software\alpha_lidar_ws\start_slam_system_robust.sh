#!/bin/bash

# 鲁棒的SLAM系统启动脚本 - 自动检测可用功能

echo "=========================================="
echo "🚀 鲁棒SLAM系统启动"
echo "=========================================="

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/robust_slam_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "检测系统功能..."

# 检查可执行文件
SLAM_NODE_AVAILABLE=false
FORCE_MATCHER_AVAILABLE=false
GPS_CONTROLLER_AVAILABLE=false

if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
    SLAM_NODE_AVAILABLE=true
    echo "✅ SLAM核心节点可用"
else
    echo "❌ SLAM核心节点不可用"
fi

if [ -f "devel/lib/state_estimation/force_start_end_loop_matcher" ]; then
    FORCE_MATCHER_AVAILABLE=true
    echo "✅ 强制匹配器可用"
else
    echo "⚠️  强制匹配器不可用"
fi

if [ -f "src/state_estimation/scripts/intelligent_gps_constraint_controller.py" ]; then
    GPS_CONTROLLER_AVAILABLE=true
    echo "✅ GPS约束控制器可用"
else
    echo "⚠️  GPS约束控制器不可用"
fi

echo ""
echo "选择启动模式："
echo "1) 完整功能模式 (需要所有组件)"
echo "2) 基础SLAM模式 (只需要核心节点)"
echo "3) 智能检测模式 (SLAM + 智能检测)"
echo "4) 自动模式 (根据可用组件自动选择)"
echo ""

read -p "请选择模式 (1-4): " choice

case $choice in
    1)
        if [ "$SLAM_NODE_AVAILABLE" = true ] && [ "$FORCE_MATCHER_AVAILABLE" = true ] && [ "$GPS_CONTROLLER_AVAILABLE" = true ]; then
            echo "启动完整功能模式..."
            MODE="full"
        else
            echo "❌ 完整功能模式不可用，缺少必要组件"
            echo "自动切换到可用的最高级模式..."
            MODE="auto"
        fi
        ;;
    2)
        if [ "$SLAM_NODE_AVAILABLE" = true ]; then
            echo "启动基础SLAM模式..."
            MODE="basic"
        else
            echo "❌ 基础SLAM模式不可用，SLAM核心节点缺失"
            exit 1
        fi
        ;;
    3)
        if [ "$SLAM_NODE_AVAILABLE" = true ]; then
            echo "启动智能检测模式..."
            MODE="intelligent"
        else
            echo "❌ 智能检测模式不可用，SLAM核心节点缺失"
            exit 1
        fi
        ;;
    4)
        echo "自动检测最佳模式..."
        MODE="auto"
        ;;
    *)
        echo "使用自动模式..."
        MODE="auto"
        ;;
esac

# 自动模式选择逻辑
if [ "$MODE" = "auto" ]; then
    if [ "$SLAM_NODE_AVAILABLE" = true ] && [ "$FORCE_MATCHER_AVAILABLE" = true ] && [ "$GPS_CONTROLLER_AVAILABLE" = true ]; then
        MODE="full"
        echo "🎯 自动选择: 完整功能模式"
    elif [ "$SLAM_NODE_AVAILABLE" = true ] && [ "$GPS_CONTROLLER_AVAILABLE" = true ]; then
        MODE="intelligent"
        echo "🎯 自动选择: 智能检测模式"
    elif [ "$SLAM_NODE_AVAILABLE" = true ]; then
        MODE="basic"
        echo "🎯 自动选择: 基础SLAM模式"
    else
        echo "❌ 没有可用的启动模式"
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "🚀 启动模式: $MODE"
echo "=========================================="

case $MODE in
    "full")
        echo "启动完整功能SLAM系统..."
        roslaunch state_estimation optimized_slam_simple.launch \
            intensity_preset:=high_quality_preset \
            gps_loop_preset:=ultra_loose_start_end_preset \
            save_directory:="$OUTPUT_DIR" \
            enable_gps_loop_closure:=true \
            enable_intensity_preservation:=true \
            enable_adaptive_optimization:=true \
            enable_simple_analysis:=true \
            enable_performance_dashboard:=true \
            enable_intelligent_detection:=true &
        
        sleep 8
        
        # 设置保守的GPS约束参数
        rosparam set /state_estimation_node/gps/enable_plane_constraint true
        rosparam set /state_estimation_node/gps/plane_constraint_weight 0.05
        rosparam set /intelligent_gps_constraint_controller/constraint_disable_fitness 0.4
        
        echo ""
        echo "✅ 完整功能系统已启动"
        echo "功能: SLAM + GPS约束控制 + 强制匹配 + 智能检测"
        ;;
        
    "intelligent")
        echo "启动智能检测SLAM系统..."
        
        # 启动基础SLAM
        roslaunch state_estimation mapping_robosense.launch &
        sleep 5
        
        # 启动智能检测器
        if [ -f "src/state_estimation/scripts/intelligent_start_end_detector.py" ]; then
            rosrun state_estimation intelligent_start_end_detector.py &
            sleep 2
        fi
        
        # 启动GPS约束控制器
        if [ "$GPS_CONTROLLER_AVAILABLE" = true ]; then
            rosrun state_estimation intelligent_gps_constraint_controller.py &
            sleep 2
        fi
        
        # 启动强度保持
        if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
            rosrun state_estimation intensity_preserving_pcd_saver \
                _save_directory:="$OUTPUT_DIR" &
            sleep 2
        fi
        
        echo ""
        echo "✅ 智能检测系统已启动"
        echo "功能: SLAM + 智能检测 + GPS约束控制"
        ;;
        
    "basic")
        echo "启动基础SLAM系统..."
        
        # 启动基础SLAM
        roslaunch state_estimation mapping_robosense.launch &
        sleep 5
        
        # 启动强度保持
        if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
            rosrun state_estimation intensity_preserving_pcd_saver \
                _save_directory:="$OUTPUT_DIR" &
            sleep 2
        fi
        
        echo ""
        echo "✅ 基础SLAM系统已启动"
        echo "功能: 基础SLAM + 强度保持"
        ;;
esac

echo ""
echo "系统状态:"
echo "  输出目录: $OUTPUT_DIR"
echo "  启动模式: $MODE"
echo ""
echo "监控命令:"
case $MODE in
    "full")
        echo "  GPS约束状态: rostopic echo /gps_constraint_control"
        echo "  ICP匹配质量: rostopic echo /icp_fitness_score"
        echo "  智能检测:    rostopic echo /intelligent_detector_status"
        echo "  强制匹配:    rostopic echo /force_match_score"
        ;;
    "intelligent")
        echo "  智能检测:    rostopic echo /intelligent_detector_status"
        echo "  GPS约束状态: rostopic echo /gps_constraint_control"
        ;;
    "basic")
        echo "  SLAM状态:    rostopic echo /aft_mapped_to_init"
        echo "  点云数据:    rostopic echo /cloud_registered"
        ;;
esac

echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
