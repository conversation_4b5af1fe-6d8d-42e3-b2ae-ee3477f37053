#!/bin/bash

# GPS 100米回环SLAM系统启动脚本
# 专门设置GPS距离起点100米以内触发SLAM回环匹配

echo "=========================================="
echo "🎯 GPS 100米回环SLAM系统启动"
echo "=========================================="
echo ""
echo "系统特性："
echo "✅ GPS距离起点100米以内触发SLAM回环匹配"
echo "✅ 智能检测是否已离开起点足够远 (150米)"
echo "✅ 根据GPS质量动态调整匹配参数"
echo "✅ 高置信度时触发强制精细匹配"
echo "✅ 完全避免GPS约束导致的点云错位"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_100m_loop_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "100米回环检测配置："
echo "1) 标准配置 (100米触发，150米离开)"
echo "2) 敏感配置 (80米触发，120米离开)"
echo "3) 宽松配置 (120米触发，200米离开)"
echo "4) 自定义配置"
echo ""

read -p "请选择配置 (1-4): " config_choice

case $config_choice in
    1)
        echo "使用标准配置..."
        START_POINT_THRESHOLD=100.0
        MIN_DEPARTURE_DISTANCE=150.0
        MIN_TRAJECTORY_LENGTH=200.0
        LOOP_COOLDOWN=30.0
        echo "✅ 标准配置: 100米触发，150米离开"
        ;;
    2)
        echo "使用敏感配置..."
        START_POINT_THRESHOLD=80.0
        MIN_DEPARTURE_DISTANCE=120.0
        MIN_TRAJECTORY_LENGTH=150.0
        LOOP_COOLDOWN=25.0
        echo "✅ 敏感配置: 80米触发，120米离开"
        ;;
    3)
        echo "使用宽松配置..."
        START_POINT_THRESHOLD=120.0
        MIN_DEPARTURE_DISTANCE=200.0
        MIN_TRAJECTORY_LENGTH=300.0
        LOOP_COOLDOWN=40.0
        echo "✅ 宽松配置: 120米触发，200米离开"
        ;;
    4)
        echo "自定义配置："
        read -p "回环触发距离 (50-150米): " START_POINT_THRESHOLD
        read -p "最小离开距离 (100-300米): " MIN_DEPARTURE_DISTANCE
        read -p "最小轨迹长度 (100-500米): " MIN_TRAJECTORY_LENGTH
        read -p "检测冷却时间 (20-60秒): " LOOP_COOLDOWN
        echo "✅ 自定义配置已设置"
        ;;
    *)
        echo "使用默认标准配置..."
        START_POINT_THRESHOLD=100.0
        MIN_DEPARTURE_DISTANCE=150.0
        MIN_TRAJECTORY_LENGTH=200.0
        LOOP_COOLDOWN=30.0
        ;;
esac

echo ""
echo "SLAM匹配增强级别："
echo "1) 高精度匹配 (适用于RTK GPS)"
echo "2) 平衡匹配 (适用于DGPS)"
echo "3) 鲁棒匹配 (适用于单点定位)"
echo ""

read -p "请选择匹配级别 (1-3): " match_level

case $match_level in
    1)
        ENHANCED_SEARCH_RADIUS=150.0
        ENHANCED_VOXEL_SIZE=0.01
        ENHANCED_MAX_ITERATIONS=800
        MULTI_RESOLUTION_LEVELS=4
        echo "✅ 高精度匹配: 搜索150m, 体素0.01m, 迭代800次"
        ;;
    2)
        ENHANCED_SEARCH_RADIUS=120.0
        ENHANCED_VOXEL_SIZE=0.02
        ENHANCED_MAX_ITERATIONS=600
        MULTI_RESOLUTION_LEVELS=3
        echo "✅ 平衡匹配: 搜索120m, 体素0.02m, 迭代600次"
        ;;
    3)
        ENHANCED_SEARCH_RADIUS=100.0
        ENHANCED_VOXEL_SIZE=0.03
        ENHANCED_MAX_ITERATIONS=400
        MULTI_RESOLUTION_LEVELS=2
        echo "✅ 鲁棒匹配: 搜索100m, 体素0.03m, 迭代400次"
        ;;
    *)
        ENHANCED_SEARCH_RADIUS=120.0
        ENHANCED_VOXEL_SIZE=0.02
        ENHANCED_MAX_ITERATIONS=600
        MULTI_RESOLUTION_LEVELS=3
        echo "✅ 默认平衡匹配"
        ;;
esac

echo ""
echo "启动GPS 100米回环SLAM系统..."

# 启动优化的SLAM系统
roslaunch state_estimation optimized_slam_simple.launch \
    intensity_preset:=high_quality_preset \
    gps_loop_preset:=ultra_loose_start_end_preset \
    save_directory:="$OUTPUT_DIR" \
    enable_gps_loop_closure:=true \
    enable_intensity_preservation:=true \
    enable_adaptive_optimization:=true \
    enable_simple_analysis:=true \
    enable_performance_dashboard:=false \
    enable_intelligent_detection:=true &

# 等待系统启动
sleep 10

echo ""
echo "设置100米回环检测参数..."

# 确保完全禁用GPS平面约束
rosparam set /state_estimation_node/gps/enable_plane_constraint false
rosparam set /state_estimation_node/gps/enable_soft_constraint false
rosparam set /state_estimation_node/gps/constraint_mode 0
rosparam set /state_estimation_node/gps/plane_constraint_weight 0.0

# 设置100米回环检测器参数
rosparam set /gps_100m_loop_detector/start_point_threshold $START_POINT_THRESHOLD
rosparam set /gps_100m_loop_detector/min_departure_distance $MIN_DEPARTURE_DISTANCE
rosparam set /gps_100m_loop_detector/min_trajectory_length $MIN_TRAJECTORY_LENGTH
rosparam set /gps_100m_loop_detector/loop_detection_cooldown $LOOP_COOLDOWN

# 设置GPS参考增强参数 (配合100米检测)
rosparam set /gps_reference_enhanced_slam/gps_reference_radius $START_POINT_THRESHOLD
rosparam set /gps_reference_enhanced_slam/slam_enhancement_radius $START_POINT_THRESHOLD
rosparam set /gps_reference_enhanced_slam/enhanced_search_radius $ENHANCED_SEARCH_RADIUS
rosparam set /gps_reference_enhanced_slam/enhanced_voxel_size $ENHANCED_VOXEL_SIZE
rosparam set /gps_reference_enhanced_slam/enhanced_max_iterations $ENHANCED_MAX_ITERATIONS
rosparam set /gps_reference_enhanced_slam/multi_resolution_levels $MULTI_RESOLUTION_LEVELS

# 设置智能检测参数 (配合100米检测)
rosparam set /intelligent_start_end_detector/return_threshold $START_POINT_THRESHOLD
rosparam set /intelligent_start_end_detector/departure_threshold $MIN_DEPARTURE_DISTANCE

# 设置强制匹配器参数 (高精度匹配)
rosparam set /force_start_end_loop_matcher/voxel_size $ENHANCED_VOXEL_SIZE
rosparam set /force_start_end_loop_matcher/search_radius $ENHANCED_SEARCH_RADIUS
rosparam set /force_start_end_loop_matcher/max_iterations $ENHANCED_MAX_ITERATIONS
rosparam set /force_start_end_loop_matcher/outlier_rejection_threshold 0.2

echo "✅ 参数设置完成"

echo ""
echo "=========================================="
echo "🚀 GPS 100米回环SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统配置:"
echo "  输出目录: $OUTPUT_DIR"
echo "  GPS平面约束: ❌ 完全禁用"
echo "  回环触发距离: ${START_POINT_THRESHOLD}米"
echo "  最小离开距离: ${MIN_DEPARTURE_DISTANCE}米"
echo "  最小轨迹长度: ${MIN_TRAJECTORY_LENGTH}米"
echo "  检测冷却时间: ${LOOP_COOLDOWN}秒"
echo "  搜索半径: ${ENHANCED_SEARCH_RADIUS}米"
echo "  体素大小: ${ENHANCED_VOXEL_SIZE}米"
echo ""
echo "工作流程:"
echo "🏁 1. 设置GPS起点位置"
echo "🚀 2. 检测离开起点${MIN_DEPARTURE_DISTANCE}米"
echo "📏 3. 监控轨迹长度达到${MIN_TRAJECTORY_LENGTH}米"
echo "🎯 4. GPS距离起点<${START_POINT_THRESHOLD}米时触发回环"
echo "💎 5. 执行高精度SLAM匹配"
echo "✅ 6. 完成回环闭合"
echo ""
echo "监控命令:"
echo "  100米回环状态: rostopic echo /gps_100m_loop_status"
echo "  距离起点:      rostopic echo /gps_distance_to_start"
echo "  回环触发:      rostopic echo /gps_100m_loop_trigger"
echo "  强制匹配:      rostopic echo /force_match_score"
echo "  SLAM增强:      rostopic echo /slam_enhancement_trigger"
echo ""
echo "实时参数调节:"
echo "  调节触发距离: rosparam set /gps_100m_loop_detector/start_point_threshold 90.0"
echo "  调节离开距离: rosparam set /gps_100m_loop_detector/min_departure_distance 130.0"
echo "  调节搜索半径: rosparam set /gps_reference_enhanced_slam/enhanced_search_radius 140.0"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果:"
echo "✅ GPS距离起点100米内自动触发SLAM回环匹配"
echo "✅ 智能检测确保已完成完整轨迹"
echo "✅ 根据GPS质量动态调整匹配精度"
echo "✅ 首尾偏差从几十米减少到几米内"
echo "✅ 完全避免GPS约束导致的点云错位"
echo ""
echo "🔍 实时监控GPS 100米回环效果:"
echo "  监控脚本: ./monitor_gps_100m_loop.sh"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
