#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SLAM回环检测优化工具
专门用于分析和优化GPS-SLAM系统的回环检测问题
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import yaml
import os

class LoopClosureOptimizer:
    def __init__(self):
        self.gps_quality_stats = defaultdict(int)
        self.trajectory_analysis = {}
        self.loop_closure_config = {}
        
    def analyze_gps_quality_issue(self, log_data):
        """分析GPS质量问题"""
        print("🔍 分析GPS质量问题...")
        
        # 解析日志中的GPS状态
        gps_status_counts = defaultdict(int)
        poor_quality_periods = []
        
        for line in log_data.split('\n'):
            if '[GPS] Status: NO_FIX (-1)' in line:
                gps_status_counts['NO_FIX'] += 1
            elif 'Poor GPS quality, status: -1' in line:
                gps_status_counts['POOR_QUALITY'] += 1
        
        print(f"📊 GPS质量统计:")
        print(f"   NO_FIX状态: {gps_status_counts['NO_FIX']} 次")
        print(f"   质量警告: {gps_status_counts['POOR_QUALITY']} 次")
        
        # 分析问题
        if gps_status_counts['NO_FIX'] > 10:
            print("❌ 严重问题: GPS信号质量极差，无法提供有效定位")
            return self.generate_gps_fix_solutions()
        
        return None
    
    def generate_gps_fix_solutions(self):
        """生成GPS修复方案"""
        solutions = {
            "immediate_fixes": [
                "检查GPS天线连接和位置",
                "确保GPS天线有清晰的天空视野",
                "检查是否有电磁干扰源",
                "验证GPS接收器配置"
            ],
            "config_adjustments": [
                "降低GPS权重，增加视觉SLAM权重",
                "调整GPS质量阈值",
                "启用纯视觉SLAM模式作为备选",
                "优化传感器融合参数"
            ],
            "parameter_tuning": {
                "gps_weight": 0.1,  # 降低GPS权重
                "visual_weight": 0.9,  # 增加视觉权重
                "gps_quality_threshold": 0,  # 只接受RTK固定解
                "loop_closure_threshold": 0.3  # 降低回环检测阈值
            }
        }
        return solutions
    
    def create_optimized_launch_config(self):
        """创建优化的launch配置"""
        print("🔧 创建优化的launch配置...")
        
        config = """
<!-- 优化的GPS-SLAM配置 -->
<launch>
    <!-- GPS配置优化 -->
    <param name="gps_weight" value="0.1" />
    <param name="visual_slam_weight" value="0.9" />
    <param name="gps_quality_threshold" value="0" />
    
    <!-- 回环检测优化 -->
    <param name="loop_closure_enabled" value="true" />
    <param name="loop_closure_threshold" value="0.3" />
    <param name="loop_closure_min_distance" value="5.0" />
    <param name="loop_closure_max_distance" value="50.0" />
    
    <!-- 视觉特征优化 -->
    <param name="feature_detection_threshold" value="0.01" />
    <param name="max_features" value="1000" />
    <param name="feature_matching_threshold" value="0.7" />
    
    <!-- 纯视觉SLAM备选模式 -->
    <param name="pure_visual_slam_fallback" value="true" />
    <param name="gps_timeout_threshold" value="5.0" />
    
    <!-- 调试输出 -->
    <param name="debug_loop_closure" value="true" />
    <param name="debug_gps_quality" value="true" />
</launch>
"""
        
        with open("optimized_mapping_config.launch", "w") as f:
            f.write(config)
        
        print("✅ 优化配置已保存到: optimized_mapping_config.launch")
        return "optimized_mapping_config.launch"
    
    def create_gps_quality_monitor(self):
        """创建GPS质量监控脚本"""
        print("📊 创建GPS质量监控脚本...")
        
        monitor_script = '''#!/usr/bin/env python3
import rospy
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import String
import time

class GPSQualityMonitor:
    def __init__(self):
        rospy.init_node('gps_quality_monitor')
        self.gps_sub = rospy.Subscriber('/rtk/gnss', NavSatFix, self.gps_callback)
        self.quality_pub = rospy.Publisher('/gps_quality_status', String, queue_size=10)
        
        self.poor_quality_count = 0
        self.last_good_gps_time = time.time()
        
    def gps_callback(self, msg):
        current_time = time.time()
        
        # 检查GPS质量
        if hasattr(msg, 'status') and msg.status.status < 0:
            self.poor_quality_count += 1
            rospy.logwarn(f"GPS质量差: status={msg.status.status}")
            
            # 如果GPS质量持续很差，建议切换到纯视觉模式
            if current_time - self.last_good_gps_time > 10.0:
                self.quality_pub.publish("SWITCH_TO_VISUAL_ONLY")
                rospy.logwarn("建议切换到纯视觉SLAM模式")
        else:
            self.poor_quality_count = 0
            self.last_good_gps_time = current_time
            self.quality_pub.publish("GPS_OK")

if __name__ == '__main__':
    monitor = GPSQualityMonitor()
    rospy.spin()
'''
        
        with open("gps_quality_monitor.py", "w") as f:
            f.write(monitor_script)
        
        os.chmod("gps_quality_monitor.py", 0o755)
        print("✅ GPS质量监控脚本已保存到: gps_quality_monitor.py")
        return "gps_quality_monitor.py"
    
    def create_loop_closure_enhancer(self):
        """创建回环检测增强脚本"""
        print("🔄 创建回环检测增强脚本...")
        
        enhancer_script = '''#!/usr/bin/env python3
import rospy
import numpy as np
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Bool
import tf2_ros
import tf2_geometry_msgs

class LoopClosureEnhancer:
    def __init__(self):
        rospy.init_node('loop_closure_enhancer')
        
        self.pose_history = []
        self.loop_closure_pub = rospy.Publisher('/loop_closure_detected', Bool, queue_size=10)
        self.pose_sub = rospy.Subscriber('/slam_pose', PoseStamped, self.pose_callback)
        
        # 参数
        self.min_loop_distance = rospy.get_param('~min_loop_distance', 5.0)
        self.max_loop_distance = rospy.get_param('~max_loop_distance', 50.0)
        self.position_threshold = rospy.get_param('~position_threshold', 2.0)
        
    def pose_callback(self, msg):
        current_pos = np.array([
            msg.pose.position.x,
            msg.pose.position.y,
            msg.pose.position.z
        ])
        
        # 检查是否可能的回环
        for i, hist_pose in enumerate(self.pose_history[:-50]):  # 排除最近的50个点
            hist_pos = np.array([
                hist_pose.pose.position.x,
                hist_pose.pose.position.y,
                hist_pose.pose.position.z
            ])
            
            distance = np.linalg.norm(current_pos - hist_pos)
            
            if self.min_loop_distance < distance < self.max_loop_distance:
                if np.linalg.norm(current_pos[:2] - hist_pos[:2]) < self.position_threshold:
                    rospy.loginfo(f"检测到可能的回环: 距离={distance:.2f}m")
                    self.loop_closure_pub.publish(Bool(True))
                    break
        
        self.pose_history.append(msg)
        
        # 限制历史记录长度
        if len(self.pose_history) > 1000:
            self.pose_history = self.pose_history[-800:]

if __name__ == '__main__':
    enhancer = LoopClosureEnhancer()
    rospy.spin()
'''
        
        with open("loop_closure_enhancer.py", "w") as f:
            f.write(enhancer_script)
        
        os.chmod("loop_closure_enhancer.py", 0o755)
        print("✅ 回环检测增强脚本已保存到: loop_closure_enhancer.py")
        return "loop_closure_enhancer.py"
    
    def generate_optimization_report(self, log_data):
        """生成优化报告"""
        print("\n" + "="*60)
        print("📋 SLAM回环检测优化报告")
        print("="*60)
        
        # 分析GPS问题
        gps_solutions = self.analyze_gps_quality_issue(log_data)
        
        print("\n🎯 主要问题:")
        print("1. GPS信号质量极差 (status=-1, NO_FIX)")
        print("2. GPS坐标固定不变，无法提供有效的全局定位")
        print("3. 回环检测依赖GPS辅助，GPS失效导致回环检测失败")
        
        print("\n🔧 解决方案:")
        print("1. 立即修复GPS硬件问题")
        print("2. 调整参数，降低GPS权重，增加视觉SLAM权重")
        print("3. 启用纯视觉SLAM模式作为备选")
        print("4. 优化回环检测阈值和特征匹配参数")
        
        print("\n📁 生成的文件:")
        config_file = self.create_optimized_launch_config()
        monitor_file = self.create_gps_quality_monitor()
        enhancer_file = self.create_loop_closure_enhancer()
        
        print(f"   - {config_file}")
        print(f"   - {monitor_file}")
        print(f"   - {enhancer_file}")
        
        print("\n🚀 使用方法:")
        print("1. 首先修复GPS硬件问题")
        print("2. 使用优化的launch配置:")
        print("   roslaunch state_estimation optimized_mapping_config.launch")
        print("3. 启动GPS质量监控:")
        print("   python3 gps_quality_monitor.py")
        print("4. 启动回环检测增强:")
        print("   python3 loop_closure_enhancer.py")
        
        return {
            'config_file': config_file,
            'monitor_file': monitor_file,
            'enhancer_file': enhancer_file,
            'solutions': gps_solutions
        }

def main():
    """主函数"""
    print("🔧 SLAM回环检测优化工具")
    print("="*50)
    
    # 模拟日志数据（实际使用时从文件读取）
    log_data = """
[INFO] [1754143677.285951232]: [GPS] Callback #17171: lat=30.737022, lon=103.970776, alt=520.69, status=-1
[INFO] [1754143677.285995297]: [GPS] Status: NO_FIX (-1), Quality: POOR
[WARN] [1754143677.286013677]: [GPS Status] Poor GPS quality, status: -1
[WARN] [1754143677.286157491]: [GPS Status] Poor GPS quality, status: -1
"""
    
    optimizer = LoopClosureOptimizer()
    results = optimizer.generate_optimization_report(log_data)
    
    print("\n✅ 优化工具运行完成!")
    print("请按照报告中的建议进行配置和测试。")

if __name__ == "__main__":
    main()
