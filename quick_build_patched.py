#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick build script for bagpy patched version
Fast solution with direct bagpy fix
"""

import os
import sys
import subprocess
import shutil
import time

def create_bagpy_version_file():
    """确保bagpy version文件存在"""
    try:
        import bagpy
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        if not os.path.exists(version_file):
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
            print(f"✅ 创建bagpy version文件: {version_file}")
        else:
            print(f"✅ bagpy version文件已存在: {version_file}")
        return True
    except Exception as e:
        print(f"⚠️  处理bagpy version文件时出错: {e}")
        return False

def quick_build():
    """快速构建exe"""
    print("🚀 开始快速构建...")
    
    # 清理
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 使用最简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=GPS_3D_Analyzer_Patched",
        "--add-data=gps_gui_analyzer_bagpy_patched.py;.",
        "gps_gui_analyzer_bagpy_patched.py"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        build_time = time.time() - start_time
        print(f"构建耗时: {build_time:.1f}秒")
        
        if result.returncode == 0:
            print("✅ 快速构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_release():
    """创建发布包"""
    print("📦 创建发布包...")
    
    release_dir = "GPS_3D_Analyzer_Patched_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制exe文件
    exe_path = "dist/GPS_3D_Analyzer_Patched.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        
        # 获取文件大小
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        print(f"✅ exe文件复制完成 ({size_mb:.1f} MB)")
    else:
        print("❌ exe文件不存在")
        return False
    
    # 创建说明文件
    readme_content = f"""# GPS 3D 轨迹分析器 - Bagpy修复版

## 🔧 修复说明
本版本直接在代码中修复了bagpy的导入问题，无需复杂的PyInstaller配置。

### ✅ 修复内容
- 直接修复bagpy的version文件问题
- 智能检测和创建缺失的文件
- 兼容模式支持（即使bagpy有问题也能运行）
- 保持所有原有功能

### 🎯 主要功能
- 📁 从ROS bag文件提取GPS数据
- 🎨 3D轨迹可视化（深灰色主题 + 深绿色文字）
- 📊 详细的GPS质量分析（中文显示）
- 💾 导出轨迹为文本文件（帧编号 X Y Z格式）
- 🎮 交互式3D控制（鼠标拖拽旋转、滚轮缩放）

### 🚀 使用方法
1. 双击 GPS_3D_Analyzer_Patched.exe 启动程序
2. 程序会自动检测和修复bagpy问题
3. 选择ROS bag文件进行分析
4. 享受完整的GPS轨迹分析功能

### 🎨 界面特色
- 深灰色专业主题 (#2E2E2E)
- 深绿色文字显示 (#00C851)
- 深色选择背景 (#404040)
- 修复的3D鼠标控制
- 完整中文支持

### 💻 系统要求
- Windows 10 64位
- 4GB内存
- 支持OpenGL的显卡

### 🔧 故障排除
如果仍有bagpy相关问题：
1. 程序会自动尝试修复
2. 支持兼容模式运行
3. 检查终端输出获取详细信息

### 📝 版本信息
- 版本: 1.0.3 (Bagpy修复版)
- 构建日期: {time.strftime("%Y-%m-%d")}
- 修复方式: 直接代码修复
- 构建时间: 快速构建

---
© 2024 GPS Analyzer Team. All rights reserved.
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("⚡ GPS 3D轨迹分析器 - 快速Bagpy修复构建")
    print("=" * 50)
    
    # 检查文件
    if not os.path.exists("gps_gui_analyzer_bagpy_patched.py"):
        print("❌ 修复版文件不存在")
        return False
    
    start_time = time.time()
    
    # 构建步骤
    steps = [
        ("处理bagpy version文件", create_bagpy_version_file),
        ("快速构建exe", quick_build),
        ("创建发布包", create_release),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ 失败于步骤: {step_name}")
            return False
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("🎉 快速Bagpy修复构建完成!")
    print("=" * 50)
    print(f"⏱️  总耗时: {total_time:.1f}秒")
    print("📁 发布包: GPS_3D_Analyzer_Patched_Release/")
    print("🚀 exe文件: GPS_3D_Analyzer_Patched.exe")
    print("\n✅ 这个版本直接修复了bagpy问题，应该可以正常运行")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
