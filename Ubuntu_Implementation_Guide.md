# Ubuntu 20.04 ROS环境下αLiDAR GPS集成实施指南

## 环境准备

### 1. 系统要求确认
```bash
# 确认系统版本
lsb_release -a
# 应该显示: Ubuntu 20.04.x LTS

# 确认ROS版本
rosversion -d
# 应该显示: noetic
```

### 2. 安装必要依赖
```bash
# 更新软件包列表
sudo apt update

# 安装GeographicLib (GPS坐标转换库)
sudo apt install -y libgeographic-dev libgeographic19

# 安装其他可能需要的依赖
sudo apt install -y \
    libeigen3-dev \
    libpcl-dev \
    libopencv-dev \
    python3-matplotlib \
    python3-numpy

# 验证GeographicLib安装
pkg-config --modversion geographic
```

### 3. 检查现有工作空间
```bash
# 进入αLiDAR工作空间
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws

# 检查当前状态
ls -la src/
catkin_make --dry-run  # 干运行检查依赖
```

## 代码修改实施

### 第一步：备份原始代码
```bash
# 创建备份目录
mkdir -p ~/alpha_lidar_backup/$(date +%Y%m%d_%H%M%S)

# 备份关键文件
cp -r ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation \
      ~/alpha_lidar_backup/$(date +%Y%m%d_%H%M%S)/

echo "原始代码已备份到: ~/alpha_lidar_backup/$(date +%Y%m%d_%H%M%S)/"
```

### 第二步：修改CMakeLists.txt
```bash
# 编辑CMakeLists.txt
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation
cp CMakeLists.txt CMakeLists.txt.bak

# 添加GeographicLib依赖
cat >> CMakeLists.txt << 'EOF'

# GPS集成相关依赖
find_package(PkgConfig REQUIRED)
pkg_check_modules(GEOGRAPHIC REQUIRED geographic)

# 添加到include目录
include_directories(${GEOGRAPHIC_INCLUDE_DIRS})

# 添加到链接库
target_link_libraries(state_estimation_node
  ${catkin_LIBRARIES}
  ${PCL_LIBRARIES}
  ${PYTHON_LIBRARIES}
  ${GEOGRAPHIC_LIBRARIES}
)
EOF
```

### 第三步：修改package.xml
```bash
# 备份并修改package.xml
cp package.xml package.xml.bak

# 在</package>之前添加GPS依赖
sed -i '/<\/package>/i\  <build_depend>libgeographic-dev</build_depend>' package.xml
sed -i '/<\/package>/i\  <run_depend>libgeographic-dev</run_depend>' package.xml
```

### 第四步：修改主要源文件
```bash
# 创建GPS集成的头文件
cat > include/gps_processor.h << 'EOF'
#ifndef GPS_PROCESSOR_H
#define GPS_PROCESSOR_H

#include <ros/ros.h>
#include <sensor_msgs/NavSatFix.h>
#include <GeographicLib/LocalCartesian.hpp>
#include <deque>
#include <mutex>
#include <Eigen/Dense>

class GPSProcessor {
private:
    std::deque<sensor_msgs::NavSatFix> gps_buffer_;
    std::mutex gps_mutex_;
    std::unique_ptr<GeographicLib::LocalCartesian> local_cartesian_;
    bool gps_origin_set_ = false;
    
    // GPS状态
    struct {
        bool is_rtk_fixed = false;
        double last_update_time = 0.0;
        int consecutive_good_fixes = 0;
    } gps_status_;
    
    // 起始位置
    Eigen::Vector3d start_gps_position_ = Eigen::Vector3d::Zero();
    Eigen::Vector3d start_slam_position_ = Eigen::Vector3d::Zero();
    bool start_positions_set_ = false;
    
    // 配置参数
    double height_correction_threshold_ = 0.3;
    double correction_rate_ = 0.1;
    
public:
    GPSProcessor();
    void gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg);
    bool getGPSPosition(double timestamp, Eigen::Vector3d& gps_position, double& gps_std);
    Eigen::Vector3d correctHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp);
    bool detectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp, Eigen::Vector3d& loop_constraint);
    void setStartPositions(const Eigen::Vector3d& gps_pos, const Eigen::Vector3d& slam_pos);
    
private:
    Eigen::Vector3d convertGPSToLocal(const sensor_msgs::NavSatFix& gps_msg);
    bool isGPSValid(const sensor_msgs::NavSatFix& gps_msg);
};

#endif // GPS_PROCESSOR_H
EOF

# 创建GPS处理器实现文件
cat > src/gps_processor.cpp << 'EOF'
#include "gps_processor.h"
#include <algorithm>
#include <cmath>

GPSProcessor::GPSProcessor() {
    local_cartesian_ = std::make_unique<GeographicLib::LocalCartesian>();
}

void GPSProcessor::gpsCallback(const sensor_msgs::NavSatFix::ConstPtr& gps_msg) {
    std::lock_guard<std::mutex> lock(gps_mutex_);
    
    if (!isGPSValid(*gps_msg)) return;
    
    // 更新GPS状态
    gps_status_.last_update_time = gps_msg->header.stamp.toSec();
    gps_status_.is_rtk_fixed = (gps_msg->status.status == sensor_msgs::NavSatStatus::STATUS_GBAS_FIX);
    
    if (gps_status_.is_rtk_fixed || gps_msg->status.status == sensor_msgs::NavSatStatus::STATUS_FIX) {
        gps_status_.consecutive_good_fixes++;
    } else {
        gps_status_.consecutive_good_fixes = 0;
    }
    
    // 设置坐标原点
    if (!gps_origin_set_ && gps_status_.consecutive_good_fixes > 5) {
        local_cartesian_->Reset(gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
        gps_origin_set_ = true;
        ROS_INFO("GPS坐标原点已设置: lat=%.8f, lon=%.8f, alt=%.3f",
                gps_msg->latitude, gps_msg->longitude, gps_msg->altitude);
    }
    
    // 添加到缓冲区
    gps_buffer_.push_back(*gps_msg);
    while (gps_buffer_.size() > 500) {
        gps_buffer_.pop_front();
    }
}

bool GPSProcessor::getGPSPosition(double timestamp, Eigen::Vector3d& gps_position, double& gps_std) {
    std::lock_guard<std::mutex> lock(gps_mutex_);
    
    if (gps_buffer_.empty() || !gps_origin_set_) return false;
    
    // 查找最近的GPS数据
    double min_time_diff = std::numeric_limits<double>::max();
    sensor_msgs::NavSatFix closest_gps;
    bool found = false;
    
    for (const auto& gps_msg : gps_buffer_) {
        double time_diff = std::abs(gps_msg.header.stamp.toSec() - timestamp);
        if (time_diff < min_time_diff && time_diff < 0.5) {
            min_time_diff = time_diff;
            closest_gps = gps_msg;
            found = true;
        }
    }
    
    if (!found) return false;
    
    gps_position = convertGPSToLocal(closest_gps);
    gps_std = gps_status_.is_rtk_fixed ? 0.02 : 1.0;
    
    return true;
}

Eigen::Vector3d GPSProcessor::correctHeightWithGPS(const Eigen::Vector3d& slam_position, double timestamp) {
    Eigen::Vector3d gps_position;
    double gps_std;
    
    if (!getGPSPosition(timestamp, gps_position, gps_std)) {
        return slam_position;
    }
    
    double height_diff = gps_position.z() - slam_position.z();
    
    if (std::abs(height_diff) > height_correction_threshold_) {
        Eigen::Vector3d corrected_position = slam_position;
        corrected_position.z() += height_diff * correction_rate_;
        
        ROS_INFO("GPS高度校正: SLAM=%.3f, GPS=%.3f, 校正=%.3f", 
                slam_position.z(), gps_position.z(), corrected_position.z());
        
        return corrected_position;
    }
    
    return slam_position;
}

bool GPSProcessor::detectGPSLoopClosure(const Eigen::Vector3d& current_slam_pos, double timestamp, 
                                       Eigen::Vector3d& loop_constraint) {
    if (!start_positions_set_) return false;
    
    Eigen::Vector3d current_gps_pos;
    double gps_std;
    
    if (!getGPSPosition(timestamp, current_gps_pos, gps_std)) {
        return false;
    }
    
    double distance_to_start = (current_gps_pos - start_gps_position_).norm();
    
    if (distance_to_start < 3.0) {
        Eigen::Vector3d slam_drift = current_slam_pos - start_slam_position_;
        Eigen::Vector3d gps_drift = current_gps_pos - start_gps_position_;
        
        loop_constraint = gps_drift - slam_drift;
        
        ROS_INFO("GPS回环检测: 距离起点%.2fm, 约束[%.3f,%.3f,%.3f]",
                distance_to_start, loop_constraint.x(), 
                loop_constraint.y(), loop_constraint.z());
        
        return loop_constraint.norm() > 0.1;
    }
    
    return false;
}

void GPSProcessor::setStartPositions(const Eigen::Vector3d& gps_pos, const Eigen::Vector3d& slam_pos) {
    start_gps_position_ = gps_pos;
    start_slam_position_ = slam_pos;
    start_positions_set_ = true;
    ROS_INFO("起始位置已记录 - SLAM: [%.3f,%.3f,%.3f], GPS: [%.3f,%.3f,%.3f]",
            slam_pos.x(), slam_pos.y(), slam_pos.z(),
            gps_pos.x(), gps_pos.y(), gps_pos.z());
}

Eigen::Vector3d GPSProcessor::convertGPSToLocal(const sensor_msgs::NavSatFix& gps_msg) {
    if (!gps_origin_set_) return Eigen::Vector3d::Zero();
    
    double x, y, z;
    local_cartesian_->Forward(gps_msg.latitude, gps_msg.longitude, gps_msg.altitude, x, y, z);
    return Eigen::Vector3d(x, y, z);
}

bool GPSProcessor::isGPSValid(const sensor_msgs::NavSatFix& gps_msg) {
    return gps_msg.status.status >= sensor_msgs::NavSatStatus::STATUS_FIX &&
           gps_msg.latitude != 0.0 && gps_msg.longitude != 0.0;
}
EOF
```

### 第五步：修改voxelMapping.cpp
```bash
# 备份原文件
cp src/voxelMapping.cpp src/voxelMapping.cpp.bak

# 在文件开头添加GPS相关头文件
sed -i '/#include "voxel_map_util.hpp"/a #include "gps_processor.h"' src/voxelMapping.cpp

# 在全局变量区域添加GPS处理器
sed -i '/shared_ptr<ImuProcess> p_imu(new ImuProcess());/a std::unique_ptr<GPSProcessor> gps_processor;' src/voxelMapping.cpp
sed -i '/std::unique_ptr<GPSProcessor> gps_processor;/a bool enable_gps_correction = true;' src/voxelMapping.cpp

# 在execute函数中添加GPS处理逻辑
# 这里需要手动编辑，因为修改较复杂
echo "请手动编辑src/voxelMapping.cpp，在execute()函数中添加GPS处理逻辑"
echo "参考alpha_lidar_gps_modification.cpp中的示例代码"
```

## 编译系统

### 第一步：清理和编译
```bash
# 进入工作空间
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws

# 清理之前的编译结果
rm -rf build/ devel/

# 设置ROS环境
source /opt/ros/noetic/setup.bash

# 编译
catkin_make -DCATKIN_WHITELIST_PACKAGES="state_estimation" -j4

# 检查编译结果
echo "编译状态: $?"
if [ $? -eq 0 ]; then
    echo "✅ 编译成功!"
else
    echo "❌ 编译失败，请检查错误信息"
    exit 1
fi
```

### 第二步：设置环境变量
```bash
# 添加到当前会话
source ~/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash

# 添加到.bashrc (可选，方便后续使用)
echo "source ~/alpha_lidar_GPS/software/alpha_lidar_ws/devel/setup.bash" >> ~/.bashrc
```

## 配置文件修改

### 修改launch文件
```bash
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/launch

# 备份原launch文件
cp mapping_robosense.launch mapping_robosense.launch.bak

# 添加GPS topic重映射
cat >> mapping_robosense.launch << 'EOF'

    <!-- GPS相关配置 -->
    <remap from="/gps/fix" to="/ublox_gps/fix"/>
    <!-- 根据实际bag文件中的GPS topic名称调整 -->
    
EOF
```

### 修改配置文件
```bash
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws/src/state_estimation/config

# 备份配置文件
cp rs16_rotation_v2.yaml rs16_rotation_v2.yaml.bak

# 添加GPS配置
cat >> rs16_rotation_v2.yaml << 'EOF'

# GPS集成配置
gps:
    enable_correction: true
    height_correction_threshold: 0.3    # 高度校正阈值(米)
    correction_rate: 0.1                # 校正速率(0-1)
    loop_closure_distance: 3.0          # 回环检测距离(米)
    rtk_position_std: 0.02              # RTK位置标准差(米)
    gps_position_std: 1.0               # 普通GPS位置标准差(米)
    timeout: 0.5                        # GPS数据超时时间(秒)

common:
    gps_topic: "/ublox_gps/fix"         # GPS topic名称
EOF
```

## 执行和测试

### 方案1：分窗口手动执行 (推荐)

#### 窗口1：启动roscore
```bash
# 新开终端窗口1
roscore
```

#### 窗口2：启动αLiDAR节点
```bash
# 新开终端窗口2
cd ~/alpha_lidar_GPS/software/alpha_lidar_ws
source devel/setup.bash

# 启动αLiDAR处理节点
roslaunch state_estimation mapping_robosense.launch
```

#### 窗口3：播放bag文件
```bash
# 新开终端窗口3
cd ~/datasets  # 或者bag文件所在目录

# 首先检查bag文件中的topic
rosbag info UM982loop_715std_maximum_synced.bag

# 播放bag文件 (暂停模式)
rosbag play UM982loop_715std_maximum_synced.bag --pause

# 按空格键开始播放
# 按空格键暂停/继续
# 按's'键单步播放
```

#### 窗口4：监控GPS数据 (可选)
```bash
# 新开终端窗口4
# 监控GPS topic
rostopic echo /ublox_gps/fix

# 或者监控GPS数据频率
rostopic hz /ublox_gps/fix
```

#### 窗口5：监控系统状态 (可选)
```bash
# 新开终端窗口5
# 监控所有topic
rostopic list

# 监控系统资源
htop

# 监控ROS节点
rosnode list
rosnode info /state_estimation_node
```

### 方案2：一键启动脚本
```bash
# 创建启动脚本
cat > ~/start_alpha_lidar_gps.sh << 'EOF'
#!/bin/bash

# 设置工作目录
WORKSPACE=~/alpha_lidar_GPS/software/alpha_lidar_ws
BAG_FILE=~/datasets/UM982loop_715std_maximum_synced.bag

# 检查文件存在性
if [ ! -f "$BAG_FILE" ]; then
    echo "错误: 找不到bag文件 $BAG_FILE"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source $WORKSPACE/devel/setup.bash

echo "启动αLiDAR GPS集成系统..."
echo "请在另一个终端中运行以下命令播放bag文件:"
echo "rosbag play $BAG_FILE --pause"
echo ""

# 启动系统
roslaunch state_estimation mapping_robosense.launch
EOF

chmod +x ~/start_alpha_lidar_gps.sh

# 使用脚本启动
~/start_alpha_lidar_gps.sh
```

## 调试和监控

### 实时监控命令
```bash
# 监控GPS数据质量
rostopic echo /ublox_gps/fix | grep -E "(status|latitude|longitude|altitude)"

# 监控系统日志
rosrun rqt_console rqt_console

# 监控tf变换
rosrun tf tf_monitor

# 监控计算性能
rostopic hz /velodyne_points
rostopic hz /imu/data
```

### 数据记录和分析
```bash
# 记录处理结果
rosbag record -O result_with_gps.bag /odometry /path /tf

# 分析处理结果
python3 ~/simple_gps_analyzer.py  # 使用之前提供的分析工具
```

## 故障排除

### 常见问题和解决方案

1. **编译错误：找不到GeographicLib**
```bash
# 重新安装GeographicLib
sudo apt remove libgeographic-dev
sudo apt install libgeographic-dev libgeographic19
pkg-config --libs geographic
```

2. **GPS topic不存在**
```bash
# 检查bag文件中的实际topic名称
rosbag info UM982loop_715std_maximum_synced.bag | grep -i gps
# 修改launch文件中的topic重映射
```

3. **系统运行缓慢**
```bash
# 检查系统资源
htop
# 调整bag播放速率
rosbag play UM982loop_715std_maximum_synced.bag -r 0.5  # 0.5倍速
```

4. **GPS数据无效**
```bash
# 检查GPS数据内容
rostopic echo /ublox_gps/fix -n 1
# 检查GPS状态字段
```

## 验证成功标准

### 系统启动成功标志
- [x] roscore正常运行
- [x] state_estimation节点启动无错误
- [x] 能够接收到GPS数据
- [x] bag文件正常播放

### GPS集成成功标志
- [x] 控制台显示"GPS坐标原点已设置"
- [x] 控制台显示"起始位置已记录"
- [x] 控制台显示"GPS高度校正"信息
- [x] 控制台显示"GPS回环检测"信息

### 性能验证
```bash
# 运行完整数据集后检查结果
ls /tmp/alpha_lidar/  # 查看生成的结果文件
python3 evaluation.py  # 运行评估脚本
```

这个完整的实施指南提供了在Ubuntu 20.04环境下部署GPS集成的所有必要步骤。建议按顺序执行，每个步骤都要验证成功后再进行下一步。
