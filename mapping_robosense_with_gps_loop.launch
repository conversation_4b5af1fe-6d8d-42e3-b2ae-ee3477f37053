<?xml version="1.0"?>
<launch>
    <!-- 增强版GPS强制回环检测优化的SLAM启动文件 -->
    <!-- 支持起点-终点、中间区域、重复访问等多种回环类型 -->

    <!-- 基础参数配置 -->
    <arg name="gps_topic" default="/rtk/gnss" />
    <arg name="loop_closure_distance_threshold" default="5.0" />
    <arg name="min_trajectory_length" default="50.0" />
    <arg name="gps_quality_threshold" default="-1" />
    <arg name="check_interval" default="1.0" />

    <!-- 增强回环检测参数 -->
    <arg name="intermediate_loop_threshold" default="8.0" />
    <arg name="min_loop_separation" default="30.0" />
    <arg name="trajectory_window_size" default="100" />
    <arg name="clustering_eps" default="3.0" />
    <arg name="min_cluster_size" default="3" />
    <arg name="revisit_threshold" default="10.0" />

    <!-- SLAM基础参数 -->
    <arg name="force_search_radius" default="10.0" />
    <arg name="loop_closure_score_threshold" default="0.3" />
    <arg name="max_search_candidates" default="10" />
    <arg name="voxel_leaf_size" default="0.1" />
    <arg name="min_keyframes_for_loop" default="50" />

    <!-- SLAM增强参数 -->
    <arg name="intermediate_search_radius" default="15.0" />
    <arg name="revisit_search_radius" default="12.0" />
    <arg name="start_end_score_threshold" default="0.25" />
    <arg name="intermediate_score_threshold" default="0.35" />
    <arg name="revisit_score_threshold" default="0.30" />
    <arg name="sliding_window_size" default="100" />
    <arg name="temporal_consistency_weight" default="0.2" />
    
    <!-- 原始SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch">
        <!-- 这里包含您原有的SLAM配置 -->
    </include>
    
    <!-- 增强版GPS强制回环检测优化器 -->
    <node name="enhanced_gps_loop_closure_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
        <!-- 基础参数 -->
        <param name="gps_topic" value="$(arg gps_topic)" />
        <param name="loop_closure_distance_threshold" value="$(arg loop_closure_distance_threshold)" />
        <param name="min_trajectory_length" value="$(arg min_trajectory_length)" />
        <param name="gps_quality_threshold" value="$(arg gps_quality_threshold)" />
        <param name="check_interval" value="$(arg check_interval)" />

        <!-- 增强参数 -->
        <param name="intermediate_loop_threshold" value="$(arg intermediate_loop_threshold)" />
        <param name="min_loop_separation" value="$(arg min_loop_separation)" />
        <param name="trajectory_window_size" value="$(arg trajectory_window_size)" />
        <param name="clustering_eps" value="$(arg clustering_eps)" />
        <param name="min_cluster_size" value="$(arg min_cluster_size)" />
        <param name="revisit_threshold" value="$(arg revisit_threshold)" />

        <remap from="/rtk/gnss" to="$(arg gps_topic)" />
    </node>

    <!-- 增强版SLAM回环检测集成模块 -->
    <node name="enhanced_slam_loop_closure_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
        <!-- 基础参数 -->
        <param name="force_search_radius" value="$(arg force_search_radius)" />
        <param name="loop_closure_score_threshold" value="$(arg loop_closure_score_threshold)" />
        <param name="max_search_candidates" value="$(arg max_search_candidates)" />
        <param name="voxel_leaf_size" value="$(arg voxel_leaf_size)" />
        <param name="min_keyframes_for_loop" value="$(arg min_keyframes_for_loop)" />

        <!-- 增强参数 -->
        <param name="intermediate_search_radius" value="$(arg intermediate_search_radius)" />
        <param name="revisit_search_radius" value="$(arg revisit_search_radius)" />
        <param name="start_end_score_threshold" value="$(arg start_end_score_threshold)" />
        <param name="intermediate_score_threshold" value="$(arg intermediate_score_threshold)" />
        <param name="revisit_score_threshold" value="$(arg revisit_score_threshold)" />
        <param name="sliding_window_size" value="$(arg sliding_window_size)" />
        <param name="temporal_consistency_weight" value="$(arg temporal_consistency_weight)" />
    </node>
    
    <!-- GPS轨迹监控和可视化 -->
    <node name="gps_trajectory_monitor" pkg="state_estimation" type="gps_trajectory_monitor.py" output="screen">
        <param name="gps_topic" value="$(arg gps_topic)" />
        <param name="publish_rate" value="1.0" />
    </node>
    
    <!-- RViz可视化 -->
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find state_estimation)/rviz/gps_loop_closure.rviz" output="screen" />
    
    <!-- 参数服务器配置 -->
    <rosparam>
        # GPS强制回环检测配置
        gps_loop_closure:
            # 距离阈值（米）- 当GPS距离起点小于此值时触发强制回环
            distance_threshold: 5.0
            
            # 最小轨迹长度（米）- 只有轨迹长度超过此值才考虑回环
            min_trajectory_length: 50.0
            
            # GPS质量阈值 - 只接受质量高于此值的GPS数据
            # -1: NO_FIX, 0: RTK_FIXED, 1: SBAS_FIX, 2: GBAS_FIX
            gps_quality_threshold: -1
            
            # 强制回环超时时间（秒）- 避免频繁触发
            force_timeout: 10.0
            
            # 检查间隔（秒）
            check_interval: 1.0
        
        # SLAM回环检测配置
        slam_loop_closure:
            # 强制搜索半径（米）- 在此半径内搜索候选关键帧
            force_search_radius: 10.0
            
            # 匹配分数阈值 - 低于此值认为匹配成功
            score_threshold: 0.3
            
            # 最大搜索候选数
            max_candidates: 10
            
            # 体素滤波叶子大小（米）
            voxel_leaf_size: 0.1
            
            # 最小关键帧数 - 少于此数量不执行回环检测
            min_keyframes: 50
    </rosparam>
    
    <!-- 日志配置 -->
    <env name="ROSCONSOLE_CONFIG_FILE" value="$(find state_estimation)/config/rosconsole.conf"/>
    
</launch>
