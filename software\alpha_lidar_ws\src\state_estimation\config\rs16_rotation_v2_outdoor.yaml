common:
    lid_topic:  "/velodyne_points"
    imu_topic:  "/imu/data"
    encoder_topic: "/imu/encoder"
    time_sync_en: false         # ONLY turn on when external time synchronization is really not possible

preprocess:
    lidar_type: 2                # 1 for Livox serials LiDAR, 2 for Velodyne LiDAR, 3 for ouster LiDAR,
    scan_line: 16
    scan_rate: 10                # only need to be set for velodyne, unit: Hz,
    blind: 0.8 
    point_filter_num: 1

mapping:
    down_sample_size: 0.3
    max_iteration: 4
    voxel_size: 1.25
    max_layer: 3                # 4 layer, 0, 1, 2, 3
    layer_point_size: [ 5, 5, 5, 5, 5 ]
    plannar_threshold: 0.01
    max_points_size: 1000
    max_cov_points_size: 1000
    init_gravity_with_pose: true

    fov_degree:    360
    det_range:     100.0
    extrinsic_est_en:  true      # true: enable the online estimation of IMU-LiDAR extrinsic,
    extrinsic_T: [ 0.03, 0.01, -0.05 ]
    extrinsic_R: [ 1, 0, 0,
                   0, -1, 0,
                   0, 0, -1 ]

    encoder_fusion_en: true     # true: enable encoder angle fusion,
    extrinsic_T_encoder_lidar:  [-0.0042, 0.0046, 0.09994]
    extrinsic_R_encoder_lidar: [-0.0002004, 3.04e-05, 1.0,
                                0.0040013, 0.999992, -2.96e-05,
                                -0.999992, 0.0040013, -0.0002005]

noise_model:
    ranging_cov: 0.04
    angle_cov: 0.1
    # v2的硬件 imu cov调低一点效果好
#    acc_cov: 0.05
#    gyr_cov: 0.0025
#    acc_cov: 0.1
#    gyr_cov: 0.01
    # 下边组大的方差对于荆长路地铁站最后长走廊退化的 情况比较好
    acc_cov: 0.5
    gyr_cov: 0.25
    b_acc_cov: 0.0000043
    b_gyr_cov: 0.000000266


publish:
    pub_voxel_map: false
    publish_max_voxel_layer: 2         # only publish 0,1,2 layer's plane
    path_en:  true
    publish_limit_z: 10000
    scan_publish_en:  true       # false: close all the point cloud output
    dense_publish_en: false # false: low down the points number in a global-frame point clouds scan.
    scan_bodyframe_pub_en: true  # true: output the point cloud scans in IMU-body-frame
    intensity_th: 2.0

pcd_save:
    pcd_save_en: false
    interval: -1                 # how many LiDAR frames saved in each pcd file;
    # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.
