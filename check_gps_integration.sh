#!/bin/bash

# GPS集成状态检查脚本

echo "=========================================="
echo "αLiDAR GPS集成状态检查"
echo "=========================================="

# 1. 检查GeographicLib是否安装
echo "1. 检查GeographicLib库..."
if command -v pkg-config >/dev/null 2>&1; then
    if pkg-config --exists geographic; then
        echo "✅ GeographicLib已安装: $(pkg-config --modversion geographic)"
        echo "   库路径: $(pkg-config --libs geographic)"
    else
        echo "❌ GeographicLib未找到"
        echo "   请安装: sudo apt install libgeographic-dev libgeographic19"
    fi
else
    echo "⚠️  pkg-config未找到，无法检查GeographicLib"
fi

# 2. 检查编译状态
echo ""
echo "2. 检查编译状态..."
WORKSPACE_PATH="$HOME/alpha_lidar_GPS/software/alpha_lidar_ws"
if [ -f "$WORKSPACE_PATH/devel/lib/state_estimation/state_estimation_node" ]; then
    echo "✅ state_estimation_node已编译"
    
    # 检查是否链接了GeographicLib
    if ldd "$WORKSPACE_PATH/devel/lib/state_estimation/state_estimation_node" 2>/dev/null | grep -q geographic; then
        echo "✅ GPS集成已启用（链接了GeographicLib）"
    else
        echo "❌ GPS集成未启用（未链接GeographicLib）"
    fi
else
    echo "❌ state_estimation_node未找到，请先编译项目"
fi

# 3. 检查配置文件
echo ""
echo "3. 检查配置文件..."
CONFIG_FILE="$WORKSPACE_PATH/src/state_estimation/config/rs16_rotation_v2.yaml"
if [ -f "$CONFIG_FILE" ]; then
    if grep -q "gps:" "$CONFIG_FILE"; then
        echo "✅ 配置文件包含GPS参数"
        echo "   GPS配置:"
        grep -A 10 "gps:" "$CONFIG_FILE" | head -6
    else
        echo "❌ 配置文件缺少GPS参数"
    fi
else
    echo "❌ 配置文件未找到"
fi

# 4. 检查launch文件
echo ""
echo "4. 检查launch文件..."
LAUNCH_FILE="$WORKSPACE_PATH/src/state_estimation/launch/mapping_robosense.launch"
if [ -f "$LAUNCH_FILE" ]; then
    if grep -q "gps" "$LAUNCH_FILE"; then
        echo "✅ launch文件包含GPS配置"
    else
        echo "❌ launch文件缺少GPS配置"
    fi
else
    echo "❌ launch文件未找到"
fi

# 5. 运行时检查建议
echo ""
echo "5. 运行时检查建议:"
echo "   启动系统后，检查以下内容："
echo "   - 终端输出是否包含 'GPS集成已启用'"
echo "   - 运行: rostopic list | grep gps"
echo "   - 运行: rosparam get /gps/enable_correction"
echo "   - 运行: rostopic echo /gps/fix -n 1"

echo ""
echo "=========================================="
echo "检查完成"
echo "=========================================="
