#!/bin/bash

# GPS轨迹分析工具快速启动脚本

echo "=========================================="
echo "🚀 GPS轨迹分析工具"
echo "=========================================="

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python3"
    exit 1
fi

# 检查依赖包
echo "🔍 检查依赖包..."
python3 -c "import rosbag, numpy, open3d" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  缺少依赖包，正在安装..."
    pip3 install rosbag numpy open3d
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败，请手动安装:"
        echo "   pip3 install rosbag numpy open3d"
        exit 1
    fi
    echo "✅ 依赖包安装完成"
fi

# 检查工具文件是否存在
if [ ! -f "gps_analyzer_tool.py" ]; then
    echo "❌ 错误: 找不到 gps_analyzer_tool.py"
    echo "请确保脚本在正确的目录中运行"
    exit 1
fi

# 给工具文件添加执行权限
chmod +x gps_analyzer_tool.py

echo "✅ 环境检查完成"
echo ""

# 如果提供了参数，直接运行
if [ $# -gt 0 ]; then
    echo "🚀 启动GPS分析工具..."
    python3 gps_analyzer_tool.py "$@"
else
    # 交互式选择bag文件
    echo "📁 请选择要分析的bag文件:"
    echo ""
    
    # 查找当前目录下的bag文件
    bag_files=(*.bag)
    if [ ${#bag_files[@]} -eq 1 ] && [ ! -f "${bag_files[0]}" ]; then
        echo "❌ 当前目录下没有找到.bag文件"
        echo ""
        echo "使用方法:"
        echo "  $0 <bag_file> [--topic <gps_topic>]"
        echo ""
        echo "示例:"
        echo "  $0 data.bag"
        echo "  $0 data.bag --topic /gps/fix"
        exit 1
    fi
    
    # 显示找到的bag文件
    echo "找到以下bag文件:"
    for i in "${!bag_files[@]}"; do
        echo "  $((i+1)). ${bag_files[i]}"
    done
    echo ""
    
    # 让用户选择
    read -p "请输入文件编号 (1-${#bag_files[@]}): " choice
    
    # 验证输入
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 1 ] || [ "$choice" -gt ${#bag_files[@]} ]; then
        echo "❌ 无效的选择"
        exit 1
    fi
    
    selected_bag="${bag_files[$((choice-1))]}"
    echo "✅ 选择了文件: $selected_bag"
    echo ""
    
    # 询问GPS topic
    echo "📡 GPS Topic设置:"
    echo "  1. /rtk/gnss (默认)"
    echo "  2. /gps/fix"
    echo "  3. 自定义"
    echo ""
    read -p "请选择GPS topic (1-3, 默认1): " topic_choice
    
    case $topic_choice in
        2)
            gps_topic="/gps/fix"
            ;;
        3)
            read -p "请输入自定义GPS topic: " gps_topic
            ;;
        *)
            gps_topic="/rtk/gnss"
            ;;
    esac
    
    echo "✅ GPS Topic: $gps_topic"
    echo ""
    
    # 运行分析
    echo "🚀 启动GPS分析工具..."
    python3 gps_analyzer_tool.py "$selected_bag" --topic "$gps_topic"
fi

echo ""
echo "=========================================="
echo "✅ GPS轨迹分析完成"
echo "=========================================="
