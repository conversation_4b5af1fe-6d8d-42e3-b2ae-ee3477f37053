#!/bin/bash

# 彻底修复编译错误的脚本

echo "=========================================="
echo "🔧 彻底修复编译错误"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 备份原始文件"
cp src/state_estimation/src/voxelMapping.cpp src/state_estimation/src/voxelMapping.cpp.backup
echo "✓ 已备份voxelMapping.cpp"

echo ""
echo "步骤2: 彻底清理有问题的代码"

# 移除所有pub_icp_fitness相关代码
echo "移除pub_icp_fitness相关代码..."
sed -i '/pub_icp_fitness/d' src/state_estimation/src/voxelMapping.cpp

# 移除GPS约束控制回调函数
echo "移除GPS约束控制回调函数..."
sed -i '/gps_constraint_control_callback/,/^}/d' src/state_estimation/src/voxelMapping.cpp

# 移除GPS约束控制订阅器
echo "移除GPS约束控制订阅器..."
sed -i '/sub_constraint_control/d' src/state_estimation/src/voxelMapping.cpp

# 注释掉动态约束控制相关变量
echo "注释掉动态约束控制变量..."
sed -i 's/^bool dynamic_constraint_control/\/\/ bool dynamic_constraint_control/' src/state_estimation/src/voxelMapping.cpp
sed -i 's/^int constraint_disable_count/\/\/ int constraint_disable_count/' src/state_estimation/src/voxelMapping.cpp
sed -i 's/^int constraint_enable_count/\/\/ int constraint_enable_count/' src/state_estimation/src/voxelMapping.cpp

echo "✓ 有问题的代码已清理"

echo ""
echo "步骤3: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤4: 安装依赖"
sudo apt update
sudo apt install -y \
    ros-noetic-std-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-geometry-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-tf \
    ros-noetic-tf2-ros \
    libpcl-dev \
    pcl-tools

echo ""
echo "步骤5: 清理并编译"
rm -rf build devel

echo "开始编译..."
catkin_make --only-pkg-with-deps state_estimation -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    source devel/setup.bash
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤6: 验证编译结果"
    
    if [ -f "devel/lib/state_estimation/state_estimation_node" ]; then
        echo "✅ SLAM核心节点编译成功"
    else
        echo "❌ SLAM核心节点编译失败"
    fi
    
    if [ -f "devel/lib/state_estimation/intensity_preserving_pcd_saver" ]; then
        echo "✅ 强度保持模块编译成功"
    fi
    
    echo ""
    echo "🎉 编译修复完成!"
    echo ""
    echo "可用功能:"
    echo "✅ 基础SLAM功能"
    echo "✅ 强度值保持"
    echo "✅ 智能首尾检测"
    echo "❌ GPS约束控制 (已移除)"
    echo ""
    echo "启动选项:"
    echo "1. 基础SLAM系统:"
    echo "   ./start_basic_slam.sh"
    echo ""
    echo "2. 手动启动:"
    echo "   roslaunch state_estimation mapping_robosense.launch"
    echo ""
    echo "3. 核心节点直接启动:"
    echo "   rosrun state_estimation state_estimation_node"
    
else
    echo ""
    echo "❌ 编译仍然失败!"
    echo ""
    echo "尝试最小化编译..."
    
    # 完全禁用GPS集成
    echo "完全禁用GPS集成..."
    sed -i 's/^#ifdef USE_GPS_INTEGRATION/#if 0  \/\/ 完全禁用GPS集成/' src/state_estimation/src/voxelMapping.cpp
    
    # 重新编译
    catkin_make --only-pkg-with-deps state_estimation -j1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 最小化编译成功!"
        echo "GPS集成功能已完全禁用"
        echo ""
        echo "可用功能:"
        echo "✅ 基础SLAM"
        echo "✅ 强度保持"
        echo "❌ GPS集成 (已禁用)"
        echo "❌ GPS约束控制 (已禁用)"
        echo ""
        echo "启动命令:"
        echo "   roslaunch state_estimation mapping_robosense.launch"
        echo ""
        echo "如需恢复完整功能:"
        echo "   cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp"
        
        source devel/setup.bash
        chmod +x *.sh
        chmod +x src/state_estimation/scripts/*.py
        
    else
        echo ""
        echo "❌ 最小化编译也失败!"
        echo ""
        echo "恢复原文件..."
        cp src/state_estimation/src/voxelMapping.cpp.backup src/state_estimation/src/voxelMapping.cpp
        
        echo ""
        echo "建议的解决方案:"
        echo "1. 检查ROS完整安装:"
        echo "   sudo apt install ros-noetic-desktop-full"
        echo ""
        echo "2. 检查PCL版本:"
        echo "   sudo apt install libpcl-dev pcl-tools"
        echo ""
        echo "3. 检查编译器支持C++14:"
        echo "   g++ --version"
        echo ""
        echo "4. 手动检查具体错误信息"
        echo ""
        echo "5. 考虑使用Docker环境或虚拟机"
        
        exit 1
    fi
fi

echo ""
echo "=========================================="
echo "编译修复完成!"
echo "=========================================="
