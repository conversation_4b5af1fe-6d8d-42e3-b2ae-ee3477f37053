#!/bin/bash

# GPS软约束回环系统编译和执行脚本

echo "=========================================="
echo "🔧 GPS软约束回环系统编译和执行"
echo "=========================================="

# 检查当前目录
if [ ! -f "src/state_estimation/CMakeLists.txt" ]; then
    echo "错误: 请在alpha_lidar_ws目录中运行此脚本"
    exit 1
fi

echo "步骤1: 设置ROS环境"
source /opt/ros/noetic/setup.bash

echo ""
echo "步骤2: 安装依赖"
sudo apt update
sudo apt install -y \
    python3-numpy \
    python3-scipy \
    ros-noetic-tf2-ros \
    ros-noetic-tf2-geometry-msgs \
    libpcl-dev \
    pcl-tools

echo ""
echo "步骤3: 清理并编译"
rm -rf build devel

# 编译系统
catkin_make -j1

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 编译成功!"
    
    # 设置环境
    source devel/setup.bash
    
    # 设置权限
    chmod +x *.sh
    chmod +x src/state_estimation/scripts/*.py
    
    echo ""
    echo "步骤4: 验证组件"
    
    # 检查可执行文件
    components=(
        "devel/lib/state_estimation/state_estimation_node:SLAM核心节点"
        "devel/lib/state_estimation/force_start_end_loop_matcher:强制匹配器"
        "src/state_estimation/scripts/gps_soft_constraint_loop_detector.py:GPS软约束检测器"
        "src/state_estimation/scripts/intelligent_start_end_detector.py:智能检测器"
        "src/state_estimation/scripts/intelligent_gps_constraint_controller.py:GPS约束控制器"
    )
    
    all_good=true
    for component in "${components[@]}"; do
        IFS=':' read -r path name <<< "$component"
        if [ -f "$path" ]; then
            echo "✅ $name"
        else
            echo "❌ $name (缺失)"
            if [[ "$path" == *"state_estimation_node"* ]]; then
                all_good=false
            fi
        fi
    done
    
    echo ""
    if [ "$all_good" = true ]; then
        echo "🎉 所有组件准备就绪!"
        echo ""
        echo "=========================================="
        echo "🚀 启动选项"
        echo "=========================================="
        echo ""
        echo "选择启动方式："
        echo "1) 启动GPS软约束SLAM系统"
        echo "2) 启动RVIZ可视化"
        echo "3) 同时启动SLAM和RVIZ"
        echo "4) 仅编译，稍后手动启动"
        echo ""
        
        read -p "请选择 (1-4): " choice
        
        case $choice in
            1)
                echo "启动GPS软约束SLAM系统..."
                ./start_gps_soft_loop_slam.sh
                ;;
            2)
                echo "启动RVIZ可视化..."
                rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz
                ;;
            3)
                echo "同时启动SLAM和RVIZ..."
                # 在后台启动SLAM
                ./start_gps_soft_loop_slam.sh &
                sleep 5
                # 启动RVIZ
                rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz
                ;;
            4)
                echo "编译完成，可以手动启动系统"
                echo ""
                echo "手动启动命令："
                echo "  启动SLAM: ./start_gps_soft_loop_slam.sh"
                echo "  启动RVIZ: rviz -d src/state_estimation/rviz/gps_soft_loop_slam.rviz"
                echo "  播放数据: rosbag play your_data.bag"
                ;;
            *)
                echo "无效选择，编译完成"
                ;;
        esac
        
    else
        echo "⚠️  部分组件缺失，但可以尝试运行"
        echo ""
        echo "可用的启动命令："
        echo "  ./start_gps_soft_loop_slam.sh"
    fi
    
else
    echo ""
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方案："
    echo "1. 检查ROS安装:"
    echo "   sudo apt install ros-noetic-desktop-full"
    echo ""
    echo "2. 检查依赖:"
    echo "   sudo apt install libpcl-dev python3-numpy"
    echo ""
    echo "3. 单独编译state_estimation包:"
    echo "   catkin_make --only-pkg-with-deps state_estimation -j1"
    echo ""
    echo "4. 检查具体错误信息并修复"
    
    exit 1
fi

echo ""
echo "=========================================="
echo "GPS软约束回环系统准备完成!"
echo "=========================================="
