
# GPS高度校正策略分析

## 1. 首尾高度差问题的根本原因
- IMU漂移累积：长时间运行导致的姿态和位置漂移
- 气压高度计误差：环境气压变化影响
- LiDAR里程计累积误差：特征匹配不准确导致的漂移

## 2. GPS/RTK数据的优势
- 绝对位置参考：提供全局坐标系下的绝对位置
- 高精度：RTK模式下可达厘米级精度
- 长期稳定：不存在累积漂移问题

## 3. 集成策略建议

### 3.1 松耦合方案（推荐）
```cpp
// 定期使用GPS位置校正累积误差
void correctWithGPS(double timestamp) {
    if (hasValidGPSFix(timestamp)) {
        Eigen::Vector3d gps_position = getGPSPosition(timestamp);
        Eigen::Vector3d slam_position = getCurrentSLAMPosition();

        Eigen::Vector3d drift = gps_position - slam_position;

        // 如果漂移超过阈值，进行校正
        if (drift.norm() > correction_threshold) {
            applySmoothCorrection(drift);
        }
    }
}
```

### 3.2 紧耦合方案（高精度）
```cpp
// 将GPS作为观测量直接融入卡尔曼滤波
void integrateGPSObservation() {
    // 构建GPS观测模型
    Eigen::VectorXd z_gps(3);  // GPS观测
    Eigen::MatrixXd H_gps(3, state_dim);  // 观测矩阵
    Eigen::MatrixXd R_gps(3, 3);  // GPS噪声协方差

    // 更新滤波器
    kf.update(z_gps, H_gps, R_gps);
}
```

### 3.3 回环约束方案
```cpp
// 利用GPS检测回环并添加约束
void addGPSLoopConstraint() {
    if (isNearStartPosition()) {
        Eigen::Vector3d start_gps = getStartGPSPosition();
        Eigen::Vector3d current_gps = getCurrentGPSPosition();

        // 添加回环约束
        addLoopClosureConstraint(start_gps, current_gps);
    }
}
```

## 4. 实施建议

### 4.1 数据预处理
- GPS状态检查：只使用RTK固定解数据
- 异常值检测：过滤GPS跳跃和多路径干扰
- 时间同步：确保GPS和LiDAR数据时间对齐

### 4.2 坐标系转换
- WGS84 → UTM：统一坐标系
- 高程基准：考虑大地高与正常高的差异
- 局部坐标系：建立以起点为原点的局部坐标系

### 4.3 权重策略
- 动态权重：根据GPS精度动态调整融合权重
- 环境自适应：室内外环境下采用不同策略
- 置信度评估：基于GPS状态和协方差调整信任度
