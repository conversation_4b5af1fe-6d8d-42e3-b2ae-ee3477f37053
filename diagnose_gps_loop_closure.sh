#!/bin/bash

# GPS回环检测诊断脚本

echo "=========================================="
echo "GPS回环检测诊断工具"
echo "=========================================="

echo "1. 检查GPS回环检测参数..."
echo "当前参数设置:"
rosparam get /gps/enable_loop_closure 2>/dev/null || echo "  enable_loop_closure: 参数未设置"
rosparam get /gps/loop_closure_distance 2>/dev/null || echo "  loop_closure_distance: 参数未设置"
rosparam get /gps/loop_closure_min_distance 2>/dev/null || echo "  loop_closure_min_distance: 参数未设置"
rosparam get /gps/enable_icp_loop_closure 2>/dev/null || echo "  enable_icp_loop_closure: 参数未设置"
rosparam get /gps/icp_trigger_distance 2>/dev/null || echo "  icp_trigger_distance: 参数未设置"

echo ""
echo "2. 检查GPS数据状态..."
echo "最近的GPS消息:"
timeout 3 rostopic echo /rtk/gnss -n 1 2>/dev/null || echo "  无法获取GPS数据，请检查topic名称"

echo ""
echo "3. 监控回环检测日志..."
echo "请在另一个终端运行以下命令来监控回环检测:"
echo "  rostopic echo /rosout | grep -E '(Loop|ICP|Start Position)'"

echo ""
echo "4. 实时GPS状态监控..."
echo "GPS数据频率:"
timeout 5 rostopic hz /rtk/gnss 2>/dev/null || echo "  无法获取GPS频率"

echo ""
echo "5. 诊断建议..."
echo "如果回环检测没有触发，请检查:"
echo "  - GPS起始位置是否已记录 (查找 'Start Position Recorded' 日志)"
echo "  - SLAM轨迹长度是否超过50米 (查找 'GPS Loop Debug' 日志)"
echo "  - GPS距离起点是否小于10米 (查找 'GPS Loop Debug' 日志)"
echo "  - 关键帧是否正在收集 (查找 'ICP Loop Debug' 日志)"

echo ""
echo "6. 手动触发测试..."
echo "可以通过以下命令调整参数进行测试:"
echo "  rosparam set /gps/loop_closure_min_distance 10.0  # 降低最小轨迹长度"
echo "  rosparam set /gps/loop_closure_distance 20.0      # 增加回环检测距离"

echo ""
echo "=========================================="
