common:
    lid_topic:  "/livox/lidar"
    imu_topic:  "/livox/imu"
    #    imu_topic:  "/livox/imu"
    time_sync_en: false         # ONLY turn on when external time synchronization is really not possible

preprocess:
    lidar_type: 1                # 1 for Livox serials LiDAR, 2 for Velodyne LiDAR, 3 for ouster LiDAR,
    scan_line: 6
    scan_rate: 10
    blind: 1.0
    point_filter_num: 1

mapping:
    down_sample_size: 0.2
    max_iteration: 4
    voxel_size: 0.25
    max_layer: 2                # 4 layer, 0, 1, 2, 3
    layer_point_size: [ 5, 5, 5, 5, 5 ]
    plannar_threshold: 0.01
    max_points_size: 1000
    max_cov_points_size: 1000

    fov_degree:    360
    det_range:     100.0
    extrinsic_est_en:  false      # true: enable the online estimation of IMU-LiDAR extrinsic,
    extrinsic_T: [ -0.011, -0.02329, -0.04412 ]
    extrinsic_R: [ 1, 0, 0,
                   0, 1, 0,
                   0, 0, 1]

noise_model:
    ranging_cov: 0.02
    angle_cov: 0.05
    # 原始
#    acc_cov: 0.5
#    gyr_cov: 0.25
    # alpha lidar
    acc_cov: 0.01
    gyr_cov: 0.001
    b_acc_cov: 0.0043
    b_gyr_cov: 0.000266


publish:
    pub_voxel_map: false
    publish_max_voxel_layer: 2         # only publish 0,1,2 layer's plane
    path_en:  true
    scan_publish_en:  true       # false: close all the point cloud output
    dense_publish_en: true       # false: low down the points number in a global-frame point clouds scan.
    scan_bodyframe_pub_en: true  # true: output the point cloud scans in IMU-body-frame

pcd_save:
    pcd_save_en: false
    interval: -1                 # how many LiDAR frames saved in each pcd file; 
                                 # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.
