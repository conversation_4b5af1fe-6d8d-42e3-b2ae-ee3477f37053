#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软约束回环处理器
接收GPS软约束回环检测信号，进行渐进式轨迹校正
"""

import rospy
import numpy as np
from std_msgs.msg import String, Bool
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from nav_msgs.msg import Path
import json
import threading
from collections import deque
import math

class SoftConstraintLoopProcessor:
    def __init__(self):
        rospy.init_node('soft_constraint_loop_processor', anonymous=True)
        
        # 参数配置
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.6)
        self.max_correction_distance = rospy.get_param('~max_correction_distance', 5.0)
        self.correction_weight = rospy.get_param('~correction_weight', 0.3)
        self.enable_gradual_correction = rospy.get_param('~enable_gradual_correction', True)
        self.correction_steps = rospy.get_param('~correction_steps', 10)
        self.min_correction_threshold = rospy.get_param('~min_correction_threshold', 0.5)
        
        # 状态变量
        self.current_pose = None
        self.correction_active = False
        self.correction_target = None
        self.correction_progress = 0
        self.total_corrections = 0
        self.successful_corrections = 0
        
        # 轨迹历史
        self.pose_history = deque(maxlen=200)
        self.correction_history = deque(maxlen=50)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 订阅器
        self.loop_trigger_sub = rospy.Subscriber('/gps_soft_loop_trigger', String, 
                                                self.loop_trigger_callback, queue_size=5)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, 
                                        self.pose_callback, queue_size=10)
        
        # 发布器
        self.corrected_pose_pub = rospy.Publisher('/corrected_pose', PoseStamped, queue_size=10)
        self.correction_status_pub = rospy.Publisher('/correction_status', String, queue_size=1)
        self.loop_closure_pub = rospy.Publisher('/loop_closure_trigger', Bool, queue_size=1)
        
        # 定时器
        self.correction_timer = rospy.Timer(rospy.Duration(0.1), self.apply_correction)
        self.status_timer = rospy.Timer(rospy.Duration(5.0), self.publish_status)
        
        rospy.loginfo("🔄 Soft Constraint Loop Processor Started")
        rospy.loginfo("Confidence threshold: %.2f", self.confidence_threshold)
        rospy.loginfo("Max correction distance: %.2f m", self.max_correction_distance)
        rospy.loginfo("Correction weight: %.2f", self.correction_weight)
    
    def loop_trigger_callback(self, msg):
        """处理回环触发信号"""
        try:
            loop_data = json.loads(msg.data)
            
            if loop_data.get('type') != 'gps_soft_constraint_loop_closure':
                return
            
            confidence = loop_data.get('metrics', {}).get('confidence', 0.0)
            
            # 置信度检查
            if confidence < self.confidence_threshold:
                rospy.logwarn("Low confidence loop closure: %.3f < %.3f", 
                             confidence, self.confidence_threshold)
                return
            
            current_pose = loop_data.get('current_pose', {})
            target_pose = loop_data.get('target_pose', {})
            
            if not current_pose or not target_pose:
                rospy.logwarn("Invalid pose data in loop trigger")
                return
            
            # 计算校正距离
            correction_distance = self.calculate_distance(current_pose, target_pose)
            
            if correction_distance > self.max_correction_distance:
                rospy.logwarn("Correction distance too large: %.2f > %.2f", 
                             correction_distance, self.max_correction_distance)
                return
            
            if correction_distance < self.min_correction_threshold:
                rospy.loginfo("Correction distance too small: %.2f < %.2f", 
                             correction_distance, self.min_correction_threshold)
                return
            
            # 启动校正
            self.start_correction(current_pose, target_pose, confidence, loop_data.get('metrics', {}))
            
        except Exception as e:
            rospy.logerr("Error processing loop trigger: %s", str(e))
    
    def pose_callback(self, msg):
        """处理位姿数据"""
        with self.lock:
            self.current_pose = {
                'x': msg.pose.position.x,
                'y': msg.pose.position.y,
                'z': msg.pose.position.z,
                'qx': msg.pose.orientation.x,
                'qy': msg.pose.orientation.y,
                'qz': msg.pose.orientation.z,
                'qw': msg.pose.orientation.w,
                'timestamp': msg.header.stamp.to_sec()
            }
            
            self.pose_history.append(self.current_pose.copy())
    
    def calculate_distance(self, pose1, pose2):
        """计算两个位姿间的距离"""
        dx = pose1['x'] - pose2['x']
        dy = pose1['y'] - pose2['y']
        dz = pose1.get('z', 0) - pose2.get('z', 0)
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def start_correction(self, current_pose, target_pose, confidence, metrics):
        """启动轨迹校正"""
        try:
            with self.lock:
                if self.correction_active:
                    rospy.logwarn("Correction already active, ignoring new trigger")
                    return
                
                self.correction_active = True
                self.correction_progress = 0
                self.total_corrections += 1
                
                # 计算校正向量
                correction_vector = {
                    'x': target_pose['x'] - current_pose['x'],
                    'y': target_pose['y'] - current_pose['y'],
                    'z': target_pose.get('z', 0) - current_pose.get('z', 0)
                }
                
                self.correction_target = {
                    'vector': correction_vector,
                    'confidence': confidence,
                    'metrics': metrics,
                    'start_time': rospy.Time.now().to_sec(),
                    'total_distance': self.calculate_distance(current_pose, target_pose)
                }
                
                # 触发SLAM系统的回环检测
                loop_trigger = Bool()
                loop_trigger.data = True
                self.loop_closure_pub.publish(loop_trigger)
                
                rospy.loginfo("🔄 Started soft correction: distance=%.2fm, confidence=%.3f", 
                             self.correction_target['total_distance'], confidence)
                
        except Exception as e:
            rospy.logerr("Error starting correction: %s", str(e))
            self.correction_active = False
    
    def apply_correction(self, event):
        """应用渐进式校正"""
        try:
            with self.lock:
                if not self.correction_active or not self.correction_target or not self.current_pose:
                    return
                
                if self.enable_gradual_correction:
                    self.apply_gradual_correction()
                else:
                    self.apply_immediate_correction()
                    
        except Exception as e:
            rospy.logerr("Error applying correction: %s", str(e))
            self.correction_active = False
    
    def apply_gradual_correction(self):
        """应用渐进式校正"""
        self.correction_progress += 1
        
        if self.correction_progress > self.correction_steps:
            self.finish_correction()
            return
        
        # 计算当前步骤的校正比例
        progress_ratio = self.correction_progress / self.correction_steps
        
        # 使用平滑函数 (sigmoid-like)
        smooth_ratio = self.smooth_correction_ratio(progress_ratio)
        
        # 应用校正
        correction_amount = {
            'x': self.correction_target['vector']['x'] * smooth_ratio * self.correction_weight,
            'y': self.correction_target['vector']['y'] * smooth_ratio * self.correction_weight,
            'z': self.correction_target['vector']['z'] * smooth_ratio * self.correction_weight
        }
        
        # 创建校正后的位姿
        corrected_pose = PoseStamped()
        corrected_pose.header.stamp = rospy.Time.now()
        corrected_pose.header.frame_id = "map"
        
        corrected_pose.pose.position.x = self.current_pose['x'] + correction_amount['x']
        corrected_pose.pose.position.y = self.current_pose['y'] + correction_amount['y']
        corrected_pose.pose.position.z = self.current_pose['z'] + correction_amount['z']
        
        corrected_pose.pose.orientation.x = self.current_pose['qx']
        corrected_pose.pose.orientation.y = self.current_pose['qy']
        corrected_pose.pose.orientation.z = self.current_pose['qz']
        corrected_pose.pose.orientation.w = self.current_pose['qw']
        
        # 发布校正后的位姿
        self.corrected_pose_pub.publish(corrected_pose)
        
        # 记录校正历史
        correction_record = {
            'timestamp': rospy.Time.now().to_sec(),
            'progress': progress_ratio,
            'correction_amount': correction_amount,
            'confidence': self.correction_target['confidence']
        }
        self.correction_history.append(correction_record)
    
    def apply_immediate_correction(self):
        """应用立即校正"""
        correction_amount = {
            'x': self.correction_target['vector']['x'] * self.correction_weight,
            'y': self.correction_target['vector']['y'] * self.correction_weight,
            'z': self.correction_target['vector']['z'] * self.correction_weight
        }
        
        # 创建校正后的位姿
        corrected_pose = PoseStamped()
        corrected_pose.header.stamp = rospy.Time.now()
        corrected_pose.header.frame_id = "map"
        
        corrected_pose.pose.position.x = self.current_pose['x'] + correction_amount['x']
        corrected_pose.pose.position.y = self.current_pose['y'] + correction_amount['y']
        corrected_pose.pose.position.z = self.current_pose['z'] + correction_amount['z']
        
        corrected_pose.pose.orientation.x = self.current_pose['qx']
        corrected_pose.pose.orientation.y = self.current_pose['qy']
        corrected_pose.pose.orientation.z = self.current_pose['qz']
        corrected_pose.pose.orientation.w = self.current_pose['qw']
        
        self.corrected_pose_pub.publish(corrected_pose)
        
        self.finish_correction()
    
    def smooth_correction_ratio(self, progress):
        """平滑校正比例函数"""
        # 使用平滑的S曲线
        return 1.0 / (1.0 + math.exp(-10 * (progress - 0.5)))
    
    def finish_correction(self):
        """完成校正"""
        self.correction_active = False
        self.successful_corrections += 1
        
        duration = rospy.Time.now().to_sec() - self.correction_target['start_time']
        
        rospy.loginfo("✅ Soft correction completed: duration=%.1fs, distance=%.2fm", 
                     duration, self.correction_target['total_distance'])
        
        self.correction_target = None
        self.correction_progress = 0
    
    def publish_status(self, event):
        """发布状态信息"""
        try:
            status = {
                'timestamp': rospy.Time.now().to_sec(),
                'processor_type': 'soft_constraint_loop_processor',
                'correction_active': self.correction_active,
                'statistics': {
                    'total_corrections': self.total_corrections,
                    'successful_corrections': self.successful_corrections,
                    'success_rate': self.successful_corrections / max(self.total_corrections, 1),
                    'pose_history_length': len(self.pose_history),
                    'correction_history_length': len(self.correction_history)
                },
                'current_correction': None
            }
            
            if self.correction_active and self.correction_target:
                status['current_correction'] = {
                    'progress': self.correction_progress / self.correction_steps,
                    'confidence': self.correction_target['confidence'],
                    'total_distance': self.correction_target['total_distance'],
                    'elapsed_time': rospy.Time.now().to_sec() - self.correction_target['start_time']
                }
            
            status_msg = String()
            status_msg.data = json.dumps(status, indent=2)
            self.correction_status_pub.publish(status_msg)
            
            if self.total_corrections > 0:
                rospy.loginfo("Soft Correction: %d total, %d successful (%.1f%%)", 
                             self.total_corrections, self.successful_corrections,
                             (self.successful_corrections / self.total_corrections) * 100)
            
        except Exception as e:
            rospy.logerr("Error publishing status: %s", str(e))

def main():
    try:
        processor = SoftConstraintLoopProcessor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr("Soft Constraint Loop Processor error: %s", str(e))

if __name__ == '__main__':
    main()
