<?xml version="1.0"?>
<launch>
    <!-- 最简单的强度保持测试启动文件 -->
    <!-- 用于测试和调试，避免所有可能的冲突 -->
    
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/intensity_test" />
    
    <!-- 只启动强度保持模块进行测试 -->
    <node name="intensity_test_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
        <!-- 基本参数 -->
        <param name="save_directory" value="$(arg save_directory)" />
        <param name="save_enabled" value="true" />
        <param name="save_raw_intensity" value="true" />
        <param name="save_loop_corrected" value="true" />
        <param name="intensity_scale_factor" value="1.0" />
        <param name="voxel_leaf_size" value="0.05" />
        <param name="max_points_per_file" value="2000000" />
        <param name="preserve_original_intensity" value="true" />
        <param name="enable_intensity_restoration" value="true" />
        
        <!-- 话题重映射 -->
        <remap from="/velodyne_points" to="/velodyne_points" />
        <remap from="/cloud_registered" to="/cloud_registered" />
        <remap from="/force_loop_closure" to="/force_loop_closure" />
        <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
    </node>
    
    <!-- 质量监控（可选） -->
    <node name="intensity_test_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
        <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
        <param name="statistics_topic" value="/intensity_statistics" />
        <param name="quality_report_topic" value="/pointcloud_quality_report" />
        <param name="report_interval" value="5.0" />
    </node>
    
</launch>
