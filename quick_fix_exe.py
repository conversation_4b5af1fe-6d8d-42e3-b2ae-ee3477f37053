#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick fix for bagpy exe issue
Create a working version by manually handling the bagpy version file
"""

import os
import sys
import shutil
import subprocess

def create_bagpy_version_file():
    """创建bagpy需要的version文件"""
    try:
        import bagpy
        bagpy_path = os.path.dirname(bagpy.__file__)
        version_file = os.path.join(bagpy_path, 'version')
        
        if not os.path.exists(version_file):
            print(f"创建bagpy version文件: {version_file}")
            with open(version_file, 'w') as f:
                f.write('0.5.0\n')
            return True
        else:
            print(f"bagpy version文件已存在: {version_file}")
            return True
            
    except Exception as e:
        print(f"创建bagpy version文件失败: {e}")
        return False

def build_simple_exe():
    """构建简单的exe文件"""
    print("🚀 构建简单版exe...")
    
    # 清理之前的构建
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    # 使用最简单的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name=GPS_3D_Analyzer_Simple",
        "gps_gui_analyzer_fixed.py"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 简单版exe构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def test_exe():
    """测试exe文件"""
    exe_path = "dist/GPS_3D_Analyzer_Simple.exe"
    
    if not os.path.exists(exe_path):
        print(f"❌ exe文件不存在: {exe_path}")
        return False
    
    print(f"✅ exe文件存在: {exe_path}")
    
    # 获取文件大小
    size = os.path.getsize(exe_path)
    size_mb = size / (1024 * 1024)
    print(f"📦 文件大小: {size_mb:.1f} MB")
    
    return True

def create_simple_release():
    """创建简单发布包"""
    print("📦 创建简单发布包...")
    
    release_dir = "GPS_3D_Analyzer_Simple_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制exe文件
    exe_path = "dist/GPS_3D_Analyzer_Simple.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        print("✅ exe文件复制完成")
    else:
        print("❌ exe文件不存在")
        return False
    
    # 创建说明文件
    readme_content = """# GPS 3D 轨迹分析器 - 简单版

## 使用说明
如果遇到bagpy相关错误，请尝试以下解决方案：

### 方案1: 安装bagpy
在命令行中运行：
pip install bagpy

### 方案2: 使用CSV数据
1. 将bag文件转换为CSV格式
2. 确保CSV包含以下列：
   - Time: 时间戳
   - latitude: 纬度
   - longitude: 经度
   - altitude: 高度
   - status.status: GPS状态

### 方案3: 联系技术支持
如果问题持续存在，请提供错误信息以获得帮助。

## 系统要求
- Windows 10 64位
- 4GB内存
- 支持OpenGL的显卡

## 界面特色
- 深灰色主题
- 深绿色文字
- 3D交互控制
- 中文界面支持
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 简单发布包创建完成: {release_dir}/")
    return True

def main():
    """主函数"""
    print("🔧 GPS 3D轨迹分析器 - 快速修复")
    print("=" * 50)
    
    # 步骤1: 创建bagpy version文件
    print("📋 步骤1: 修复bagpy version文件...")
    create_bagpy_version_file()
    
    # 步骤2: 构建简单exe
    print("\n📋 步骤2: 构建简单exe...")
    if not build_simple_exe():
        print("❌ 构建失败")
        return False
    
    # 步骤3: 测试exe
    print("\n📋 步骤3: 测试exe文件...")
    if not test_exe():
        print("❌ 测试失败")
        return False
    
    # 步骤4: 创建发布包
    print("\n📋 步骤4: 创建发布包...")
    if not create_simple_release():
        print("❌ 发布包创建失败")
        return False
    
    print("\n" + "=" * 50)
    print("✅ 快速修复完成!")
    print("=" * 50)
    print("📁 发布包: GPS_3D_Analyzer_Simple_Release/")
    print("🚀 exe文件: GPS_3D_Analyzer_Simple.exe")
    print("\n💡 如果仍有bagpy错误，请参考README.txt中的解决方案")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
