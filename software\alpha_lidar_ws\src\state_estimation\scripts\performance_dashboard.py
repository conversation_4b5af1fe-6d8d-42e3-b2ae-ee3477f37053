#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时性能监控和优化仪表板
提供系统性能的实时可视化监控和自动优化建议
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
from collections import deque, defaultdict
from std_msgs.msg import String, Float64
from sensor_msgs.msg import PointCloud2
import psutil
import os

class PerformanceDashboard:
    def __init__(self):
        rospy.init_node('performance_dashboard', anonymous=True)
        
        # GUI设置
        self.root = tk.Tk()
        self.root.title("SLAM系统性能监控仪表板")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2b2b2b')
        
        # 数据存储
        self.max_history = 100
        self.performance_data = {
            'timestamps': deque(maxlen=self.max_history),
            'cpu_usage': deque(maxlen=self.max_history),
            'memory_usage': deque(maxlen=self.max_history),
            'gps_quality': deque(maxlen=self.max_history),
            'intensity_quality': deque(maxlen=self.max_history),
            'loop_detection_rate': deque(maxlen=self.max_history),
            'processing_fps': deque(maxlen=self.max_history),
            'anomaly_rate': deque(maxlen=self.max_history)
        }
        
        # 系统状态
        self.system_status = {
            'slam_active': False,
            'gps_loop_active': False,
            'intensity_preservation_active': False,
            'adaptive_optimization_active': False,
            'overall_health': 'Unknown'
        }
        
        # 实时统计
        self.current_stats = {
            'total_points_processed': 0,
            'total_loops_detected': 0,
            'total_corrections_applied': 0,
            'uptime_seconds': 0,
            'current_fps': 0.0,
            'current_cpu': 0.0,
            'current_memory': 0.0
        }
        
        # 订阅器
        self.setup_subscribers()
        
        # GUI组件
        self.setup_gui()
        
        # 启动监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self.monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        # 启动GUI更新
        self.start_time = time.time()
        self.update_gui()
        
        rospy.loginfo("Performance Dashboard Started")
    
    def setup_subscribers(self):
        """设置ROS订阅器"""
        # 性能指标订阅
        rospy.Subscriber('/performance_metrics', String, self.performance_callback)
        rospy.Subscriber('/intensity_statistics', String, self.intensity_callback)
        rospy.Subscriber('/pointcloud_quality_report', String, self.quality_callback)
        rospy.Subscriber('/optimization_status', String, self.optimization_callback)
        rospy.Subscriber('/parameter_changes', String, self.parameter_callback)
        
        # 系统状态订阅
        rospy.Subscriber('/velodyne_points', PointCloud2, self.pointcloud_callback)
        rospy.Subscriber('/force_loop_closure', String, self.loop_callback)
    
    def setup_gui(self):
        """设置GUI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 实时监控标签页
        self.monitoring_frame = ttk.Frame(notebook)
        notebook.add(self.monitoring_frame, text="实时监控")
        self.setup_monitoring_tab()
        
        # 系统状态标签页
        self.status_frame = ttk.Frame(notebook)
        notebook.add(self.status_frame, text="系统状态")
        self.setup_status_tab()
        
        # 性能分析标签页
        self.analysis_frame = ttk.Frame(notebook)
        notebook.add(self.analysis_frame, text="性能分析")
        self.setup_analysis_tab()
        
        # 优化建议标签页
        self.optimization_frame = ttk.Frame(notebook)
        notebook.add(self.optimization_frame, text="优化建议")
        self.setup_optimization_tab()
    
    def setup_monitoring_tab(self):
        """设置实时监控标签页"""
        # 创建图表框架
        chart_frame = ttk.Frame(self.monitoring_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图表
        self.fig, self.axes = plt.subplots(2, 2, figsize=(12, 8))
        self.fig.patch.set_facecolor('#2b2b2b')
        
        # 设置子图
        self.axes[0, 0].set_title('系统资源使用率', color='white')
        self.axes[0, 0].set_ylabel('使用率 (%)', color='white')
        self.axes[0, 0].tick_params(colors='white')
        
        self.axes[0, 1].set_title('GPS和强度质量', color='white')
        self.axes[0, 1].set_ylabel('质量分数', color='white')
        self.axes[0, 1].tick_params(colors='white')
        
        self.axes[1, 0].set_title('处理性能', color='white')
        self.axes[1, 0].set_ylabel('FPS', color='white')
        self.axes[1, 0].tick_params(colors='white')
        
        self.axes[1, 1].set_title('异常检测率', color='white')
        self.axes[1, 1].set_ylabel('异常率 (%)', color='white')
        self.axes[1, 1].tick_params(colors='white')
        
        # 设置图表背景
        for ax in self.axes.flat:
            ax.set_facecolor('#3b3b3b')
            ax.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 启动动画
        self.animation = animation.FuncAnimation(
            self.fig, self.update_charts, interval=1000, blit=False)
    
    def setup_status_tab(self):
        """设置系统状态标签页"""
        # 状态指示器框架
        status_frame = ttk.LabelFrame(self.status_frame, text="系统模块状态")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建状态指示器
        self.status_indicators = {}
        modules = [
            ('SLAM核心', 'slam_active'),
            ('GPS回环检测', 'gps_loop_active'),
            ('强度值保持', 'intensity_preservation_active'),
            ('自适应优化', 'adaptive_optimization_active')
        ]
        
        for i, (name, key) in enumerate(modules):
            frame = ttk.Frame(status_frame)
            frame.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='w')
            
            label = ttk.Label(frame, text=name + ":")
            label.pack(side=tk.LEFT)
            
            indicator = tk.Label(frame, text="●", font=("Arial", 16), fg="red")
            indicator.pack(side=tk.LEFT, padx=(5, 0))
            
            self.status_indicators[key] = indicator
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(self.status_frame, text="运行统计")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建统计标签
        self.stats_labels = {}
        stats_items = [
            ('运行时间', 'uptime'),
            ('处理点数', 'points_processed'),
            ('检测回环', 'loops_detected'),
            ('应用修正', 'corrections_applied'),
            ('当前FPS', 'current_fps'),
            ('CPU使用率', 'current_cpu'),
            ('内存使用率', 'current_memory'),
            ('系统健康', 'overall_health')
        ]
        
        for i, (name, key) in enumerate(stats_items):
            frame = ttk.Frame(stats_frame)
            frame.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='w')
            
            ttk.Label(frame, text=name + ":").pack(side=tk.LEFT)
            
            value_label = ttk.Label(frame, text="--", font=("Arial", 10, "bold"))
            value_label.pack(side=tk.LEFT, padx=(10, 0))
            
            self.stats_labels[key] = value_label
    
    def setup_analysis_tab(self):
        """设置性能分析标签页"""
        # 分析结果显示
        analysis_text_frame = ttk.LabelFrame(self.analysis_frame, text="性能分析结果")
        analysis_text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建文本显示区域
        self.analysis_text = tk.Text(
            analysis_text_frame, 
            bg='#3b3b3b', 
            fg='white', 
            font=("Consolas", 10),
            wrap=tk.WORD
        )
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(analysis_text_frame, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 分析控制按钮
        control_frame = ttk.Frame(self.analysis_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="生成详细报告", 
                  command=self.generate_detailed_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="导出数据", 
                  command=self.export_performance_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除历史", 
                  command=self.clear_history).pack(side=tk.LEFT, padx=5)
    
    def setup_optimization_tab(self):
        """设置优化建议标签页"""
        # 建议显示区域
        recommendations_frame = ttk.LabelFrame(self.optimization_frame, text="优化建议")
        recommendations_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.recommendations_text = tk.Text(
            recommendations_frame,
            bg='#3b3b3b',
            fg='white',
            font=("Consolas", 10),
            wrap=tk.WORD
        )
        
        rec_scrollbar = ttk.Scrollbar(recommendations_frame, orient=tk.VERTICAL, 
                                     command=self.recommendations_text.yview)
        self.recommendations_text.configure(yscrollcommand=rec_scrollbar.set)
        
        self.recommendations_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rec_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 优化控制
        opt_control_frame = ttk.Frame(self.optimization_frame)
        opt_control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(opt_control_frame, text="应用推荐设置", 
                  command=self.apply_recommendations).pack(side=tk.LEFT, padx=5)
        ttk.Button(opt_control_frame, text="重置为默认", 
                  command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(opt_control_frame, text="保存配置", 
                  command=self.save_configuration).pack(side=tk.LEFT, padx=5)
    
    def performance_callback(self, msg):
        """处理性能指标消息"""
        try:
            data = json.loads(msg.data)
            current_time = time.time()
            
            self.performance_data['timestamps'].append(current_time)
            
            # 更新性能数据
            if 'processing_statistics' in data:
                stats = data['processing_statistics']
                self.current_stats['total_points_processed'] = stats.get('total_points_processed', 0)
                self.current_stats['current_fps'] = stats.get('average_fps', 0.0)
        
        except Exception as e:
            rospy.logerr("Error processing performance data: %s", str(e))
    
    def intensity_callback(self, msg):
        """处理强度统计消息"""
        try:
            data = json.loads(msg.data)
            if 'quality_score' in data:
                self.performance_data['intensity_quality'].append(data['quality_score'])
        except Exception as e:
            rospy.logerr("Error processing intensity data: %s", str(e))
    
    def quality_callback(self, msg):
        """处理质量报告消息"""
        try:
            data = json.loads(msg.data)
            if 'quality_metrics' in data:
                metrics = data['quality_metrics']
                quality_score = metrics.get('overall_quality_score', 0.0)
                self.performance_data['intensity_quality'].append(quality_score)
        except Exception as e:
            rospy.logerr("Error processing quality data: %s", str(e))
    
    def optimization_callback(self, msg):
        """处理优化状态消息"""
        self.system_status['adaptive_optimization_active'] = True
        self.update_recommendations_display(msg.data)
    
    def parameter_callback(self, msg):
        """处理参数变化消息"""
        self.update_analysis_display("Parameter Update:\n" + msg.data + "\n\n")
    
    def pointcloud_callback(self, msg):
        """处理点云消息"""
        self.system_status['slam_active'] = True
        self.system_status['intensity_preservation_active'] = True
    
    def loop_callback(self, msg):
        """处理回环检测消息"""
        self.system_status['gps_loop_active'] = True
        self.current_stats['total_loops_detected'] += 1
    
    def monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active and not rospy.is_shutdown():
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                current_time = time.time()
                
                # 更新性能数据
                self.performance_data['timestamps'].append(current_time)
                self.performance_data['cpu_usage'].append(cpu_percent)
                self.performance_data['memory_usage'].append(memory_percent)
                
                # 更新当前统计
                self.current_stats['current_cpu'] = cpu_percent
                self.current_stats['current_memory'] = memory_percent
                self.current_stats['uptime_seconds'] = current_time - self.start_time
                
                # 评估系统健康状态
                self.assess_system_health()
                
                time.sleep(1)
                
            except Exception as e:
                rospy.logerr("Error in monitoring loop: %s", str(e))
                time.sleep(5)
    
    def assess_system_health(self):
        """评估系统健康状态"""
        health_score = 1.0
        
        # 基于CPU使用率
        if self.current_stats['current_cpu'] > 90:
            health_score -= 0.3
        elif self.current_stats['current_cpu'] > 70:
            health_score -= 0.1
        
        # 基于内存使用率
        if self.current_stats['current_memory'] > 90:
            health_score -= 0.3
        elif self.current_stats['current_memory'] > 80:
            health_score -= 0.1
        
        # 基于模块状态
        active_modules = sum(self.system_status.values())
        if active_modules < 2:
            health_score -= 0.2
        
        # Determine health level
        if health_score >= 0.8:
            self.system_status['overall_health'] = "Excellent"
        elif health_score >= 0.6:
            self.system_status['overall_health'] = "Good"
        elif health_score >= 0.4:
            self.system_status['overall_health'] = "Fair"
        else:
            self.system_status['overall_health'] = "Poor"
    
    def update_charts(self, frame):
        """更新图表"""
        try:
            # 清除所有子图
            for ax in self.axes.flat:
                ax.clear()
                ax.set_facecolor('#3b3b3b')
                ax.grid(True, alpha=0.3)
                ax.tick_params(colors='white')
            
            if not self.performance_data['timestamps']:
                return
            
            timestamps = list(self.performance_data['timestamps'])
            time_labels = [(t - timestamps[0]) for t in timestamps]
            
            # 系统资源使用率
            if self.performance_data['cpu_usage'] and self.performance_data['memory_usage']:
                self.axes[0, 0].plot(time_labels, list(self.performance_data['cpu_usage']), 
                                   'r-', label='CPU', linewidth=2)
                self.axes[0, 0].plot(time_labels, list(self.performance_data['memory_usage']),
                                   'b-', label='Memory', linewidth=2)
                self.axes[0, 0].set_title('System Resource Usage', color='white')
                self.axes[0, 0].set_ylabel('Usage (%)', color='white')
                self.axes[0, 0].legend()
                self.axes[0, 0].set_ylim(0, 100)
            
            # GPS和强度质量
            if self.performance_data['gps_quality'] or self.performance_data['intensity_quality']:
                if self.performance_data['gps_quality']:
                    gps_times = time_labels[-len(self.performance_data['gps_quality']):]
                    self.axes[0, 1].plot(gps_times, list(self.performance_data['gps_quality']),
                                       'g-', label='GPS Quality', linewidth=2)

                if self.performance_data['intensity_quality']:
                    int_times = time_labels[-len(self.performance_data['intensity_quality']):]
                    self.axes[0, 1].plot(int_times, list(self.performance_data['intensity_quality']),
                                       'orange', label='Intensity Quality', linewidth=2)

                self.axes[0, 1].set_title('GPS and Intensity Quality', color='white')
                self.axes[0, 1].set_ylabel('Quality Score', color='white')
                self.axes[0, 1].legend()
                self.axes[0, 1].set_ylim(0, 1)
            
            # 处理性能
            if self.performance_data['processing_fps']:
                fps_times = time_labels[-len(self.performance_data['processing_fps']):]
                self.axes[1, 0].plot(fps_times, list(self.performance_data['processing_fps']),
                                   'cyan', label='Processing FPS', linewidth=2)
                self.axes[1, 0].set_title('Processing Performance', color='white')
                self.axes[1, 0].set_ylabel('FPS', color='white')
                self.axes[1, 0].legend()

            # Anomaly detection rate
            if self.performance_data['anomaly_rate']:
                anom_times = time_labels[-len(self.performance_data['anomaly_rate']):]
                self.axes[1, 1].plot(anom_times, list(self.performance_data['anomaly_rate']),
                                   'red', label='Anomaly Rate', linewidth=2)
                self.axes[1, 1].set_title('Anomaly Detection Rate', color='white')
                self.axes[1, 1].set_ylabel('Anomaly Rate (%)', color='white')
                self.axes[1, 1].legend()

            # Set x-axis labels
            for ax in self.axes.flat:
                ax.set_xlabel('Time (seconds)', color='white')
            
            plt.tight_layout()
            
        except Exception as e:
            rospy.logerr("Error updating charts: %s", str(e))
    
    def update_gui(self):
        """更新GUI显示"""
        try:
            # 更新状态指示器
            for key, indicator in self.status_indicators.items():
                if self.system_status.get(key, False):
                    indicator.config(fg="green")
                else:
                    indicator.config(fg="red")
            
            # 更新统计标签
            uptime = int(self.current_stats['uptime_seconds'])
            uptime_str = f"{uptime//3600:02d}:{(uptime%3600)//60:02d}:{uptime%60:02d}"
            
            updates = {
                'uptime': uptime_str,
                'points_processed': f"{self.current_stats['total_points_processed']:,}",
                'loops_detected': f"{self.current_stats['total_loops_detected']}",
                'corrections_applied': f"{self.current_stats['total_corrections_applied']}",
                'current_fps': f"{self.current_stats['current_fps']:.1f}",
                'current_cpu': f"{self.current_stats['current_cpu']:.1f}%",
                'current_memory': f"{self.current_stats['current_memory']:.1f}%",
                'overall_health': self.system_status['overall_health']
            }
            
            for key, value in updates.items():
                if key in self.stats_labels:
                    self.stats_labels[key].config(text=value)
            
            # Set health status color
            health = self.system_status['overall_health']
            if health == "Excellent":
                color = "green"
            elif health == "Good":
                color = "blue"
            elif health == "Fair":
                color = "orange"
            else:
                color = "red"
            
            self.stats_labels['overall_health'].config(foreground=color)
            
        except Exception as e:
            rospy.logerr("Error updating GUI: %s", str(e))
        
        # 定时更新
        self.root.after(1000, self.update_gui)
    
    def update_analysis_display(self, text):
        """更新分析显示"""
        self.analysis_text.insert(tk.END, text)
        self.analysis_text.see(tk.END)
    
    def update_recommendations_display(self, text):
        """更新建议显示"""
        self.recommendations_text.delete(1.0, tk.END)
        self.recommendations_text.insert(tk.END, text)
    
    def generate_detailed_report(self):
        """生成详细报告"""
        try:
            report = f"""
=== SLAM系统性能详细报告 ===
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

系统运行状态:
- 运行时间: {int(self.current_stats['uptime_seconds'])}秒
- SLAM核心: {'活跃' if self.system_status['slam_active'] else '非活跃'}
- GPS回环检测: {'活跃' if self.system_status['gps_loop_active'] else '非活跃'}
- 强度值保持: {'活跃' if self.system_status['intensity_preservation_active'] else '非活跃'}
- 自适应优化: {'活跃' if self.system_status['adaptive_optimization_active'] else '非活跃'}

处理统计:
- 总处理点数: {self.current_stats['total_points_processed']:,}
- 检测回环数: {self.current_stats['total_loops_detected']}
- 应用修正数: {self.current_stats['total_corrections_applied']}
- 当前处理FPS: {self.current_stats['current_fps']:.2f}

系统资源:
- CPU使用率: {self.current_stats['current_cpu']:.1f}%
- 内存使用率: {self.current_stats['current_memory']:.1f}%
- 系统健康状态: {self.system_status['overall_health']}

性能趋势:
- 平均CPU使用率: {np.mean(list(self.performance_data['cpu_usage'])) if self.performance_data['cpu_usage'] else 0:.1f}%
- 平均内存使用率: {np.mean(list(self.performance_data['memory_usage'])) if self.performance_data['memory_usage'] else 0:.1f}%
- 平均强度质量: {np.mean(list(self.performance_data['intensity_quality'])) if self.performance_data['intensity_quality'] else 0:.3f}

建议:
- 系统运行{'正常' if self.system_status['overall_health'] in ['优秀', '良好'] else '需要关注'}
- {'CPU使用率较高，建议优化处理算法' if self.current_stats['current_cpu'] > 80 else 'CPU使用率正常'}
- {'内存使用率较高，建议检查内存泄漏' if self.current_stats['current_memory'] > 80 else '内存使用率正常'}

=== 报告结束 ===
            """
            
            self.update_analysis_display(report)
            
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {str(e)}")
    
    def export_performance_data(self):
        """导出性能数据"""
        try:
            import csv
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # 写入标题
                    writer.writerow(['时间戳', 'CPU使用率', '内存使用率', 'GPS质量', '强度质量', '处理FPS', '异常率'])
                    
                    # 写入数据
                    max_len = max(len(self.performance_data[key]) for key in self.performance_data.keys())
                    
                    for i in range(max_len):
                        row = []
                        for key in ['timestamps', 'cpu_usage', 'memory_usage', 'gps_quality', 
                                   'intensity_quality', 'processing_fps', 'anomaly_rate']:
                            if i < len(self.performance_data[key]):
                                row.append(self.performance_data[key][i])
                            else:
                                row.append('')
                        writer.writerow(row)
                
                messagebox.showinfo("成功", f"数据已导出到: {filename}")
        
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def clear_history(self):
        """清除历史数据"""
        if messagebox.askyesno("确认", "确定要清除所有历史数据吗？"):
            for key in self.performance_data:
                self.performance_data[key].clear()
            
            self.analysis_text.delete(1.0, tk.END)
            messagebox.showinfo("成功", "历史数据已清除")
    
    def apply_recommendations(self):
        """应用推荐设置"""
        messagebox.showinfo("信息", "推荐设置应用功能正在开发中...")
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        if messagebox.askyesno("确认", "确定要重置为默认设置吗？"):
            messagebox.showinfo("信息", "默认设置重置功能正在开发中...")
    
    def save_configuration(self):
        """保存配置"""
        messagebox.showinfo("信息", "配置保存功能正在开发中...")
    
    def run(self):
        """运行仪表板"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            rospy.logerr("Dashboard error: %s", str(e))
    
    def on_closing(self):
        """关闭处理"""
        self.monitoring_active = False
        plt.close('all')
        self.root.quit()
        self.root.destroy()

def main():
    try:
        dashboard = PerformanceDashboard()
        
        rospy.loginfo("Performance Dashboard running...")
        rospy.loginfo("GUI interface started for real-time monitoring")
        
        # 在单独线程中运行ROS spin
        ros_thread = threading.Thread(target=rospy.spin)
        ros_thread.daemon = True
        ros_thread.start()
        
        # 运行GUI主循环
        dashboard.run()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("Performance Dashboard stopped")
    except Exception as e:
        rospy.logerr("Performance Dashboard exception: %s", str(e))

if __name__ == '__main__':
    main()
