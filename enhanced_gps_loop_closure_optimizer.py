#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版GPS回环检测优化器
支持路径中间区域的回环检测和匹配
包括起点-终点回环、中间交叉回环、重复路径回环等
"""

import rospy
import numpy as np
import math
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped, Point
from std_msgs.msg import Bool, Float64, Int32
from nav_msgs.msg import Path
import tf2_ros
import tf2_geometry_msgs
from threading import Lock
import time
from collections import deque
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import cdist

class EnhancedGPSLoopClosureOptimizer:
    def __init__(self):
        rospy.init_node('enhanced_gps_loop_closure_optimizer', anonymous=True)
        
        # 基础参数配置
        self.loop_closure_distance_threshold = rospy.get_param('~loop_closure_distance_threshold', 5.0)
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 50.0)
        self.gps_quality_threshold = rospy.get_param('~gps_quality_threshold', -1)
        self.check_interval = rospy.get_param('~check_interval', 1.0)
        
        # 增强回环检测参数
        self.intermediate_loop_threshold = rospy.get_param('~intermediate_loop_threshold', 8.0)  # 中间回环距离阈值
        self.min_loop_separation = rospy.get_param('~min_loop_separation', 30.0)  # 最小回环分离距离
        self.trajectory_window_size = rospy.get_param('~trajectory_window_size', 100)  # 轨迹窗口大小
        self.clustering_eps = rospy.get_param('~clustering_eps', 3.0)  # 聚类距离参数
        self.min_cluster_size = rospy.get_param('~min_cluster_size', 3)  # 最小聚类大小
        self.revisit_threshold = rospy.get_param('~revisit_threshold', 10.0)  # 重访阈值
        
        # GPS话题
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        
        # 轨迹数据存储
        self.gps_trajectory = deque(maxlen=2000)  # GPS轨迹历史
        self.pose_trajectory = deque(maxlen=2000)  # 位姿轨迹历史
        self.trajectory_timestamps = deque(maxlen=2000)  # 时间戳历史
        
        # 回环检测状态
        self.detected_loops = []  # 已检测到的回环
        self.loop_candidates = []  # 当前回环候选
        self.last_loop_detection_time = 0
        self.loop_detection_cooldown = 5.0  # 回环检测冷却时间
        
        # 线程锁
        self.data_lock = Lock()
        
        # 发布器
        self.force_loop_closure_pub = rospy.Publisher('/force_loop_closure', Bool, queue_size=1)
        self.loop_distance_pub = rospy.Publisher('/loop_closure_distance', Float64, queue_size=1)
        self.intermediate_loop_pub = rospy.Publisher('/intermediate_loop_detected', Bool, queue_size=1)
        self.loop_candidates_pub = rospy.Publisher('/loop_candidates_count', Int32, queue_size=1)
        self.trajectory_analysis_pub = rospy.Publisher('/trajectory_analysis', Bool, queue_size=1)
        
        # 订阅器
        self.gps_sub = rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)
        self.pose_sub = rospy.Subscriber('/aft_mapped_to_init', PoseStamped, self.pose_callback)
        
        # 定时器
        self.analysis_timer = rospy.Timer(rospy.Duration(self.check_interval), self.analyze_trajectory)
        
        rospy.loginfo("🚀 增强版GPS回环检测优化器启动")
        rospy.loginfo(f"📏 基础回环距离阈值: {self.loop_closure_distance_threshold:.1f}m")
        rospy.loginfo(f"🔄 中间回环距离阈值: {self.intermediate_loop_threshold:.1f}m")
        rospy.loginfo(f"📐 最小回环分离距离: {self.min_loop_separation:.1f}m")
        rospy.loginfo(f"📊 轨迹窗口大小: {self.trajectory_window_size}")
        
    def gps_callback(self, msg):
        """GPS回调函数"""
        with self.data_lock:
            # 检查GPS质量
            if hasattr(msg, 'status') and msg.status.status < self.gps_quality_threshold:
                return
            
            current_time = rospy.Time.now().to_sec()
            gps_data = {
                'latitude': msg.latitude,
                'longitude': msg.longitude,
                'altitude': msg.altitude,
                'status': msg.status.status if hasattr(msg, 'status') else -1,
                'timestamp': current_time,
                'local_coords': None  # 将在后续计算
            }
            
            # 转换为本地坐标
            if len(self.gps_trajectory) > 0:
                origin = self.gps_trajectory[0]
                local_coords = self.gps_to_local_coords(gps_data, origin)
                gps_data['local_coords'] = local_coords
            else:
                gps_data['local_coords'] = [0.0, 0.0, 0.0]
                rospy.loginfo(f"📍 设置GPS原点: lat={msg.latitude:.8f}, lon={msg.longitude:.8f}")
            
            self.gps_trajectory.append(gps_data)
            self.trajectory_timestamps.append(current_time)
    
    def pose_callback(self, msg):
        """SLAM位姿回调函数"""
        with self.data_lock:
            pose_data = {
                'position': [msg.pose.position.x, msg.pose.position.y, msg.pose.position.z],
                'orientation': [msg.pose.orientation.x, msg.pose.orientation.y, 
                              msg.pose.orientation.z, msg.pose.orientation.w],
                'timestamp': msg.header.stamp.to_sec()
            }
            self.pose_trajectory.append(pose_data)
    
    def gps_to_local_coords(self, gps_data, origin):
        """将GPS坐标转换为本地坐标"""
        lat_diff = gps_data['latitude'] - origin['latitude']
        lon_diff = gps_data['longitude'] - origin['longitude']
        alt_diff = gps_data['altitude'] - origin['altitude']
        
        # 转换为米
        x = lon_diff * 111320.0 * math.cos(math.radians(origin['latitude']))
        y = lat_diff * 110540.0
        z = alt_diff
        
        return [x, y, z]
    
    def analyze_trajectory(self, event):
        """分析轨迹并检测各种类型的回环"""
        with self.data_lock:
            if len(self.gps_trajectory) < 10:
                return
            
            current_time = time.time()
            if (current_time - self.last_loop_detection_time) < self.loop_detection_cooldown:
                return
            
            # 1. 检测起点-终点回环
            self.detect_start_end_loop()
            
            # 2. 检测中间区域回环
            self.detect_intermediate_loops()
            
            # 3. 检测重复路径回环
            self.detect_revisit_loops()
            
            # 4. 发布分析结果
            self.publish_analysis_results()
    
    def detect_start_end_loop(self):
        """检测起点-终点回环"""
        if len(self.gps_trajectory) < 2:
            return
        
        start_pos = np.array(self.gps_trajectory[0]['local_coords'])
        current_pos = np.array(self.gps_trajectory[-1]['local_coords'])
        
        distance = np.linalg.norm(current_pos - start_pos)
        trajectory_length = self.calculate_trajectory_length()
        
        # 发布距离信息
        self.loop_distance_pub.publish(Float64(data=distance))
        
        if (distance <= self.loop_closure_distance_threshold and 
            trajectory_length >= self.min_trajectory_length):
            
            loop_info = {
                'type': 'start_end',
                'distance': distance,
                'start_idx': 0,
                'end_idx': len(self.gps_trajectory) - 1,
                'confidence': self.calculate_loop_confidence(0, len(self.gps_trajectory) - 1)
            }
            
            if self.is_new_loop(loop_info):
                self.trigger_loop_closure(loop_info)
    
    def detect_intermediate_loops(self):
        """检测中间区域回环"""
        if len(self.gps_trajectory) < self.trajectory_window_size:
            return
        
        # 获取最近的轨迹窗口
        recent_trajectory = list(self.gps_trajectory)[-self.trajectory_window_size:]
        positions = np.array([point['local_coords'] for point in recent_trajectory])
        
        # 使用滑动窗口检测回环
        window_size = min(50, len(positions) // 4)
        
        for i in range(len(positions) - window_size):
            current_segment = positions[i:i+window_size]
            
            # 在之前的轨迹中搜索相似区域
            for j in range(0, max(0, i - self.min_loop_separation)):
                if j + window_size >= len(positions):
                    continue
                
                candidate_segment = positions[j:j+window_size]
                
                # 计算段间距离
                segment_distance = self.calculate_segment_distance(current_segment, candidate_segment)
                
                if segment_distance <= self.intermediate_loop_threshold:
                    loop_info = {
                        'type': 'intermediate',
                        'distance': segment_distance,
                        'start_idx': j,
                        'end_idx': i,
                        'confidence': self.calculate_segment_confidence(current_segment, candidate_segment)
                    }
                    
                    if self.is_new_loop(loop_info):
                        self.trigger_loop_closure(loop_info)
                        return  # 一次只处理一个回环
    
    def detect_revisit_loops(self):
        """检测重复访问区域的回环"""
        if len(self.gps_trajectory) < 20:
            return
        
        # 获取所有位置
        positions = np.array([point['local_coords'] for point in self.gps_trajectory])
        
        # 使用DBSCAN聚类找到密集区域
        try:
            clustering = DBSCAN(eps=self.clustering_eps, min_samples=self.min_cluster_size)
            cluster_labels = clustering.fit_predict(positions[:, :2])  # 只使用x,y坐标
            
            # 分析聚类结果
            unique_labels = set(cluster_labels)
            for label in unique_labels:
                if label == -1:  # 噪声点
                    continue
                
                cluster_indices = np.where(cluster_labels == label)[0]
                if len(cluster_indices) < self.min_cluster_size:
                    continue
                
                # 检查聚类中的时间分离
                cluster_times = [self.trajectory_timestamps[i] for i in cluster_indices]
                time_gaps = self.find_time_gaps(cluster_times, cluster_indices)
                
                if len(time_gaps) > 0:  # 发现时间间隔，表示重复访问
                    loop_info = {
                        'type': 'revisit',
                        'cluster_label': label,
                        'indices': cluster_indices.tolist(),
                        'time_gaps': time_gaps,
                        'confidence': len(cluster_indices) / len(positions)
                    }
                    
                    if self.is_new_loop(loop_info):
                        self.trigger_loop_closure(loop_info)
                        
        except Exception as e:
            rospy.logwarn(f"聚类分析失败: {e}")
    
    def calculate_segment_distance(self, segment1, segment2):
        """计算两个轨迹段之间的距离"""
        if len(segment1) != len(segment2):
            return float('inf')
        
        distances = np.linalg.norm(segment1 - segment2, axis=1)
        return np.mean(distances)
    
    def calculate_segment_confidence(self, segment1, segment2):
        """计算段匹配的置信度"""
        distances = np.linalg.norm(segment1 - segment2, axis=1)
        max_distance = np.max(distances)
        mean_distance = np.mean(distances)
        
        # 置信度基于距离的一致性
        confidence = 1.0 / (1.0 + mean_distance + max_distance * 0.1)
        return confidence
    
    def find_time_gaps(self, times, indices):
        """找到时间序列中的间隔"""
        if len(times) < 2:
            return []
        
        sorted_pairs = sorted(zip(times, indices))
        gaps = []
        
        for i in range(1, len(sorted_pairs)):
            time_diff = sorted_pairs[i][0] - sorted_pairs[i-1][0]
            index_diff = abs(sorted_pairs[i][1] - sorted_pairs[i-1][1])
            
            # 如果时间相近但索引相差很大，说明是重复访问
            if time_diff < 10.0 and index_diff > 20:
                gaps.append({
                    'time_gap': time_diff,
                    'index_gap': index_diff,
                    'indices': [sorted_pairs[i-1][1], sorted_pairs[i][1]]
                })
        
        return gaps
    
    def calculate_loop_confidence(self, start_idx, end_idx):
        """计算回环的置信度"""
        if start_idx >= end_idx or end_idx >= len(self.gps_trajectory):
            return 0.0
        
        start_pos = np.array(self.gps_trajectory[start_idx]['local_coords'])
        end_pos = np.array(self.gps_trajectory[end_idx]['local_coords'])
        
        distance = np.linalg.norm(end_pos - start_pos)
        trajectory_span = end_idx - start_idx
        
        # 置信度基于距离和轨迹跨度
        confidence = 1.0 / (1.0 + distance) * min(1.0, trajectory_span / 100.0)
        return confidence
    
    def calculate_trajectory_length(self):
        """计算轨迹总长度"""
        if len(self.gps_trajectory) < 2:
            return 0.0
        
        total_length = 0.0
        for i in range(1, len(self.gps_trajectory)):
            pos1 = np.array(self.gps_trajectory[i-1]['local_coords'])
            pos2 = np.array(self.gps_trajectory[i]['local_coords'])
            total_length += np.linalg.norm(pos2 - pos1)
        
        return total_length
    
    def is_new_loop(self, loop_info):
        """检查是否是新的回环"""
        for existing_loop in self.detected_loops:
            if existing_loop['type'] == loop_info['type']:
                if loop_info['type'] == 'start_end':
                    return False  # 起点-终点回环只能有一个
                elif loop_info['type'] == 'intermediate':
                    # 检查是否是相似的中间回环
                    if (abs(existing_loop['start_idx'] - loop_info['start_idx']) < 20 and
                        abs(existing_loop['end_idx'] - loop_info['end_idx']) < 20):
                        return False
        
        return True
    
    def trigger_loop_closure(self, loop_info):
        """触发回环闭合"""
        current_time = time.time()
        
        rospy.logwarn(f"🔄 检测到{loop_info['type']}回环!")
        rospy.logwarn(f"📊 回环信息: {loop_info}")
        
        # 发布强制回环信号
        self.force_loop_closure_pub.publish(Bool(data=True))
        
        if loop_info['type'] == 'intermediate':
            self.intermediate_loop_pub.publish(Bool(data=True))
        
        # 记录回环
        loop_info['detection_time'] = current_time
        self.detected_loops.append(loop_info)
        self.last_loop_detection_time = current_time
        
        rospy.loginfo(f"✅ 已记录回环，总计: {len(self.detected_loops)} 个")
    
    def publish_analysis_results(self):
        """发布分析结果"""
        # 发布候选数量
        self.loop_candidates_pub.publish(Int32(data=len(self.loop_candidates)))
        
        # 发布轨迹分析状态
        analysis_active = len(self.gps_trajectory) > self.trajectory_window_size
        self.trajectory_analysis_pub.publish(Bool(data=analysis_active))
    
    def get_status_info(self):
        """获取详细状态信息"""
        with self.data_lock:
            if len(self.gps_trajectory) == 0:
                return "等待GPS数据..."
            
            trajectory_length = self.calculate_trajectory_length()
            detected_loops_info = []
            
            for loop in self.detected_loops:
                loop_desc = f"{loop['type']}回环(置信度:{loop.get('confidence', 0):.3f})"
                detected_loops_info.append(loop_desc)
            
            status = f"""
增强版GPS回环检测状态:
📊 轨迹点数: {len(self.gps_trajectory)}
📐 轨迹长度: {trajectory_length:.1f}m
🔄 已检测回环: {len(self.detected_loops)} 个
   {', '.join(detected_loops_info) if detected_loops_info else '无'}
📏 起点距离阈值: {self.loop_closure_distance_threshold:.1f}m
🔄 中间回环阈值: {self.intermediate_loop_threshold:.1f}m
📊 分析窗口大小: {self.trajectory_window_size}
            """
            return status.strip()

def main():
    try:
        optimizer = EnhancedGPSLoopClosureOptimizer()
        
        # 状态监控
        def print_status(event):
            rospy.loginfo_throttle(15, optimizer.get_status_info())
        
        status_timer = rospy.Timer(rospy.Duration(15.0), print_status)
        
        rospy.loginfo("✅ 增强版GPS回环检测优化器运行中...")
        rospy.loginfo("💡 支持的回环类型:")
        rospy.loginfo("   🔄 起点-终点回环: 传统的首尾闭合")
        rospy.loginfo("   🔄 中间区域回环: 路径中间的交叉回环")
        rospy.loginfo("   🔄 重复访问回环: 多次经过同一区域")
        
        rospy.spin()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("🛑 增强版GPS回环检测优化器已停止")

if __name__ == '__main__':
    main()
