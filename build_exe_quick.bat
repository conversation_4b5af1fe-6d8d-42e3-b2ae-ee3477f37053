@echo off
echo ========================================
echo GPS 3D 轨迹分析器 - 快速打包构建
echo ========================================
echo.

echo 📋 检查文件...
if not exist "gps_gui_analyzer_fixed.py" (
    echo ❌ 主程序文件不存在: gps_gui_analyzer_fixed.py
    pause
    exit /b 1
)

echo ✅ 主程序文件存在
echo.

echo 📦 安装依赖包...
pip install bagpy numpy pandas matplotlib pyinstaller pillow setuptools wheel
echo.

echo 🚀 开始构建可执行文件...
pyinstaller --onefile --windowed --name=GPS_3D_Analyzer ^
    --hidden-import=bagpy ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=mpl_toolkits.mplot3d ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.scrolledtext ^
    --collect-all=matplotlib ^
    --collect-all=bagpy ^
    --exclude-module=PyQt5 ^
    --exclude-module=PyQt6 ^
    --exclude-module=PySide2 ^
    --exclude-module=PySide6 ^
    gps_gui_analyzer_fixed.py

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 📁 创建发布包...
if exist "GPS_3D_Analyzer_Release" rmdir /s /q "GPS_3D_Analyzer_Release"
mkdir "GPS_3D_Analyzer_Release"

if exist "dist\GPS_3D_Analyzer.exe" (
    copy "dist\GPS_3D_Analyzer.exe" "GPS_3D_Analyzer_Release\"
    echo ✅ 可执行文件复制完成
) else (
    echo ❌ 可执行文件不存在
    pause
    exit /b 1
)

echo.
echo 📝 创建说明文件...
echo # GPS 3D 轨迹分析器 > "GPS_3D_Analyzer_Release\README.txt"
echo. >> "GPS_3D_Analyzer_Release\README.txt"
echo ## 使用方法 >> "GPS_3D_Analyzer_Release\README.txt"
echo 1. 双击 GPS_3D_Analyzer.exe 启动程序 >> "GPS_3D_Analyzer_Release\README.txt"
echo 2. 选择ROS bag文件 >> "GPS_3D_Analyzer_Release\README.txt"
echo 3. 设置GPS话题（默认: /rtk/gnss） >> "GPS_3D_Analyzer_Release\README.txt"
echo 4. 选择输出文件位置 >> "GPS_3D_Analyzer_Release\README.txt"
echo 5. 点击"开始分析" >> "GPS_3D_Analyzer_Release\README.txt"
echo 6. 查看3D轨迹和分析结果 >> "GPS_3D_Analyzer_Release\README.txt"
echo. >> "GPS_3D_Analyzer_Release\README.txt"
echo ## 系统要求 >> "GPS_3D_Analyzer_Release\README.txt"
echo - Windows 10 64位 >> "GPS_3D_Analyzer_Release\README.txt"
echo - 最小4GB内存 >> "GPS_3D_Analyzer_Release\README.txt"
echo - 支持OpenGL的显卡 >> "GPS_3D_Analyzer_Release\README.txt"

echo.
echo ========================================
echo ✅ 构建完成！
echo ========================================
echo.
echo 📁 发布文件位置: GPS_3D_Analyzer_Release\
echo 🚀 可执行文件: GPS_3D_Analyzer.exe
echo.

for %%f in ("GPS_3D_Analyzer_Release\*") do (
    echo   📄 %%~nxf
)

echo.
echo 💡 现在可以分发 GPS_3D_Analyzer_Release 文件夹
echo 💡 用户只需双击 GPS_3D_Analyzer.exe 即可运行
echo.
pause
