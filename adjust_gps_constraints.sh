#!/bin/bash

# GPS约束强度调整脚本

echo "=========================================="
echo "αLiDAR GPS约束强度调整工具"
echo "=========================================="

if [ $# -eq 0 ]; then
    echo "用法: $0 [mode]"
    echo ""
    echo "约束模式:"
    echo "  0 - 禁用GPS约束"
    echo "  1 - 保守模式 (推荐，温和约束)"
    echo "  2 - 中等模式 (平衡约束)"
    echo "  3 - 激进模式 (强约束，可能过度校正)"
    echo ""
    echo "当前参数查看:"
    echo "  rosparam get /gps/xy_correction_threshold"
    echo "  rosparam get /gps/xy_correction_rate"
    echo "  rosparam get /gps/plane_constraint_weight"
    exit 1
fi

MODE=$1

case $MODE in
    0)
        echo "设置为禁用模式..."
        rosparam set /gps/enable_correction false
        rosparam set /gps/enable_loop_closure false
        rosparam set /gps/enable_plane_constraint false
        echo "✅ GPS约束已禁用"
        ;;
    1)
        echo "设置为保守模式（推荐）..."
        rosparam set /gps/enable_correction true
        rosparam set /gps/enable_loop_closure true
        rosparam set /gps/enable_plane_constraint true
        rosparam set /gps/xy_correction_threshold 0.8
        rosparam set /gps/xy_correction_rate 0.03
        rosparam set /gps/plane_constraint_weight 0.1
        rosparam set /gps/loop_closure_distance 3.0
        echo "✅ GPS约束设置为保守模式"
        ;;
    2)
        echo "设置为中等模式..."
        rosparam set /gps/enable_correction true
        rosparam set /gps/enable_loop_closure true
        rosparam set /gps/enable_plane_constraint true
        rosparam set /gps/xy_correction_threshold 0.5
        rosparam set /gps/xy_correction_rate 0.05
        rosparam set /gps/plane_constraint_weight 0.2
        rosparam set /gps/loop_closure_distance 3.0
        echo "✅ GPS约束设置为中等模式"
        ;;
    3)
        echo "设置为激进模式（谨慎使用）..."
        rosparam set /gps/enable_correction true
        rosparam set /gps/enable_loop_closure true
        rosparam set /gps/enable_plane_constraint true
        rosparam set /gps/xy_correction_threshold 0.3
        rosparam set /gps/xy_correction_rate 0.08
        rosparam set /gps/plane_constraint_weight 0.3
        rosparam set /gps/loop_closure_distance 4.0
        echo "✅ GPS约束设置为激进模式"
        ;;
    *)
        echo "❌ 无效的模式: $MODE"
        echo "请使用 0, 1, 2, 或 3"
        exit 1
        ;;
esac

echo ""
echo "当前GPS约束参数:"
echo "  XY校正阈值: $(rosparam get /gps/xy_correction_threshold 2>/dev/null || echo 'N/A')"
echo "  XY校正率: $(rosparam get /gps/xy_correction_rate 2>/dev/null || echo 'N/A')"
echo "  平面约束权重: $(rosparam get /gps/plane_constraint_weight 2>/dev/null || echo 'N/A')"
echo "  回环检测距离: $(rosparam get /gps/loop_closure_distance 2>/dev/null || echo 'N/A')"

echo ""
echo "注意: 参数更改将在下次处理GPS数据时生效"
echo "建议: 如果拼接效果不理想，尝试模式1（保守模式）"
