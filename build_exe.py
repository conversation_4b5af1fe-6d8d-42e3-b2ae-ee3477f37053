#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for GPS GUI Analyzer
Creates standalone executable for Windows 10 64-bit
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def build_executable():
    """Build executable using PyInstaller"""
    print("Building executable...")
    
    # PyInstaller command for enhanced version
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--windowed",                   # No console window
        "--name=GPS_3D_Analyzer_Enhanced", # Executable name
        "--add-data=requirements.txt;.", # Include requirements
        "--hidden-import=bagpy",        # Ensure bagpy is included
        "--hidden-import=matplotlib.backends.backend_tkagg",
        "--hidden-import=matplotlib.backends.backend_tk",
        "--hidden-import=mpl_toolkits.mplot3d",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.scrolledtext",
        "--collect-all=matplotlib",     # Include all matplotlib files
        "--collect-all=bagpy",          # Include all bagpy files
        "gps_gui_analyzer_enhanced.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ Executable built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build executable: {e}")
        return False
    except FileNotFoundError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        try:
            subprocess.check_call(cmd)
            print("✅ Executable built successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to build executable: {e}")
            return False

def create_icon():
    """Create a simple icon file"""
    icon_content = """
    # Simple icon placeholder
    # In a real scenario, you would have a proper .ico file
    """
    
    # For now, we'll skip the icon and remove it from the command
    return True

def package_release():
    """Package the release"""
    print("Packaging release...")
    
    # Create release directory
    release_dir = "GPS_3D_Analyzer_Release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # Copy executable
    exe_path = os.path.join("dist", "GPS_3D_Analyzer_Enhanced.exe")
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        print(f"✅ Copied executable to {release_dir}")
    else:
        print("❌ Executable not found in dist folder")
        return False
    
    # Create README
    readme_content = """# GPS 3D Trajectory Analyzer

## Description
A GUI application for analyzing GPS trajectories from ROS bag files with 3D visualization.

## Features
- Extract GPS data from ROS bag files
- 3D trajectory visualization with quality color coding
- Detailed analysis results
- Export trajectory to text file (Frame_ID X Y Z format)
- Real-time terminal output

## Usage
1. Run GPS_3D_Analyzer.exe
2. Select a ROS bag file using the Browse button
3. Specify GPS topic (default: /rtk/gnss)
4. Choose output file location
5. Click "Start Analysis"
6. View 3D trajectory and analysis results
7. Trajectory will be saved to the specified text file

## GPS Quality Color Coding
- Green Solid Line: RTK Fixed Solution (Highest accuracy)
- Blue Dashed Line: SBAS Fix
- Yellow Dashed Line: GBAS Fix
- Red Dashed Line: No Fix
- Purple Dashed Line: Other

## Output Format
The output text file contains trajectory points in the format:
Frame_ID X Y Z

Where:
- Frame_ID: Sequential frame number
- X, Y, Z: Local coordinates in meters (ENU system)

## System Requirements
- Windows 10 64-bit
- Minimum 4GB RAM
- Graphics card supporting OpenGL

## Support
For issues or questions, please check the terminal output for detailed error messages.
"""
    
    with open(os.path.join(release_dir, "README.txt"), "w") as f:
        f.write(readme_content)
    
    print(f"✅ Release package created in {release_dir}")
    return True

def main():
    """Main build process"""
    print("=" * 60)
    print("GPS 3D Analyzer - Build Script")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Build executable
    if not build_executable():
        return
    
    # Package release
    if not package_release():
        return
    
    print("\n" + "=" * 60)
    print("✅ Build completed successfully!")
    print("=" * 60)
    print(f"Executable location: GPS_3D_Analyzer_Release/GPS_3D_Analyzer_Enhanced.exe")
    print("You can now distribute the GPS_3D_Analyzer_Release folder")

if __name__ == "__main__":
    main()
