# -*- mode: python ; coding: utf-8 -*-
# Complete bagpy-fixed spec file

import sys
from pathlib import Path

block_cipher = None

# 完整的隐藏导入列表
hiddenimports = [
    # bagpy相关
    'bagpy',
    'bagpy.bagreader',
    'bagpy.bagpy',
    
    # ROS相关
    'rosbag',
    'rospy',
    'genpy',
    'std_msgs',
    'sensor_msgs',
    'geometry_msgs',
    'nav_msgs',
    
    # 数据处理
    'pandas',
    'numpy',
    'matplotlib',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_agg',
    'mpl_toolkits.mplot3d',
    'mpl_toolkits.mplot3d.axes3d',
    
    # GUI
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    
    # 系统
    'threading',
    'collections',
    'math',
    'time',
    'locale',
    'pathlib',
    'tempfile',
    'traceback',
    'csv',
    'random',
]

# 完整的数据文件列表 - 包含所有bagpy文件
datas = [
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\bagreader.py', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\README.md', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\version', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\__init__.py', r'bagpy'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\__pycache__\bagreader.cpython-313.pyc', r'bagpy\__pycache__'),
    (r'C:\Users\<USER>\Miniconda3\Lib\site-packages\bagpy\__pycache__\__init__.cpython-313.pyc', r'bagpy\__pycache__'),
]

# 二进制文件
binaries = []

a = Analysis(
    ['gps_gui_analyzer_fixed.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6', 
        'PySide2',
        'PySide6',
        'wx',
        'tornado',
        'sphinx',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
a.binaries = [x for x in a.binaries if not x[0].startswith('api-ms-win')]
a.binaries = [x for x in a.binaries if not x[0].startswith('ucrtbase')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GPS_3D_Analyzer_BagpyFixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
