# 强度值保持的PCD保存配置文件

# ========================================
# 基础保存参数
# ========================================
intensity_preserving_pcd_saver:
  # 保存目录设置
  save_directory: "/home/<USER>/slam_share/aLidar/intensity_preserved"
  save_enabled: true                    # 是否启用保存功能
  
  # 保存选项
  save_raw_intensity: true              # 保存原始强度值
  save_loop_corrected: true             # 保存回环校正后的点云
  save_incremental: true                # 增量保存
  save_final_map: true                  # 保存最终地图
  
  # 强度值处理参数
  intensity_scale_factor: 1.0           # 强度值缩放因子
  preserve_original_intensity: true     # 保持原始强度值
  intensity_normalization: false        # 是否标准化强度值
  intensity_min_threshold: 0.0          # 强度值最小阈值
  intensity_max_threshold: 255.0        # 强度值最大阈值
  
  # 点云处理参数
  voxel_leaf_size: 0.05                 # 体素滤波大小(米) - 较小保持更多细节
  max_points_per_file: 2000000          # 每个文件最大点数
  enable_intensity_restoration: true     # 启用强度值恢复
  nearest_neighbor_threshold: 0.05      # 最近邻搜索阈值(米)
  
  # 文件格式设置
  save_format: "pcd_binary"             # 保存格式: pcd_binary, pcd_ascii, ply
  compression_enabled: false            # 是否启用压缩
  
  # 质量控制参数
  remove_nan_points: true               # 移除NaN点
  remove_infinite_points: true          # 移除无穷大点
  intensity_outlier_removal: false      # 强度值异常点移除
  
  # 统计和监控
  publish_intensity_statistics: true    # 发布强度值统计
  log_intensity_range: true             # 记录强度值范围
  intensity_histogram_bins: 256         # 强度值直方图分箱数

# ========================================
# 话题配置
# ========================================
topics:
  # 输入话题
  raw_pointcloud_topic: "/velodyne_points"           # 原始点云话题
  processed_pointcloud_topic: "/cloud_registered"    # 处理后点云话题
  loop_closure_topic: "/force_loop_closure"          # 回环检测话题
  pose_topic: "/aft_mapped_to_init"                  # 位姿话题
  
  # 输出话题
  enhanced_pointcloud_topic: "/enhanced_pointcloud_with_intensity"  # 增强点云话题
  intensity_stats_topic: "/intensity_statistics"                    # 强度统计话题
  quality_report_topic: "/pointcloud_quality_report"               # 质量报告话题

# ========================================
# 不同场景的预设配置
# ========================================

# 高质量保存预设 - 保持最大细节和强度精度
high_quality_preset:
  intensity_preserving_pcd_saver:
    voxel_leaf_size: 0.02               # 更小的体素大小
    intensity_scale_factor: 1.0         # 不缩放强度值
    max_points_per_file: 5000000        # 更大的文件大小
    save_format: "pcd_binary"           # 二进制格式保持精度
    compression_enabled: false          # 不压缩保持质量
    intensity_normalization: false      # 不标准化保持原始值

# 平衡质量预设 - 质量和文件大小的平衡
balanced_preset:
  intensity_preserving_pcd_saver:
    voxel_leaf_size: 0.05               # 中等体素大小
    intensity_scale_factor: 1.0         # 保持原始强度
    max_points_per_file: 2000000        # 中等文件大小
    save_format: "pcd_binary"           # 二进制格式
    compression_enabled: false          # 不压缩
    intensity_normalization: false      # 保持原始强度范围

# 紧凑保存预设 - 减小文件大小但保持强度值
compact_preset:
  intensity_preserving_pcd_saver:
    voxel_leaf_size: 0.1                # 较大的体素大小
    intensity_scale_factor: 1.0         # 保持强度值
    max_points_per_file: 1000000        # 较小的文件大小
    save_format: "pcd_binary"           # 二进制格式
    compression_enabled: true           # 启用压缩
    intensity_normalization: true       # 标准化强度值

# 实时处理预设 - 适合实时处理的配置
realtime_preset:
  intensity_preserving_pcd_saver:
    voxel_leaf_size: 0.15               # 更大的体素大小减少计算
    intensity_scale_factor: 1.0         # 保持强度值
    max_points_per_file: 500000         # 较小文件便于处理
    save_incremental: true              # 增量保存
    enable_intensity_restoration: false # 关闭强度恢复减少计算
    publish_intensity_statistics: false # 关闭统计减少计算

# ========================================
# 调试和开发配置
# ========================================
debug_settings:
  # 详细日志
  verbose_logging: true                 # 详细日志输出
  log_every_n_frames: 100              # 每N帧记录一次日志
  
  # 调试保存
  save_debug_info: true                 # 保存调试信息
  save_intensity_maps: true             # 保存强度值映射
  save_before_after_comparison: true    # 保存处理前后对比
  
  # 性能监控
  monitor_processing_time: true         # 监控处理时间
  monitor_memory_usage: true            # 监控内存使用
  
  # 验证设置
  verify_intensity_preservation: true   # 验证强度值保持
  intensity_preservation_tolerance: 0.01 # 强度值保持容差

# ========================================
# 文件组织配置
# ========================================
file_organization:
  # 目录结构
  create_subdirectories: true           # 创建子目录
  organize_by_date: true                # 按日期组织
  organize_by_session: true             # 按会话组织
  
  # 文件命名
  filename_prefix: "intensity_preserved" # 文件名前缀
  include_timestamp: true               # 包含时间戳
  include_point_count: true             # 包含点数信息
  include_intensity_range: true         # 包含强度范围信息
  
  # 元数据保存
  save_metadata: true                   # 保存元数据文件
  metadata_format: "yaml"               # 元数据格式: yaml, json, xml

# ========================================
# 质量保证配置
# ========================================
quality_assurance:
  # 强度值验证
  validate_intensity_range: true        # 验证强度值范围
  expected_intensity_min: 0.0           # 期望的最小强度值
  expected_intensity_max: 255.0         # 期望的最大强度值
  
  # 点云质量检查
  check_point_density: true             # 检查点密度
  min_points_per_scan: 1000             # 每次扫描最小点数
  max_points_per_scan: 200000           # 每次扫描最大点数
  
  # 几何一致性检查
  check_geometric_consistency: true     # 检查几何一致性
  max_point_distance: 100.0             # 最大点距离(米)
  
  # 报告生成
  generate_quality_report: true         # 生成质量报告
  quality_report_interval: 1000         # 质量报告间隔(帧数)
