#!/bin/bash

# 快速启动优化SLAM系统脚本

echo "=== 优化SLAM系统快速启动 ==="

# 检查环境
if [ ! -f "devel/setup.bash" ]; then
    echo "错误: 请先编译系统 (运行 ./compile_test.sh)"
    exit 1
fi

# 设置环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/optimized_output_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "选择启动模式:"
echo "1) 完整优化系统 (推荐)"
echo "2) 简化优化系统 (适用于缺少依赖的环境)"
echo "3) GPS质量差环境优化"
echo "4) 高精度强度保持"
echo "5) 实时处理优化"
echo "6) 自定义配置"
echo ""

read -p "请选择模式 (1-6): " choice

case $choice in
    1)
        echo "启动完整优化系统..."
        roslaunch state_estimation optimized_slam_system.launch \
            intensity_preset:=high_quality_preset \
            gps_loop_preset:=poor_gps_preset \
            save_directory:="$OUTPUT_DIR" \
            enable_performance_dashboard:=true
        ;;
    2)
        echo "启动简化优化系统（适用于缺少依赖的环境）..."
        roslaunch state_estimation optimized_slam_simple.launch \
            intensity_preset:=high_quality_preset \
            gps_loop_preset:=poor_gps_preset \
            save_directory:="$OUTPUT_DIR" \
            enable_simple_analysis:=true \
            enable_performance_dashboard:=false
        ;;
    3)
        echo "启动GPS质量差环境优化..."
        roslaunch state_estimation optimized_slam_system.launch \
            gps_loop_preset:=poor_gps_preset \
            intensity_preset:=high_quality_preset \
            enable_adaptive_optimization:=true \
            save_directory:="$OUTPUT_DIR"
        ;;
    4)
        echo "启动高精度强度保持..."
        roslaunch state_estimation optimized_slam_system.launch \
            intensity_preset:=ultra_high_quality_preset \
            enable_advanced_analysis:=true \
            enable_performance_dashboard:=true \
            save_directory:="$OUTPUT_DIR"
        ;;
    5)
        echo "启动实时处理优化..."
        roslaunch state_estimation optimized_slam_system.launch \
            intensity_preset:=realtime_preset \
            gps_loop_preset:=fast_preset \
            enable_adaptive_optimization:=true \
            save_directory:="$OUTPUT_DIR"
        ;;
    6)
        echo "自定义配置启动..."
        echo "可用的强度预设: high_quality_preset, balanced_preset, compact_preset"
        echo "可用的GPS预设: poor_gps_preset, good_gps_preset, high_performance_preset"
        
        read -p "强度预设 (默认: high_quality_preset): " intensity_preset
        intensity_preset=${intensity_preset:-high_quality_preset}
        
        read -p "GPS预设 (默认: poor_gps_preset): " gps_preset
        gps_preset=${gps_preset:-poor_gps_preset}
        
        read -p "启用性能仪表板? (y/n, 默认: y): " dashboard
        dashboard=${dashboard:-y}
        
        if [ "$dashboard" = "y" ]; then
            dashboard_flag="true"
        else
            dashboard_flag="false"
        fi
        
        roslaunch state_estimation optimized_slam_system.launch \
            intensity_preset:="$intensity_preset" \
            gps_loop_preset:="$gps_preset" \
            enable_performance_dashboard:="$dashboard_flag" \
            save_directory:="$OUTPUT_DIR"
        ;;
    *)
        echo "无效选择，使用默认配置..."
        roslaunch state_estimation optimized_slam_system.launch \
            save_directory:="$OUTPUT_DIR"
        ;;
esac

echo ""
echo "=== 系统已启动 ==="
echo "输出目录: $OUTPUT_DIR"
echo ""
echo "在另一个终端中播放bag文件:"
echo "rosbag play your_data.bag"
echo ""
echo "监控系统状态:"
echo "rostopic echo /performance_metrics"
echo "rostopic echo /intensity_analysis_report"
echo "rostopic echo /optimization_status"
