#!/bin/bash

# GPS软约束SLAM系统启动脚本 - 深度优化版本

echo "=========================================="
echo "🎯 GPS软约束SLAM系统启动"
echo "=========================================="
echo ""
echo "系统特点："
echo "✅ 不使用GPS平面约束，避免点云拼接问题"
echo "✅ 基于GPS位置的智能软约束回环检测"
echo "✅ 综合考虑GPS质量变化的自适应策略"
echo "✅ 渐进式轨迹校正，保持SLAM连续性"
echo "✅ 实时GPS质量分析和预测"
echo ""

# 设置ROS环境
source /opt/ros/noetic/setup.bash
source devel/setup.bash

# 检查ROS是否运行
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动 roscore..."
    roscore &
    sleep 3
fi

# 创建输出目录
OUTPUT_DIR="/home/<USER>/slam_share/aLidar/gps_soft_constraint_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "输出目录: $OUTPUT_DIR"

echo ""
echo "配置选项："
echo "1) 高质量GPS环境 (RTK固定解)"
echo "2) 中等质量GPS环境 (RTK浮点解)"
echo "3) 低质量GPS环境 (单点定位)"
echo "4) 混合质量GPS环境 (质量变化)"
echo "5) 自定义配置"
echo ""

read -p "请选择GPS环境类型 (1-5): " gps_env_choice

case $gps_env_choice in
    1)
        echo "配置高质量GPS环境..."
        GPS_HIGH_QUALITY_THRESHOLD=5
        GPS_MEDIUM_QUALITY_THRESHOLD=4
        HIGH_QUALITY_PROXIMITY=10.0
        MEDIUM_QUALITY_PROXIMITY=15.0
        LOW_QUALITY_PROXIMITY=25.0
        SLAM_STRICT_THRESHOLD=5.0
        SLAM_NORMAL_THRESHOLD=8.0
        SLAM_LOOSE_THRESHOLD=15.0
        MIN_TIME_GAP=20.0
        HEADING_THRESHOLD=30.0
        CONFIDENCE_THRESHOLD=0.8
        CORRECTION_WEIGHT=0.4
        echo "✅ 高质量GPS配置已设置"
        ;;
    2)
        echo "配置中等质量GPS环境..."
        GPS_HIGH_QUALITY_THRESHOLD=4
        GPS_MEDIUM_QUALITY_THRESHOLD=2
        HIGH_QUALITY_PROXIMITY=12.0
        MEDIUM_QUALITY_PROXIMITY=20.0
        LOW_QUALITY_PROXIMITY=30.0
        SLAM_STRICT_THRESHOLD=6.0
        SLAM_NORMAL_THRESHOLD=12.0
        SLAM_LOOSE_THRESHOLD=20.0
        MIN_TIME_GAP=25.0
        HEADING_THRESHOLD=40.0
        CONFIDENCE_THRESHOLD=0.6
        CORRECTION_WEIGHT=0.3
        echo "✅ 中等质量GPS配置已设置"
        ;;
    3)
        echo "配置低质量GPS环境..."
        GPS_HIGH_QUALITY_THRESHOLD=2
        GPS_MEDIUM_QUALITY_THRESHOLD=0
        HIGH_QUALITY_PROXIMITY=15.0
        MEDIUM_QUALITY_PROXIMITY=25.0
        LOW_QUALITY_PROXIMITY=40.0
        SLAM_STRICT_THRESHOLD=8.0
        SLAM_NORMAL_THRESHOLD=15.0
        SLAM_LOOSE_THRESHOLD=25.0
        MIN_TIME_GAP=30.0
        HEADING_THRESHOLD=50.0
        CONFIDENCE_THRESHOLD=0.4
        CORRECTION_WEIGHT=0.2
        echo "✅ 低质量GPS配置已设置"
        ;;
    4)
        echo "配置混合质量GPS环境..."
        GPS_HIGH_QUALITY_THRESHOLD=4
        GPS_MEDIUM_QUALITY_THRESHOLD=1
        HIGH_QUALITY_PROXIMITY=12.0
        MEDIUM_QUALITY_PROXIMITY=20.0
        LOW_QUALITY_PROXIMITY=30.0
        SLAM_STRICT_THRESHOLD=6.0
        SLAM_NORMAL_THRESHOLD=12.0
        SLAM_LOOSE_THRESHOLD=20.0
        MIN_TIME_GAP=25.0
        HEADING_THRESHOLD=40.0
        CONFIDENCE_THRESHOLD=0.6
        CORRECTION_WEIGHT=0.3
        echo "✅ 混合质量GPS配置已设置"
        ;;
    5)
        echo "自定义配置："
        read -p "GPS高质量阈值 (3-6): " GPS_HIGH_QUALITY_THRESHOLD
        read -p "GPS中等质量阈值 (0-3): " GPS_MEDIUM_QUALITY_THRESHOLD
        read -p "高质量GPS接近阈值 (8-15m): " HIGH_QUALITY_PROXIMITY
        read -p "中等质量GPS接近阈值 (15-25m): " MEDIUM_QUALITY_PROXIMITY
        read -p "低质量GPS接近阈值 (25-40m): " LOW_QUALITY_PROXIMITY
        read -p "置信度阈值 (0.4-0.8): " CONFIDENCE_THRESHOLD
        read -p "校正权重 (0.1-0.5): " CORRECTION_WEIGHT
        
        SLAM_STRICT_THRESHOLD=6.0
        SLAM_NORMAL_THRESHOLD=12.0
        SLAM_LOOSE_THRESHOLD=20.0
        MIN_TIME_GAP=25.0
        HEADING_THRESHOLD=40.0
        echo "✅ 自定义配置已设置"
        ;;
    *)
        echo "使用默认混合质量配置..."
        GPS_HIGH_QUALITY_THRESHOLD=4
        GPS_MEDIUM_QUALITY_THRESHOLD=1
        HIGH_QUALITY_PROXIMITY=12.0
        MEDIUM_QUALITY_PROXIMITY=20.0
        LOW_QUALITY_PROXIMITY=30.0
        SLAM_STRICT_THRESHOLD=6.0
        SLAM_NORMAL_THRESHOLD=12.0
        SLAM_LOOSE_THRESHOLD=20.0
        MIN_TIME_GAP=25.0
        HEADING_THRESHOLD=40.0
        CONFIDENCE_THRESHOLD=0.6
        CORRECTION_WEIGHT=0.3
        ;;
esac

echo ""
echo "是否启用RViz可视化？"
read -p "启用RViz (y/n): " enable_rviz
ENABLE_RVIZ=$([ "$enable_rviz" = "y" ] && echo "true" || echo "false")

echo ""
echo "是否启用强度保存？"
read -p "启用强度保存 (y/n): " enable_intensity
ENABLE_INTENSITY=$([ "$enable_intensity" = "y" ] && echo "true" || echo "false")

echo ""
echo "=========================================="
echo "🚀 启动GPS软约束SLAM系统"
echo "=========================================="

# 使用launch文件启动系统
roslaunch state_estimation gps_soft_constraint_slam.launch \
    save_directory:="$OUTPUT_DIR" \
    gps_high_quality_threshold:=$GPS_HIGH_QUALITY_THRESHOLD \
    gps_medium_quality_threshold:=$GPS_MEDIUM_QUALITY_THRESHOLD \
    high_quality_proximity:=$HIGH_QUALITY_PROXIMITY \
    medium_quality_proximity:=$MEDIUM_QUALITY_PROXIMITY \
    low_quality_proximity:=$LOW_QUALITY_PROXIMITY \
    slam_strict_threshold:=$SLAM_STRICT_THRESHOLD \
    slam_normal_threshold:=$SLAM_NORMAL_THRESHOLD \
    slam_loose_threshold:=$SLAM_LOOSE_THRESHOLD \
    min_time_gap:=$MIN_TIME_GAP \
    heading_threshold:=$HEADING_THRESHOLD \
    enable_rviz:=$ENABLE_RVIZ \
    enable_intensity_save:=$ENABLE_INTENSITY &

# 等待系统启动
sleep 8

# 设置软约束处理器参数
echo "设置软约束处理器参数..."
rosparam set /soft_constraint_loop_processor/confidence_threshold $CONFIDENCE_THRESHOLD
rosparam set /soft_constraint_loop_processor/correction_weight $CORRECTION_WEIGHT
rosparam set /soft_constraint_loop_processor/enable_gradual_correction true
rosparam set /soft_constraint_loop_processor/correction_steps 10

# 设置质量分析器参数
echo "设置质量分析器参数..."
rosparam set /gps_quality_analyzer/analysis_window 100
rosparam set /gps_quality_analyzer/report_interval 8.0
rosparam set /gps_quality_analyzer/enable_quality_prediction true

echo ""
echo "=========================================="
echo "🎯 GPS软约束SLAM系统已启动"
echo "=========================================="
echo ""
echo "系统配置："
echo "  输出目录: $OUTPUT_DIR"
echo "  GPS高质量阈值: $GPS_HIGH_QUALITY_THRESHOLD"
echo "  GPS中等质量阈值: $GPS_MEDIUM_QUALITY_THRESHOLD"
echo "  高质量GPS接近阈值: ${HIGH_QUALITY_PROXIMITY}m"
echo "  中等质量GPS接近阈值: ${MEDIUM_QUALITY_PROXIMITY}m"
echo "  低质量GPS接近阈值: ${LOW_QUALITY_PROXIMITY}m"
echo "  置信度阈值: $CONFIDENCE_THRESHOLD"
echo "  校正权重: $CORRECTION_WEIGHT"
echo "  RViz可视化: $ENABLE_RVIZ"
echo "  强度保存: $ENABLE_INTENSITY"
echo ""
echo "系统特性："
echo "🎯 GPS软约束回环检测 - 不干扰点云拼接"
echo "📊 实时GPS质量分析和自适应调整"
echo "🔄 渐进式轨迹校正 - 保持SLAM连续性"
echo "💎 完整强度值保持"
echo "📈 质量预测和趋势分析"
echo ""
echo "监控命令："
echo "  GPS软约束触发: rostopic echo /gps_soft_loop_trigger"
echo "  回环置信度:    rostopic echo /loop_confidence_score"
echo "  GPS质量报告:   rostopic echo /gps_quality_report"
echo "  GPS质量分数:   rostopic echo /gps_quality_score"
echo "  校正状态:      rostopic echo /correction_status"
echo "  校正后位姿:    rostopic echo /corrected_pose"
echo ""
echo "参数调节命令："
echo "  调节置信度阈值: rosparam set /soft_constraint_loop_processor/confidence_threshold 0.7"
echo "  调节校正权重:   rosparam set /soft_constraint_loop_processor/correction_weight 0.4"
echo "  调节接近阈值:   rosparam set /gps_soft_loop_detector/high_quality_proximity 15.0"
echo ""
echo "在另一个终端播放bag文件:"
echo "  rosbag play your_data.bag"
echo ""
echo "预期效果："
echo "✅ 根据GPS质量自动调整回环检测策略"
echo "✅ 高质量GPS时使用严格阈值，低质量时放宽阈值"
echo "✅ 软约束校正不会破坏点云拼接连续性"
echo "✅ 实时质量分析提供最优参数建议"
echo "✅ 首尾偏差显著减少，同时保持轨迹平滑"
echo ""
echo "按 Ctrl+C 停止系统"

# 等待用户中断
wait
