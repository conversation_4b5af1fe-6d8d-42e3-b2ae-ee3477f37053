/**
 * 增强版SLAM回环检测集成模块
 * 支持多种类型的回环检测：起点-终点、中间区域、重复访问
 * 针对不同回环类型采用不同的搜索和匹配策略
 */

#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64.h>
#include <std_msgs/Int32.h>
#include <std_msgs/String.h>
#include <sensor_msgs/PointCloud2.h>
#include <geometry_msgs/PoseStamped.h>
#include <nav_msgs/Path.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/registration/icp.h>
#include <pcl/registration/ndt.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl_conversions/pcl_conversions.h>

#include <vector>
#include <deque>
#include <mutex>
#include <thread>
#include <chrono>
#include <map>
#include <algorithm>

struct LoopCandidate {
    int keyframe_id;
    double distance;
    double confidence;
    std::string loop_type;
    Eigen::Matrix4f initial_guess;
};

class EnhancedSLAMLoopClosureIntegration {
private:
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 订阅器
    ros::Subscriber force_loop_closure_sub_;
    ros::Subscriber intermediate_loop_sub_;
    ros::Subscriber pointcloud_sub_;
    ros::Subscriber pose_sub_;
    ros::Subscriber intelligent_force_sub_;      // 智能强制回环订阅器
    ros::Subscriber precise_match_sub_;          // 精细匹配订阅器
    
    // 发布器
    ros::Publisher loop_closure_result_pub_;
    ros::Publisher corrected_pose_pub_;
    ros::Publisher loop_closure_info_pub_;
    ros::Publisher loop_type_pub_;
    ros::Publisher matching_score_pub_;
    
    // 参数配置
    struct LoopClosureParams {
        double force_search_radius;
        double loop_closure_score_threshold;
        int max_search_candidates;
        double voxel_leaf_size;
        int min_keyframes_for_loop;
        double intermediate_search_radius;
        double revisit_search_radius;
        double start_end_score_threshold;
        double intermediate_score_threshold;
        double revisit_score_threshold;
        int sliding_window_size;
    } params_;
    
    // 数据存储
    std::deque<sensor_msgs::PointCloud2> keyframe_clouds_;
    std::deque<geometry_msgs::PoseStamped> keyframe_poses_;
    std::deque<ros::Time> keyframe_timestamps_;
    
    // 状态管理
    std::mutex data_mutex_;
    bool loop_closure_in_progress_;
    std::string current_loop_type_;
    int processed_loops_count_;
    
public:
    EnhancedSLAMLoopClosureIntegration() : 
        private_nh_("~"),
        loop_closure_in_progress_(false),
        current_loop_type_("none"),
        processed_loops_count_(0) {
        
        // 读取参数
        loadParameters();
        
        // 初始化订阅器
        force_loop_closure_sub_ = nh_.subscribe("/force_loop_closure", 1, 
            &EnhancedSLAMLoopClosureIntegration::forceLoopClosureCallback, this);
        intermediate_loop_sub_ = nh_.subscribe("/intermediate_loop_detected", 1,
            &EnhancedSLAMLoopClosureIntegration::intermediateLoopCallback, this);
        pointcloud_sub_ = nh_.subscribe("/cloud_registered", 1,
            &EnhancedSLAMLoopClosureIntegration::pointCloudCallback, this);
        pose_sub_ = nh_.subscribe("/aft_mapped_to_init", 1,
            &EnhancedSLAMLoopClosureIntegration::poseCallback, this);

        // 智能检测器订阅器
        intelligent_force_sub_ = nh_.subscribe("/intelligent_force_loop_closure", 1,
            &EnhancedSLAMLoopClosureIntegration::intelligentForceCallback, this);
        precise_match_sub_ = nh_.subscribe("/force_precise_start_end_match", 1,
            &EnhancedSLAMLoopClosureIntegration::preciseMatchCallback, this);

        // 初始化发布器
        loop_closure_result_pub_ = nh_.advertise<std_msgs::Bool>("/loop_closure_result", 1);
        corrected_pose_pub_ = nh_.advertise<geometry_msgs::PoseStamped>("/corrected_pose", 1);
        loop_closure_info_pub_ = nh_.advertise<std_msgs::Float64>("/loop_closure_score", 1);
        loop_type_pub_ = nh_.advertise<std_msgs::String>("/detected_loop_type", 1);
        matching_score_pub_ = nh_.advertise<std_msgs::Float64>("/matching_score", 1);
        
        ROS_INFO("Enhanced SLAM Loop Closure Integration Module Started");
        printParameters();
    }
    
    void loadParameters() {
        private_nh_.param("force_search_radius", params_.force_search_radius, 10.0);
        private_nh_.param("loop_closure_score_threshold", params_.loop_closure_score_threshold, 0.3);
        private_nh_.param("max_search_candidates", params_.max_search_candidates, 10);
        private_nh_.param("voxel_leaf_size", params_.voxel_leaf_size, 0.1);
        private_nh_.param("min_keyframes_for_loop", params_.min_keyframes_for_loop, 50);
        private_nh_.param("intermediate_search_radius", params_.intermediate_search_radius, 15.0);
        private_nh_.param("revisit_search_radius", params_.revisit_search_radius, 12.0);
        private_nh_.param("start_end_score_threshold", params_.start_end_score_threshold, 0.25);
        private_nh_.param("intermediate_score_threshold", params_.intermediate_score_threshold, 0.35);
        private_nh_.param("revisit_score_threshold", params_.revisit_score_threshold, 0.30);
        private_nh_.param("sliding_window_size", params_.sliding_window_size, 100);
    }
    
    void printParameters() {
        ROS_INFO("Loop Closure Parameters Configuration:");
        ROS_INFO("   Basic search radius: %.1fm", params_.force_search_radius);
        ROS_INFO("   Intermediate loop search radius: %.1fm", params_.intermediate_search_radius);
        ROS_INFO("   Revisit loop search radius: %.1fm", params_.revisit_search_radius);
        ROS_INFO("   Start-end score threshold: %.3f", params_.start_end_score_threshold);
        ROS_INFO("   Intermediate loop score threshold: %.3f", params_.intermediate_score_threshold);
        ROS_INFO("   Revisit loop score threshold: %.3f", params_.revisit_score_threshold);
    }
    
    void forceLoopClosureCallback(const std_msgs::Bool::ConstPtr& msg) {
        if (msg->data && !loop_closure_in_progress_) {
            current_loop_type_ = "start_end";
            ROS_WARN("Received start-end forced loop closure request!");
            performEnhancedLoopClosure();
        }
    }
    
    void intermediateLoopCallback(const std_msgs::Bool::ConstPtr& msg) {
        if (msg->data && !loop_closure_in_progress_) {
            current_loop_type_ = "intermediate";
            ROS_WARN("Received intermediate loop closure request!");
            performEnhancedLoopClosure();
        }
    }
    
    void pointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        keyframe_clouds_.push_back(*msg);
        keyframe_timestamps_.push_back(msg->header.stamp);
        
        if (keyframe_clouds_.size() > 1500) {
            keyframe_clouds_.pop_front();
            keyframe_timestamps_.pop_front();
        }
    }
    
    void poseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        keyframe_poses_.push_back(*msg);
        
        if (keyframe_poses_.size() > 1500) {
            keyframe_poses_.pop_front();
        }
    }
    
    void performEnhancedLoopClosure() {
        loop_closure_in_progress_ = true;
        
        std::thread loop_thread([this]() {
            this->executeLoopClosureDetection();
        });
        loop_thread.detach();
    }
    
    void executeLoopClosureDetection() {
        ROS_INFO("Starting enhanced loop closure detection search (type: %s)...", current_loop_type_.c_str());

        std::lock_guard<std::mutex> lock(data_mutex_);

        if (keyframe_clouds_.size() < params_.min_keyframes_for_loop) {
            ROS_WARN("Insufficient keyframes (%lu < %d)", keyframe_clouds_.size(), params_.min_keyframes_for_loop);
            loop_closure_in_progress_ = false;
            return;
        }
        
        // 根据回环类型选择不同的检测策略
        std::vector<LoopCandidate> candidates;
        
        if (current_loop_type_ == "start_end") {
            candidates = findStartEndLoopCandidates();
        } else if (current_loop_type_ == "intermediate") {
            candidates = findIntermediateLoopCandidates();
        } else {
            candidates = findRevisitLoopCandidates();
        }
        
        ROS_INFO("Found %lu %s loop candidates", candidates.size(), current_loop_type_.c_str());
        
        // 执行点云匹配
        LoopCandidate best_candidate;
        bool found_valid_loop = false;
        double best_score = std::numeric_limits<double>::max();
        
        for (const auto& candidate : candidates) {
            double score = performAdvancedPointCloudMatching(candidate);
            
            double threshold = getScoreThresholdForType(current_loop_type_);
            
            if (score < best_score && score < threshold) {
                best_score = score;
                best_candidate = candidate;
                found_valid_loop = true;
            }
        }
        
        // 发布结果
        publishLoopClosureResult(found_valid_loop, best_candidate, best_score);
        
        processed_loops_count_++;
        loop_closure_in_progress_ = false;
        current_loop_type_ = "none";
        
        ROS_INFO("Enhanced loop closure detection completed (total processed: %d)", processed_loops_count_);
    }
    
    std::vector<LoopCandidate> findStartEndLoopCandidates() {
        std::vector<LoopCandidate> candidates;
        
        int search_range = std::min(50, (int)(keyframe_poses_.size() * 0.3));
        
        for (int i = 0; i < search_range; i++) {
            double distance = calculatePoseDistance(
                keyframe_poses_.back(), keyframe_poses_[i]);
            
            if (distance <= params_.force_search_radius) {
                LoopCandidate candidate;
                candidate.keyframe_id = i;
                candidate.distance = distance;
                candidate.confidence = 1.0 / (1.0 + distance);
                candidate.loop_type = "start_end";
                candidates.push_back(candidate);
            }
        }
        
        return candidates;
    }
    
    std::vector<LoopCandidate> findIntermediateLoopCandidates() {
        std::vector<LoopCandidate> candidates;
        
        int current_idx = keyframe_poses_.size() - 1;
        int window_start = std::max(0, current_idx - params_.sliding_window_size);
        
        for (int i = window_start; i < current_idx - 30; i++) {
            double distance = calculatePoseDistance(
                keyframe_poses_[current_idx], keyframe_poses_[i]);
            
            if (distance <= params_.intermediate_search_radius) {
                LoopCandidate candidate;
                candidate.keyframe_id = i;
                candidate.distance = distance;
                candidate.confidence = calculateTemporalConsistency(i, current_idx);
                candidate.loop_type = "intermediate";
                candidates.push_back(candidate);
            }
        }
        
        return candidates;
    }
    
    std::vector<LoopCandidate> findRevisitLoopCandidates() {
        std::vector<LoopCandidate> candidates;
        
        int current_idx = keyframe_poses_.size() - 1;
        
        for (int i = 0; i < current_idx - 20; i++) {
            double distance = calculatePoseDistance(
                keyframe_poses_[current_idx], keyframe_poses_[i]);
            
            if (distance <= params_.revisit_search_radius) {
                double time_gap = (keyframe_timestamps_[current_idx] - keyframe_timestamps_[i]).toSec();
                
                if (time_gap > 10.0) {
                    LoopCandidate candidate;
                    candidate.keyframe_id = i;
                    candidate.distance = distance;
                    candidate.confidence = std::min(1.0, time_gap / 60.0);
                    candidate.loop_type = "revisit";
                    candidates.push_back(candidate);
                }
            }
        }
        
        return candidates;
    }
    
    double calculateTemporalConsistency(int idx1, int idx2) {
        double time_diff = std::abs((keyframe_timestamps_[idx2] - keyframe_timestamps_[idx1]).toSec());
        double index_diff = std::abs(idx2 - idx1);
        
        double temporal_score = 1.0 / (1.0 + time_diff / 30.0);
        double spatial_score = std::min(1.0, index_diff / 100.0);
        
        return temporal_score * 0.8 + spatial_score * 0.2;
    }
    
    double performAdvancedPointCloudMatching(const LoopCandidate& candidate) {
        pcl::PointCloud<pcl::PointXYZ>::Ptr source(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::PointCloud<pcl::PointXYZ>::Ptr target(new pcl::PointCloud<pcl::PointXYZ>);
        
        pcl::fromROSMsg(keyframe_clouds_.back(), *source);
        pcl::fromROSMsg(keyframe_clouds_[candidate.keyframe_id], *target);
        
        if (candidate.loop_type == "start_end") {
            return performNDTMatching(source, target);
        } else if (candidate.loop_type == "intermediate") {
            return performICPMatching(source, target);
        } else {
            return performHybridMatching(source, target);
        }
    }
    
    double performNDTMatching(pcl::PointCloud<pcl::PointXYZ>::Ptr source,
                             pcl::PointCloud<pcl::PointXYZ>::Ptr target) {
        pcl::NormalDistributionsTransform<pcl::PointXYZ, pcl::PointXYZ> ndt;
        ndt.setTransformationEpsilon(0.01);
        ndt.setStepSize(0.1);
        ndt.setResolution(1.0);
        ndt.setMaximumIterations(35);
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr source_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::PointCloud<pcl::PointXYZ>::Ptr target_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        
        applyVoxelFilter(source, source_filtered);
        applyVoxelFilter(target, target_filtered);
        
        ndt.setInputSource(source_filtered);
        ndt.setInputTarget(target_filtered);
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr output_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        ndt.align(*output_cloud);
        
        return ndt.getFitnessScore();
    }
    
    double performICPMatching(pcl::PointCloud<pcl::PointXYZ>::Ptr source,
                             pcl::PointCloud<pcl::PointXYZ>::Ptr target) {
        pcl::IterativeClosestPoint<pcl::PointXYZ, pcl::PointXYZ> icp;
        icp.setMaximumIterations(50);
        icp.setTransformationEpsilon(1e-6);
        icp.setEuclideanFitnessEpsilon(1e-6);
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr source_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::PointCloud<pcl::PointXYZ>::Ptr target_filtered(new pcl::PointCloud<pcl::PointXYZ>);
        
        applyVoxelFilter(source, source_filtered);
        applyVoxelFilter(target, target_filtered);
        
        icp.setInputSource(source_filtered);
        icp.setInputTarget(target_filtered);
        
        pcl::PointCloud<pcl::PointXYZ>::Ptr output_cloud(new pcl::PointCloud<pcl::PointXYZ>);
        icp.align(*output_cloud);
        
        return icp.getFitnessScore();
    }
    
    double performHybridMatching(pcl::PointCloud<pcl::PointXYZ>::Ptr source,
                                pcl::PointCloud<pcl::PointXYZ>::Ptr target) {
        double ndt_score = performNDTMatching(source, target);
        double icp_score = performICPMatching(source, target);
        
        return ndt_score * 0.6 + icp_score * 0.4;
    }
    
    void applyVoxelFilter(pcl::PointCloud<pcl::PointXYZ>::Ptr input,
                         pcl::PointCloud<pcl::PointXYZ>::Ptr output) {
        pcl::VoxelGrid<pcl::PointXYZ> voxel_filter;
        voxel_filter.setLeafSize(params_.voxel_leaf_size, params_.voxel_leaf_size, params_.voxel_leaf_size);
        voxel_filter.setInputCloud(input);
        voxel_filter.filter(*output);
    }
    
    double getScoreThresholdForType(const std::string& loop_type) {
        if (loop_type == "start_end") {
            return params_.start_end_score_threshold;
        } else if (loop_type == "intermediate") {
            return params_.intermediate_score_threshold;
        } else {
            return params_.revisit_score_threshold;
        }
    }
    
    void publishLoopClosureResult(bool success, const LoopCandidate& candidate, double score) {
        std_msgs::Bool result_msg;
        result_msg.data = success;
        loop_closure_result_pub_.publish(result_msg);
        
        std_msgs::String type_msg;
        type_msg.data = current_loop_type_;
        loop_type_pub_.publish(type_msg);
        
        std_msgs::Float64 score_msg;
        score_msg.data = score;
        matching_score_pub_.publish(score_msg);
        
        if (success) {
            ROS_INFO("%s loop closure detection successful!", current_loop_type_.c_str());
            ROS_INFO("Best matching score: %.4f", score);
            ROS_INFO("Matched keyframe: %d", candidate.keyframe_id);
            ROS_INFO("Spatial distance: %.2fm", candidate.distance);
            ROS_INFO("Confidence: %.3f", candidate.confidence);
        } else {
            ROS_WARN("%s loop closure detection failed", current_loop_type_.c_str());
        }
    }
    
    double calculatePoseDistance(const geometry_msgs::PoseStamped& pose1, 
                                const geometry_msgs::PoseStamped& pose2) {
        double dx = pose1.pose.position.x - pose2.pose.position.x;
        double dy = pose1.pose.position.y - pose2.pose.position.y;
        double dz = pose1.pose.position.z - pose2.pose.position.z;
        
        return sqrt(dx*dx + dy*dy + dz*dz);
    }

    // 智能强制回环回调函数
    void intelligentForceCallback(const std_msgs::String::ConstPtr& msg) {
        try {
            ROS_WARN("🎯 Received intelligent force loop closure request!");
            ROS_INFO("Intelligent detection data: %s", msg->data.c_str());

            // 设置为智能首尾回环类型
            current_loop_type_ = "intelligent_start_end";

            // 触发强制回环检测
            std_msgs::Bool force_msg;
            force_msg.data = true;
            forceLoopClosureCallback(boost::make_shared<std_msgs::Bool>(force_msg));

        } catch (const std::exception& e) {
            ROS_ERROR("Error in intelligent force callback: %s", e.what());
        }
    }

    // 精细匹配回调函数
    void preciseMatchCallback(const std_msgs::Bool::ConstPtr& msg) {
        if (msg->data) {
            ROS_INFO("🔥 Received precise matching request - enabling enhanced matching");
            // 临时降低匹配阈值以提高精度
            double original_threshold = params_.start_end_score_threshold;
            params_.start_end_score_threshold = std::min(original_threshold * 1.5, 0.9);

            // 增大搜索范围
            double original_radius = params_.force_search_radius;
            params_.force_search_radius = std::min(original_radius * 1.5, 80.0);

            ROS_INFO("Enhanced matching: threshold=%.3f, radius=%.1f",
                     params_.start_end_score_threshold, params_.force_search_radius);
        }
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "enhanced_slam_loop_closure_integration");
    
    try {
        EnhancedSLAMLoopClosureIntegration integration;
        
        ROS_INFO("Enhanced SLAM Loop Closure Integration Module running...");
        ROS_INFO("Supported loop types:");
        ROS_INFO("   Start-end loops (NDT registration)");
        ROS_INFO("   Intermediate loops (ICP registration)");
        ROS_INFO("   Revisit loops (Hybrid registration)");
        
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Enhanced SLAM Loop Closure Integration Module exception: %s", e.what());
        return -1;
    }

    ROS_INFO("Enhanced SLAM Loop Closure Integration Module stopped");
    return 0;
}
