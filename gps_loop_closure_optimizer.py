#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS强制回环检测优化器
当GPS位置接近起点时，强制SLAM搜索回环匹配点云
适用于首尾区域有偏移但没有触发自动回环的情况
"""

import rospy
import numpy as np
import math
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseStamped, Point
from std_msgs.msg import Bool, Float64
from nav_msgs.msg import Path
import tf2_ros
import tf2_geometry_msgs
from threading import Lock
import time

class GPSLoopClosureOptimizer:
    def __init__(self):
        rospy.init_node('gps_loop_closure_optimizer', anonymous=True)
        
        # 参数配置
        self.loop_closure_distance_threshold = rospy.get_param('~loop_closure_distance_threshold', 5.0)  # 米
        self.min_trajectory_length = rospy.get_param('~min_trajectory_length', 50.0)  # 最小轨迹长度
        self.gps_quality_threshold = rospy.get_param('~gps_quality_threshold', -1)  # GPS质量阈值，-1表示接受所有质量
        self.force_loop_closure_timeout = rospy.get_param('~force_loop_closure_timeout', 10.0)  # 强制回环超时时间
        self.check_interval = rospy.get_param('~check_interval', 1.0)  # 检查间隔
        
        # GPS话题
        self.gps_topic = rospy.get_param('~gps_topic', '/rtk/gnss')
        
        # 状态变量
        self.start_gps_position = None
        self.current_gps_position = None
        self.trajectory_length = 0.0
        self.last_position = None
        self.loop_closure_triggered = False
        self.last_force_time = 0
        
        # 线程锁
        self.data_lock = Lock()
        
        # 发布器
        self.force_loop_closure_pub = rospy.Publisher('/force_loop_closure', Bool, queue_size=1)
        self.loop_distance_pub = rospy.Publisher('/loop_closure_distance', Float64, queue_size=1)
        self.trajectory_length_pub = rospy.Publisher('/trajectory_length', Float64, queue_size=1)
        self.loop_status_pub = rospy.Publisher('/loop_closure_status', Bool, queue_size=1)
        
        # 订阅器
        self.gps_sub = rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 定时器
        self.check_timer = rospy.Timer(rospy.Duration(self.check_interval), self.check_loop_closure)
        
        rospy.loginfo("🚀 GPS强制回环检测优化器启动")
        rospy.loginfo(f"📏 回环距离阈值: {self.loop_closure_distance_threshold:.1f}m")
        rospy.loginfo(f"📐 最小轨迹长度: {self.min_trajectory_length:.1f}m")
        rospy.loginfo(f"📡 GPS话题: {self.gps_topic}")
        
    def gps_callback(self, msg):
        """GPS回调函数"""
        with self.data_lock:
            # 检查GPS质量
            if hasattr(msg, 'status') and msg.status.status < self.gps_quality_threshold:
                return
            
            current_pos = np.array([msg.latitude, msg.longitude, msg.altitude])
            
            # 记录起点位置
            if self.start_gps_position is None:
                self.start_gps_position = current_pos
                rospy.loginfo(f"📍 记录GPS起点: lat={msg.latitude:.8f}, lon={msg.longitude:.8f}, alt={msg.altitude:.3f}")
                rospy.loginfo(f"📊 GPS状态: {msg.status.status}, 质量: {self.get_gps_quality_name(msg.status.status)}")
            
            # 更新当前位置
            self.current_gps_position = current_pos
            
            # 计算轨迹长度
            if self.last_position is not None:
                distance = self.calculate_gps_distance(self.last_position, current_pos)
                if distance < 100:  # 过滤异常跳跃
                    self.trajectory_length += distance
            
            self.last_position = current_pos
            
            # 发布轨迹长度
            self.trajectory_length_pub.publish(Float64(data=self.trajectory_length))
    
    def calculate_gps_distance(self, pos1, pos2):
        """计算两个GPS点之间的距离（米）"""
        lat1, lon1, alt1 = pos1
        lat2, lon2, alt2 = pos2
        
        # 使用Haversine公式计算水平距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        horizontal_distance = R * c
        vertical_distance = abs(alt2 - alt1)
        
        # 3D距离
        total_distance = math.sqrt(horizontal_distance**2 + vertical_distance**2)
        
        return total_distance
    
    def check_loop_closure(self, event):
        """检查是否需要强制回环"""
        with self.data_lock:
            if (self.start_gps_position is None or 
                self.current_gps_position is None or
                self.trajectory_length < self.min_trajectory_length):
                return
            
            # 计算当前位置到起点的距离
            loop_distance = self.calculate_gps_distance(
                self.start_gps_position, 
                self.current_gps_position
            )
            
            # 发布回环距离
            self.loop_distance_pub.publish(Float64(data=loop_distance))
            
            # 检查是否需要强制回环
            current_time = time.time()
            should_force_loop = (
                loop_distance <= self.loop_closure_distance_threshold and
                not self.loop_closure_triggered and
                (current_time - self.last_force_time) > self.force_loop_closure_timeout
            )
            
            if should_force_loop:
                self.trigger_force_loop_closure(loop_distance)
            
            # 发布回环状态
            self.loop_status_pub.publish(Bool(data=self.loop_closure_triggered))
    
    def trigger_force_loop_closure(self, distance):
        """触发强制回环检测"""
        rospy.logwarn(f"🔄 触发强制回环检测!")
        rospy.logwarn(f"📏 当前距离起点: {distance:.2f}m (阈值: {self.loop_closure_distance_threshold:.1f}m)")
        rospy.logwarn(f"📐 轨迹总长度: {self.trajectory_length:.1f}m")
        
        # 发布强制回环信号
        force_msg = Bool()
        force_msg.data = True
        self.force_loop_closure_pub.publish(force_msg)
        
        # 更新状态
        self.loop_closure_triggered = True
        self.last_force_time = time.time()
        
        # 记录GPS信息
        if self.current_gps_position is not None:
            lat, lon, alt = self.current_gps_position
            rospy.loginfo(f"📍 当前GPS位置: lat={lat:.8f}, lon={lon:.8f}, alt={alt:.3f}")
        
        if self.start_gps_position is not None:
            lat, lon, alt = self.start_gps_position
            rospy.loginfo(f"📍 起点GPS位置: lat={lat:.8f}, lon={lon:.8f}, alt={alt:.3f}")
    
    def get_gps_quality_name(self, status):
        """获取GPS质量名称"""
        quality_names = {
            -1: "NO_FIX",
            0: "RTK_FIXED", 
            1: "SBAS_FIX",
            2: "GBAS_FIX",
            3: "OTHER"
        }
        return quality_names.get(status, f"UNKNOWN({status})")
    
    def reset_loop_closure(self):
        """重置回环状态"""
        with self.data_lock:
            self.loop_closure_triggered = False
            rospy.loginfo("🔄 回环状态已重置")
    
    def get_status_info(self):
        """获取状态信息"""
        with self.data_lock:
            if self.start_gps_position is None or self.current_gps_position is None:
                return "等待GPS数据..."
            
            loop_distance = self.calculate_gps_distance(
                self.start_gps_position, 
                self.current_gps_position
            )
            
            status = f"""
GPS强制回环检测状态:
📏 距离起点: {loop_distance:.2f}m (阈值: {self.loop_closure_distance_threshold:.1f}m)
📐 轨迹长度: {self.trajectory_length:.1f}m (最小: {self.min_trajectory_length:.1f}m)
🔄 回环状态: {'已触发' if self.loop_closure_triggered else '未触发'}
📍 起点GPS: {self.start_gps_position[0]:.8f}, {self.start_gps_position[1]:.8f}
📍 当前GPS: {self.current_gps_position[0]:.8f}, {self.current_gps_position[1]:.8f}
            """
            return status.strip()

    def publish_status_markers(self):
        """发布状态标记用于RViz可视化"""
        try:
            from visualization_msgs.msg import Marker, MarkerArray
            from geometry_msgs.msg import Point

            if not hasattr(self, 'marker_pub'):
                self.marker_pub = rospy.Publisher('/gps_loop_closure_markers', MarkerArray, queue_size=1)

            markers = MarkerArray()

            with self.data_lock:
                if self.start_gps_position is not None and self.current_gps_position is not None:
                    # 起点标记
                    start_marker = Marker()
                    start_marker.header.frame_id = "map"
                    start_marker.header.stamp = rospy.Time.now()
                    start_marker.ns = "gps_loop_closure"
                    start_marker.id = 0
                    start_marker.type = Marker.SPHERE
                    start_marker.action = Marker.ADD
                    start_marker.pose.position.x = 0  # 相对于起点
                    start_marker.pose.position.y = 0
                    start_marker.pose.position.z = 0
                    start_marker.pose.orientation.w = 1.0
                    start_marker.scale.x = 2.0
                    start_marker.scale.y = 2.0
                    start_marker.scale.z = 2.0
                    start_marker.color.r = 0.0
                    start_marker.color.g = 1.0
                    start_marker.color.b = 0.0
                    start_marker.color.a = 0.8
                    markers.markers.append(start_marker)

                    # 阈值圆圈
                    circle_marker = Marker()
                    circle_marker.header.frame_id = "map"
                    circle_marker.header.stamp = rospy.Time.now()
                    circle_marker.ns = "gps_loop_closure"
                    circle_marker.id = 1
                    circle_marker.type = Marker.CYLINDER
                    circle_marker.action = Marker.ADD
                    circle_marker.pose.position.x = 0
                    circle_marker.pose.position.y = 0
                    circle_marker.pose.position.z = 0
                    circle_marker.pose.orientation.w = 1.0
                    circle_marker.scale.x = self.loop_closure_distance_threshold * 2
                    circle_marker.scale.y = self.loop_closure_distance_threshold * 2
                    circle_marker.scale.z = 0.1
                    circle_marker.color.r = 1.0
                    circle_marker.color.g = 1.0
                    circle_marker.color.b = 0.0
                    circle_marker.color.a = 0.3
                    markers.markers.append(circle_marker)

            self.marker_pub.publish(markers)

        except ImportError:
            pass  # visualization_msgs不可用时跳过

def main():
    try:
        optimizer = GPSLoopClosureOptimizer()

        # 状态监控定时器
        def print_status(event):
            status_info = optimizer.get_status_info()
            rospy.loginfo_throttle(10, status_info)
            # 发布可视化标记
            optimizer.publish_status_markers()

        status_timer = rospy.Timer(rospy.Duration(5.0), print_status)

        rospy.loginfo("✅ GPS强制回环检测优化器运行中...")
        rospy.loginfo("💡 使用说明:")
        rospy.loginfo("   - 当GPS距离起点小于%.1fm时，将发布强制回环信号", optimizer.loop_closure_distance_threshold)
        rospy.loginfo("   - 监听话题 /force_loop_closure 获取强制回环信号")
        rospy.loginfo("   - 监听话题 /loop_closure_distance 获取回环距离")
        rospy.loginfo("   - 监听话题 /trajectory_length 获取轨迹长度")
        rospy.loginfo("   - 在RViz中查看 /gps_loop_closure_markers 可视化")

        rospy.spin()

    except rospy.ROSInterruptException:
        rospy.loginfo("🛑 GPS强制回环检测优化器已停止")

if __name__ == '__main__':
    main()
