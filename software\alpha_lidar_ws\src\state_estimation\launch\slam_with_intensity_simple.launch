<?xml version="1.0"?>
<launch>
    <!-- 简化的带强度保持的SLAM系统启动文件 -->
    <!-- 避免节点名称冲突，直接启动所需组件 -->
    
    <!-- 参数配置 -->
    <arg name="intensity_preset" default="high_quality_preset" />
    <arg name="gps_loop_preset" default="poor_gps_preset" />
    <arg name="save_directory" default="/home/<USER>/slam_share/aLidar/intensity_preserved" />
    <arg name="enable_intensity_preservation" default="true" />
    <arg name="enable_gps_loop_closure" default="true" />
    
    <!-- 配置文件路径 -->
    <arg name="intensity_config_file" default="$(find state_estimation)/config/intensity_preserving_config.yaml" />
    <arg name="gps_config_file" default="$(find state_estimation)/config/gps_loop_closure_params.yaml" />
    
    <!-- 基础SLAM系统 -->
    <include file="$(find state_estimation)/launch/mapping_robosense.launch" />
    
    <!-- GPS回环检测系统 -->
    <group if="$(arg enable_gps_loop_closure)">
        <!-- 加载GPS回环检测配置 -->
        <rosparam file="$(arg gps_config_file)" command="load" />
        <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)" />
        
        <!-- GPS回环检测优化器 -->
        <node name="gps_loop_optimizer" pkg="state_estimation" type="enhanced_gps_loop_closure_optimizer.py" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_gps_loop_closure_optimizer" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_gps_loop_closure_optimizer" />
            <remap from="/rtk/gnss" to="/rtk/gnss" />
        </node>
        
        <!-- SLAM回环检测集成模块 -->
        <node name="slam_loop_integration" pkg="state_estimation" type="enhanced_slam_loop_closure_integration" output="screen">
            <rosparam file="$(arg gps_config_file)" command="load" ns="enhanced_slam_loop_closure_integration" />
            <rosparam file="$(arg gps_config_file)" command="load" ns="$(arg gps_loop_preset)/enhanced_slam_loop_closure_integration" />
        </node>
    </group>
    
    <!-- 强度值保持系统 -->
    <group if="$(arg enable_intensity_preservation)">
        <!-- 加载强度保持配置 -->
        <rosparam file="$(arg intensity_config_file)" command="load" />
        <rosparam file="$(arg intensity_config_file)" command="load" ns="$(arg intensity_preset)" />
        
        <!-- 强度保持PCD保存模块 -->
        <node name="intensity_pcd_saver" pkg="state_estimation" type="intensity_preserving_pcd_saver" output="screen">
            <param name="save_directory" value="$(arg save_directory)" />
            <remap from="/velodyne_points" to="/velodyne_points" />
            <remap from="/cloud_registered" to="/cloud_registered" />
            <remap from="/force_loop_closure" to="/force_loop_closure" />
            <remap from="/aft_mapped_to_init" to="/aft_mapped_to_init" />
        </node>
        
        <!-- 强度值质量监控 -->
        <node name="intensity_monitor" pkg="state_estimation" type="intensity_quality_monitor.py" output="screen">
            <param name="monitor_topic" value="/enhanced_pointcloud_with_intensity" />
            <param name="statistics_topic" value="/intensity_statistics" />
            <param name="quality_report_topic" value="/pointcloud_quality_report" />
            <param name="report_interval" value="10.0" />
        </node>
    </group>
    
    <!-- 参数服务器配置 -->
    <rosparam>
        # 强度值保持系统配置
        intensity_preservation:
            preserve_original_intensity: true
            enable_intensity_restoration: true
            save_with_loop_correction: true
            intensity_range_validation: true
            geometric_consistency_check: true
            save_incremental_maps: true
            save_final_optimized_map: true
            publish_intensity_statistics: true
            log_processing_performance: true
    </rosparam>
    
</launch>
